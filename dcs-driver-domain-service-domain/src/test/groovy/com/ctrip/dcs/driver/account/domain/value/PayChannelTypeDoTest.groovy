package com.ctrip.dcs.driver.account.domain.value

import com.ctrip.dcs.driver.account.domain.service.PayChannelTypeGreyUtil
import com.ctrip.dcs.driver.account.infrastructure.constant.PayChannelTypeEnum
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification
import spock.lang.Unroll

/*
作者：pl.yang
创建时间：2025/5/20-下午8:18-2025
*/
class PayChannelTypeDoTest extends Specification {
    @Unroll
    def "测试支付渠道类型判断：场景描述=#scenario"() {
        given: "初始化测试数据"

        try (MockedStatic<PayChannelTypeGreyUtil> utilities = Mockito.mockStatic(PayChannelTypeGreyUtil.class)) {
            utilities.when { PayChannelTypeGreyUtil.isGrey(driverId) }.thenReturn(isGrey)

            when: "创建支付渠道类型对象并获取结果"
            def payChannel = new PayChannelTypeDo(null, isOversea, driverId)
            def result = payChannel.payChannelTypeV2()

            then: "验证返回结果"
            result == expectedResult
        }

        where:
        scenario                   | isOversea | driverId  | isGrey | expectedResult
        "境外用户在灰度名单中"     | true      | "D123456" | true   | PayChannelTypeEnum.YEEPAY.code
        "境外用户不在灰度名单中"   | true      | "D654321" | false  | PayChannelTypeEnum.PAYONEER.code
        "境内用户（灰度名单中）"     | false     | "D111111" | true   | PayChannelTypeEnum.PAYONEER.code
        "境内用户（不在灰度名单中）" | false     | "D222222" | false  | PayChannelTypeEnum.PAYONEER.code
    }


}

