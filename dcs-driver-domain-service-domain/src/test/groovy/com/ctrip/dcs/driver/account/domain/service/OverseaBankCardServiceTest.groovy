package com.ctrip.dcs.driver.account.domain.service

import com.ctrip.dcs.driver.account.domain.condition.BindOverseaBankCardCondition
import com.ctrip.dcs.driver.account.domain.enums.BankCardResultEnum
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum
import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardStatusEnum
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig
import com.ctrip.dcs.driver.account.infrastructure.value.AccountIdentityDTO
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo
import com.ctrip.dcs.driver.gateway.*
import com.ctrip.dcs.driver.value.BaseResult
import com.ctrip.dcs.driver.value.bank.BankCardResultModel
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidResponseType
import com.ctrip.igt.ResponseResult
import org.mockito.MockitoAnnotations
import spock.lang.Specification

/*
作者：pl.yang
创建时间：2025/5/20-下午7:19-2025
*/

class OverseaBankCardServiceTest extends Specification {
    AccountService accountService = Mock(AccountService)
    AccountBankCardRecordRepository accountBankCardRecordRepository = Mock(AccountBankCardRecordRepository)
    PaymentRouterRepository paymentRouterRepository = Mock(PaymentRouterRepository)
    CardInfoManagementRepository cardInfoManagementRepository = Mock(CardInfoManagementRepository)
    YeePayRepository yeePayRepository = Mock(YeePayRepository)
    ArchCoreInfoRepository archCoreInfoRepository = Mock(ArchCoreInfoRepository)
    DriverWalletInfoConfig driverWalletInfoConfig=Mock(DriverWalletInfoConfig)
    //driverAccountConfig
    DriverAccountConfig driverAccountConfig = Mock(DriverAccountConfig)

    OverseaBankCardService service = new OverseaBankCardService()
    //qmqSendRepository
     QmqSendRepository qmqSendRepository = Mock(QmqSendRepository)

    def setup() {
        service.accountService = accountService
        service.accountBankCardRecordRepository = accountBankCardRecordRepository
        service.paymentRouterRepository = paymentRouterRepository
        service.cardInfoManagementRepository = cardInfoManagementRepository
        service.yeePayRepository = yeePayRepository
        service.archCoreInfoRepository = archCoreInfoRepository
        service.driverWalletInfoConfig = driverWalletInfoConfig
        service.driverAccountConfig = driverAccountConfig
        service.qmqSendRepository=qmqSendRepository
        driverAccountConfig.isCheckBandCardTypeIsDepositCard() >> true

        MockitoAnnotations.initMocks(this)
    }

    // 场景1：银行卡信息为空
    def "当银行卡信息为空时返回错误码12001"() {
        given: "构建空银行卡信息请求"
        def condition = BindOverseaBankCardCondition.builder()
                .bankCardInfo(null)
                .build()

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证返回结果"
        result.code == "12001"
        result.message == "The bank card information is empty"
    }

    // 场景2：用户不存在
    def "当用户不存在时返回错误码15002"() {
        given: "构建有效请求"
        def condition = BindOverseaBankCardCondition.builder()
                .uid("nonExistUser")
        .driverId(12345L)
                .bankCardInfo(AccountBankCardDo.builder().build())
                .build()

        and: "模拟用户不存在"
        accountService.getAccountInfoByUID("nonExistUser") >> null

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证返回结果"
        result.code == "15002"
        result.message.contains("The user nonExistUser does not exist")
    }

    // 场景3：存在已绑定的银行卡
    def "当存在已绑定的银行卡时返回错误码15003"() {
        given: "构建有效请求"
        def condition = BindOverseaBankCardCondition.builder()
                .uid("boundUser")
                .driverId(12345L)
                .bankCardInfo(AccountBankCardDo.builder().build())
                .build()

        and: "模拟已绑定记录"
        def boundCard = AccountBankCardDo.builder()
                .cardStatus(BankCardStatusDo.builder()
                        .bankCardStatusEnum(BankCardStatusEnum.BIND)
                        .build())
                .build()
        accountService.getAccountInfoByUID("boundUser") >> new AccountInfoDTO()
        accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(_) >> BaseResult.successResult(boundCard)

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证返回结果"
        result.code == "15003"
        result.message.contains("There are bound cards")
    }

    // 场景4：银行卡类型不是储蓄卡
    def "当银行卡类型为信用卡时返回错误码15004"() {
        given: "构建有效请求"
        def condition = BindOverseaBankCardCondition.builder()
                .uid("creditCardUser")
                .driverId(12345L)
                .bankCardInfo(AccountBankCardDo.builder().cardNo("123456").build())
                .build()

        and: "模拟返回信用卡类型"
        accountService.getAccountInfoByUID("creditCardUser") >> new AccountInfoDTO()
        accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(_) >> BaseResult.successResult(null)
        paymentRouterRepository.queryBankChannelInfo(_, _, _) >> new BankCardResultModel(
                resultCode: BankCardResultEnum.OK,
                cardBankType: 1
        )

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证返回结果"
        result.code == "15004"
        result.message.contains("not a deposit card")
    }

    // 场景5：境内卡参数校验失败
    def "当境内卡参数校验失败时返回错误码15006"() {
        given: "构建境内卡请求"
        def bankCard = AccountBankCardDo.builder()
                .isOverseaBankCard(false)
                .cardNo("622202")
                .idNumber("encryptedId")
                .phoneNo("***********")
                .build()

        def condition = BindOverseaBankCardCondition.builder()
                .uid("domesticUser")
                .driverId(12345L)
                .bankCardInfo(bankCard)
                .build()

        and: "模拟参数校验失败"
        def accountInfo = new AccountInfoDTO(name: "张三", phoneNumber: "***********")

        accountService.getAccountInfoByUID("domesticUser") >> accountInfo
        accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(_) >> BaseResult.successResult(null)
        paymentRouterRepository.queryBankChannelInfo(_, _, _) >> new BankCardResultModel(
                resultCode: BankCardResultEnum.OK,
                cardBankType: 2
        )
        archCoreInfoRepository.decryptIdCard(_) >> "310101199001011234"
        yeePayRepository.checkDriverBandCardValid(_, _, _, _, _, _) >>
                new CheckDriverBandCardValidResponseType(
                        responseResult: new ResponseResult(success: false),
                        businessCode: "CHECK_FAIL",
                        businessMsg: "验证失败"
                )

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证返回结果"
        result.code == "15006"
        result.message.contains("checkBankCardPram error")
    }
    // 场景6：保存到金融系统失败
    def "当保存到金融系统失败时返回错误码15005"() {
        given: "构建有效请求"
        def condition = BindOverseaBankCardCondition.builder()
                .uid("financeFailUser")
                .driverId(1000065)
                .bankCardInfo(AccountBankCardDo.builder().cardNo("622848").build())
                .build()

        and: "模拟金融系统返回空"
        accountService.getAccountInfoByUID("financeFailUser") >> new AccountInfoDTO()
        accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(_) >> BaseResult.successResult(null)
        paymentRouterRepository.queryBankChannelInfo(_, _, _) >> new BankCardResultModel(resultCode: BankCardResultEnum.OK, cardBankType: 2)
        yeePayRepository.checkDriverBandCardValid(_, _, _, _, _, _) >> new CheckDriverBandCardValidResponseType(
                responseResult: new ResponseResult(success: true),
                businessCode: "CHECK_SUCCESS",
                businessMsg: "验证成功"
        )
        cardInfoManagementRepository.createCardInfo(_, _) >> null

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证返回结果"
        result.code == "15005"
        result.message == "createCardInfo error"
    }

    // 场景7：易宝已绑定且数据库更新成功
    def "当易宝已绑定时返回成功并更新数据库"() {
        given: "构建境外卡请求"
        def bankCard = AccountBankCardDo.builder()
                .isOverseaBankCard(true)
                .cardNo("************")
                .build()

        def condition = BindOverseaBankCardCondition.builder()
                .uid("yeepayBoundUser")
                .driverId(1000065)
                .bankCardInfo(bankCard)
                .build()

        and: "模拟易宝已绑定状态"
        def accountInfo = new AccountInfoDTO(identityDTOList: [new AccountIdentityDTO(sourceId: "DRV123", valid: true, source: AccountSouceEnum.DRIVER_SOURCE.name)])

        accountService.getAccountInfoByUID("yeepayBoundUser") >> accountInfo
        accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(_) >> BaseResult.successResult(null)
        paymentRouterRepository.queryBankChannelInfo(_, _, _) >> new BankCardResultModel(
                resultCode: BankCardResultEnum.OK,
                cardBankType: 2
        )
        cardInfoManagementRepository.createCardInfo(_, _) >> "SAVED_CARD_123"
        yeePayRepository.queryBankCardStatus(_) >> BankCardStatusDo.builder().bankCardStatusEnum(BankCardStatusEnum.BIND).build()
        accountBankCardRecordRepository.saveOrUpdate(_) >> 1

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证成功结果"
        result.code == "200"
        result.message == "Successful binding"
    }

    // 场景8：易宝未绑定但提交成功
    def "当易宝未绑定但提交成功时返回成功"() {
        given: "构建有效请求"
        def bankCard = AccountBankCardDo.builder()
                .isOverseaBankCard(true)
                .driverId(1000065)
                .cardNo("************")
                .build()

        def condition = BindOverseaBankCardCondition.builder()
                .uid("yeepayNewUser")
                .driverId(1000065)
                .bankCardInfo(bankCard)
                .build()

        and: "模拟易宝未绑定状态"
        def accountInfo = new AccountInfoDTO(identityDTOList: [new AccountIdentityDTO(sourceId: "DRV123", valid: true, source: AccountSouceEnum.DRIVER_SOURCE.name)])

        accountService.getAccountInfoByUID("yeepayNewUser") >> accountInfo
        accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(_) >> BaseResult.successResult(null)
        paymentRouterRepository.queryBankChannelInfo(_, _, _) >> new BankCardResultModel(
                resultCode: BankCardResultEnum.OK,
                cardBankType: 2
        )
        cardInfoManagementRepository.createCardInfo(_, _) >> "SAVED_CARD_123"
        yeePayRepository.queryBankCardStatus(_) >> BankCardStatusDo.builder().bankCardStatusEnum(BankCardStatusEnum.UNBIND).build()
        yeePayRepository.submitYeepayRecipientReport(_) >> BaseResult.successResult(true)
        accountBankCardRecordRepository.saveOrUpdate(_) >> 1

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证成功结果"
        result.code == "200"
        result.message == "Successful binding"
    }

    // 场景9：易宝提交失败
    def "当易宝提交失败时返回对应错误码"() {
        given: "构建有效请求"
        def bankCard = AccountBankCardDo.builder()
                .isOverseaBankCard(true)
                .cardNo("************")
                .driverId(12345L)
                .build()

        def condition = BindOverseaBankCardCondition.builder()
                .uid("yeepayFailUser")
                .driverId(1000065)
                .bankCardInfo(bankCard)
                .build()

        and: "模拟易宝提交失败"
        def accountInfo = new AccountInfoDTO(identityDTOList:  [new AccountIdentityDTO(sourceId: "DRV123",  valid: true,source: AccountSouceEnum.DRIVER_SOURCE.name)])

        accountService.getAccountInfoByUID("yeepayFailUser") >> accountInfo
        accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(_) >> BaseResult.successResult(null)
        paymentRouterRepository.queryBankChannelInfo(_, _, _) >> new BankCardResultModel(
                resultCode: BankCardResultEnum.OK,
                cardBankType: 2
        )
        cardInfoManagementRepository.createCardInfo(_, _) >> "SAVED_CARD_123"
        yeePayRepository.queryBankCardStatus(_) >> BankCardStatusDo.builder().bankCardStatusEnum(BankCardStatusEnum.UNBIND).build()
        yeePayRepository.submitYeepayRecipientReport(_) >> BaseResult.failResult("YE_001", "商户信息校验失败")
        driverWalletInfoConfig.getOverseaBindCardFailReasonErrorCode(_)>> "YE_001"

        when: "执行绑卡操作"
        def result = service.doBindOverseaBankCard(condition)

        then: "验证失败结果"
        result.code == "YE_001"
        assert result.message.contains("商户信息校验失败")
    }


}

