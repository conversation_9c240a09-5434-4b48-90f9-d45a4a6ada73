package com.ctrip.dcs.driver.account.domain.service


import cn.hutool.core.date.DateUtil
import com.ctrip.dcs.driver.account.domain.value.DriverInfoDo
import com.ctrip.dcs.driver.account.infrastructure.qconfig.YibaoGreyQConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfig
import com.ctrip.dcs.driver.gateway.DriverInfoRepository
import com.ctrip.dcs.geo.domain.repository.TimeZoneRepository
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime

/*
作者：pl.yang
创建时间：2025/5/20-下午8:32-2025
*/

class PayChannelTypeGreyTest extends Specification {
    PayChannelTypeGrey payChannelTypeGrey = new PayChannelTypeGrey()
    YibaoGreyQConfig mockConfig = Mock(YibaoGreyQConfig)
    TimeZoneRepository mockTimeRepo = Mock(TimeZoneRepository)
    DriverInfoRepository mockDriverRepo = Mock(DriverInfoRepository)
    BusinessConfig businessConfig = Mock(BusinessConfig)

    def setup() {
        payChannelTypeGrey.businessConfig = businessConfig;
        businessConfig.getDriverInfoCacheExpireMinutes() >> 300
        payChannelTypeGrey.init()
        payChannelTypeGrey.yibaoGreyQConfig = mockConfig
        payChannelTypeGrey.timeZoneRepository = mockTimeRepo
        payChannelTypeGrey.driverInfoRepository = mockDriverRepo
    }

    @Unroll
    def "测试灰度判断：场景=#scenario"() {
        given: "配置灰度开关和参数"
        mockConfig.getYibaoGreySwitch() >> greySwitch
        mockConfig.getYibaoGreyConfig() >> configList

        and: "模拟时间转换"
        mockTimeRepo.transform(2L, _, cityId) >> convertedTime

        and: "模拟司机信息查询"
        mockDriverRepo.queryDriverInfoByTms(_) >> new DriverInfoDo(cityId: cityId)

        when: "执行灰度判断"
        def result = payChannelTypeGrey.inner_isGrey(driverId)

        then: "验证结果"
        result == expectedResult

        where:
        scenario           | greySwitch | driverId  | cityId | configList                                                               | convertedTime           | expectedResult
        // 开关关闭直接返回true
        "灰度开关关闭"     | false      | "1001"    | 100    | ""                                                                       | LocalDateTime.MIN       | true

        // 司机ID在灰度名单
        "司机ID在灰度名单" | true       | "347602"  | 200    | [config(driverIds: "347602", time: "2025-05-10 00:00:00")]               | parseTime("2025-05-15") | true

        // 城市ID在灰度名单
        "城市ID在灰度名单" | true       | "2000"    | 788    | [config(cityIds: "788", time: "2025-05-10 00:00:00")]                    | parseTime("2025-05-20") | true

        // 司机ID和城市都不在名单
        "不在任何灰度名单" | true       | "9999"    | 999    | [config(driverIds: "1001", cityIds: "100", time: "2025-05-10 00:00:00")] | parseTime("2025-05-15") | false

        // 时间不满足条件
        "时间早于配置时间" | true       | "347602"  | 200    | [config(driverIds: "347602", time: "2025-05-20 00:00:00")]               | parseTime("2025-05-15") | false

        // 无效司机ID格式
        "无效司机ID格式"   | true       | "invalid" | 300    | []                                                                       | LocalDateTime.MIN       | false
    }

    def "测试缓存有效性"() {
        given: "配置有效司机信息"
        mockConfig.getYibaoGreySwitch() >> true
        mockDriverRepo.queryDriverInfoByTms(1001L) >> new DriverInfoDo(cityId: 200)
        mockDriverRepo.queryDriverInfoByGms(1001L) >> null

        when: "首次查询并缓存"
        def result1 = payChannelTypeGrey.getCityIdByDriderId("1001")

        then: "验证缓存结果"
        result1 == 200

        when: "再次查询"
        mockDriverRepo.queryDriverInfoByTms(1001L) >> new DriverInfoDo(cityId: 300)
        def result2 = payChannelTypeGrey.getCityIdByDriderId("1001")

        then: "验证缓存未更新"
        result2 == 200
    }

    // 辅助方法
    private static LocalDateTime parseTime(String time) {
        return DateUtil.parse(time).toLocalDateTime()
    }

    private static YibaoGreyQConfig.YibaoGreyConfigDO config(Map params) {
        new YibaoGreyQConfig.YibaoGreyConfigDO(
                driverIds: params.driverIds ?: "",
                cityIds: params.cityIds ?: "",
                time: params.time
        )
    }

}

