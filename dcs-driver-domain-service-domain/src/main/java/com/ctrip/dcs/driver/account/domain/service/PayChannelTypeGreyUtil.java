package com.ctrip.dcs.driver.account.domain.service;
/*
作者：pl.yang
创建时间：2025/5/8-下午7:59-2025
*/


import com.ctrip.dcs.driver.account.infrastructure.qconfig.YibaoGreyQConfig;
import com.ctrip.igt.framework.common.spring.InstanceLocator;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PayChannelTypeGreyUtil
 * @Package com.ctrip.dcs.driver.account.infrastructure.utils
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 下午7:59
 */

//支付灰度工具
public class PayChannelTypeGreyUtil {


    public static boolean isGrey(String driverOrDriverGuideDriverIdStr) {
        return InstanceLocator.getInstance(PayChannelTypeGrey.class).inner_isGrey(driverOrDriverGuideDriverIdStr);
    }
}
