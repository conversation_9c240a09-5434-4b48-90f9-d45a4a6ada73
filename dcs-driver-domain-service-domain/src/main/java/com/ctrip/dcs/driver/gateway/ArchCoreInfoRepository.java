package com.ctrip.dcs.driver.gateway;

import com.ctrip.arch.coreinfo.enums.KeyType;
import java.util.List;
import java.util.Map;
public interface ArchCoreInfoRepository {
    String decryptIdCard(String decryptedIdCard);

    String encryptIdCard(String idCard);

    String encryptByType(KeyType keyType, String value);

    Map<String/*加密前的信息*/,String/*加密后的信息*/> batchEncryptByType(KeyType keyType, List<String> values);

    String decryptByType(KeyType keyType, String decryptValue);

    boolean isEncrypt(KeyType keyType, String value);
}
