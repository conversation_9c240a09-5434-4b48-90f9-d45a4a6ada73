package com.ctrip.dcs.driver.account.domain.service;

import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.dcs.driver.account.domain.condition.PhoneInfoCondition;
import com.ctrip.dcs.driver.account.domain.condition.RegisterDriverUdlCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.RegisterNewAccountParam;
import com.ctrip.dcs.driver.account.infrastructure.value.RegisterNewAccountV1Param;
import com.ctrip.dcs.driver.account.infrastructure.value.UpdateAccountParam;
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType;
import com.ctrip.dcs.driver.value.account.AccountUidInfo;

import java.util.List;

public interface AccountService {
    AccountBaseInfoPO register(RegisterNewAccountParam param);
    //向导注册
    AccountUidInfo registerV1(RegisterNewAccountV1Param param);

    UpdateAccountResponseType updateAccount(UpdateAccountParam param);

    AccountInfoDTO getAccountInfoByUID(String uid);

    //通过司机id查询 udl
    List<AccountUDLDo> getAccountUDLBySource(List<String> sourceIds);

    List<AccountUDLDo> getAccountUDLByUid(List<String> uids);

    AccountInfoDTO getAccountInfoBySource(String source, String sourceId);

    List<AccountInfoDTO> getAccountInfoByName(String name);

    List<AccountInfoDTO> getAccountInfoByIdCard(String idCard);

    AccountInfoDTO getAccountInfoByMobilePhone(String countryCode, String phoneNumber);

    /**
     * 批量通过手机号查询账号信息
     * @param phoneInfoConditions
     * @return
     */
    List<AccountInfoDTO> batchGetAccountInfoByMobilePhone(List<PhoneInfoCondition> phoneInfoConditions);

    AccountInfoDTO getAccountInfoByEmail(String email);

    AccountInfoDTO getAccountInfoByPayoneer(String payoneerAccountId);

    List<AccountInfoDTO> batchGetAccountInfoByUID(List<String> uidList);

    AccountInfoDTO getAccountByDriverId(Long driverId);

    List<AccountInfoDTO> batchGetAccountByDriverId(List<Long> driverIdList);

    List<AccountMapperPO> getIdentityByUid(String uid);

    List<AccountMapperPO> getIdentityByDriverId(Long driverId);

    void updateAccountIdentityState(String uid, AccountSouceEnum sourceEnum, boolean valid);

    void refreshAccountCache(String uid);

    void reCalcDriverIdentityState(String uid);

    NumberDTO splitPhoneNumber(String countryCode, String number);

    /**
     * 专门用来注册ota司机的udl的
     * @param registerDriverUdlCondition
     * @return
     */
    AccountUDLDo registerDriverUdl(RegisterDriverUdlCondition registerDriverUdlCondition);
}
