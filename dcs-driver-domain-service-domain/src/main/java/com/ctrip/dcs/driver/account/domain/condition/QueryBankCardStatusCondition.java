package com.ctrip.dcs.driver.account.domain.condition;
/*
作者：pl.yang
创建时间：2025/5/9-下午4:29-2025
*/


import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: QueryBankCardStatusCondition
 * @Package com.ctrip.dcs.driver.account.domain.condition
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/9 下午4:29
 */

@Builder
@Getter
public class QueryBankCardStatusCondition {
    private String requestId;
    //accountNumber
    //【银行账户号码】仅【境内收款人】需要传入
    private String accountNumber;
    private String type;
}
