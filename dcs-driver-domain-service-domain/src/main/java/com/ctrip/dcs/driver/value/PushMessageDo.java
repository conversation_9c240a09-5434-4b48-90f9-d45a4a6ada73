package com.ctrip.dcs.driver.value;
/*
作者：pl.yang
创建时间：2025/5/8-下午9:08-2025
*/


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PushMessageDo
 * @Package com.ctrip.dcs.driver.value
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 下午9:08
 */

@Data
public class PushMessageDo {
    private List<Long> driverIds;
    private List<Long> guideIds;
    private List<String> pushUidList;
    private String templateId;
    private Map<String, String> sharkValues;
    private Map<String, String> data;
}
