package com.ctrip.dcs.driver.gateway;
/*
作者：pl.yang
创建时间：2025/4/24-下午8:04-2025
*/


import com.ctrip.dcs.driver.account.domain.condition.RegisterDriverUdlCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AcoountGateway
 * @Package com.ctrip.dcs.driver.account.infrastructure.gateway
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/24 下午8:04
 */


public interface AccountRepository {

    AccountUDLDo saveOtaDriverUdl(RegisterDriverUdlCondition accountUDLDo);

    List<AccountUDLDo> batchQueryByUid(List<String> sourceIds);
    List<AccountUDLDo> batchQueryBySourceId(List<String> sourceIds);

    String createOtaUid(String driverId);
}
