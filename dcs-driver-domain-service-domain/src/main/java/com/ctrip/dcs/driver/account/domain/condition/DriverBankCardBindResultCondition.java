package com.ctrip.dcs.driver.account.domain.condition;
/*
作者：pl.yang
创建时间：2025/5/8-下午8:44-2025
*/


import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DriverBankCardBindResultCondition
 * @Package com.ctrip.dcs.driver.account.domain.condition
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 下午8:44
 */

@Data
public class DriverBankCardBindResultCondition {
    private String requestId;
    //http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
    private String reasonCode;
    private String type;
    //SUCCESS
    private String status;
}
