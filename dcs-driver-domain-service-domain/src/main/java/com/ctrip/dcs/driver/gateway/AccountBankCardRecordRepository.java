package com.ctrip.dcs.driver.gateway;
/*
作者：pl.yang
创建时间：2025/5/7-下午8:15-2025
*/

import com.ctrip.dcs.driver.account.domain.condition.AccountBankCardProcessingCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.value.BaseResult;

import java.util.List;

//
public interface AccountBankCardRecordRepository {
    AccountBankCardDo queryAccountBankCard(String uid, String idCard);

    //从db查询数据，又从金融那边查询数据 获取该用户绑定的最新的银行卡信息
    BaseResult<AccountBankCardDo> queryAccountBindingLastBankCardV2(String uid);


    int insert(AccountBankCardDo insertAccountBankCardRecord);

    int update(AccountBankCardDo accountBankCardRecord);

    BaseResult<AccountBankCardDo> queryByRequestId(String requestId);

    //不存在则插入，存在则更新
    int saveOrUpdate(AccountBankCardDo insertAccountBankCardRecord);

    AccountBankCardDo queryAccountBankCardByCard(String idCard);

    int updateBankCardRecordStatus(AccountBankCardDo data);

    /**
     *
     * @param accountBankCardProcessingCondition
     * @return
     */
    List<AccountBankCardDo> scanDataBankCardProcessing(AccountBankCardProcessingCondition accountBankCardProcessingCondition);

    /**
     * 银行卡号 查询数据
     * @param bardCardNo
     * @return
     */
    BaseResult<AccountBankCardDo> queryAccountBankCard(String bardCardNo);

}
