package com.ctrip.dcs.driver.account.domain.service;
/*
作者：pl.yang
创建时间：2025/5/19-下午9:15-2025
*/


import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.FIFOCache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctrip.dcs.driver.account.domain.value.DriverInfoDo;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.YibaoGreyQConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfig;
import com.ctrip.dcs.driver.gateway.DriverInfoRepository;
import com.ctrip.dcs.geo.domain.repository.TimeZoneRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PayChannelTypeGrey
 * @Package com.ctrip.dcs.driver.account.infrastructure.utils
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/19 下午9:15
 */

@Component
public class PayChannelTypeGrey {
    public static final Logger logger = LoggerFactory.getLogger(PayChannelTypeGrey.class);
    //todo 缓存5分钟 需要评估是否需要加 缓存呢，如果不需要则需要取消掉
    public FIFOCache<String, Long> cacheCityId =null;
    @Autowired
    BusinessConfig businessConfig;

    @PostConstruct
    public void init() {
        cacheCityId = CacheUtil.newFIFOCache(10000, businessConfig.getDriverInfoCacheExpireMinutes() * 1000L);
    }

    @Autowired
    YibaoGreyQConfig yibaoGreyQConfig;
    @Autowired
    TimeZoneRepository timeZoneRepository;
    @Autowired
    DriverInfoRepository driverInfoRepository;


    public boolean inner_isGrey(String driverId) {
        if(!yibaoGreyQConfig.getYibaoGreySwitch()){
            //关闭灰度 标识 开了全量，则直接返回true
            return true;
        }
        if (yibaoGreyQConfig.getYibaoGreySwitch()) {
            int cityId = (int) getCityIdByDriderId(driverId);
            LocalDateTime localDateTime = bjTimeconvertLocalDateTime(cityId, LocalDateTimeUtil.now());
            if (greyCityId(cityId, localDateTime)) {
                logger.info("greyCityId", "driverId is:" + driverId);
                Cat.logEvent(CatEventType.PayChannelTypeGrey, "greyCity");
                return true;
            }
            if (greyDriverId(driverId, localDateTime)) {
                logger.info("greyDriverId", "driverId is:" + driverId);
                return true;
            }
            logger.info("not grey", "driverId is:" + driverId);
        }
        return false;
    }

    private boolean greyDriverId(String driverId, LocalDateTime dateTime) {
        List<YibaoGreyQConfig.YibaoGreyConfigDO> yibaoGreyConfig = yibaoGreyQConfig.getYibaoGreyConfig();
        if (CollUtil.isNotEmpty(yibaoGreyConfig)) {
            for (YibaoGreyQConfig.YibaoGreyConfigDO yibaoGreyConfigDO : yibaoGreyConfig) {
                List<Integer> driverIds = yibaoGreyConfigDO.getDriverIds();
                if (CollUtil.isNotEmpty(driverIds)) {
                    for (Integer selectDriverId : driverIds) {
                        if (paseInt(driverId) == selectDriverId) {
                            //命中城市，并且时间在配置时间之后 则返回true
                            if (dateTime.isAfter(yibaoGreyConfigDO.getTimeAsLocalDateTime())) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private LocalDateTime bjTimeconvertLocalDateTime(long cityId, LocalDateTime bjTime) {
        return timeZoneRepository.transform(2L, bjTime, cityId);
    }

    private boolean greyCityId(int citId, LocalDateTime dateTime) {
        List<YibaoGreyQConfig.YibaoGreyConfigDO> yibaoGreyConfig = yibaoGreyQConfig.getYibaoGreyConfig();
        if (CollUtil.isNotEmpty(yibaoGreyConfig)) {
            for (YibaoGreyQConfig.YibaoGreyConfigDO yibaoGreyConfigDO : yibaoGreyConfig) {
                List<Integer> cityIds = yibaoGreyConfigDO.getCityIds();
                if (CollUtil.isNotEmpty(cityIds)) {
                    for (Integer selectCityId : cityIds) {
                        if (citId == selectCityId) {
                            //命中城市，并且时间在配置时间之后 则返回true
                            if (dateTime.isAfter(yibaoGreyConfigDO.getTimeAsLocalDateTime())) {
                                return true;
                            }
                        }
                    }
                }
            }
        }
        return false;
    }

    private long paseInt(String driverId) {
        try {
            if (StringUtils.isNotBlank(driverId)) {
                return Long.parseLong(driverId);
            }
        } catch (Exception e) {
            return -1L;
        }
        return -1L;

    }

    //需要更新下 城市id ,不能缓存太久
    protected long getCityIdByDriderId(String drId) {
        if (StringUtils.isBlank(drId)) {
            return -1L;
        }
        return cacheCityId.get(drId, false, () -> {
            long drvIdLong = paseInt(drId);
            DriverInfoDo driverInfoDo = driverInfoRepository.queryDriverInfoByTms(drvIdLong);
            if (driverInfoDo != null) {
                return driverInfoDo.getCityId();
            }
            driverInfoDo = driverInfoRepository.queryDriverInfoByGms(drvIdLong);
            if (driverInfoDo != null) {
                return driverInfoDo.getCityId();
            }
            return -1L;

        });

    }
}
