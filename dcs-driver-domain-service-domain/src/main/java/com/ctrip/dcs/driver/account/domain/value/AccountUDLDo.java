package com.ctrip.dcs.driver.account.domain.value;
/*
作者：pl.yang
创建时间：2025/2/25-上午11:41-2025
*/


import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.dianping.cat.Cat;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountUDLDo
 * @Package com.ctrip.dcs.driver.account.domain.value
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/2/25 上午11:41
 */

@Data
@Builder
public class AccountUDLDo {
    //司机id
    private String sourceId;
    //司机uid
    private String uid;
    //账户UDL
    private String udl;
    private Long cityId;

    public Boolean getIsOversea() {
        if(StringUtils.isBlank(getUdl())){
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "UDL_Empty");
            return null;
        }
        switch (getUdl()) {
            case Constants.UDL_US_SPD:
                return true;
            case Constants.UDL_CN_CSPD:
                return false;
            default:
                //为空的 默认为国内
                return false;
        }
    }

}
