package com.ctrip.dcs.driver.account.domain.condition;
/*
作者：pl.yang
创建时间：2025/4/28-上午11:14-2025
*/


import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BindOverseaBankCardCondition
 * @Package com.ctrip.dcs.driver.account.application.condition
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 上午11:14
 */

@Getter
@Builder
public class BindOverseaBankCardCondition {
    /**
     * uid
     */
    public String uid;

    /**
     * driverId
     */
    public Long driverId;


    /**
     * 绑卡信息
     */
    public AccountBankCardDo bankCardInfo;
}
