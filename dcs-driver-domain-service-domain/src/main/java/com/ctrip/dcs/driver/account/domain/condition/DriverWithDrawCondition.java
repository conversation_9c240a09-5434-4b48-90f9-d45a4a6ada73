package com.ctrip.dcs.driver.account.domain.condition;
/*
作者：pl.yang
创建时间：2025/5/14-下午8:39-2025
*/


/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DriverWithDrawCondition
 * @Package com.ctrip.dcs.driver.account.domain.condition
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/14 下午8:39
 */

import lombok.Data;

/**
 *  //{
 *     //    "driverId":546466
 *     //    "reasonCode": "mst-*****************",
 *     //    "money": "CNY100.02",
 *     //    "reasonCode":"G06195"
 *     //}
 */
@Data
public class DriverWithDrawCondition {
    private Long driverId;
    //错误原因
    private String reasonCode;
    private String money;
    //时间
    private String datetime;

}
