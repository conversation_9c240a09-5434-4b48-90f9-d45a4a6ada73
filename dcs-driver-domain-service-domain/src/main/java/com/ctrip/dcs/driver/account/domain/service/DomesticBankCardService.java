package com.ctrip.dcs.driver.account.domain.service;
/*
作者：pl.yang
创建时间：2025/5/7-上午10:59-2025
*/


import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.domain.condition.UnbindBankCardCondition;
import com.ctrip.dcs.driver.gateway.AccountBankCardRecordRepository;
import com.ctrip.dcs.driver.value.BaseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DomesticBankCardService
 * @Package com.ctrip.dcs.driver.account.application.service.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/7 上午10:59
 */

@Component
public class DomesticBankCardService {
    @Autowired
    AccountBankCardRecordRepository accountBankCardRecordAdapter;
    public BaseResult<Void> doUnbindBankCard(UnbindBankCardCondition condition, AccountBankCardDo accountBankCardRecord) {
        accountBankCardRecord.getCardStatus().unbind();//解绑

        int result = accountBankCardRecordAdapter.updateBankCardRecordStatus(accountBankCardRecord);
        if (result > 0) {
            return BaseResult.successResult(null);
        }
        return BaseResult.failResult("625","unbind_bank_card_fail");
    }
}
