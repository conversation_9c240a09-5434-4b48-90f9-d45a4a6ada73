package com.ctrip.dcs.driver.gateway;
/*
作者：pl.yang
创建时间：2025/5/7-上午11:20-2025
*/

import com.ctrip.dcs.driver.account.domain.condition.DeleteYeepayRecipientReportCondition;
import com.ctrip.dcs.driver.account.domain.condition.QueryBankCardStatusCondition;
import com.ctrip.dcs.driver.account.domain.condition.SubmitYeepayRecipientReportCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidResponseType;

public interface YeePayRepository {

    CheckDriverBandCardValidResponseType checkDriverBandCardValid(long driverId,
                                                                  String driverName,
                                                                  String idCardNo,
                                                                  String driverMobile,
                                                                  String driverBankReservedMobile,
                                                                  String bankCardNo);

    //给易宝提交银行卡信息
    public BaseResult<Boolean> submitYeepayRecipientReport(SubmitYeepayRecipientReportCondition submitYeepayRecipientReportCondition);

    //易宝解绑银行卡信息
    public boolean unBindBankCard(AccountBankCardDo bankCardInfo);

    //从易宝中查询 银行卡的状态
    BankCardStatusDo queryBankCardStatus(QueryBankCardStatusCondition condition);

    //删除易宝银行卡信息
    public BaseResult<Boolean> deleteYeepayRecipientReport(DeleteYeepayRecipientReportCondition build);
}
