package com.ctrip.dcs.driver.account.domain.value;
/*
作者：pl.yang
创建时间：2025/4/28-下午4:15-2025
*/


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardAreaTypeEnum;
import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardStatusEnum;
import com.ctrip.dcs.driver.account.infrastructure.constant.IdCardTypeEnum;
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountBankCardDo
 * @Package com.ctrip.dcs.driver.account.domain.value
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 下午4:15
 */

@Getter
@Builder
public class AccountBankCardDo {
    public static final Logger logger = LoggerFactory.getLogger(AccountBankCardDo.class);
    private Long id;
    /**
     * uid
     */
    private String uid;

    /**
     * 0默认1 绑卡 2解绑 3:审核中 4:绑卡失败
     */
    private BankCardStatusDo cardStatus=BankCardStatusDo.defaultUNKNOW();

    /**
     * 操作时间
     */
    private Timestamp operateTime;

    //易宝的请求id 我们系统自动生成的
    private String requestId;

    /**
     * driverId
     */
    public Long driverId;

    /**
     * 携程国家id
     */
    public Long countryId;
    /**
     * 易宝国家三字码
     */
    public String yeePayCountryThreeCode;
    //易宝城市名称
    public String yeePayCityName;

    /**
     * 携程城市id
     */
    public Long cityId;

    // 银行账户币种
    private String accountCurrency;
    //  银行卡区域类型      //1:境内司机境内卡
    //    //2：境外司机境内卡
    //    //3：境外司机境外卡
    private BankCardAreaTypeEnum bankCardAreaTypeEnum;
    // 境内境外卡
    private boolean isOverseaBankCard;
    //公司金融那边的id
    private String cardInfoId;
    // 银行账户名称  银行账户收款人名称
    private String accountName;
    // 银行卡号 银行账户号码
    private String cardNo;
    // 国际区号
    private String phoneCountryCode;
    // 电话号码
    private String phoneNo;
    // FEDWIRE号码
    private String fedwireNumber;
    // 清算号
    private String sortCode;
    // IBAN 号码
    private String iban;

    // ABA NUMBER
    private String abaNumber;
    // 银行路由号码
    private String routingNumber;
    // 银行的IFS码
    private String ifsCode;
    // 证件类型
    private IdCardTypeEnum idType;
    // 证件号码
    private String idNumber;
//  =============上面字段保存db中============================


//    ======================下面字段保存的 金融那边的=========================

    // 列表选择的开户银行对应的易宝CODE
    private String bankNameCode;
    // 银行详细地址
    private String bankAddress;
    // 银行编码（香港）
    private String bankCode;
    // 分支行编码
    private String branchCode;
    // 列表选择的开户银行所在的省/州对应的易宝CODE
    private String provinceCode;
    // 开户银行所在的省/州
    private String province;

    // 开户银行所在地的邮编
    private String postCode; //zip_code
    // 开户银行名称
    private String bankName;//bank_branch_name 银行支行名称
    // EMAIL
    private String email;//email
    // 供应商所在地址
    private String address;//billing_address
    // SWIFTCODE
    private String swiftCode; //swift_code

    /**
     * 海外供应商为个人时填写first name
     */
    private String firstName;

    /**
     * 供应商为个人时填写middle name
     */
    private String middleName;

    /**
     * 海外供应商为个人时填写last name
     */
    private String lastName;
    //    分支行名称
    private String branchName;


    public String getType() {
        return isOverseaBankCard ? "OVERSEAS_SUPPLIER" : "DOMESTIC_SUPPLIER"; //境内境外卡
    }

    public Integer getBankCardAreaTypeEnumCode() {
        if (bankCardAreaTypeEnum == null) {
            logger.warn("bankCardAreaTypeEnum is null");
            Cat.logEvent(CatEventType.QUERY_BANK_CARD_AREA_TYPE_ENUM_RESULT, "ErrorResult");
            return null;
        }
        return bankCardAreaTypeEnum.getCode();
    }


    public synchronized String createOrGetRequestId(String driverId) {
        //yeepay_${司机id}_${timestamp}
        //正则 表达式   ^[a-zA-Z0-9_-]*$     长度不超过32位
        if (CharSequenceUtil.isNotBlank(requestId)) {
            return requestId;
        }
        //********** 5位
        this.requestId = CharSequenceUtil.format("yp_{}_{}", driverId, DateUtil.currentSeconds());
        return requestId;
    }

    public Integer getCardStatusCode() {
        if(cardStatus==null){
            return BankCardStatusEnum.UNKNOW.getCode();
        }
        return cardStatus.getBankCardStatusEnum().getCode();
    }


    public void initCardStatus() {
        cardStatus = BankCardStatusDo.defaultUNKNOW();
    }

    public void createAreType(boolean isOverseaDrived) {
        if (isOverseaDrived) {
            if (this.isOverseaBankCard()) {
                this.bankCardAreaTypeEnum = (BankCardAreaTypeEnum.overseasDriver_overseasCard);
            }else {
                this.bankCardAreaTypeEnum = (BankCardAreaTypeEnum.overseasDriver_domesticCard);
            }
        } else {
            if (this.isOverseaBankCard()) {
                this.bankCardAreaTypeEnum = (BankCardAreaTypeEnum.domesticdriver_domesticcard);
            }else {
                this.bankCardAreaTypeEnum = (BankCardAreaTypeEnum.overseasDriver_domesticCard);
            }
        }

    }

    public void setCardNo(String saveBankCardNo) {
        this.cardNo = saveBankCardNo;
    }

    public void changeRequestId(String requestId){
        this.requestId=requestId;
    }

}
