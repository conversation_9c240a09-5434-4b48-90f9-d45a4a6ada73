package com.ctrip.dcs.driver.gateway;
/*
作者：pl.yang
创建时间：2025/5/19-下午9:18-2025
*/


import com.ctrip.dcs.driver.account.domain.value.DriverInfoDo;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DriverInfoRepository
 * @Package com.ctrip.dcs.driver.gateway
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/19 下午9:18
 */


public interface DriverInfoRepository {
    //供应链查询司机信息
    DriverInfoDo queryDriverInfoByTms(Long driverId);
    //司导查询司机信息
    DriverInfoDo queryDriverInfoByGms(Long driverId);
}
