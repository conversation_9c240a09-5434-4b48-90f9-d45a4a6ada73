package com.ctrip.dcs.driver.gateway;
/*
作者：pl.yang
创建时间：2025/5/7-下午1:59-2025
*/


import com.ctrip.dcs.driver.value.bank.BankCardResultModel;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PaymentRouterRepositry
 * @Package com.ctrip.dcs.driver.gateway
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/7 下午1:59
 */


public interface PaymentRouterRepository {
    BankCardResultModel queryBankChannelInfo(String uid, String cardNo,boolean isOversea);
}
