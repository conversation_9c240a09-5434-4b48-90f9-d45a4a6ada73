package com.ctrip.dcs.driver.account.domain.value;
/*
作者：pl.yang
创建时间：2025/5/20-上午10:51-2025
*/


import com.ctrip.dcs.driver.account.domain.service.PayChannelTypeGreyUtil;
import com.ctrip.dcs.driver.account.infrastructure.constant.PayChannelTypeEnum;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;

import static cn.hutool.core.text.CharSequenceUtil.format;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PayChannelTypeDo
 * @Package com.ctrip.dcs.driver.account.domain.value
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/20 上午10:51
 */


public class PayChannelTypeDo {
    private Integer payChannelType;
    private final boolean isOversea;
    private final String driverId;
    public static final Logger logger = LoggerFactory.getLogger(PayChannelTypeDo.class);

    public PayChannelTypeDo(Integer payChannelType,boolean isOversea,String driverId) {
        this.payChannelType = payChannelType;//不使用
        this.isOversea = isOversea;
        this.driverId = driverId;
    }

    public Integer payChannelTypeV2() {
        int payChannelType = 1;
        //检查返回的数据是否对吗？万一db 返回的不对则有可能出现问题
        if (isOversea) {
            //境外，如果在灰度中，或则灰度了全量 都是返回4，不能返回其他数据
            if (PayChannelTypeGreyUtil.isGrey(driverId)) {
                payChannelType = PayChannelTypeEnum.YEEPAY.getCode();
            } else {
                payChannelType = PayChannelTypeEnum.PAYONEER.getCode();
            }
        } else {
            payChannelType = PayChannelTypeEnum.PPM.getCode();
        }
        Cat.logEvent(CatEventType.PayChannelTypeGrey, format("payChannelTypeV2:{},IsOversea:{}", payChannelType, isOversea));
        return payChannelType;
    }

}
