package com.ctrip.dcs.driver.account.domain.value;

import cn.hutool.core.collection.CollUtil;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountIdentityDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

@Data
public class AccountInfoDTO {
    public static final Logger logger = LoggerFactory.getLogger(AccountInfoDTO.class);
    /**
     * 携程uid
     */
    private String uid;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 国家码
     */
    private String countryCode;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 派安盈账户id
     */
    private String payoneerAccountId;

    /**
     * ppm账户id
     */
    private String ppmAccountId;


    /**
     * 注册来源 司机：Driver，向导：Guide，司导：DriverGuide
     */
    private String registerSource;

    /**
     * 是否境外，可能为空
     */
    private Boolean isOversea;

    /**
     * 身份列表
     */
    private List<AccountIdentityDTO> identityDTOList;

    /**
     * 提现状态，0：不可提现；1：可提现
     * */
    private Integer withdrawStatus;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * udl
     */
    private String udl;



    //获取司机，或则司导的 司机id 注意 司机id,跟司导id 是一样的
    public String getDriverOrDriverGuideDriverIdStr() {
        try {
            List<AccountIdentityDTO> identityDTOList = getIdentityDTOList();
            if (CollUtil.isEmpty(identityDTOList)) {
                return null;
            }
            AccountIdentityDTO accountIdentityDTO = identityDTOList.stream()
                    .filter(AccountIdentityDTO::isDriverOrDriverGuide).findFirst().orElse(null);
            if(accountIdentityDTO==null){
                return null;
            }
            return accountIdentityDTO.getSourceId();
        } catch (Exception e) {
            return null;
        }
    }

    //获取司机，或则司导的 司机id
    public Long getDriverOrDriverGuideDriverId() {
        try {
            String driverIdStr = getDriverOrDriverGuideDriverIdStr();
            if(StringUtils.isNotBlank(driverIdStr)){
                return Long.parseLong(driverIdStr);
            }
        } catch (Exception e) {
            logger.warn("getDriverId error", e);
        }
        return null;
    }



    public Integer payChannelTypeV1() {
        return new PayChannelTypeDo(null, Optional.ofNullable(isOversea).orElse(false), getDriverOrDriverGuideDriverIdStr()).payChannelTypeV2();
    }


}
