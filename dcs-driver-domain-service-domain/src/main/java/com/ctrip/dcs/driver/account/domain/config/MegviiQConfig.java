package com.ctrip.dcs.driver.account.domain.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

@Getter
@Setter
@Component
@QMapConfig("megv_live_still.properties")
public class MegviiQConfig {

    //旷视appid
    private String megvApppid;
    //旷视新加坡host
    private String megvSGPHost;
    //获取token接口地址
    private String accessTokenUrl;
    //活体校验接口地址
    private String submitVerifyUrl;
    //活体场景sceneid
    private String livenessId;
    //比对场景sceneid
    private String verifyId;
    //签名算法版本
    private String signVersion;
    //有效时间
    private Integer signExpiredSeconds;
    //海外图片服务器地址
    private String uploadDomain;
    //上传地址：
    private String uploadUrl;
    //上传图片channel配置
    private String channel;

    private String kmsTokenApiKey;

    private String kmsTokenApiSecret;

    /**
     * 旷视响应报文加密公钥
     */
    private String resEncryptionPubKeyToken;

    /**
     * 旷视响应密文解密私钥
     */
    private String resDecryptionPrvKeyToken;

    /**
     * 旷视请求报文加密公钥
     */
    private String reqEncryptionPubKeyToken;

}
