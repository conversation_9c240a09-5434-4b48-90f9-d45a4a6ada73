package com.ctrip.dcs.driver.account.domain.service;
/*
作者：pl.yang
创建时间：2025/5/7-上午10:14-2025
*/


import cn.hutool.core.text.CharSequenceUtil;
import com.ctrip.dcs.driver.account.domain.condition.*;
import com.ctrip.dcs.driver.account.domain.enums.BankCardResultEnum;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.domain.value.CardInfoDto;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.gateway.*;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.driver.value.bank.BankCardResultModel;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: OvereadBankCardService
 * @Package com.ctrip.dcs.driver.account.application.service.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/7 上午10:14
 */

@Component
public class OverseaBankCardService {
    public static final Logger logger = LoggerFactory.getLogger(OverseaBankCardService.class);
    public static final String SUCCESSFUL_BINDING = "Successful binding";
    public static final String FAILED_TO_SAVE_BANK_CARD_INFORMATION = "Failed to save bank card information";

    @Autowired
    DriverWalletInfoConfig driverWalletInfoConfig;

    @Autowired
    DriverAccountConfig driverAccountConfig;
    @Autowired
    AccountService accountService;
    @Autowired
    AccountBankCardRecordRepository accountBankCardRecordRepository;
    @Autowired
    PaymentRouterRepository paymentRouterRepository;
    @Autowired
    CardInfoManagementRepository cardInfoManagementRepository;
    @Autowired
    YeePayRepository yeePayRepository;
    @Autowired
    ArchCoreInfoRepository archCoreInfoRepository;
    @Autowired
    QmqSendRepository qmqSendRepository;


    public BaseResult<Void> doBindOverseaBankCard(BindOverseaBankCardCondition bankCardCondition) {
        //校验基本参数
        if (bankCardCondition.getBankCardInfo() == null|| bankCardCondition.getDriverId() == null) {
            return BaseResult.failResultCat("12001", "The bank card information is empty", CatEventType.Bind_Oversea_BankCard, "The bank card information is empty.");
        }
        AccountBankCardDo bankCardInfo = bankCardCondition.getBankCardInfo();
        //1、uid不存在
        AccountInfoDTO accountInfo = accountService.getAccountInfoByUID(bankCardCondition.uid);
        if (Objects.isNull(accountInfo)) {
            String message = CharSequenceUtil.format("The user {} does not exist.", bankCardCondition.uid);
            return BaseResult.failResultCat("15002", message, CatEventType.Bind_Oversea_BankCard, "The bank card information is empty.");
        }
        //2、存在已绑定的卡，返回错误提示
        BaseResult<AccountBankCardDo> result = accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(bankCardCondition.uid);
        AccountBankCardDo accountBankCardRecord = result.getData();
        if (Objects.nonNull(accountBankCardRecord) && (accountBankCardRecord.getCardStatus().isBind()||accountBankCardRecord.getCardStatus().isAuditing())) {
            //2、遍历绑卡历史记录，如果有绑定的卡（status：1）、报备失败（status：4）的同卡号的卡，本次绑卡为UPDATE
            return BaseResult.failResultCat("15003",CharSequenceUtil.format("uid:{},There are bound cards", bankCardCondition.uid), CatEventType.Bind_Oversea_BankCard, "There are bound cards");
        }
        //3:检查绑定的卡是否为储蓄卡
        boolean isDepositCard = checkBandCardTypeIsDepositCard(bankCardCondition);
        if (!isDepositCard) {
            return BaseResult.failResultCat("15004", CharSequenceUtil.format("uid:{},The card is not a deposit card", bankCardCondition.uid), CatEventType.Bind_Oversea_BankCard,"The card is not a deposit card");
        }
        //3.1 如果境外司机绑定境内卡的时候需要，校验下参数
        if (!bankCardInfo.isOverseaBankCard()) {
            BaseResult<String> baseResult = checkBankCardPram(accountInfo, bankCardCondition);
            if (baseResult.isFail()) {
                return BaseResult.failResultCat("15006", "checkBankCardPram error," + baseResult.getMessage(), CatEventType.Bind_Oversea_BankCard, "checkBankCardPram error");
            }
        }
        //4、 金融存储银行卡号，获取金融cardid  //每次保存的 返回的金融id都不一样
        CardInfoDto cardInfoDto = convertCardInfo(bankCardInfo);
        String saveBankCardNo = cardInfoManagementRepository.createCardInfo(accountInfo, cardInfoDto);
        if (StringUtils.isBlank(saveBankCardNo)) {
            return BaseResult.failResultCat("15005", "createCardInfo error", CatEventType.Bind_Oversea_BankCard, "createCardInfo error");
        }


        //5：提交个易宝
        return submitYeepayRecipientReport(accountBankCardRecord, bankCardCondition, accountInfo, saveBankCardNo);


    }

    private BaseResult<String> checkBankCardPram(AccountInfoDTO accountInfo, BindOverseaBankCardCondition bankCardCondition) {
        Long driverId = bankCardCondition.getDriverId();
        AccountBankCardDo bankCardInfo = bankCardCondition.getBankCardInfo();
        //todo 需要解密身份证
        String idCardNo = archCoreInfoRepository.decryptIdCard(bankCardInfo.getIdNumber());
        //4、结算——易宝流程
        CheckDriverBandCardValidResponseType settlementCheckResult = yeePayRepository.checkDriverBandCardValid(
                driverId,
                bankCardInfo.getAccountName(),
                idCardNo,
                accountInfo.getPhoneNumber(),//账户保存的手机号
                bankCardInfo.getPhoneNo(),//银行卡 预留的
                bankCardInfo.getCardNo());
        if (settlementCheckResult == null) {
            return BaseResult.failResult("500", "CheckDriverBandCardValidResponseType is empty");
        }
        boolean isSettlementSuccess = settlementCheckResult.getResponseResult() != null && settlementCheckResult.getResponseResult().isSuccess();
        if (!isSettlementSuccess) {
            logger.warn("checkDriverBandCardValid", settlementCheckResult.getResponseResult() != null ? settlementCheckResult.getResponseResult().getReturnMessage() : "Unknown error");
            return BaseResult.failResult("500", settlementCheckResult.businessCode + ":" + settlementCheckResult.businessMsg);
        }
        return BaseResult.successResult(null);
    }

    private CardInfoDto convertCardInfo(AccountBankCardDo bankCardInfo) {
        CardInfoDto cardInfoDto = new CardInfoDto();
        cardInfoDto.setCreditCardNumber(bankCardInfo.getCardNo());
        return cardInfoDto;
    }

    private @NotNull BaseResult<Void> submitYeepayRecipientReport(AccountBankCardDo oldAccountBankCardRecord, BindOverseaBankCardCondition bankCardCondition, AccountInfoDTO accountInfo, String saveBankCardNo) {
        AccountBankCardDo bankCardInfo = bankCardCondition.getBankCardInfo();
        AccountBankCardDo insertAccountBankCardRecord = this.buildAccountBankCardRecord(bankCardCondition, accountInfo, saveBankCardNo);
        logger.info("submitBankCardData", CharSequenceUtil.format("uid:{}", saveBankCardNo));
        String requestId = "";
        String type = "";
        if (oldAccountBankCardRecord != null) {
            requestId = oldAccountBankCardRecord.getRequestId();
            type = oldAccountBankCardRecord.getType();
        } else {
            requestId = insertAccountBankCardRecord.createOrGetRequestId(accountInfo.getDriverOrDriverGuideDriverIdStr());
            type = insertAccountBankCardRecord.getType();
        }

        //提交易宝前需要 看易宝的状态
        QueryBankCardStatusCondition cardStatusCondition = QueryBankCardStatusCondition.builder().requestId(requestId).accountNumber(bankCardInfo.getCardNo()).type(type).build();
        BankCardStatusDo bankCardStatusDo = yeePayRepository.queryBankCardStatus(cardStatusCondition);
        if (bankCardStatusDo.isBind() || bankCardStatusDo.isAuditing()) {
            if(StringUtils.isNotBlank(bankCardStatusDo.getRequestId())){
                if(!StringUtils.equals(requestId,bankCardStatusDo.getRequestId())){
                    //注意：只有绑定成功，或则绑定中，才需要更新requestId，使用db那个requestId
                    insertAccountBankCardRecord.changeRequestId(bankCardStatusDo.getRequestId());
                }
            }
            logger.info("submitBankCardData", CharSequenceUtil.format("uid:{},requestId:{}", saveBankCardNo, requestId));
            //易宝已经绑定过了
            Cat.logEvent(CatEventType.Bind_Oversea_BankCard, "The card is bind");
            //卡设置为审核
            if(insertAccountBankCardRecord.getCardStatus()==null){
                insertAccountBankCardRecord.initCardStatus();
            }
            if (bankCardStatusDo.isBind()) {
                insertAccountBankCardRecord.getCardStatus().bindBankSuccess();
                sendBindSuccessMessage(insertAccountBankCardRecord);
            } else {
                insertAccountBankCardRecord.getCardStatus().bindBankAuditing();
            }
            insertAccountBankCardRecord.setCardNo(saveBankCardNo);
            //6：保存数据，到数据库中
            int result = accountBankCardRecordRepository.saveOrUpdate(insertAccountBankCardRecord);
            if (result <= 0) {
                return BaseResult.failResultCat("625", FAILED_TO_SAVE_BANK_CARD_INFORMATION, CatEventType.Bind_Oversea_BankCard, FAILED_TO_SAVE_BANK_CARD_INFORMATION);
            }
            return BaseResult.resultCat(null, "200", SUCCESSFUL_BINDING, CatEventType.Bind_Oversea_BankCard, SUCCESSFUL_BINDING);
        } else {
            //如果易宝那边 银行是绑定失败的情况下，需要删除易宝的数据，重新绑定
            if (bankCardStatusDo.isBindFailed()) {
                Cat.logEvent(CatEventType.Bind_Oversea_BankCard, "The card bind failed, to delete yeepay data");
                AccountBankCardDo accountBankCardDo = AccountBankCardDo.builder().requestId(bankCardStatusDo.getRequestId()/*易宝已经存在的requestid*/).isOverseaBankCard(bankCardInfo.isOverseaBankCard()).build();
                DeleteYeepayRecipientReportCondition condition = DeleteYeepayRecipientReportCondition.builder().accountBankCardDo(accountBankCardDo).build();
                BaseResult<Boolean> booleanBaseResult = yeePayRepository.deleteYeepayRecipientReport(condition);
                if (booleanBaseResult.isSuccess()) {
                    logger.info("deleteYeepayRecipientReport success " + requestId);
                } else {
                    logger.info("deleteYeepayRecipientReport failed " + requestId);
                }
            }

            //易宝没有绑定
            Cat.logEvent(CatEventType.Bind_Oversea_BankCard, "The card to bind");
            logger.info("submitYeepayRecipientReport");
            //这里有具体的错误码
            SubmitYeepayRecipientReportCondition condition = SubmitYeepayRecipientReportCondition.builder().accountBankCardDo(bankCardCondition.getBankCardInfo()).build();
            BaseResult<Boolean> isSubmitResult = yeePayRepository.submitYeepayRecipientReport(condition);
            if (isSubmitResult.isFail()) {
                logger.info("submitYeepayRecipientReport_result", isSubmitResult.getCode() + isSubmitResult.getMessage());
                return BaseResult.failResultCat(driverWalletInfoConfig.getOverseaBindCardFailReasonErrorCode(isSubmitResult.getCode()), isSubmitResult.getMessage() + " error:" + isSubmitResult.getCode() + " requestId:" + requestId, CatEventType.Bind_Oversea_BankCard, isSubmitResult.getMessage());
            }
            //卡设置为审核中
            // 这里会出现一个问题，就是报备成功了，有可能db出现错误了，造成数据不一致,问题不大，db 保证数据一致性，这里就不做处理了
            if(insertAccountBankCardRecord.getCardStatus()==null){
                insertAccountBankCardRecord.initCardStatus();
            }
            insertAccountBankCardRecord.getCardStatus().bindBankAuditing();
            insertAccountBankCardRecord.setCardNo(saveBankCardNo);
            //6：保存数据，到数据库中
            int result = accountBankCardRecordRepository.saveOrUpdate(insertAccountBankCardRecord);
            if (result <= 0) {
                return BaseResult.failResultCat("625", FAILED_TO_SAVE_BANK_CARD_INFORMATION, CatEventType.Bind_Oversea_BankCard, FAILED_TO_SAVE_BANK_CARD_INFORMATION);
            }
            return BaseResult.resultCat(null, "200", SUCCESSFUL_BINDING, CatEventType.Bind_Oversea_BankCard, SUCCESSFUL_BINDING);
        }
    }

    private void sendBindSuccessMessage(AccountBankCardDo insertAccountBankCardRecord) {
        Map<String, String> data = new HashMap<>(2);
        data.put("requestId", insertAccountBankCardRecord.getRequestId());
        data.put("status", "SUCCESS");
        Map<String, Object> properties = new HashMap<>(1);
        properties.put("data", JacksonUtil.serialize(data));
        logger.info("sendBindSuccessMessage", JacksonUtil.serialize(data));
        qmqSendRepository.sendQmq(Constants.DRIVER_BANK_CARD_REGISTRATION_RESULT, properties);

    }


    private AccountBankCardDo buildAccountBankCardRecord(BindOverseaBankCardCondition bankCardCondition, AccountInfoDTO accountInfo, String saveBankCardNo) {
        //areType 设置
        AccountBankCardDo bankCardInfo = bankCardCondition.getBankCardInfo();
        //境外司机
        bankCardInfo.createAreType(true);

        return bankCardInfo;
    }

    private boolean checkBandCardTypeIsDepositCard(BindOverseaBankCardCondition bankCardCondition) {
//        return true;
        if (!driverAccountConfig.isCheckBandCardTypeIsDepositCard()) {
            return true;
        }
        try {
            BankCardResultModel bankCardResultModel = paymentRouterRepository.queryBankChannelInfo(bankCardCondition.getUid(), bankCardCondition.getBankCardInfo().getCardNo(),true);
            //1信用卡 2:储蓄卡
            if (BankCardResultEnum.OK.equals(bankCardResultModel.getResultCode()) && bankCardResultModel.getCardBankType() == 2) {
                logger.info("checkBandCardTypeIsDepositCard SUCCESS");
                return true;
            }
        } catch (Exception e) {
            logger.warn("queryBankChannelInfo error");
        }
        return false;
    }

    public BaseResult<Void> doOverseaUnbindBankCard(UnbindBankCardCondition condition, AccountBankCardDo accountBankCardRecord) {
        accountBankCardRecord.getCardStatus().unbind();//解绑
        //去易宝解绑
        BaseResult<Boolean> booleanBaseResult = yeePayRepository.deleteYeepayRecipientReport(DeleteYeepayRecipientReportCondition.builder().accountBankCardDo(accountBankCardRecord).build());
        if (booleanBaseResult.isSuccess()) {
            logger.info("deleteYeepayRecipientReport success " + accountBankCardRecord.getRequestId());
        } else {
            logger.info("deleteYeepayRecipientReport failed " + accountBankCardRecord.getRequestId());
            return BaseResult.failResult("626", "Failed to delete yeepay info，"+booleanBaseResult.getMessage());
        }
        int result = accountBankCardRecordRepository.updateBankCardRecordStatus(accountBankCardRecord);
        if (result > 0) {
            return BaseResult.successResult(null);
        }
        return BaseResult.failResult("625", "Failed to save bank card information");
    }
}
