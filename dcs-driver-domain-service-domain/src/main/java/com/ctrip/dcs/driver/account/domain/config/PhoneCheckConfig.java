package com.ctrip.dcs.driver.account.domain.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.MapConfig;

import java.util.Map;
import java.util.Optional;

@Component
public class PhoneCheckConfig {


	Map<String, String> file = MapConfig.get("phone.check.properties", Feature.create().setFailOnNotExists(false).build()).asMap();

	public String getSkillGroupId() {
		return file.get("skillGroupId");
	}

	/**
	 * 语音验证码group
	 * @return
	 */
	public String getVoiceSkillGroupId() {
		return file.get("voice.skillGroupId");
	}

	public String getSkillFlowId(String language) {
		return file.get(StringUtils.lowerCase(language) + ".flow");
	}

	public String getSpeechTemplate(String language) {
		return file.get(StringUtils.lowerCase(language) + ".text");
	}

	public String getVoiceSkillFlowId(String language) {
		return file.get(StringUtils.lowerCase(language) + ".voice.flow");
	}

	public String getVoiceSpeechTemplate(String language) {
		return file.get(StringUtils.lowerCase(language) + ".voice.text");
	}

	// 60天内只创建1个任务
	public Integer getRepeatTaskThresholdDays() {
		return Optional.ofNullable(file.get("repeatTaskThresholdDays"))
				.map(Integer::valueOf)
				.orElse(60);
	}

	// 当地时间次日早上10点打第一通电话
	public Integer getFirstCallDay() {
		return Optional.ofNullable(file.get("firstCallDay"))
				.map(Integer::valueOf)
				.orElse(1);
	}

	// 当地时间次日早上10点打第一通电话
	public Integer getFirstCallHour() {
		return Optional.ofNullable(file.get("firstCallHour"))
				.map(Integer::valueOf)
				.orElse(10);
	}

	// 单个任务最多重试3次
	public Integer getMaxRetryCount() {
		return Optional.ofNullable(file.get("maxRetryCount"))
				.map(Integer::valueOf)
				.orElse(3);
	}

	// 每次重试间隔4小时
	public Integer getRetryIntervalSeconds() {
		return Optional.ofNullable(file.get("retryIntervalSeconds"))
				.map(Integer::valueOf)
				.orElse(14400);
	}

	// 最大震铃秒数
	public Integer getMaxAlertSeconds() {
		return Optional.ofNullable(file.get("maxAlertSeconds"))
				.map(Integer::valueOf)
				.orElse(null);
	}

	// 最大通话秒数
	public Integer getMaxDurationSeconds() {
		return Optional.ofNullable(file.get("maxDurationSeconds"))
				.map(Integer::valueOf)
				.orElse(null);
	}

	// 单个任务最多重试3次
	public Integer getVoiceMaxRetryCount() {
		return Optional.ofNullable(file.get("voice.maxRetryCount"))
				.map(Integer::valueOf)
				.orElse(1);
	}

	// 每次重试间隔4小时
	public Integer getVoiceRetryIntervalSeconds() {
		return Optional.ofNullable(file.get("voice.retryIntervalSeconds"))
				.map(Integer::valueOf)
				.orElse(10);
	}

	// 最大震铃秒数
	public Integer getVoiceMaxAlertSeconds() {
		return Optional.ofNullable(file.get("voice.maxAlertSeconds"))
				.map(Integer::valueOf)
				.orElse(null);
	}

	// 最大通话秒数
	public Integer getVoiceMaxDurationSeconds() {
		return Optional.ofNullable(file.get("voice.maxDurationSeconds"))
				.map(Integer::valueOf)
				.orElse(null);
	}

}