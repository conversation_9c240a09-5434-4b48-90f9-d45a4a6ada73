package com.ctrip.dcs.driver.account.domain.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/11 11:43
 * @Description: 异常码类
 */
public class DriverDomainErrorCode {

    /**
     * 通用异常
     */
    public static final DomainErrorCode PARAM_ILLEGAL = new DomainErrorCode("401", "PARAM_ILLEGAL");


    /**
     * 语音验证码相关业务异常 100001～100099
     */
    public static class VoiceCodeError {
        /**
         * 账户不存在
         */
        public static final DomainErrorCode DRIVER_ACCOUNT_NOT_EXIST = new DomainErrorCode("100001", "DRIVER_ACCOUNT_NOT_EXIST");
        /**
         * 手机号提交IVR外呼失败
         */
        public static final DomainErrorCode IVR_SUBMIT_OUT_CALL_ERROR = new DomainErrorCode("100002", "IVR_SUBMIT_OUT_CALL_ERROR");

    }


    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DomainErrorCode {
        /**
         * 异常码
         */
        private String errCode;
        /**
         * 错误信息
         */
        private String errMsg;

    }

}
