package com.ctrip.dcs.driver.gateway;
/*
作者：pl.yang
创建时间：2025/5/7-下午2:06-2025
*/

import com.ctrip.dcs.driver.account.domain.value.CardInfoDto;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;

import java.util.List;
import java.util.Map;

public interface CardInfoManagementRepository {
    /*
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
     * 4.19）新增卡信息接口-createPayCardInfo
     **/
    String createCardInfo(String driverName, String encryptCardNo);

    public String createCardInfo(AccountInfoDTO accountInfoDTO, CardInfoDto bankCardInfoDo);

    String getSinglePayCardInfo(String cardInfoId);
    //根据金融id查询卡信息
    CardInfoDto queryCardInfo(String cardInfoId);

    //根据卡号查询查询卡信息
    Map<String/*卡号*/,String/*金融id*/> queryCardInfoByCardNo(List<String> cardNo);
}
