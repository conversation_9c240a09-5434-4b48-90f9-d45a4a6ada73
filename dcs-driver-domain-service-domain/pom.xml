<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dcs-driver-domain-service</artifactId>
        <groupId>com.ctrip.dcs.driver</groupId>
        <version>1.0.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>dcs-driver-domain-service-domain</artifactId>

    <name>dcs-driver-domain-service-domain</name>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>logging-interceptor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.igt.framework</groupId>
                    <artifactId>http</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver</groupId>
            <artifactId>dcs-driver-domain-service-infrastructure</artifactId>
        </dependency>
    </dependencies>
</project>
