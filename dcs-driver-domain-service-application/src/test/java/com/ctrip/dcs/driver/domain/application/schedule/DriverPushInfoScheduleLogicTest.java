package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;


public class DriverPushInfoScheduleLogicTest extends TestBase {

    @Tested
    DriverPushInfoScheduleLogic driverPushInfoScheduleLogic;

    @Injectable
    PushConfigRedisLogic pushConfigRedisLogic;

    @Injectable
    private DriverOrderPushConfigDao driverOrderPushConfigDao;

    @Injectable
    DirectorRedis directorRedis;

    @Test
    public void syncHonourInfoFromBIDB() {
        new Expectations(){
            {
                driverOrderPushConfigDao.count();
                result = 0;
            }
        };
        driverPushInfoScheduleLogic.syncPushInfoToRedis(null);

        List<DriverOrderPushConfigPO> driverOrderPushConfigList = new ArrayList<>();
        DriverOrderPushConfigPO driverOrderPushConfigPO = new DriverOrderPushConfigPO();
        driverOrderPushConfigPO.setDrvId(1L);
        driverOrderPushConfigPO.setDrvLanguage("en-us");
        driverOrderPushConfigList.add(driverOrderPushConfigPO);
        new Expectations(){
            {
                driverOrderPushConfigDao.count();
                result = 2001;

                 driverOrderPushConfigDao.findByLimit(0, 1000);
                 result = driverOrderPushConfigList;

                driverOrderPushConfigDao.findByLimit(1000, 1000);
                result = driverOrderPushConfigList;

                driverOrderPushConfigDao.findByLimit(2000, 1000);
                result = driverOrderPushConfigList;
            }
        };
        driverPushInfoScheduleLogic.syncPushInfoToRedis(null);
        Assert.assertEquals(driverOrderPushConfigList.size(), 1);
    }

}
