package com.ctrip.dcs.driver.account.application.util;

import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import com.ctrip.dcs.driver.gateway.impl.PaymentTokenUtils;
import com.ctrip.infosec.kms.KmsUtilCacheable;
import com.ctrip.infosec.kms.pojo.KmsKey;
import com.ctrip.infosec.kms.pojo.KmsResponse;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Mocked;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class PaymentTokenUtilsTest {

    @Tested
    private PaymentTokenUtils paymentTokenUtils;

    @Injectable
    private DriverWalletInfoConfig driverWalletInfoConfig;

    @Test
    public void test(@Mocked KmsUtilCacheable kmsUtilCacheable) throws KMSUtils.KmsException {
        KmsResponse<KmsKey> kmsNullResponse = new KmsResponse<KmsKey>();
        kmsNullResponse.setCode(1);
        KmsKey kmsNullKey = new KmsKey();
        kmsNullKey.setKeyValue("");
        kmsNullResponse.setResult(kmsNullKey);
        new Expectations(){
            {
                kmsUtilCacheable.getKey(this.anyString);
                result = kmsNullResponse;
            }
        };
        try {
            paymentTokenUtils.initializeKeys();
        }catch (KMSUtils.KmsException e) {

        }
        Assert.assertNull(paymentTokenUtils.paymentPublicEncryptKey());
        Assert.assertNull(paymentTokenUtils.driverPrivateEncryptKey());

        KmsResponse<KmsKey> kmsResponse = new KmsResponse<KmsKey>();
        kmsResponse.setCode(0);
        KmsKey kmsKey = new KmsKey();
        kmsKey.setKeyValue("keyValue");
        kmsResponse.setResult(kmsKey);
        new Expectations(){
            {
                kmsUtilCacheable.getKey(this.anyString);
                result = kmsResponse;
            }
        };
        paymentTokenUtils.initializeKeys();
        Assert.assertEquals("keyValue", paymentTokenUtils.paymentPublicEncryptKey());
        Assert.assertEquals("keyValue", paymentTokenUtils.driverPrivateEncryptKey());
    }
}
