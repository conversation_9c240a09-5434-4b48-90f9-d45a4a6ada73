package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverNoticeRecordPO;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

/**
 * Created by <AUTHOR> on 2023/3/8 14:49
 */

public class NoticeRedisLogicTest {

  @Tested
  NoticeRedisLogic noticeRedisLogic;

  @Injectable
  DirectorRedis directorRedis;

  @Test
  public void getValidNotice() {
    noticeRedisLogic.getValidNotice();
  }

  @Test
  public void isMyNotice() {
    noticeRedisLogic.isMyNotice(1L, "1000004");
  }

  @Test
  public void getNoticeContent() {
    noticeRedisLogic.getNoticeContent(1L);
  }

  @Test
  public void updateValidNotice() {
    DriverNoticeRecordPO recordPO = new DriverNoticeRecordPO();
    recordPO.setId(1L);
    recordPO.setNoticeConditionType(1);
    recordPO.setDriverStatus(15);
    recordPO.setStartDate(Timestamp.valueOf(LocalDateTime.now()));
    recordPO.setEndDate(Timestamp.valueOf(LocalDateTime.now()));
    noticeRedisLogic.updateValidNotice(Arrays.asList(recordPO));
    noticeRedisLogic.updateValidNotice(Collections.emptyList());
  }

  @Test
  public void updateNoticeContent() {
    DriverNoticeRecordPO recordPO = new DriverNoticeRecordPO();
    recordPO.setId(1L);
    recordPO.setNoticeStatus(1);
    recordPO.setNoticeConditionType(1);
    recordPO.setDriverStatus(15);
    recordPO.setStartDate(Timestamp.valueOf(LocalDateTime.now()));
    recordPO.setEndDate(Timestamp.valueOf(LocalDateTime.now()));
    noticeRedisLogic.updateNoticeContent(recordPO);
  }

  @Test
  public void cacheNoticeTarget() {
    noticeRedisLogic.cacheNoticeTarget(1L, Arrays.asList(1000004L), Timestamp.valueOf(LocalDateTime.now()));
  }

  @Test
  public void isNeedSendMail() {
    boolean isNeed = noticeRedisLogic.isNeedSendMail(1L, "1000004");
    Assert.assertNotNull(isNeed);
  }

  @Test
  public void markMailSendFail() {
    noticeRedisLogic.markMailSendFail("1", "1000004");
  }
}
