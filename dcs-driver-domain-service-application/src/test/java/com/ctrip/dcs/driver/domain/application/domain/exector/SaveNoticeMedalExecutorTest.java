package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.value.honour.DriverMedalObject;
import mockit.Mocked;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


public class SaveNoticeMedalExecutorTest extends TestBase {

    @Test
    public void testSaveNoticeMedalExecutor(@Mocked DriverMedalObject driverMedalObject){
        SaveNoticeMedalExecutor saveNoticeMedalExecutor = new SaveNoticeMedalExecutor(driverMedalObject, this.domainBeanFactory);

        String origin = "Antiepidemicpioneer,goldMedal^202311";
        String result = saveNoticeMedalExecutor.bulidSaveInfo(origin, new ArrayList<>(), true, new ArrayList<>());
        Assert.assertEquals(result, origin);

        List<String> newMedalCodeList = Arrays.asList("Antiepidemicpioneer", "goodmanner");
        List<String> newFormSpecialList = Arrays.asList("goldMedal^202312", "test");
        result = saveNoticeMedalExecutor.bulidSaveInfo(origin, newMedalCodeList, true, newFormSpecialList);
        Assert.assertNotNull( result);
    }


}
