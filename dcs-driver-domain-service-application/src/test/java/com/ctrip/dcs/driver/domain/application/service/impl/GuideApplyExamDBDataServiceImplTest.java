package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideApplyExamDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideApplyExamPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;


public class GuideApplyExamDBDataServiceImplTest {
  @Tested
  GuideApplyExamDBDataServiceImpl guideApplyExamDBDataServiceImpl;

  @Injectable
  private GuideApplyExamDao guideApplyExamDao;

  @Test
  public void testGuideApplyExamDBDataService(){
    testInsertGuideApplyExam();
    GuideApplyExamModel model = testQueryGuideApplyExamByAccountAndSubject();
    testQueryGuideApplyExamByAccount();
    testQueryPassedGuideApplyExamByAccount();
    testCountCallExamFailed();
    testQueryCallExamFailedRecords();
    testQueryApplyFailedRecords();
    testUpdateGuideApplyExam();
    testQueryMaxId();
    testQueryMinId();
    testQueryUpassedBetweenIds();
    Assert.assertEquals(model.getExamAccountId(),"guide0002129");
  }


  public void testInsertGuideApplyExam(){
    guideApplyExamDBDataServiceImpl.insertGuideApplyExam(new GuideApplyExamPO());
  }


  public GuideApplyExamModel testQueryGuideApplyExamByAccountAndSubject(){
    String examAccountId = "guide0002129";
    String applySubject = "DDXDCJ2024";
    List<GuideApplyExamPO> guideApplyExamPOS = getGuideApplyExamPOS();


    new Expectations(){
      {
        guideApplyExamDao.queryGuideApplyExam(examAccountId,applySubject);
        result = new ArrayList<>();
      }
    };
    guideApplyExamDBDataServiceImpl.queryGuideApplyExamByAccountAndSubject(examAccountId,applySubject);

    new Expectations(){
      {
        guideApplyExamDao.queryGuideApplyExam(examAccountId,applySubject);
        result = guideApplyExamPOS;
      }
    };
    return guideApplyExamDBDataServiceImpl.queryGuideApplyExamByAccountAndSubject(examAccountId,applySubject);
  }

  public void testQueryGuideApplyExamByAccount(){
    String examAccountId = "guide0002129";
    List<GuideApplyExamPO> guideApplyExamPOS = getGuideApplyExamPOS();
    new Expectations(){
      {
        guideApplyExamDao.queryInfoByExamAccountId(examAccountId);
        result = guideApplyExamPOS;
      }
    };
    guideApplyExamDBDataServiceImpl.queryGuideApplyExamByAccount(examAccountId);
    new Expectations(){
      {
        guideApplyExamDao.queryInfoByExamAccountId(examAccountId);
        result = new ArrayList<>();
      }
    };
    guideApplyExamDBDataServiceImpl.queryGuideApplyExamByAccount(examAccountId);
  }

  public void testQueryPassedGuideApplyExamByAccount(){
    String examAccountId = "guide0002129";
    List<GuideApplyExamPO> guideApplyExamPOS = getGuideApplyExamPOS();
    new Expectations(){
      {
        guideApplyExamDao.queryPassedInfoByExamAccountId(examAccountId);
        result = guideApplyExamPOS;
      }
    };
    guideApplyExamDBDataServiceImpl.queryPassedGuideApplyExamByAccount(examAccountId);
    new Expectations(){
      {
        guideApplyExamDao.queryPassedInfoByExamAccountId(examAccountId);
        result = new ArrayList<>();
      }
    };
    guideApplyExamDBDataServiceImpl.queryPassedGuideApplyExamByAccount(examAccountId);
  }

  public void testCountCallExamFailed() {
    Long count = 1L;
    new Expectations() {{
      guideApplyExamDao.countCallExamFailed();
      result = count;
    }};
    guideApplyExamDBDataServiceImpl.countCallExamFailed();
  }

  public void testQueryCallExamFailedRecords(){
    int start = 0;
    int end =10;
    List<GuideApplyExamPO> guideApplyExamPOS = getGuideApplyExamPOS();

    new Expectations(){
      {
        guideApplyExamDao.queryCallExamFailedRecords(start,end);
        result = guideApplyExamPOS;
      }
    };
    guideApplyExamDBDataServiceImpl.queryCallExamFailedRecords(start,end);
    new Expectations(){
      {
        guideApplyExamDao.queryCallExamFailedRecords(start,end);
        result = new ArrayList<>();
      }
    };
    guideApplyExamDBDataServiceImpl.queryCallExamFailedRecords(start,end);
  }

  public void testQueryApplyFailedRecords(){
    int start = 0;
    int end =10;
    List<GuideApplyExamPO> guideApplyExamPOS = getGuideApplyExamPOS();

    new Expectations(){
      {
        guideApplyExamDao.queryApplyFailedRecords(start,end);
        result = guideApplyExamPOS;
      }
    };
    guideApplyExamDBDataServiceImpl.queryApplyFailedRecords(start,end);
    new Expectations(){
      {
        guideApplyExamDao.queryApplyFailedRecords(start,end);
        result = new ArrayList<>();
      }
    };
    guideApplyExamDBDataServiceImpl.queryApplyFailedRecords(start,end);
  }

  public void testUpdateGuideApplyExam(){
    guideApplyExamDBDataServiceImpl.updateGuideApplyExam(new GuideApplyExamPO());
  }

  public void testQueryMaxId(){
    GuideApplyExamPO guideApplyExamPO = new GuideApplyExamPO();
    guideApplyExamPO.setId(100L);
    Timestamp time = new Timestamp(1686636458573L);
    new Expectations(){{
      guideApplyExamDao.queryMaxIdByChangeTime(time);
      result = guideApplyExamPO;
    }};
    guideApplyExamDBDataServiceImpl.queryMaxId(time);
  }

  public void testQueryMinId(){
    GuideApplyExamPO guideApplyExamPO = new GuideApplyExamPO();
    guideApplyExamPO.setId(100L);
    Timestamp time = new Timestamp(1686636458573L);
    new Expectations(){{
      guideApplyExamDao.queryMinIdByChangeTime(time);
      result = guideApplyExamPO;
    }};
    guideApplyExamDBDataServiceImpl.queryMinId(time);
  }

  public void testQueryUpassedBetweenIds(){
    Long start = 0L;
    Long end =10L;
    List<GuideApplyExamPO> guideApplyExamPOS = getGuideApplyExamPOS();

    new Expectations(){
      {
        guideApplyExamDao.queryUpassedBetweenIds(start,end);
        result = guideApplyExamPOS;
      }
    };
    guideApplyExamDBDataServiceImpl.queryUpassedBetweenIds(start,end);
    new Expectations(){
      {
        guideApplyExamDao.queryUpassedBetweenIds(start,end);
        result = new ArrayList<>();
      }
    };
    guideApplyExamDBDataServiceImpl.queryUpassedBetweenIds(start,end);
  }


  private List<GuideApplyExamPO> getGuideApplyExamPOS() {
    List<GuideApplyExamPO> guideApplyExamPOS = new ArrayList<>();
    GuideApplyExamPO guideApplyExamPO = new GuideApplyExamPO();
    guideApplyExamPO.setApplySubject("DDXDCJ2024");
    guideApplyExamPO.setId(1L);
    guideApplyExamPO.setExamAccountId("guide0002129");
    guideApplyExamPO.setSubjectName("初级笔试");
    guideApplyExamPO.setGuideName("test");
    guideApplyExamPO.setAccount("***********");
    guideApplyExamPO.setApplyTime(new Timestamp(1686636458573L));
    guideApplyExamPO.setDatachangeCreatetime(new Timestamp(1686636458573L));
    guideApplyExamPOS.add(guideApplyExamPO);
    return guideApplyExamPOS;
  }
}
