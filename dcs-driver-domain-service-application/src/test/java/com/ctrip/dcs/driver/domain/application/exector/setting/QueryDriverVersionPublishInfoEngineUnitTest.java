package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivVersionPublishInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivVersionPublishInfoPO;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPointPopInfoResponseType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverVersionPublishInfoRequestType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverVersionPublishInfoResponseType;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


public class QueryDriverVersionPublishInfoEngineUnitTest extends TestBase {

    @Tested
    QueryDriverVersionPublishInfoEngine queryDriverVersionPublishInfoEngine;

    @Injectable
    DrivVersionPublishInfoDao drivVersionPublishInfoDao;

    @Test
    public void test() {
        QueryDriverVersionPublishInfoRequestType requestType = new QueryDriverVersionPublishInfoRequestType();
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverPointPopInfoResponseType.class).init();
        List<DrivVersionPublishInfoPO> drivVersionPublishInfoPOList = new ArrayList<>();
        DrivVersionPublishInfoPO drivVersionPublishInfoPO = new DrivVersionPublishInfoPO();
        drivVersionPublishInfoPO.setPackageVersion("8.35.7");
        drivVersionPublishInfoPO.setInnerVersion("835007000");
        drivVersionPublishInfoPO.setPlatformType(1);
        drivVersionPublishInfoPO.setIsFullPublish(false);
        drivVersionPublishInfoPO.setGrayCityList("4,28");
        drivVersionPublishInfoPO.setUpdateDesc("更新提醒：\n8.35.7CRN框架升级");
        drivVersionPublishInfoPO.setUpdateDescEn("update：\n8.35.7CRN test");
        drivVersionPublishInfoPO.setRemark("测试弹框");
        drivVersionPublishInfoPO.setDownloadUrl("itms-services://?action=download-manifest&url=https://download2.ctripcorp.com/mcd/trip_driver_V8.35.7_07-18_15-20_Release-Inhouse_FAT_18073702_info.plist");
        drivVersionPublishInfoPO.setIsH5DownloadUri(false);
        drivVersionPublishInfoPO.setIsLowestVersion(true);
        drivVersionPublishInfoPO.setDatachangeLasttime(Timestamp.valueOf("2023-07-19 10:55:34.757"));
        drivVersionPublishInfoPOList.add(drivVersionPublishInfoPO);
        new Expectations(){
            {
                drivVersionPublishInfoDao.selectAll();
                result = drivVersionPublishInfoPOList;
            }
        };
        QueryDriverVersionPublishInfoResponseType responseType = queryDriverVersionPublishInfoEngine.execute(requestType);
        Assert.assertEquals(responseType.driverVersionPublishInfoList.size(), 1);
    }

    @Test
    public void testPO(){
        DrivVersionPublishInfoPO drivVersionPublishInfoPO = new DrivVersionPublishInfoPO();

        drivVersionPublishInfoPO.setDatachangeCreatetime(Timestamp.valueOf("2023-07-19 10:55:34.757"));
        drivVersionPublishInfoPO.setDatachangeLasttime(Timestamp.valueOf("2023-07-19 10:55:34.757"));
        drivVersionPublishInfoPO.setDownloadUrl(Strings.EMPTY);
        drivVersionPublishInfoPO.setGrayCityList(Strings.EMPTY);
        drivVersionPublishInfoPO.setId(1L);
        drivVersionPublishInfoPO.setInnerVersion(Strings.EMPTY);
        drivVersionPublishInfoPO.setIsFullPublish(false);
        drivVersionPublishInfoPO.setIsH5DownloadUri(false);
        drivVersionPublishInfoPO.setIsLowestVersion(false);
        drivVersionPublishInfoPO.setLastUpdateTime(Timestamp.valueOf("2023-07-19 10:55:34.757"));
        drivVersionPublishInfoPO.setLastUpdateUser(Strings.EMPTY);
        drivVersionPublishInfoPO.setPackageVersion(Strings.EMPTY);
        drivVersionPublishInfoPO.setPlatformType(1);
        drivVersionPublishInfoPO.setRemark(Strings.EMPTY);
        drivVersionPublishInfoPO.setUpdateDesc(Strings.EMPTY);
        drivVersionPublishInfoPO.setUpdateDescEn(Strings.EMPTY);
        drivVersionPublishInfoPO.setChannelInfo("channelInfo");
        boolean boolValue = drivVersionPublishInfoPO.getIsFullPublish();
        boolValue = drivVersionPublishInfoPO.getIsH5DownloadUri();
        boolValue = drivVersionPublishInfoPO.getIsLowestVersion();
        int intValue = drivVersionPublishInfoPO.getPlatformType();
        String strValue = drivVersionPublishInfoPO.getDownloadUrl();
        strValue = drivVersionPublishInfoPO.getGrayCityList();
        strValue = drivVersionPublishInfoPO.getInnerVersion();
        strValue = drivVersionPublishInfoPO.getLastUpdateUser();
        strValue = drivVersionPublishInfoPO.getPackageVersion();
        strValue = drivVersionPublishInfoPO.getRemark();
        strValue = drivVersionPublishInfoPO.getUpdateDesc();
        strValue = drivVersionPublishInfoPO.getUpdateDescEn();
        Timestamp timestamp = drivVersionPublishInfoPO.getDatachangeCreatetime();
        timestamp = drivVersionPublishInfoPO.getDatachangeLasttime();
        timestamp = drivVersionPublishInfoPO.getLastUpdateTime();
        Assert.assertNotNull(drivVersionPublishInfoPO);
        strValue = drivVersionPublishInfoPO.getChannelInfo();
        Assert.assertEquals("channelInfo", strValue);
    }
}
