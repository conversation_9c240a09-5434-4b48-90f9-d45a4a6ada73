package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBankCardListRequestType;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBankCardListResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.supply.driver.dto.CardInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


public class QueryDriverBankCardListEngineUnitTest extends TestBase {

    @Tested
    QueryDriverBankCardListEngine queryDriverBankCardListEngine;

    @Test
    public void test() {
        QueryDriverBankCardListRequestType requestType = new QueryDriverBankCardListRequestType();
        requestType.driverId = 1000004L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverBankCardListResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;

        List<CardInfo> cardInfoList = new ArrayList<>();
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;

                domainBeanFactory.financeConvertService().queryDriverCardList(requestType.driverId);
                result = cardInfoList;
            }
        };

        QueryDriverBankCardListResponseType responseType = queryDriverBankCardListEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        CardInfo cardInfo = new CardInfo();
        cardInfo.bankCard = "bankCard";
        cardInfo.bankName = "bankName";
        cardInfo.bankId = "bankId";
        cardInfoList.add(cardInfo);
        new Expectations(){
            {
                domainBeanFactory.financeConvertService().queryDriverCardList(requestType.driverId);
                result = cardInfoList;
            }
        };

        responseType = queryDriverBankCardListEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
