package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi.AdmPrdTrhDriverHonorInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi.DimPrdTrhDriverMedalInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi.DimPrdTrhDriverMedalRankingDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivHonourMedalNoticeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivHonourRankLikeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalRankingPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourMedalNoticeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourRankLikeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;


public class HonourDBDataServiceImplTest {

    @Tested
    HonourDBDataServiceImpl honourDBDataServiceImpl;

    @Injectable
    private DrivHonourMedalNoticeRecordDao drivHonourMedalNoticeRecordDao;

    @Injectable
    private DrivHonourRankLikeRecordDao drivHonourRankLikeRecordDao;

    @Injectable
    private AdmPrdTrhDriverHonorInfoDao admPrdTrhDriverHonorInfoDao;

    @Injectable
    private DimPrdTrhDriverMedalInfoDao dimPrdTrhDriverMedalInfoDao;

    @Injectable
    private DimPrdTrhDriverMedalRankingDao dimPrdTrhDriverMedalRankingDao;

    @Test
    public void queryMaxBatchDataInfo() {
        new Expectations() {
            {

            }
        };
        honourDBDataServiceImpl.queryMaxBatchDataInfo();
    }

    @Test
    public void queryMaxBatchDataDetail() {
        honourDBDataServiceImpl.queryMaxBatchDataDetail(1000004L);
    }

    @Test
    public void queryDriverMedalCount() {
        long driverId= 1000004L;
        AdmPrdTrhDriverHonorInfoPO admPrdTrhDriverHonorInfoPO = new AdmPrdTrhDriverHonorInfoPO();
        admPrdTrhDriverHonorInfoPO.setMedalGetCnt(1L);
        new Expectations() {
            {
                admPrdTrhDriverHonorInfoDao.findDriverInfo(driverId);
                result = admPrdTrhDriverHonorInfoPO;
            }
        };
        int expacebatchId = honourDBDataServiceImpl.queryDriverMedalCount(driverId);
        Assert.assertEquals(1, expacebatchId);
    }

    @Test
    public void queryNoticedMedalInfo(){
        new Expectations() {
            {

            }
        };
        honourDBDataServiceImpl.queryNoticedMedalInfo(1000004L);
    }

    @Test
    public void queryNoticedMedalList(){
        long driverId = 1000004L;
        DrivHonourMedalNoticeRecordPO medalNoticeRecordPO = new DrivHonourMedalNoticeRecordPO();
        medalNoticeRecordPO.setDriverId(driverId);
        medalNoticeRecordPO.setFormMedalNotice("1,2,3");
        medalNoticeRecordPO.setActiveMedalNotice("4^2022,5^2021,6^2022");
        medalNoticeRecordPO.setCommonMedalNotice("7,8,9");
        new Expectations() {
            {
                drivHonourMedalNoticeRecordDao.findOne(driverId);
                result = medalNoticeRecordPO;
            }
        };
        honourDBDataServiceImpl.queryNoticedMedalList(driverId);
    }

    @Test
    public void insertNoticedMedalInfo(){
        new Expectations() {
            {

            }
        };
        DrivHonourMedalNoticeRecordPO medalNoticeRecordPO = new DrivHonourMedalNoticeRecordPO();
        honourDBDataServiceImpl.insertNoticedMedalInfo(medalNoticeRecordPO);
    }

    @Test
    public void updateNoticedMedalInfo(){
        new Expectations() {
            {

            }
        };
        DrivHonourMedalNoticeRecordPO medalNoticeRecordPO = new DrivHonourMedalNoticeRecordPO();
        honourDBDataServiceImpl.updateNoticedMedalInfo(medalNoticeRecordPO);
    }

    @Test
    public void queryBasicMedalInfo(){
        new Expectations() {
            {

            }
        };
        honourDBDataServiceImpl.queryBasicMedalInfo();
    }

    @Test
    public void queryDriverMedalRankInfo(){
        long driverId = 1000004L;
        List<DimPrdTrhDriverMedalRankingPO> dimPrdTrhDriverMedalRankingPOS = new ArrayList<>();
        DimPrdTrhDriverMedalRankingPO medalRankingPO = new DimPrdTrhDriverMedalRankingPO();
        medalRankingPO.setDrvId(driverId);
        medalRankingPO.setMedalType("order_day_medal");
        medalRankingPO.setMedalGrade(1);
        medalRankingPO.setGradeGetDrvCnt(1L);
        medalRankingPO.setMedalGradeGetRank(1L);
        dimPrdTrhDriverMedalRankingPOS.add(medalRankingPO);
        new Expectations() {
            {
                dimPrdTrhDriverMedalRankingDao.findDriverRankInfo(driverId);
                result = dimPrdTrhDriverMedalRankingPOS;
            }
        };
        honourDBDataServiceImpl.queryDriverMedalRankInfo(driverId);
    }

    @Test
    public void queryCityRankList(){
        long batchId = 1L;
        long cityId = 2L;
        List<AdmPrdTrhDriverHonorInfoPO> admPrdTrhDriverHonorInfoPOS = new ArrayList<>();
        AdmPrdTrhDriverHonorInfoPO honorInfoPO = new AdmPrdTrhDriverHonorInfoPO();
        honorInfoPO.setBatchTime(batchId);
        honorInfoPO.setCityId(cityId);
        honorInfoPO.setDataTime("2022-08-09 09:49:00");
        honorInfoPO.setDataTimeMonth("2022-08");
        honorInfoPO.setDataTimeWeek("2022-08-09");
        honorInfoPO.setDrvId(1000004L);
        honorInfoPO.setDrvName("driver");
        honorInfoPO.setMonthOrderCnt(1L);
        honorInfoPO.setMonthRanking(2L);
        honorInfoPO.setWeekOrderCnt(3L);
        honorInfoPO.setWeekRanking(4L);
        admPrdTrhDriverHonorInfoPOS.add(honorInfoPO);
        new Expectations() {
            {
                admPrdTrhDriverHonorInfoDao.findCityWeekRankList(batchId, cityId);
                result = admPrdTrhDriverHonorInfoPOS;

                admPrdTrhDriverHonorInfoDao.findCityMonthRankList(batchId, cityId);
                result = admPrdTrhDriverHonorInfoPOS;
            }
        };
        List<RankOriginalDataModel> list = honourDBDataServiceImpl.queryCityRankList(batchId, cityId, true);
        Assert.assertNotNull(list);

        list = honourDBDataServiceImpl.queryCityRankList(batchId, cityId, false);
        Assert.assertNotNull(list);
    }

    @Test
    public void searchHonourRankLikeInfo(){
        new Expectations() {
            {

            }
        };
        honourDBDataServiceImpl.searchHonourRankLikeInfo(20220801, 1000004L);
    }

    @Test
    public void insertHonourRankLikeInfo(){
        new Expectations() {
            {

            }
        };
        DrivHonourRankLikeRecordPO rankLikeRecordPO = new DrivHonourRankLikeRecordPO();
        honourDBDataServiceImpl.insertHonourRankLikeInfo(rankLikeRecordPO);
    }

    @Test
    public void updateHonourRankLikeInfo(){
        new Expectations() {
            {

            }
        };
        DrivHonourRankLikeRecordPO rankLikeRecordPO = new DrivHonourRankLikeRecordPO();
        honourDBDataServiceImpl.updateHonourRankLikeInfo(rankLikeRecordPO);
    }
}


