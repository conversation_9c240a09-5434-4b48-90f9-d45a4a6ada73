package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.igt.framework.common.language.Language;
import com.ctrip.igt.framework.common.language.LanguageContext;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.util.HashMap;


public class CommonMultipleLanguagesTest {

    @Tested
    CommonMultipleLanguages commonMultipleLanguages;

    @Injectable
    private CommonlTemplateUtils igtRestfulTemplateUtils;

    @Test
    public void test() {
        LanguageContext.setLanguage(Language.newBuilder().withLanguage("zh-cn").build());

//        commonMultipleLanguages.content(Strings.EMPTY, new HashMap<>());
        commonMultipleLanguages.getContent(Strings.EMPTY);
        commonMultipleLanguages.getContent("test", Strings.EMPTY);
    }

}
