package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.honour.QueryCustomerMedalListRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryCustomerMedalListResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.CustomerMedalSimpleInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormHonourWallDriverMedalInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import com.google.common.reflect.TypeToken;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Mocked;
import mockit.Tested;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;


public class QueryCustomerMedalListEngineTest extends TestBase {

    @Tested
    QueryCustomerMedalListEngine queryCustomerMedalListEngine;

    @Injectable
    CommonMultipleLanguages commonMultipleLanguages;

    @Test
    public void execute(@Mocked ExecutorService executorService) {
        DriverInfo driverInfo = bulidDriverInfo();
        QueryCustomerMedalListRequestType requestType = new QueryCustomerMedalListRequestType();
        requestType.setDriverId(driverInfo.driverId);
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryCustomerMedalListResponseType.class).init();

        List<FormHonourWallDriverMedalInfo> honourWallMedalInfo = new ArrayList<>();
        FormHonourWallDriverMedalInfo formHonourWallDriverMedalInfo = new FormHonourWallDriverMedalInfo();
        formHonourWallDriverMedalInfo.setDriverRank(10);
        formHonourWallDriverMedalInfo.setTotalRank(20);
        formHonourWallDriverMedalInfo.setLight(true);
        formHonourWallDriverMedalInfo.setCode("honour");
        honourWallMedalInfo.add(formHonourWallDriverMedalInfo);

        FormHonourWallDriverMedalInfo goldMedal = new FormHonourWallDriverMedalInfo();
        goldMedal.setDriverRank(10);
        goldMedal.setTotalRank(20);
        goldMedal.setLight(true);
        goldMedal.setCode("goldMedal");
        honourWallMedalInfo.add(goldMedal);

        List<MedalBasicInfoModel> medalBasicInfoModels = JacksonUtil.deserialize(
                "[{\"type\":\"GOODCAR\",\"medalCode\":\"goodaction\",\"bimedalType\":\"cnzj_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1,\"medalGradeMin\":1,\"medalGradeMax\":50},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments50\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":50,\"medalGradeMin\":50,\"medalGradeMax\":100},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments100\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":100,\"medalGradeMin\":100,\"medalGradeMax\":150},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments150\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":150,\"medalGradeMin\":150,\"medalGradeMax\":200},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments200\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":200,\"medalGradeMin\":200,\"medalGradeMax\":300},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments300\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":300,\"medalGradeMin\":300,\"medalGradeMax\":500},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments500\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":500,\"medalGradeMin\":500,\"medalGradeMax\":800},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments800\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":800,\"medalGradeMin\":800,\"medalGradeMax\":1000},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1000\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1000,\"medalGradeMin\":1000,\"medalGradeMax\":1200},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1200\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1200,\"medalGradeMin\":1200,\"medalGradeMax\":1500},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1500\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1500,\"medalGradeMin\":1500,\"medalGradeMax\":2000},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments2000\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":2000,\"medalGradeMin\":2000,\"medalGradeMax\":9999999999},{\"type\":\"GOODMAP\",\"medalCode\":\"goodaction\",\"bimedalType\":\"hdtrlz_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders1\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":1,\"medalGradeMin\":1,\"medalGradeMax\":50},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders50\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":50,\"medalGradeMin\":50,\"medalGradeMax\":100},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders100\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":100,\"medalGradeMin\":100,\"medalGradeMax\":200},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders200\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":200,\"medalGradeMin\":200,\"medalGradeMax\":500},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders500\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":500,\"medalGradeMin\":500,\"medalGradeMax\":1000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders1000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":1000,\"medalGradeMin\":1000,\"medalGradeMax\":1500},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders1500\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":1500,\"medalGradeMin\":1500,\"medalGradeMax\":2000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders2000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":2000,\"medalGradeMin\":2000,\"medalGradeMax\":3000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders3000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":3000,\"medalGradeMin\":3000,\"medalGradeMax\":5000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders5000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":5000,\"medalGradeMin\":5000,\"medalGradeMax\":8000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders8000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":8000,\"medalGradeMin\":8000,\"medalGradeMax\":10000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders10000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":10000,\"medalGradeMin\":10000,\"medalGradeMax\":9999999999},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1,\"medalGradeMin\":1,\"medalGradeMax\":50},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays50\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":50,\"medalGradeMin\":50,\"medalGradeMax\":100},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays100\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":100,\"medalGradeMin\":100,\"medalGradeMax\":200},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays200\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":200,\"medalGradeMin\":200,\"medalGradeMax\":300},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays300\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":300,\"medalGradeMin\":300,\"medalGradeMax\":500},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays500\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":500,\"medalGradeMin\":500,\"medalGradeMax\":800},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays800\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":800,\"medalGradeMin\":800,\"medalGradeMax\":1000},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1000\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1000,\"medalGradeMin\":1000,\"medalGradeMax\":1500},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1500\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1500,\"medalGradeMin\":1500,\"medalGradeMax\":1800},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1800\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1800,\"medalGradeMin\":1800,\"medalGradeMax\":2000},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays2000\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":2000,\"medalGradeMin\":2000,\"medalGradeMax\":2500},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays2500\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":2500,\"medalGradeMin\":2500,\"medalGradeMax\":9999999999},{\"type\":\"GOODMANNER\",\"medalCode\":\"goodaction\",\"bimedalType\":\"tdhfwj_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999},{\"type\":\"GOODLUGGAGE\",\"medalCode\":\"goodaction\",\"bimedalType\":\"zdbnxl_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999}]",
                new TypeToken<List<MedalBasicInfoModel>>(){}.getType());

        AdmPrdTrhDriverHonorInfoPO biDriverMedalInfo = new AdmPrdTrhDriverHonorInfoPO();
        biDriverMedalInfo.setOrderDay(100L);
        biDriverMedalInfo.setOrderDayMedalGrade(100);
        biDriverMedalInfo.setOrderCnt(500L);
        biDriverMedalInfo.setOrderCntMedalGrade(500);
        biDriverMedalInfo.setGoodComment(1000L);
        biDriverMedalInfo.setGoodCommentMedalGrade(1000);
        biDriverMedalInfo.setTdhfwjTagMedal(1);
        biDriverMedalInfo.setCnzjTagMedal(1);
        biDriverMedalInfo.setHdtrlzTagMedal(1);
        biDriverMedalInfo.setZdbnxlTagMedal(1);
        biDriverMedalInfo.setMedalGetCnt(50L);

        GoldMedalDriverModel goldMedalDriverModel = new GoldMedalDriverModel();
        goldMedalDriverModel.setType("goldMedal");
        goldMedalDriverModel.setLight(true);
        goldMedalDriverModel.setMonthTime("202311");
        goldMedalDriverModel.setTotalCount(3);

        CustomerMedalSimpleInfo customerMedalSimpleInfo = new CustomerMedalSimpleInfo();
        customerMedalSimpleInfo.setCustomerShow(true);
        customerMedalSimpleInfo.setCustomerShowIndex(1);

        new Expectations(){
            {
                domainBeanFactory.honourImgConfig().isCustomerMedalCache();
                result = true;

                domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverInfo.driverId);
                result = driverInfo;

                domainBeanFactory.honourFormConfig().getHonourWallMedalInfo(driverInfo.driverId, false, this.withAny((GoldMedalDriverModel) this.any));
                result = honourWallMedalInfo;

                domainBeanFactory.honourRedisLogic().queryCurrentMedalBasicInfo();
                result = medalBasicInfoModels;

                domainBeanFactory.honourFormConfig().getCustomerMedalInfo(this.anyString);
                result = customerMedalSimpleInfo;

                domainBeanFactory.honourDBDataService().queryMaxBatchDataDetail(driverInfo.driverId);
                result = biDriverMedalInfo;
            }
        };

        QueryCustomerMedalListResponseType responseType = queryCustomerMedalListEngine.execute(requestType);
        Assert.assertEquals(responseType.responseResult.returnCode, "200");
        Assert.assertEquals(responseType.medalList.size(), 8);

        new Expectations(){
            {
                domainBeanFactory.honourRedisLogic().getDriverCustomerMedalInfo(driverInfo.driverId);
                result = Pair.of(true, Collections.emptyList());
            }
        };
        responseType = queryCustomerMedalListEngine.execute(requestType);
        Assert.assertEquals(responseType.responseResult.returnCode, "200");
        Assert.assertEquals(responseType.medalList.size(), 0);
    }
}
