package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.domain.CommonResult;
import com.ctrip.dcs.driver.domain.honour.SubmitDriverLikeRequestType;
import com.ctrip.dcs.driver.domain.honour.SubmitDriverLikeResponseType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class SubmitDriverLikeEngineTest extends TestBase {

    @Tested
    SubmitDriverLikeEngine submitDriverLikeEngine;

    @Test
    public void execute() {
        DriverInfo driverInfo = bulidSimpleDriverInfo();
        SubmitDriverLikeRequestType requestType = new SubmitDriverLikeRequestType();
        requestType.setDriverId(driverInfo.driverId);
        requestType.setLikedDriverId(driverInfo.driverId);
        requestType.setRefs("BFDA88B969EE100CF18956045A646C9F7ECD40BB5673FFB7B6330C4F5B38AA5CA0C5FEBC88E9CD132559F785AC77D27961910B01E3A49490");
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(SubmitDriverLikeResponseType.class).init();

        new Expectations(){
            {
                domainBeanFactory.commonCryptographicUtils().decryptByDES(requestType.refs);
                result = "1_1_1_1_1";
            }
        };

        try {
            SubmitDriverLikeResponseType responseType = submitDriverLikeEngine.execute(requestType);
            Assert.assertEquals(responseType.responseResult.returnCode, "200");
        }catch (Exception e) {

        }
    }

    @Test
    public void testCommonResult() {
        CommonResult commonResult = CommonResult.newBuilder()
                .withCode("code")
                .withSuccess(true)
                .withMsg("msg").build();
        Assert.assertNotNull(commonResult.getCode());
        Assert.assertNotNull(commonResult.getMsg());
        Assert.assertNotNull(commonResult.isSuccess());
    }

}
