package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.exam.SubmitApplyExamRequestType;
import com.ctrip.dcs.driver.domain.exam.SubmitApplyExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import java.util.UUID;


public class SubmitApplyExamEngineTest extends TestBase {
  @Tested
  SubmitApplyExamEngine submitApplyExamEngine;

  @Injectable
  LockService lockService;

  @Test
  public void test(){
    SubmitApplyExamRequestType request = new SubmitApplyExamRequestType();
    request.setExamAccountId("guide0001971");
    request.setApplySubject("DDXDCJ2024");
    request.setAccount("***********");
    request.setGuideId(1971L);
    request.setGuideName("ceshi");
    ServiceExecuteContext.newContext()
        .withRequest(request)
        .withGlobalTraceId(UUID.randomUUID().toString())
        .withResponseClass(SubmitApplyExamResponseType.class).init();
    new Expectations(){{
      domainBeanFactory.guideExamConfig().getExamNameByDeptId("DDXDCJ2024");
      result = "中级笔试";
    }};
    SubmitApplyExamResponseType execute = submitApplyExamEngine.execute(request);
    Assert.assertEquals(execute.responseResult.returnCode,"200");
  }
}
