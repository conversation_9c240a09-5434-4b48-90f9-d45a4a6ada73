package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.finance.ResetFinancePasswordRequestType;
import com.ctrip.dcs.driver.domain.finance.ResetFinancePasswordResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.MesTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class ResetFinancePasswordEngineUnitTest extends TestBase {

    @Tested
    ResetFinancePasswordEngine resetFinancePasswordEngine;

    @Test
    public void test() {
        ResetFinancePasswordRequestType requestType = new ResetFinancePasswordRequestType();
        requestType.driverId = 1000004L;
        requestType.verificationCode = "code";
        requestType.identitycode = "identitycode";
        requestType.password = "password";
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(ResetFinancePasswordResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;
        driverInfo.driverPhone = "13812345678";
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;
            }
        };

        ResetFinancePasswordResponseType responseType = resetFinancePasswordEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.VERIFYCODE_FAIL.getCode(), responseType.responseResult.returnCode);

        new Expectations(){
            {
                domainBeanFactory.infrastructureServiceProxy().checkPhoneCode(
                        driverInfo.driverPhone, requestType.verificationCode, MesTypeEnum.MODIFY_WITHDRAW_PASSWORD);
                result = true;

                domainBeanFactory.financeConvertService().driverResetPsw(this.withAny((WithdrawModel) this.any));
                result = FinanceResultEnum.OK.getCode();
            }
        };
        responseType = resetFinancePasswordEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
