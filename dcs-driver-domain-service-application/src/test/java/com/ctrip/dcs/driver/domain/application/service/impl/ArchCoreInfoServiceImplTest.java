package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.arch.coreinfo.entity.soa.Data;
import com.ctrip.arch.coreinfo.entity.soa.Key;
import com.ctrip.arch.coreinfo.entity.soa.Request;
import com.ctrip.arch.coreinfo.entity.soa.Response;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.driver.domain.application.TestBase;
import mockit.Tested;

import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Collections;


public class ArchCoreInfoServiceImplTest extends TestBase {

	@Tested
	ArchCoreInfoServiceImpl archCoreInfoService;

	@Test
	public void decryptIdCard() throws Exception {
		String decryptedIdCard = "3206FhWIwEdQR3r188";
		Request request = new Request();
		Key key = new Key();
		key.key = decryptedIdCard;
		key.type = KeyType.Identity_Card.getCode();
		request.setInfos(Collections.singletonList(key));
		Response response = new Response();
		Data data = new Data();
		data.setResult("320682199211091188");
		response.results = Collections.singletonList(data);
		String idCard = archCoreInfoService.decryptIdCard(decryptedIdCard);
		Assert.assertNotNull(idCard);
	}

	@Test
	public void encryptIdCard() throws Exception {
		String encryptIdCard = "320682199211091188";
		Request request = new Request();
		Key key = new Key();
		key.key = encryptIdCard;
		key.type = KeyType.Identity_Card.getCode();
		request.setInfos(Collections.singletonList(key));
		Response response = new Response();
		Data data = new Data();
		data.setResult("3206FhWIwEdQR3r188");
		response.results = Collections.singletonList(data);
		String idCard = archCoreInfoService.encryptIdCard(encryptIdCard);
		Assert.assertTrue(StringUtils.isNotBlank(idCard));
	}
}
