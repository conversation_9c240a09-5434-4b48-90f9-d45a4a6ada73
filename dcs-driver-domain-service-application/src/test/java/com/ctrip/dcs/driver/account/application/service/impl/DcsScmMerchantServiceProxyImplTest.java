package com.ctrip.dcs.driver.account.application.service.impl;

import com.ctrip.dcs.driver.account.infrastructure.adapter.DcsScmMerchantServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.adapter.soa.RhbSettlementBillSoaServiceProxy;
import com.ctrip.dcs.driver.gateway.YeePayRepository;
import com.ctrip.dcs.driver.gateway.impl.YeePayRepositoryImpl;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidRequestType;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.io.IOException;


public class DcsScmMerchantServiceProxyImplTest {

    @Tested
    YeePayRepositoryImpl yeePayRepository;

    @Injectable
    DcsScmMerchantServiceProxy dcsScmMerchantServiceProxy;
    @Injectable
    RhbSettlementBillSoaServiceProxy rhbSettlementBillSoaServiceProxy;
    @Injectable
    DriverAccountConfig driverAccountConfig;

    @Test
    public void createCardInfo() throws IOException {
        new Expectations(){ {
                dcsScmMerchantServiceProxy.checkDriverBandCardValid(this.withAny((CheckDriverBandCardValidRequestType) this.any));
                result = null;
            } };
        CheckDriverBandCardValidResponseType responseType = yeePayRepository.checkDriverBandCardValid(
                1L, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        Assert.assertNull(responseType);

        CheckDriverBandCardValidResponseType checkDriverBandCardValidResponseType = new CheckDriverBandCardValidResponseType();
        checkDriverBandCardValidResponseType.businessCode = "CFC102017";
        new Expectations(){ {
                dcsScmMerchantServiceProxy.checkDriverBandCardValid(this.withAny((CheckDriverBandCardValidRequestType) this.any));
                result = checkDriverBandCardValidResponseType;
            } };
        responseType = yeePayRepository.checkDriverBandCardValid(
                1L, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        Assert.assertNotNull(responseType);
        Assert.assertEquals("CFC102017", responseType.businessCode);
    }
}
