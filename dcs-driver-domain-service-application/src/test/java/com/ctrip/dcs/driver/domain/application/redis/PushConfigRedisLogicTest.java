package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.setting.DriverPushConfigInfoDTO;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class PushConfigRedisLogicTest {

  @Tested
  PushConfigRedisLogic pushConfigRedisLogic;

  @Injectable
  DirectorRedis directorRedis;

  private final static long driverId = 1000004L;

  @Test
  public void saveDriverPushConfig() {
    pushConfigRedisLogic.saveDriverPushConfig(driverId, new DriverPushConfigInfoDTO());
    Assert.assertEquals(driverId, 1000004L);
  }

  @Test
  public void queryDriverPushConfig() {
    pushConfigRedisLogic.queryDriverPushConfig(driverId);
    Assert.assertEquals(driverId, 1000004L);
  }

  @Test
  public void updateDriverLanguage() {
    pushConfigRedisLogic.updateDriverLanguage(driverId, "en-us");
    pushConfigRedisLogic.updateDriverLanguage(driverId, "zh-cn");
    Assert.assertEquals(driverId, 1000004L);
  }

}
