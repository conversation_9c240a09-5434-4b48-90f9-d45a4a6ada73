package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class CommonCryptographicUtilsTest {

    @Tested
    CommonCryptographicUtils commonCryptographicUtils;

    @Injectable
    private CommonLogger logger;

    @Test
    public void test() {
        String originalValue = "test";
        String desValue = "05BBDE9E24BB2A81";
        try {
            commonCryptographicUtils.initialize();
        } catch (KMSUtils.KmsException e) {
            Assert.assertFalse(false);
        }

        String encryptValue = commonCryptographicUtils.encryptByDES(originalValue);
//        Assert.assertEquals(encryptValue, desValue);
        String decryptValue = commonCryptographicUtils.decryptByDES(desValue);
//        Assert.assertNotNull(decryptValue, originalValue);
    }
}
