package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.account.domain.config.MegviiQConfig;
import com.ctrip.dcs.driver.domain.application.common.CommonCryptographicUtils;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyParamDTO;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyResultDTO;
import com.ctrip.dcs.driver.domain.application.exector.faceauth.CompareDriverImageByMegviiExecutor;
import com.ctrip.dcs.driver.domain.application.service.faceauth.megvii.FaceComparisonResultEnum;
import com.ctrip.dcs.driver.domain.application.service.faceauth.megvii.VerifyFaceService;
import com.ctrip.dcs.driver.domain.application.util.ImageFileUtil;
import com.ctrip.dcs.driver.domain.facerecognize.CompareDriverImageByMegvRequestType;
import com.ctrip.dcs.driver.domain.facerecognize.CompareDriverImageByMegvResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverOverseaFaceVerifyDetailDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverOverseaFaceVerifyRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverOverseaFaceVerifyDetailPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverOverseaFaceVerifyRecordPO;
import com.ctrip.igt.framework.infrastructure.constant.ServiceResponseConstants;
import com.ctrip.tour.driver.utility.log.LogHelper;
import mockit.Deencapsulation;
import mockit.Mock;
import mockit.MockUp;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CompareDriverImageByMegviiExecutorTest {

    private CompareDriverImageByMegviiExecutor instance;
    private MegviiQConfig megviiQConfig = new MegviiQConfig();

    @Before
    public void init() {
        instance = Deencapsulation.newUninitializedInstance(CompareDriverImageByMegviiExecutor.class);
        Deencapsulation.setField(instance, "megviiQConfig", megviiQConfig);
        Deencapsulation.setField(instance, "driverOverseaFaceVerifyRecordDao", Deencapsulation.newUninitializedInstance(DriverOverseaFaceVerifyRecordDao.class));
        Deencapsulation.setField(instance, "commonCryptographicUtils", Deencapsulation.newUninitializedInstance(CommonCryptographicUtils.class));
        Deencapsulation.setField(instance, "verifyFaceService", Deencapsulation.newUninitializedInstance(VerifyFaceService.class));
        Deencapsulation.setField(instance, "driverOverseaFaceVerifyDetailDao", Deencapsulation.newUninitializedInstance(DriverOverseaFaceVerifyDetailDao.class));
    }

    @Test
    public void test_execute() {
        new MockUp<CommonCryptographicUtils>() {
            @Mock
            public String decryptByDES(String decryptString) {
                return "mock.jpg";
            }
        };
        List<byte[]> bufList = new ArrayList<>();
        new MockUp<ImageFileUtil>() {
            @Mock
            public byte[] downloadThenCompressImage(String fileName) {
                return bufList.removeFirst();
            }
        };
        boolean[] hasError = {false};
        new MockUp<LogHelper>() {
            @Mock
            public void info(String title, String message, Map<String, String> attrs) {}
            @Mock
            public void warn(String title, Throwable throwable, Map<String, String> attrs) {
                hasError[0] = true;
            }
        };
        V5VerifyResultDTO[] resultH = {null};
        boolean[] thrEx = {false};
        new MockUp<VerifyFaceService>() {
            @Mock
            public V5VerifyResultDTO verify(V5VerifyParamDTO params) throws Exception {
                if (thrEx[0]) {
                    throw new RuntimeException("mock");
                }
                return resultH[0];
            }
        };
        new MockUp<DriverOverseaFaceVerifyRecordDao>() {
            @Mock
            public int insert(DriverOverseaFaceVerifyRecordPO daoPojo) {
                return 1;
            }
        };
        new MockUp<DriverOverseaFaceVerifyDetailDao>() {
            @Mock
            public int insert(DriverOverseaFaceVerifyDetailPO daoPojo) {
                return 1;
            }
        };

        CompareDriverImageByMegvRequestType request = new CompareDriverImageByMegvRequestType();
        request.setDriverId(1L);

        bufList.add(null);
        CompareDriverImageByMegvResponseType res = instance.execute(request);
        Assert.assertEquals(FaceComparisonResultEnum.REF_MISSING.getValue(), res.getResult().intValue());

        bufList.clear();
        bufList.add(new byte[]{0, 1});
        bufList.add(null);
        res = instance.execute(request);
        Assert.assertEquals(FaceComparisonResultEnum.CMP_MISSING.getValue(), res.getResult().intValue());

        bufList.clear();
        bufList.add(new byte[]{0, 1});
        bufList.add(new byte[]{0, 1});
        res = instance.execute(request);
        Assert.assertEquals(res.getResponseResult().getReturnCode(), ServiceResponseConstants.ResStatus.EXCEPTION_CODE);

        bufList.clear();
        bufList.add(new byte[]{0, 1});
        bufList.add(new byte[]{0, 1});
        thrEx[0] = true;
        instance.execute(request);
        Assert.assertTrue(hasError[0]);

        bufList.clear();
        bufList.add(new byte[]{0, 1});
        bufList.add(new byte[]{0, 1});
        thrEx[0] = false;
        resultH[0] = new V5VerifyResultDTO();
        resultH[0].setResultCode(1000);
        res = instance.execute(request);
        Assert.assertEquals(FaceComparisonResultEnum.PASS.getValue(), res.getResult().intValue());

        bufList.clear();
        bufList.add(new byte[]{0, 1});
        bufList.add(new byte[]{0, 1});
        thrEx[0] = false;
        resultH[0] = new V5VerifyResultDTO();
        resultH[0].setResultCode(2000);
        res = instance.execute(request);
        Assert.assertEquals(FaceComparisonResultEnum.NOT_PASS.getValue(), res.getResult().intValue());
    }

}
