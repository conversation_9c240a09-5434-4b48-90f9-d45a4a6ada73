package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.account.domain.config.MegviiQConfig;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyParamDTO;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyResultDTO;
import com.ctrip.dcs.driver.domain.application.service.faceauth.megvii.VerifyFaceService;
import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import com.ctrip.tour.driver.utility.json.JacksonUtil;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import com.ning.http.client.Response;
import com.ning.http.client.cookie.Cookie;
import com.ning.http.client.uri.Uri;
import lombok.SneakyThrows;
import mockit.Deencapsulation;
import mockit.Mock;
import mockit.MockUp;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;

import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.List;

public class VerifyFaceServiceTest {

    private VerifyFaceService instance;

    private MegviiQConfig megviiQConfig = new MegviiQConfig();

    @Before
    public void init() {
        instance = Deencapsulation.newUninitializedInstance(VerifyFaceService.class);
        Deencapsulation.setField(instance, "megviiQConfig", megviiQConfig);
    }

    @SneakyThrows
    @Test
    public void test_verify() {
        megviiQConfig.setVerifyId("verifyId");
        megviiQConfig.setKmsTokenApiKey("apiKey");
        megviiQConfig.setKmsTokenApiSecret("apiSecret");
        megviiQConfig.setSignExpiredSeconds(60);
        megviiQConfig.setReqEncryptionPubKeyToken("reqEncryptionPubKeyToken");
        megviiQConfig.setMegvSGPHost("https://baidu.com");
        megviiQConfig.setSubmitVerifyUrl("/faceid/v5/sdk/verify");
        new MockUp<KMSUtils>() {
            @Mock
            public String key(String token) throws KMSUtils.KmsException {
                if ("apiKey".equals(token)) {
                    return "mockAPiKey";
                } else if ("apiSecret".equals(token)) {
                    return "mockApiSecret";
                } else if ("reqEncryptionPubKeyToken".equals(token)) {
                    return "-----BEGIN PUBLIC KEY-----\n" +
                            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCBSc4iLjnf7CQsF7KrmmXWXMDA\n" +
                            "xPZbXIxf0Gdf5QsoBTOgwzSTIUgbdh7srEFrq3f2wjcpRuFHhhRNP1UujQM3onXi\n" +
                            "La9SyckZluJhlFHmurY9P3YVnnXdtw4tMDHIkWhFIygArN4dYkbQdNw4HUMvKixu\n" +
                            "JIZfGkDfBqT+0p2FfwIDAQAB\n" +
                            "-----END PUBLIC KEY-----";
                }
                return null;
            }
        };
        new MockUp<QunarAsyncClient>() {
            @Mock
            public ListenableFuture<Response> post(String url, QHttpOption option) throws IOException {
                return Futures.immediateFuture(new Response() {
                    @Override
                    public int getStatusCode() {
                        return 0;
                    }

                    @Override
                    public String getStatusText() {
                        return "";
                    }

                    @Override
                    public byte[] getResponseBodyAsBytes() throws IOException {
                        return new byte[0];
                    }

                    @Override
                    public ByteBuffer getResponseBodyAsByteBuffer() throws IOException {
                        return null;
                    }

                    @Override
                    public InputStream getResponseBodyAsStream() throws IOException {
                        return null;
                    }

                    @Override
                    public String getResponseBodyExcerpt(int maxLength, String charset) throws IOException {
                        return "";
                    }

                    @Override
                    public String getResponseBody(String charset) throws IOException {
                        return "";
                    }

                    @Override
                    public String getResponseBodyExcerpt(int maxLength) throws IOException {
                        return "";
                    }

                    @Override
                    public String getResponseBody() throws IOException {
                        V5VerifyResultDTO resultDTO = new V5VerifyResultDTO();
                        resultDTO.setResultCode(1000);
                        return JacksonUtil.toJSONString(resultDTO);
                    }

                    @Override
                    public Uri getUri() {
                        return null;
                    }

                    @Override
                    public String getContentType() {
                        return "";
                    }

                    @Override
                    public String getHeader(String name) {
                        return "";
                    }

                    @Override
                    public List<String> getHeaders(String name) {
                        return List.of();
                    }

                    @Override
                    public FluentCaseInsensitiveStringsMap getHeaders() {
                        return null;
                    }

                    @Override
                    public boolean isRedirected() {
                        return false;
                    }

                    @Override
                    public List<Cookie> getCookies() {
                        return List.of();
                    }

                    @Override
                    public boolean hasResponseStatus() {
                        return false;
                    }

                    @Override
                    public boolean hasResponseHeaders() {
                        return false;
                    }

                    @Override
                    public boolean hasResponseBody() {
                        return false;
                    }
                });
            }
        };
        V5VerifyParamDTO params = new V5VerifyParamDTO();
        params.setBizNo("bizNo");
        params.setComparisonType("0");
        params.setDataType("1");
        params.setVerifyId(megviiQConfig.getVerifyId());
        params.setUuid("1");
        params.setImageFileName("cmp.jpeg");
        params.setImage(new byte[] {(byte)0,(byte)0,(byte)1,(byte)1});
        params.setImageRef1(new byte[] {(byte)0,(byte)0,(byte)1,(byte)1});
        params.setImageRef1FileName("ref.jpeg");
        V5VerifyResultDTO result = instance.verify(params);
        Assert.assertEquals(1000, result.getResultCode().intValue());
    }

}
