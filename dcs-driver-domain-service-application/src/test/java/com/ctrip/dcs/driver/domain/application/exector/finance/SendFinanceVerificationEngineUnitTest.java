package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.finance.SendFinanceVerificationRequestType;
import com.ctrip.dcs.driver.domain.finance.SendFinanceVerificationResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.MesTypeEnum;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class SendFinanceVerificationEngineUnitTest extends TestBase {

    @Tested
    SendFinanceVerificationEngine sendFinanceVerificationEngine;

    @Test
    public void test() {
        SendFinanceVerificationRequestType requestType = new SendFinanceVerificationRequestType();
        requestType.driverId = 1000004L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(SendFinanceVerificationResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;
            }
        };

        SendFinanceVerificationResponseType responseType = sendFinanceVerificationEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.SEND_VERIFYCODE_FAIL.getCode(), responseType.responseResult.returnCode);

        driverInfo.driverPhone = "13812345678";
        new Expectations(){
            {
                domainBeanFactory.infrastructureServiceProxy().sendMessageByPhone(driverInfo.driverPhone, MesTypeEnum.MODIFY_WITHDRAW_PASSWORD);
                result = FinanceResultEnum.OK.getCode();
            }
        };
        responseType = sendFinanceVerificationEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
