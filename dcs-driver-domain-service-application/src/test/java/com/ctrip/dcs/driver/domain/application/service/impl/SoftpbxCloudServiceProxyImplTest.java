package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.platform.SoftpbxCloudServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.model.extension.IVRExtDataInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.extension.IVRInfoModel;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class SoftpbxCloudServiceProxyImplTest extends TestBase {

	@Tested
	SoftpbxCloudServiceProxyImpl softpbxCloudServiceProxyImpl;

	@Injectable
	private SoftpbxCloudServiceProxy softpbxCloudServiceProxy;

	@Test
	public void sendAppUserInfo() {
		IVRInfoModel ivrInfoModel = new IVRInfoModel();
		ivrInfoModel.setDriverId(1000004L);
		ivrInfoModel.setPageId("10650046091");
		IVRExtDataInfoModel ivrExtDataInfoModel = new IVRExtDataInfoModel();
		ivrExtDataInfoModel.setDriverId(String.valueOf(1000004));
		ivrExtDataInfoModel.setOrderId("36556391616");
		ivrExtDataInfoModel.setProdectId("881833523916495223");
		ivrExtDataInfoModel.setDrivermobile("13743Mj5150");
		ivrExtDataInfoModel.setCityId(String.valueOf(2));
		ivrExtDataInfoModel.setServicePhone("0513-68192122");
		String result = softpbxCloudServiceProxyImpl.sendAppUserInfo(ivrInfoModel);
		Assert.assertNotNull(result);
	}
}
