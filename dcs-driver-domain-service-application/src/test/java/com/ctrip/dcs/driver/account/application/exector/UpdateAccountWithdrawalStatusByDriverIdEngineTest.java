package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.domain.account.UpdateAccountWithdrawalStatusByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.UpdateAccountWithdrawalStatusByDriverIdResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class UpdateAccountWithdrawalStatusByDriverIdEngineTest {

    @Tested
    UpdateAccountWithdrawalStatusByDriverIdEngine updateAccountWithdrawalStatusByDriverIdEngine;

    @Injectable
    AccountBaseInfoDao accountBaseInfoDao;

    @Injectable
    AccountService accountService;

    @Test
    public void bindAccountBankCard()  {
        UpdateAccountWithdrawalStatusByDriverIdRequestType requestType = new UpdateAccountWithdrawalStatusByDriverIdRequestType();
        requestType.driverId = 123456L;
        new Expectations() {{
            accountService.getAccountByDriverId(this.anyLong);
            result = null;
        }};
        UpdateAccountWithdrawalStatusByDriverIdResponseType responseType = updateAccountWithdrawalStatusByDriverIdEngine.execute(requestType);
        Assert.assertEquals("500", responseType.responseResult.returnCode);

        AccountInfoDTO accountInfo = new AccountInfoDTO();
        accountInfo.setUid(StringUtils.EMPTY);
        new Expectations() {{
            accountService.getAccountByDriverId(this.anyLong);
            result = accountInfo;
        }};
        responseType = updateAccountWithdrawalStatusByDriverIdEngine.execute(requestType);
        Assert.assertEquals("500", responseType.responseResult.returnCode);

        accountInfo.setUid("uid");
        new Expectations() {{
            accountBaseInfoDao.queryByUID(this.anyString);
            result = null;
        }};
        responseType = updateAccountWithdrawalStatusByDriverIdEngine.execute(requestType);
        Assert.assertEquals("500", responseType.responseResult.returnCode);

        new Expectations() {{
            accountService.getAccountByDriverId(this.anyLong);
            result = accountInfo;

            accountBaseInfoDao.queryByUID(this.anyString);
            result = new AccountBaseInfoPO();

            accountBaseInfoDao.update(this.withAny((AccountBaseInfoPO) this.any));
            result = -1;
        }};
        responseType = updateAccountWithdrawalStatusByDriverIdEngine.execute(requestType);
        Assert.assertEquals("500", responseType.responseResult.returnCode);

        new Expectations() {{
            accountBaseInfoDao.update(this.withAny((AccountBaseInfoPO) this.any));
            result = 1;
        }};
        responseType = updateAccountWithdrawalStatusByDriverIdEngine.execute(requestType);
        Assert.assertEquals("200", responseType.responseResult.returnCode);
    }
}
