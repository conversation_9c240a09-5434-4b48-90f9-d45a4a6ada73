package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.redis.PointInfoRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverPointRestfulShowInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.setting.DriverPointRestfulPopInfoModel;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPointPopInfoRequestType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPointPopInfoResponseType;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class QueryDriverPointPopInfoEngineUnitTest extends TestBase {

    @Tested
    QueryDriverPointPopInfoEngine queryDriverPointPopInfoEngine;

    @Injectable
    private DriverPointRestfulShowInfoDao driverPointRestfulShowInfoDao;

    @Injectable
    PointInfoRedisLogic pointInfoRedisLogic;

    @Test
    public void test() {
        QueryDriverPointPopInfoRequestType requestType = new QueryDriverPointPopInfoRequestType();
        requestType.driverId = 1000004L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverPointPopInfoResponseType.class).init();

        new Expectations(){
            {
                pointInfoRedisLogic.queryDriverPointInfo(requestType.driverId);
                result = null;

                driverPointRestfulShowInfoDao.findOne(requestType.driverId);
                result = new DriverPointRestfulShowInfoPO();
            }
        };
        QueryDriverPointPopInfoResponseType responseType = queryDriverPointPopInfoEngine.execute(requestType);
        Assert.assertFalse(responseType.hasMainPageInfo);
        Assert.assertFalse(responseType.hasVoidanceInfo);

        DriverPointRestfulPopInfoModel driverPointRestfulPopInfo = new DriverPointRestfulPopInfoModel();
        driverPointRestfulPopInfo.setNovicePointGuidePop(1);
        driverPointRestfulPopInfo.setNovicePointVoidancePop(1);
        new Expectations(){
            {
                pointInfoRedisLogic.queryDriverPointInfo(requestType.driverId);
                result = driverPointRestfulPopInfo;
            }
        };
        responseType = queryDriverPointPopInfoEngine.execute(requestType);
        Assert.assertTrue(responseType.hasMainPageInfo);
        Assert.assertTrue(responseType.hasVoidanceInfo);
    }
}
