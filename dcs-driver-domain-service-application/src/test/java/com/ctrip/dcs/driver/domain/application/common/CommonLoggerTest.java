package com.ctrip.dcs.driver.domain.application.common;

import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.Assert;

import java.beans.IntrospectionException;
import java.util.HashMap;


public class CommonLoggerTest {

    @Tested
    CommonLogger commonLogger;

    @Test
    public void test() {
        IntrospectionException introspectionException = new IntrospectionException("new IntrospectionException");
        CommonLogger.INSTANCE.warn("CommonLoggerTest error", introspectionException.getMessage());
        Assert.assertNotNull(introspectionException.getMessage());

        Exception exception = new Exception("new Exception");
        CommonLogger.INSTANCE.warn("CommonLoggerTest error", exception);
        Assert.assertNotNull(exception.getMessage());

        CommonLogger.INSTANCE.info("CommonLoggerTest info", "message");

        CommonLogger.INSTANCE.info("CommonLoggerTest info", "message", new HashMap<>());

        CommonLogger.INSTANCE.infoFormat("CommonLoggerTest info", "message", new HashMap<>());

        Throwable throwable = new Throwable("new Throwable");
        CommonLogger.INSTANCE.error(throwable);
    }

}
