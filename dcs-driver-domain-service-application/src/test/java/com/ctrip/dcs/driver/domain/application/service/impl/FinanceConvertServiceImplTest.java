package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.supply.SupplyAccountTransferServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.FinanceConfig;
import com.ctrip.dcs.supply.driver.dto.*;
import com.ctrip.igt.ResponseResult;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.util.ArrayList;
import java.util.List;


public class FinanceConvertServiceImplTest extends TestBase {

	@Tested
	FinanceConvertServiceImpl financeConvertService;

	@Injectable
	SupplyAccountTransferServiceProxy supplyAccountTransferServiceProxy;

	@Injectable
	CommonMultipleLanguages commonMultipleLanguages;

	@Injectable
	FinanceConfig financeQconfig;

	private final static long driverId = 100038374L;
	private final static boolean oldProcess = false;

	@Test
	public void queryDriverWithdrawBalance() {
		QueryDriverWithdrawBalanceRequestType requestType = new QueryDriverWithdrawBalanceRequestType();
		requestType.driverId = driverId;
		requestType.sourceType = 1;

		QueryDriverWithdrawBalanceResponseType responseType = new QueryDriverWithdrawBalanceResponseType();
		responseType.responseResult = new ResponseResult();
		responseType.responseResult.returnCode = FinanceResultEnum.OK.getCode();
		responseType.data = new Balance();
		new Expectations() {
			{
				supplyAccountTransferServiceProxy.queryDriverWithdrawBalance(requestType);
				result = responseType;
			}
		};

		Pair<String, Balance> result = financeConvertService.queryDriverWithdrawBalance(driverId, oldProcess);
		Assert.assertEquals(FinanceResultEnum.OK.getCode(), result.getLeft());
		Assert.assertNotNull(result.getRight());
	}

	@Test
	public void queryDriverCardList() {
		QueryDriverCardListRequestType requestType = new QueryDriverCardListRequestType();
		requestType.driverId = driverId;

		QueryDriverCardListResponseType responseType = new QueryDriverCardListResponseType();
		responseType.responseResult = new ResponseResult();
		responseType.responseResult.returnCode = FinanceResultEnum.OK.getCode();
		responseType.data = new ArrayList<>();
		responseType.data.add(new CardInfo());
		new Expectations() {
			{
				supplyAccountTransferServiceProxy.queryDriverCardList(requestType);
				result = responseType;
			}
		};

		List<CardInfo> result = financeConvertService.queryDriverCardList(driverId);
		Assert.assertTrue(CollectionUtils.isNotEmpty(result));
	}

	@Test
	public void driverWithdraw() {
		WithdrawModel withdrawModel = new WithdrawModel();
		withdrawModel.setDriverId(driverId);
		withdrawModel.setOldProcess(oldProcess);

		DriverWithdrawRequestType requestType = new DriverWithdrawRequestType();
		requestType.driverId = driverId;
		requestType.sourceType = 1;
		requestType.driverId = withdrawModel.getDriverId();
		requestType.amount = withdrawModel.getAmount();
		requestType.bankCard = withdrawModel.getBankCard();
		requestType.bankCardInfo = withdrawModel.getBankCardInfo();
		requestType.driverName = withdrawModel.getDriverName();
		requestType.password = withdrawModel.getPassword();

		DriverWithdrawResponseType responseType = new DriverWithdrawResponseType();
		responseType.responseResult = new ResponseResult();
		responseType.responseResult.returnCode = FinanceResultEnum.OK.getCode();
		new Expectations() {
			{
				supplyAccountTransferServiceProxy.driverWithdraw(requestType);
				result = responseType;
			}
		};

		String result = financeConvertService.driverWithdraw(withdrawModel);
		Assert.assertEquals(FinanceResultEnum.OK.getCode(), result);
	}

	@Test
	public void driverResetPsw() {
		WithdrawModel withdrawModel = new WithdrawModel();
		withdrawModel.setDriverId(driverId);
		withdrawModel.setOldProcess(oldProcess);

		DriverResetPswRequestType requestType = new DriverResetPswRequestType();
		requestType.driverId = driverId;
		requestType.sourceType = 1;
		requestType.identitycode = withdrawModel.getIdentitycode();
		requestType.password = withdrawModel.getPassword();

		DriverResetPswResponseType responseType = new DriverResetPswResponseType();
		responseType.responseResult = new ResponseResult();
		responseType.responseResult.returnCode = FinanceResultEnum.OK.getCode();
		new Expectations() {
			{
				supplyAccountTransferServiceProxy.driverResetPsw(requestType);
				result = responseType;
			}
		};

		String result = financeConvertService.driverResetPsw(withdrawModel);
		Assert.assertEquals(FinanceResultEnum.OK.getCode(), result);
	}

	@Test
	public void convertDriverFinanceResult() {
		String result = financeConvertService.convertDriverFinanceResult(Strings.EMPTY);
		Assert.assertEquals(result, FinanceResultEnum.ERROR.getCode());

		result = financeConvertService.convertDriverFinanceResult("200");
		Assert.assertEquals(result, FinanceResultEnum.OK.getCode());

		new Expectations() {
			{
				financeQconfig.getConvertCode("1001");
				result = "601";
			}
		};
		result = financeConvertService.convertDriverFinanceResult("1001");
		Assert.assertEquals(result, FinanceResultEnum.BALANCE_FAIL.getCode());

		result = financeConvertService.convertDriverFinanceResult("601");
		Assert.assertEquals(result, FinanceResultEnum.DATA_FAIL.getCode());

		new Expectations(){
			{
				financeQconfig.getConvertCode("602");
				result = FinanceResultEnum.BANKCARDLIST_FAIL.getCode();
			}
		};
		result = financeConvertService.convertDriverFinanceResult("602");
		Assert.assertEquals(result, FinanceResultEnum.BANKCARDLIST_FAIL.getCode());
	}

	@Test
	public void getFinanceResult() {
		String result = financeConvertService.getFinanceResult("200");
		Assert.assertTrue(StringUtils.isBlank(result));
		result = financeConvertService.getFinanceResult("201");
		Assert.assertTrue(StringUtils.isBlank(result));
		new Expectations() {
			{
				financeQconfig.getConvertShark("601");
				result = "driver.finance.querybalance.fail";

				commonMultipleLanguages.getContent("driver.finance.querybalance.fail");
				result = "driver.finance.querybalance.fail";
			}
		};
		result = financeConvertService.getFinanceResult("601");
		Assert.assertTrue(StringUtils.isNoneBlank(result));

		new Expectations() {
			{
				financeQconfig.getConvertShark("699");
				result = "driver.finance.querybalance.fail";
			}
		};
		result = financeConvertService.getFinanceResult("699");
		Assert.assertTrue(StringUtils.isNoneBlank(result));
	}
	@Test
	public void queryDriverBalanceRecord() {
		QueryDriverBalanceRecordRequestType requestType = new QueryDriverBalanceRecordRequestType();
		requestType.driverId = driverId;
		requestType.beginDate = Strings.EMPTY;
		requestType.endDate = Strings.EMPTY;

		QueryDriverBalanceRecordResponseType responseType = new QueryDriverBalanceRecordResponseType();
		responseType.responseResult = new ResponseResult();
		responseType.responseResult.returnCode = FinanceResultEnum.OK.getCode();
		responseType.data = new ArrayList<>();
		responseType.data.add(new BalanceRecordDTO());
		new Expectations() {
			{
				supplyAccountTransferServiceProxy.queryDriverBalanceRecord(requestType);
				result = responseType;
			}
		};

		List<BalanceRecordDTO> result = financeConvertService.queryDriverBalanceRecord(driverId, Strings.EMPTY, Strings.EMPTY);
		Assert.assertTrue(CollectionUtils.isNotEmpty(result));
	}
}
