package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsDrvLoginInformationDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsVerifyEventDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.TmsDrvLoginInformationPO;
import com.ctrip.dcs.driver.domain.verify.CreateDriverChangeEquipmentEventRequestType;
import com.ctrip.dcs.driver.domain.verify.CreateDriverChangeEquipmentEventResponseType;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.UUID;


public class CreateDriverChangeEquipmentEventEngineUnitTest extends TestBase {

    @Tested
    CreateDriverChangeEquipmentEventEngine createDriverChangeEquipmentEventEngine;

    @Injectable
    private TmsVerifyEventDao tmsVerifyEventDao;
    @Injectable
    private TmsDrvLoginInformationDao tmsDrvLoginInformationDao;

    @Test
    public void test() {
        CreateDriverChangeEquipmentEventRequestType requestType = new CreateDriverChangeEquipmentEventRequestType();
        requestType.driverId = 1L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(CreateDriverChangeEquipmentEventResponseType.class).init();

        CreateDriverChangeEquipmentEventResponseType responseType = createDriverChangeEquipmentEventEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        requestType.internalScope = 1;
        responseType = createDriverChangeEquipmentEventEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        requestType.internalScope = 0;
        new Expectations(){
            {
                tmsDrvLoginInformationDao.findOne(requestType.driverId);
                result = null;
            }
        };
        responseType = createDriverChangeEquipmentEventEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        TmsDrvLoginInformationPO tmsDrvLoginInformationPO = new TmsDrvLoginInformationPO();
        tmsDrvLoginInformationPO.setDrvImei("drvImei");
        tmsDrvLoginInformationPO.setId(0L);
        tmsDrvLoginInformationPO.getId();
        tmsDrvLoginInformationPO.setDrvId(1L);
        tmsDrvLoginInformationPO.getDrvId();
        tmsDrvLoginInformationPO.setDrvLoginTime(Timestamp.valueOf(LocalDateTime.now()));
        tmsDrvLoginInformationPO.getDrvLoginTime();
        tmsDrvLoginInformationPO.setDrvLocLong(0d);
        tmsDrvLoginInformationPO.getDrvLocLong();
        tmsDrvLoginInformationPO.setDrvLocLat(0d);
        tmsDrvLoginInformationPO.getDrvLocLat();
        tmsDrvLoginInformationPO.setDrvLocCsys(Strings.EMPTY);
        tmsDrvLoginInformationPO.getDrvLocCsys();
        tmsDrvLoginInformationPO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.now()));
        tmsDrvLoginInformationPO.getDatachangeCreatetime();
        tmsDrvLoginInformationPO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
        tmsDrvLoginInformationPO.getDatachangeLasttime();
        tmsDrvLoginInformationPO.setCreateUser(Strings.EMPTY);
        tmsDrvLoginInformationPO.getCreateUser();
        tmsDrvLoginInformationPO.setModifyUser(Strings.EMPTY);
        tmsDrvLoginInformationPO.getModifyUser();
        new Expectations(){
            {
                tmsDrvLoginInformationDao.findOne(requestType.driverId);
                result = tmsDrvLoginInformationPO;
            }
        };
        responseType = createDriverChangeEquipmentEventEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        requestType.driverImei = "drvImei";
        responseType = createDriverChangeEquipmentEventEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        requestType.isSaveLoginRecord = false;
        responseType = createDriverChangeEquipmentEventEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
