package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.exam.QueryExamRequestType;
import com.ctrip.dcs.driver.domain.exam.QueryExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


public class QueryExamEngineTest  extends TestBase {
  @Tested
  QueryExamEngine queryExamEngine;

//  @Test
//  public void test(){
//    List<GuideApplyExamModel> guideApplyExamModels = new ArrayList<>();
//    GuideApplyExamModel model = getGuideApplyExamModel();
//    guideApplyExamModels.add(model);
//    List<FromGuideExamInfo> validityGuideExamInfoList = getValidityGuideExamInfoList();
//    QueryExamRequestType requestType = new QueryExamRequestType();
//    requestType.setExamAccountId("guide0001971");
//    requestType.setGuideId(1971L);
//    ServiceExecuteContext.newContext()
//        .withRequest(requestType)
//        .withGlobalTraceId(UUID.randomUUID().toString())
//        .withResponseClass(QueryExamResponseType.class).init();
//
//    new Expectations(){
//      {
//        domainBeanFactory.guideApplyExamDBDataService()
//            .queryGuideApplyExamByAccount("guide0001971");
//        result = guideApplyExamModels;
//
//        domainBeanFactory.guideExamConfig().queryValidityExam();
//        result = validityGuideExamInfoList;
//
//        domainBeanFactory.guideExamConfig()
//            .getExamInfoByDeptId("DDXDZJ2024");
//        result = getZJ();
//
//        domainBeanFactory.guideExamScoreDBDataService()
//            .countByExamAccountIdApplySubject("guide0001971","DDXDZJ2024");
//        result = 1;
//      }
//    };
//    QueryExamResponseType response =  queryExamEngine.execute(requestType);
//    Assert.assertEquals(true, response.guideApplyExamDTO.examRecord);
//
//    new Expectations(){{
//      domainBeanFactory.guideExamScoreDBDataService()
//          .countByExamAccountIdApplySubject("guide0001971","DDXDZJ2024");
//      result = 0;
//    }};
//    QueryExamResponseType response1 = queryExamEngine.execute(requestType);
//    Assert.assertEquals(false, response1.guideApplyExamDTO.examRecord);
//
//
//    new Expectations(){{
//      domainBeanFactory.guideExamConfig()
//          .getExamInfoByDeptId("DDXDZJ2024");
//      result = getExpiredZJ();
//    }};
//    QueryExamResponseType response2 = queryExamEngine.execute(requestType);
//    Assert.assertEquals(true, response2.guideApplyExamDTO.moreExam);
//
//    new Expectations(){
//      {
//        domainBeanFactory.guideApplyExamDBDataService()
//            .queryGuideApplyExamByAccount("guide0001971");
//        result = new ArrayList<>();
//      }
//    };
//    QueryExamResponseType response3 = queryExamEngine.execute(requestType);
//    Assert.assertEquals(true, response3.guideApplyExamDTO.moreExam);
//  }

  private GuideApplyExamModel getGuideApplyExamModel() {
    GuideApplyExamModel model = new GuideApplyExamModel();
    model.setExamAccountId("guide0001971");
    model.setGuideId(1971L);
    model.setAccount("<EMAIL>");
    model.setSubjectName("中级笔试");
    model.setApplyResult(0);
    model.setExamIsPassed(0);
    model.setCallExamSuccess(0);
    model.setGuideName("何海龙");
    model.setApplySubject("DDXDZJ2024");
    return model;
  }

  private GuideApplyExamModel getGuideApplyExamModel1() {
    GuideApplyExamModel model = new GuideApplyExamModel();
    model.setExamAccountId("guide0002185");
    model.setGuideId(2185L);
    model.setAccount("***********");
    model.setSubjectName("初级笔试");
    model.setApplyResult(0);
    model.setExamIsPassed(0);
    model.setCallExamSuccess(0);
    model.setGuideName("H5");
    model.setApplySubject("DDXDCJ2024");
    return model;
  }

  private List<FromGuideExamInfo> getValidityGuideExamInfoList(){
    List<FromGuideExamInfo> list = new ArrayList<>();
    FromGuideExamInfo ddxdcj = new FromGuideExamInfo();
    ddxdcj.setStartTime("2023-10-01 00:00:00");
    ddxdcj.setEndTime("2024-12-31 23:59:59");
    ddxdcj.setExamId("00003");
    ddxdcj.setDeptId("DDXDCJ2024");
    ddxdcj.setParentDeptId("DDXDCJ");
    ddxdcj.setDeptName("初级笔试");
    ddxdcj.setStartTimeSecond(1696089600000L);

    FromGuideExamInfo ddxdzj = new FromGuideExamInfo();
    ddxdzj.setStartTime("2023-10-01 00:00:00");
    ddxdzj.setEndTime("2024-12-31 23:59:59");
    ddxdzj.setExamId("00002");
    ddxdzj.setDeptId("DDXDZJ2024");
    ddxdzj.setParentDeptId("DDXDZJ");
    ddxdzj.setDeptName("中级笔试");
    ddxdzj.setStartTimeSecond(1696089600000L);
    list.add(ddxdcj);
    list.add(ddxdzj);
    return list;
  }

  public FromGuideExamInfo getCJ(){
    FromGuideExamInfo ddxdcj = new FromGuideExamInfo();
    ddxdcj.setStartTime("2023-10-01 00:00:00");
    ddxdcj.setEndTime("2024-12-31 23:59:59");
    ddxdcj.setExamId("00003");
    ddxdcj.setDeptId("DDXDCJ2024");
    ddxdcj.setParentDeptId("DDXDCJ");
    ddxdcj.setDeptName("初级笔试");
    ddxdcj.setStartTimeSecond(1696089600000L);
    return ddxdcj;
  }

  public FromGuideExamInfo getZJ(){
    FromGuideExamInfo ddxdzj = new FromGuideExamInfo();
    ddxdzj.setStartTime("2023-10-01 00:00:00");
    ddxdzj.setEndTime("2024-12-31 23:59:59");
    ddxdzj.setExamId("00002");
    ddxdzj.setDeptId("DDXDZJ2024");
    ddxdzj.setParentDeptId("DDXDZJ");
    ddxdzj.setDeptName("中级笔试");
    ddxdzj.setStartTimeSecond(1696089600000L);
    return ddxdzj;
  }

  public FromGuideExamInfo getExpiredZJ(){
    FromGuideExamInfo ddxdzj = new FromGuideExamInfo();
    ddxdzj.setStartTime("2023-10-01 00:00:00");
    ddxdzj.setEndTime("2023-11-01 23:59:59");
    ddxdzj.setExamId("00002");
    ddxdzj.setDeptId("DDXDZJ2024");
    ddxdzj.setParentDeptId("DDXDZJ");
    ddxdzj.setDeptName("中级笔试");
    ddxdzj.setStartTimeSecond(1696089600000L);
    return ddxdzj;
  }
}
