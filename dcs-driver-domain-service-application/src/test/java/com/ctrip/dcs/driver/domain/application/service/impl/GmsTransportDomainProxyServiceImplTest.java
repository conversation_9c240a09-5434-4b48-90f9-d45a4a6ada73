package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.transport.GmsTransportDomainServiceProxy;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideBaseInfoByIdRequestType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideBaseInfoByIdResponseType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideBaseInfoDTO;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;



public class GmsTransportDomainProxyServiceImplTest extends TestBase {

    @Tested
    GmsTransportDomainProxyServiceImpl transportDomainProxyServiceImpl;

    @Injectable
    private GmsTransportDomainServiceProxy gmsTransportDomainServiceProxy;

    @Test
    public void queryGuideBaseInfoById() {
        GuideBaseInfoDTO guideBaseInfoDTO = super.bulidGuideInfo();
        QueryGuideBaseInfoByIdRequestType requestType = new QueryGuideBaseInfoByIdRequestType();
        requestType.guideId = guideBaseInfoDTO.guideId;

        QueryGuideBaseInfoByIdResponseType responseType = new QueryGuideBaseInfoByIdResponseType();
        responseType.baseInfo = guideBaseInfoDTO;
        new Expectations(){
            {
                gmsTransportDomainServiceProxy.queryGuideBaseInfoById(requestType);
                result = responseType;
            }
        };
        GuideBaseInfoDTO guideInfoResult = transportDomainProxyServiceImpl.queryGuideBaseInfoById(guideBaseInfoDTO.guideId);
        Assert.assertNotNull(guideInfoResult);
    }
}


