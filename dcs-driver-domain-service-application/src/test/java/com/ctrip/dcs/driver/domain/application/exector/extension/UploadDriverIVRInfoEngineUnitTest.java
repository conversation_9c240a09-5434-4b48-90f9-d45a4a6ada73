package com.ctrip.dcs.driver.domain.application.exector.extension;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.service.ExtensionService;
import com.ctrip.dcs.driver.domain.extension.UploadDriverIVRInfoRequestType;
import com.ctrip.dcs.driver.domain.extension.UploadDriverIVRInfoResponseType;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBankCardListResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.model.extension.IVRInfoModel;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class UploadDriverIVRInfoEngineUnitTest extends TestBase {

    @Tested
    UploadDriverIVRInfoEngine uploadDriverIVRInfoEngine;

    @Injectable
    private ExtensionService extensionService;

    @Test
    public void test() {
        UploadDriverIVRInfoRequestType requestType = new UploadDriverIVRInfoRequestType();
        requestType.driverId = 1000004L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverBankCardListResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;

                extensionService.sendAppUserInfo(this.withAny((IVRInfoModel) this.any));
                result = UUID.randomUUID().toString();
            }
        };

        UploadDriverIVRInfoResponseType responseType = uploadDriverIVRInfoEngine.execute(requestType);
        Assert.assertEquals("200", responseType.responseResult.returnCode);
    }
}
