package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBalanceRecordRequestType;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBalanceRecordResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.supply.driver.dto.BalanceRecordDTO;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.apache.commons.lang.math.RandomUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


public class QueryDriverBalanceRecordEngineUnitTest extends TestBase {

    @Tested
    QueryDriverBalanceRecordEngine queryDriverBalanceRecordEngine;

    @Test
    public void test() {
        QueryDriverBalanceRecordRequestType requestType = new QueryDriverBalanceRecordRequestType();
        requestType.driverId = 1000004L;
        requestType.beginDate = Strings.EMPTY;
        requestType.endDate = Strings.EMPTY;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverBalanceRecordResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;

        List<BalanceRecordDTO> balanceRecordList = new ArrayList<>();
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;

                domainBeanFactory.financeConvertService().queryDriverBalanceRecord(requestType.driverId,
                        requestType.beginDate, requestType.endDate);
                result = balanceRecordList;
            }
        };

        QueryDriverBalanceRecordResponseType responseType = queryDriverBalanceRecordEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        BalanceRecordDTO balanceRecord = new BalanceRecordDTO();
        balanceRecord.amount = BigDecimal.ONE;
        balanceRecord.applyStatus = 1;
        balanceRecord.applyStatusDesc = "applyStatusDesc";
        balanceRecord.bankString = "bankString";
        balanceRecord.recordId = RandomUtils.nextLong();
        balanceRecord.recordTime = "recordTime";
        balanceRecordList.add(balanceRecord);
        new Expectations(){
            {
                domainBeanFactory.financeConvertService().queryDriverBalanceRecord(requestType.driverId,
                        requestType.beginDate, requestType.endDate);
                result = balanceRecordList;
            }
        };

        responseType = queryDriverBalanceRecordEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
