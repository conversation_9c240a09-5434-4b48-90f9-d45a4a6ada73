package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.infrastructure.InfrastructureServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.MesTypeEnum;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.infrastructureservice.executor.contract.CheckMobilePhoneCodeRequestType;
import com.ctrip.igt.infrastructureservice.executor.contract.CheckMobilePhoneCodeResponseType;
import com.ctrip.igt.infrastructureservice.executor.contract.SendMesByPhoneRequestType;
import com.ctrip.igt.infrastructureservice.executor.contract.SendMesByPhoneResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class InfrastructureServiceProxyImplTest extends TestBase {

	@Tested
	InfrastructureServiceProxyImpl infrastructureService;

	@Injectable
	private InfrastructureServiceProxy infrastructureServiceProxy;

	@Test
	public void sendMessageByPhone() {
		String mobilePhone = "mobilePhone";
		SendMesByPhoneRequestType request = new SendMesByPhoneRequestType();
		request.setSf("DRIVER_DOMAIN_SERVICE");
		request.setChannel(MesTypeEnum.MODIFY_WITHDRAW_PASSWORD.getChannel());
		request.setCountryCode("86");
		request.setMessageCode(MesTypeEnum.MODIFY_WITHDRAW_PASSWORD.getMesCode());
		request.setMobilePhone(mobilePhone);

		SendMesByPhoneResponseType responseType = new SendMesByPhoneResponseType();
		responseType.responseResult = new ResponseResult();
		responseType.responseResult.returnCode = FinanceResultEnum.OK.getCode();
		new Expectations() {
			{
				infrastructureServiceProxy.sendMessageByPhone(request);
				result = responseType;
			}
		};

		String result = infrastructureService.sendMessageByPhone(mobilePhone, MesTypeEnum.MODIFY_WITHDRAW_PASSWORD);
		Assert.assertEquals(result, FinanceResultEnum.OK.getCode());
	}

	@Test
	public void checkPhoneCode() {
		String mobilePhone = "mobilePhone";
		String code = "code";
		CheckMobilePhoneCodeRequestType request = new CheckMobilePhoneCodeRequestType();
		request.setSf("DRIVER_DOMAIN_SERVICE");
		request.setChannel(MesTypeEnum.MODIFY_WITHDRAW_PASSWORD.getChannel());
		request.setCountryCode("86");
		request.setMessageCode(MesTypeEnum.MODIFY_WITHDRAW_PASSWORD.getMesCode());
		request.setMobilePhone(mobilePhone);
		request.setCode(code);

		CheckMobilePhoneCodeResponseType responseType = new CheckMobilePhoneCodeResponseType();
		responseType.responseResult = new ResponseResult();
		responseType.responseResult.returnCode = FinanceResultEnum.OK.getCode();
		new Expectations() {
			{
				infrastructureServiceProxy.checkPhoneCode(request);
				result = responseType;
			}
		};
		boolean result = infrastructureService.checkPhoneCode(mobilePhone, code, MesTypeEnum.MODIFY_WITHDRAW_PASSWORD);
		Assert.assertTrue(result);
	}
}
