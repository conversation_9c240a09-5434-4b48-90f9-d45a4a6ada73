package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivLoginInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivLoginInfoPO;
import com.ctrip.dcs.driver.domain.setting.DealDriverLoginInfoRequestType;
import com.ctrip.dcs.driver.domain.setting.DealDriverLoginInfoResponseType;
import com.ctrip.dcs.driver.domain.setting.DriverLoginInfoDTO;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPointPopInfoResponseType;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.UUID;


public class DealDriverLoginInfoEngineUnitTest extends TestBase {

    @Tested
    DealDriverLoginInfoEngine dealDriverLoginInfoEngine;

    @Injectable
    DrivLoginInfoDao drivLoginInfoDao;

    @Test
    public void test() {
        DealDriverLoginInfoRequestType requestType = new DealDriverLoginInfoRequestType();
        requestType.driverLoginInfo = new DriverLoginInfoDTO();
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverPointPopInfoResponseType.class).init();
        DrivLoginInfoPO drivLoginInfoPO = new DrivLoginInfoPO();
        drivLoginInfoPO.setPhoneSign("F253C06143B7B6ACEBA4B0997EBCA1AB0A0A3BD59665EB871E9E850C32FF24B21CD3484DAE3E89BF4602EBF141607941");
        new Expectations(){
            {
                drivLoginInfoDao.findOne(this.withAny(anyLong));
                result = drivLoginInfoPO;
            }
        };
        DealDriverLoginInfoResponseType responseType = dealDriverLoginInfoEngine.execute(requestType);
        Assert.assertNotNull(responseType.driverLoginInfo.phoneSign);

        requestType.driverLoginInfo.type = 1;
        new Expectations(){
            {
                drivLoginInfoDao.findOne(this.withAny(anyLong));
                result = null;
            }
        };
        responseType = dealDriverLoginInfoEngine.execute(requestType);
        Assert.assertNull(responseType.driverLoginInfo);

        requestType.driverLoginInfo.phoneSign = "F253C06143B7B6ACEBA4B0997EBCA1AB0A0A3BD59665EB871E9E850C32FF24B21CD3484DAE3E89BF4602EBF141607941";
        requestType.driverLoginInfo.terminalInfo = "F253C06143B7B6ACEBA4B0997EBCA1AB0A0A3BD59665EB871E9E850C32FF24B21CD3484DAE3E89BF4602EBF141607941";
        requestType.driverLoginInfo.osType = 1;
        new Expectations(){
            {
                drivLoginInfoDao.findOne(this.withAny(anyLong));
                result = drivLoginInfoPO;
            }
        };
        responseType = dealDriverLoginInfoEngine.execute(requestType);
        Assert.assertNull(responseType.driverLoginInfo);
    }

    @Test
    public void testPO(){
        DrivLoginInfoPO drivLoginInfoPO = new DrivLoginInfoPO();

        drivLoginInfoPO.setAddress(Strings.EMPTY);
        drivLoginInfoPO.setCreateTime(Timestamp.valueOf("2023-07-19 10:55:34.757"));
        drivLoginInfoPO.setDatachangeLasttime(Timestamp.valueOf("2023-07-19 10:55:34.757"));
        drivLoginInfoPO.setDrivId(1L);
        drivLoginInfoPO.setId(1L);
        drivLoginInfoPO.setIgexin(Strings.EMPTY);
        drivLoginInfoPO.setLatitude(BigDecimal.ZERO);
        drivLoginInfoPO.setLongitude(BigDecimal.ZERO);
        drivLoginInfoPO.setOsType(1);
        drivLoginInfoPO.setPhoneSign(Strings.EMPTY);
        drivLoginInfoPO.setServiceLevel(BigDecimal.ZERO);
        drivLoginInfoPO.setTerminalInfo(Strings.EMPTY);
        drivLoginInfoPO.setUpdateTime(Timestamp.valueOf("2023-07-19 10:55:34.757"));
        drivLoginInfoPO.setWorkStatus(1);
        drivLoginInfoPO.setXiaomi(Strings.EMPTY);
        drivLoginInfoPO.setYacca(Strings.EMPTY);

        BigDecimal bigDecimalValue = drivLoginInfoPO.getLatitude();
        bigDecimalValue = drivLoginInfoPO.getLongitude();
        bigDecimalValue = drivLoginInfoPO.getServiceLevel();
        int intValue =  drivLoginInfoPO.getOsType();
        intValue =  drivLoginInfoPO.getWorkStatus();
        Long longValue = drivLoginInfoPO.getDrivId();
        longValue = drivLoginInfoPO.getId();
        String strValue = drivLoginInfoPO.getAddress();
        strValue = drivLoginInfoPO.getIgexin();
        strValue = drivLoginInfoPO.getPhoneSign();
        strValue = drivLoginInfoPO.getTerminalInfo();
        strValue = drivLoginInfoPO.getXiaomi();
        strValue = drivLoginInfoPO.getYacca();
        Timestamp timestamp = drivLoginInfoPO.getCreateTime();
        timestamp = drivLoginInfoPO.getDatachangeLasttime();
        timestamp = drivLoginInfoPO.getUpdateTime();
        Assert.assertNotNull(drivLoginInfoPO);
    }
}
