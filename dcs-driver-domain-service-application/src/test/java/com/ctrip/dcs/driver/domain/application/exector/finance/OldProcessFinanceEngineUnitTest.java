package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.finance.OldProcessFinanceRequestType;
import com.ctrip.dcs.driver.domain.finance.OldProcessFinanceResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.supply.driver.dto.Balance;
import com.ctrip.dcs.tms.transport.api.regulation.OldDriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class OldProcessFinanceEngineUnitTest extends TestBase {

    @Tested
    OldProcessFinanceEngine oldProcessFinanceEngine;

    @Test
    public void testQueryBalance() {
        OldProcessFinanceRequestType requestType = new OldProcessFinanceRequestType();
        requestType.driverName = "test";
        requestType.identitycode = "123456789012345678";
        requestType.driverPhone = "1234567890";
        requestType.type = 1;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(OldProcessFinanceResponseType.class).init();

        new Expectations() {
            {
                domainBeanFactory.tmsTransportServiceProxy().queryOldDriver(requestType.driverPhone);
                result = null;
            }
        };

        OldProcessFinanceResponseType responseType = oldProcessFinanceEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.DRIVER_NOT_EXIST.getCode(), responseType.responseResult.returnCode);

        OldDriverInfo oldDriverInfo = new OldDriverInfo();
        oldDriverInfo.internalScope = 0;
        oldDriverInfo.drvId = 1000004L;
        new Expectations() {
            {
                domainBeanFactory.tmsTransportServiceProxy().queryOldDriver(requestType.driverPhone);
                result = oldDriverInfo;
            }
        };

        responseType = oldProcessFinanceEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.NAME_CHECK_FAIL.getCode(), responseType.responseResult.returnCode);

        oldDriverInfo.drvName = requestType.driverName;
        responseType = oldProcessFinanceEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.IDENTITYCODE_FAIL.getCode(), responseType.responseResult.returnCode);

        oldDriverInfo.drvIdcard = requestType.identitycode;
        Balance balance = new Balance();
        new Expectations() {
            {
                domainBeanFactory.financeConvertService().queryDriverWithdrawBalance(1000004L, true);
                result = Pair.of(FinanceResultEnum.OK.getCode(), balance);
            }
        };

        responseType = oldProcessFinanceEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }

    @Test
    public void testWithdraw() {
        OldProcessFinanceRequestType requestType = new OldProcessFinanceRequestType();
        requestType.driverName = "test";
        requestType.identitycode = "123456789012345678";
        requestType.driverPhone = "1234567890";
        requestType.type = 2;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(OldProcessFinanceResponseType.class).init();
        OldDriverInfo oldDriverInfo = new OldDriverInfo();
        oldDriverInfo.internalScope = 0;
        oldDriverInfo.drvId = 1000004L;
        oldDriverInfo.drvName = requestType.driverName;
        oldDriverInfo.drvIdcard = requestType.identitycode;

        Balance balance = new Balance();
        new Expectations() {
            {
                domainBeanFactory.tmsTransportServiceProxy().queryOldDriver(requestType.driverPhone);
                result = oldDriverInfo;

                domainBeanFactory.financeConvertService().queryDriverWithdrawBalance(1000004L, true);
                result = Pair.of(FinanceResultEnum.OK.getCode(), balance);

                domainBeanFactory.financeConvertService().driverResetPsw(this.withAny((WithdrawModel) this.any));
                result = FinanceResultEnum.OK.getCode();

                domainBeanFactory.financeConvertService().driverWithdraw(this.withAny((WithdrawModel) this.any));
                result = FinanceResultEnum.OK.getCode();
            }
        };

        OldProcessFinanceResponseType responseType = oldProcessFinanceEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
