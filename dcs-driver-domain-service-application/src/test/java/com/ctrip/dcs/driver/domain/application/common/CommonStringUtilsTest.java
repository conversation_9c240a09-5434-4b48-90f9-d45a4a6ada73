package com.ctrip.dcs.driver.domain.application.common;

import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.spockframework.util.Assert;


public class CommonStringUtilsTest {

    @Tested
    CommonStringUtils commonStringUtils;

    @Test
    public void test() {
        String[] strValue = commonStringUtils.splite("test,test,testring", 4);
        Assert.notNull(strValue);
    }

}
