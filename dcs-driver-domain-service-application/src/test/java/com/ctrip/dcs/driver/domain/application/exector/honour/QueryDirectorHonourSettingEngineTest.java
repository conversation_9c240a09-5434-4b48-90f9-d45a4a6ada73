package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.honour.QueryDirectorHonourSettingRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryDirectorHonourSettingResponseType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideBaseInfoDTO;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Arrays;
import java.util.UUID;


public class QueryDirectorHonourSettingEngineTest extends TestBase {

    @Tested
    QueryDirectorHonourSettingEngine queryDirectorHonourSettingEngine;

    @Test
    public void execute() {
        DriverInfo driverInfo = bulidDriverInfo();
        QueryDirectorHonourSettingRequestType requestDriver = new QueryDirectorHonourSettingRequestType();
        requestDriver.setDirectorId(driverInfo.driverId);
        requestDriver.setDirectorType(1);
        ServiceExecuteContext.newContext()
                .withRequest(requestDriver)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDirectorHonourSettingResponseType.class).init();

        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverInfo.driverId);
                result = driverInfo;

                domainBeanFactory.honourFormConfig().isShowMedal();
                result = true;

                domainBeanFactory.honourFormConfig().getRankOpenCity();
                result = Arrays.asList(1L,2L,3L);

                domainBeanFactory.honourRedisLogic().isNoticeRankUpdate(driverInfo.driverId);
                result = true;
            }
        };

        QueryDirectorHonourSettingResponseType responseTypeDriver = queryDirectorHonourSettingEngine.execute(requestDriver);
        Assert.assertTrue(responseTypeDriver.isShowMedal);
        Assert.assertTrue(responseTypeDriver.isShowRank);
        Assert.assertTrue(responseTypeDriver.isNoticeRank);
        Assert.assertEquals(responseTypeDriver.medalCount.intValue(), 0);

        GuideBaseInfoDTO guideBaseInfoDTO = bulidGuideInfo();
        new Expectations(){
            {
                domainBeanFactory.gmsTransportDomainServiceProxy().queryGuideBaseInfoById(guideBaseInfoDTO.guideId);
                result = guideBaseInfoDTO;
            }
        };

        QueryDirectorHonourSettingRequestType requestTypeGuide = new QueryDirectorHonourSettingRequestType();
        requestTypeGuide.setDirectorId(123L);
        requestTypeGuide.setDirectorType(2);
        QueryDirectorHonourSettingResponseType responseTypeGuide = queryDirectorHonourSettingEngine.execute(requestTypeGuide);
        Assert.assertFalse(responseTypeGuide.isShowMedal);
        Assert.assertFalse(responseTypeGuide.isShowRank);
        Assert.assertFalse(responseTypeGuide.isNoticeRank);
        Assert.assertEquals(responseTypeDriver.medalCount.intValue(), 0);
    }

}
