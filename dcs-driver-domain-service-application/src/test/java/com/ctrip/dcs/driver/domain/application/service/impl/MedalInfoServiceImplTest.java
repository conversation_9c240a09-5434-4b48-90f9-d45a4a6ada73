package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.tourai.TourAIOneServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import com.ctrip.tour.ai.one.service.GetDataRequestType;
import com.ctrip.tour.ai.one.service.GetDataResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;


public class MedalInfoServiceImplTest extends TestBase {

    @Tested
    MedalInfoServiceImpl medalInfoServiceImpl;

    @Injectable
    private DriverMessageServiceProxy driverMessageServiceProxy;

    @Injectable
    private TourAIOneServiceProxy tourAIOneServiceProxy;

    @Test
    public void queryBasicMedalInfo() {
        List<DimPrdTrhDriverMedalInfoPO> medalInfoList = new ArrayList<>();
        DimPrdTrhDriverMedalInfoPO medalInfoPO11 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO11.setMedalType("order_day_medal");
        medalInfoPO11.setMedalGrade(1L);
        medalInfoPO11.setMedalGradeMin(1L);
        medalInfoPO11.setMedalGradeMax(49L);
        medalInfoList.add(medalInfoPO11);
        DimPrdTrhDriverMedalInfoPO medalInfoPO12 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO12.setMedalType("order_day_medal");
        medalInfoPO12.setMedalGrade(50L);
        medalInfoPO12.setMedalGradeMin(50L);
        medalInfoPO12.setMedalGradeMax(99L);
        medalInfoList.add(medalInfoPO12);

        DimPrdTrhDriverMedalInfoPO medalInfoPO21 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO21.setMedalType("order_cnt_medal");
        medalInfoPO21.setMedalGrade(1L);
        medalInfoPO21.setMedalGradeMin(1L);
        medalInfoPO21.setMedalGradeMax(49L);
        medalInfoList.add(medalInfoPO21);

        DimPrdTrhDriverMedalInfoPO medalInfoPO31 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO31.setMedalType("good_comment_medal");
        medalInfoPO31.setMedalGrade(1L);
        medalInfoPO31.setMedalGradeMin(1L);
        medalInfoPO31.setMedalGradeMax(49L);
        medalInfoList.add(medalInfoPO31);
        DimPrdTrhDriverMedalInfoPO medalInfoPO32 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoList.add(medalInfoPO32);

        DimPrdTrhDriverMedalInfoPO medalInfoPO41 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO41.setMedalType("tdhfwj_medal");
        medalInfoPO41.setMedalGrade(50L);
        medalInfoPO41.setMedalGradeMin(50L);
        medalInfoPO41.setMedalGradeMax(50L);
        medalInfoList.add(medalInfoPO41);

        DimPrdTrhDriverMedalInfoPO medalInfoPO42 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO42.setMedalType("cnzj_medal");
        medalInfoPO42.setMedalGrade(50L);
        medalInfoPO42.setMedalGradeMin(50L);
        medalInfoPO42.setMedalGradeMax(50L);
        medalInfoList.add(medalInfoPO42);

        DimPrdTrhDriverMedalInfoPO medalInfoPO43 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO43.setMedalType("hdtrlz_medal");
        medalInfoPO43.setMedalGrade(50L);
        medalInfoPO43.setMedalGradeMin(50L);
        medalInfoPO43.setMedalGradeMax(50L);
        medalInfoList.add(medalInfoPO43);

        DimPrdTrhDriverMedalInfoPO medalInfoPO44 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO44.setMedalType("zdbnxl_medal");
        medalInfoPO44.setMedalGrade(50L);
        medalInfoPO44.setMedalGradeMin(50L);
        medalInfoPO44.setMedalGradeMax(50L);
        medalInfoList.add(medalInfoPO44);

        DimPrdTrhDriverMedalInfoPO medalInfoPO0 = new DimPrdTrhDriverMedalInfoPO();
        medalInfoPO0.setMedalType("default");
        medalInfoPO0.setMedalGrade(1L);
        medalInfoPO0.setMedalGradeMin(1L);
        medalInfoPO0.setMedalGradeMax(49L);
        medalInfoList.add(medalInfoPO0);
        new Expectations() {
            {
                honourDBDataService.queryBasicMedalInfo();
                result = medalInfoList;
            }
        };
        List<MedalBasicInfoModel> result = medalInfoServiceImpl.queryBasicMedalInfo();
        Assert.assertEquals(Math.subtractExact(medalInfoList.size(), result.size()), 2);
    }

    @Test
    public void updateMedalInfo() {
        medalInfoServiceImpl.updateMedalInfo();
    }

    @Test
    public void updateMedalInfoWithDriverIdList() {
        medalInfoServiceImpl.updateMedalInfo();
    }
    //
    // @Test
    // public void getGoldMedalDriverInfo() {
    //
    //     new Expectations() {
    //         {
    //             daasGateway.queryGoldMedalDriverInfo(this.withAny((String) this.any));
    //             result = null;
    //         }
    //     };
    //     GoldMedalDriverModel goldMedalDriverModel = medalInfoServiceImpl.getGoldMedalDriverInfo(1L);
    //     Assert.assertNotNull(goldMedalDriverModel);
    //
    //     GetDataResponseType responseType = new GetDataResponseType();
    //     responseType.setBacks("[{\"driver_id_last\":\"1000004\",\"defect_cnt\":0,\"complete_cnt\":3,\"use_month_bj\":\"2023-09\"}]");
    //     new Expectations() {
    //         {
    //             tourAIOneServiceProxy.getData(this.withAny((GetDataRequestType) this.any));
    //             result = responseType;
    //         }
    //     };
    //     goldMedalDriverModel = medalInfoServiceImpl.getGoldMedalDriverInfo(1000004L);
    //     Assert.assertNotNull(goldMedalDriverModel);
    // }

}


