package com.ctrip.dcs.driver.account.application.service;

import com.ctrip.dcs.driver.convert.CardInfoManagementConvert;
import com.ctrip.dcs.driver.domain.infrastructure.utils.RsaUtil;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.dcs.driver.gateway.impl.CardInfoManagementRepositoryImpl;
import com.ctrip.dcs.driver.gateway.impl.PaymentTokenUtils;
import com.ctrip.dcs.driver.account.infrastructure.adapter.ThirdpartyCardInfoManagementServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.payment.soa.thirdpartycardinfo.service.JwsRequestType;
import com.ctrip.payment.soa.thirdpartycardinfo.service.JwsResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;


public class CardInfoManagementRepositoryTest {

    @Tested
    CardInfoManagementRepositoryImpl cardInfoManagementService;

    @Injectable
    DriverWalletInfoConfig driverWalletInfoConfig;

    @Injectable
    PaymentTokenUtils paymentTokenUtils;

    @Injectable
    ThirdpartyCardInfoManagementServiceProxy thirdpartyCardInfoManagementServiceProxy;
    @Injectable
    CardInfoManagementConvert cardInfoManagementConvert;
    @Injectable
    RsaUtil rsaUtil;

    private static final String uid = "uid";
    private static final String encryptCardNo = "encryptCardNo";

    private final static String PUB_KEY =
            "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCdLrr1avVHbvsSfE2st2aBs1aQ0ZpHv9J/J3/iCCnHULBHgNOJC/a4bbwXr0yz3pmURDDJgaGtIL5qI27zPl43YZFPETRdu/lni9Re7NK81qgfP4ah+10afUmrhR3636Q9Lk77NxECfqvANVDiwuvw7/MfGKek97g8eN9grCAhWQIDAQAB";
    private final static String PRI_KEY =
            "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJ0uuvVq9Udu+xJ8Tay3ZoGzVpDRmke/0n8nf+IIKcdQsEeA04kL9rhtvBevTLPemZREMMmBoa0gvmojbvM+XjdhkU8RNF27+WeL1F7s0rzWqB8/hqH7XRp9SauFHfrfpD0uTvs3EQJ+q8A1UOLC6/Dv8x8Yp6T3uDx432CsICFZAgMBAAECgYA3Bqf+yJ3rjweoVgnfQClLmJvigO5Q1e98Ajj7yT3PAAlmTsw/Owf5Urk5VW41veToCeNGd/JAqKjggV0THK9giXqiusEzIXsWn4/OstP38cx4c4nzBo5RjUw4hmQCP6UWle2gUn2c5I4n+XVVFXoJMX3yyHWlLYr/8EYQPaiGzQJBAMsQbhQwgDM+z5l0+Pxsylpl87TC1gr13UEAlNX7yoF5Y6w7zt+/yQeyERNGodEQOLdeY/CbtnnGFUvgFoQkmJ8CQQDGKF7emVRIMv/SQT8DNuhBijzFINlKJ7QvWxatYw4PgqCzvLexcR8EqQFWwjMqqasJlN+BQwuigkyKiRM9OusHAkEArIdfH6Q9qEybkeSCj+XW34tzzocj2NggjlPyIQT3f54lg3alRBHsua8fNuNyJPDFX3bBLvL9zOTxvy+7sbZuPQJAemFWbg2uIsT0f8rx+q8/c7LHY0utRwIMYy7Ta5QAjA216CuGG11stbkW7ZkUdqXAzzMVckSQ+/WTBgAwVKF9owJAW9T2ZQzYB1XhE6Ix6FTlY87w/5RlucNO3OqZ9RUr7IaO69+rWMP8Y+joufnwHDKeXo3JLwtx5u7QjrcIOlDIVg==";

    @Test
    public void createCardInfo() throws IOException {
        new Expectations(){
            {
                paymentTokenUtils.driverPrivateEncryptKey();
                result = PRI_KEY;

                paymentTokenUtils.paymentPublicEncryptKey();
                result = PUB_KEY;

                thirdpartyCardInfoManagementServiceProxy.createPayCardInfo(this.withAny((JwsRequestType) this.any));
                result = null;
            }
        };
        String value = cardInfoManagementService.createCardInfo(uid, encryptCardNo);
        Assert.assertEquals(StringUtils.EMPTY, value);

        JwsResponseType jwsResponseType = new JwsResponseType();
        new Expectations(){
            {
                thirdpartyCardInfoManagementServiceProxy.createPayCardInfo(this.withAny((JwsRequestType) this.any));
                result = jwsResponseType;
            }
        };
        value = cardInfoManagementService.createCardInfo(uid, encryptCardNo);
        Assert.assertEquals(StringUtils.EMPTY, value);

        jwsResponseType.header = "eyJhbGciOiAiUlMyNTYiLCAgImtleSI6ICJ0ZXN0MDAwMSJ9";
        jwsResponseType.setPayload(
                "eyJoZWFkZXIiOiB7ImJ1c1R5cGUiOiAiMTEiLCJndWlkIjogIjIzYzFlMGMzLTU4MDctNDkxZC0zNDM0LWI1YjUyNTkxNjA5MCIsImlwIjogIjEwLjMyLjE2NC4yNiIsImxvY2FsIjogImVuLXVzIiwicmVxdWVzdElkIjogIjIwMTgxMjI0MzQ1NDUzMTExIiwicm1zVG9rZW4iOiAiIiwic2l0ZSI6ICJFTiIsInNvdXJjZSI6ICJvbmxpbmVhcGkiLCJ0b2tlbiI6IG51bGwsInVpZCI6ICJEMjIzNzI1OTY5OCIsInZlcnNpb24iOiAiMS4wIn0sIm1lcmNoYW50IjogeyJmcm9tVXJsIjogbnVsbCwibWVyY2hhbnRJZCI6ICIyMDAyNzQiLCJub3RpZnlVcmwiOiAiaHR0cDovL3dzLmZsaWdodC5pYnUuZmF0OC5xYS5udC5jdHJpcGNvcnAuY29tL2ludGxmbGlnaHRhcGkvanNvbi9HYUZsaWdodFBheW1lbnROb3RpZnlGb3JPbmxpbmVJbnRsIiwicmVjYWxsVXJsIjogIkZsaWdodC5PcmRlci5QYXltZW50RXhlY3V0aW9uV1MuTm90aWZ5UGF5bWVudFJlc3VsdCIsInJldHVyblVybCI6ICIvL3d3dy5mYXQ1NS5xYS5udC50cmlwY29ycC5jb20vZmxpZ2h0cy9jb21wbGV0ZSJ9LCJvcmRlciI6IHsiZXhjaGFuZ2VSYXRlIjogIjEiLCJhdXRvQXBwbHlCaWxsIjogIjEiLCJvcmRlckFtb3VudCI6IDk4LCJvcmRlckN1cnJlbmN5IjogIkNOWSIsIm9yZGVySWQiOiAiMTk3ODEwMCIsIm9yZGVyU3ViVGl0bGUiOiAiIiwib3JkZXJUaXRsZSI6ICLmtYvor5Xkuqflk4EiLCJwYXlEZWFkTGluZSI6ICIyMDE5MDIxODIyNTQ1NSJ9LCJwYXltZW50VHlwZSI6IHsib3B0VHlwZSI6IDAsInBheVN1YlRpdGxlIjogbnVsbCwicGF5U3ViVHlwZSI6IDEsInBheVRpdGxlIjogbnVsbCwicGF5VHlwZSI6IDEsInBheWVlIjogMX19");
        jwsResponseType.setSignature(
                "kMOK-dGFa-bGJAbIVWzRVGxWurubOH9tbf0v-6nEnC00NU5oFksZbFX-zw1BsnoLwk4UYbDLvmthq368DL9V38qhuP1tnkannGXrtPdFmB6ruAIi2wyaom-Oq93Ep7PWqb4954pfpfEFDrgm7NJQKJfvcAIlCGDqTH4kTrcWrTI");
        new Expectations(){
            {
                thirdpartyCardInfoManagementServiceProxy.createPayCardInfo(this.withAny((JwsRequestType) this.any));
                result = jwsResponseType;
            }
        };
        value = cardInfoManagementService.createCardInfo(uid, encryptCardNo);
        Assert.assertEquals(StringUtils.EMPTY, value);
    }

    @Test
    public void getSinglePayCardInfo() throws IOException {
        new Expectations(){
            {
                paymentTokenUtils.driverPrivateEncryptKey();
                result = PRI_KEY;

                paymentTokenUtils.paymentPublicEncryptKey();
                result = PUB_KEY;

                thirdpartyCardInfoManagementServiceProxy.getPayCardInfos(this.withAny((JwsRequestType) this.any));
                result = null;
            }
        };

        String value = cardInfoManagementService.getSinglePayCardInfo(encryptCardNo);
        Assert.assertEquals(StringUtils.EMPTY, value);

        JwsResponseType jwsResponseType = new JwsResponseType();
        new Expectations(){
            {
                thirdpartyCardInfoManagementServiceProxy.getPayCardInfos(this.withAny((JwsRequestType) this.any));
                result = jwsResponseType;
            }
        };
        value = cardInfoManagementService.getSinglePayCardInfo(encryptCardNo);
        Assert.assertEquals(StringUtils.EMPTY, value);

        jwsResponseType.header = "eyJhbGciOiAiUlMyNTYiLCAgImtleSI6ICJ0ZXN0MDAwMSJ9";
        jwsResponseType.setPayload(
                "eyJoZWFkZXIiOiB7ImJ1c1R5cGUiOiAiMTEiLCJndWlkIjogIjIzYzFlMGMzLTU4MDctNDkxZC0zNDM0LWI1YjUyNTkxNjA5MCIsImlwIjogIjEwLjMyLjE2NC4yNiIsImxvY2FsIjogImVuLXVzIiwicmVxdWVzdElkIjogIjIwMTgxMjI0MzQ1NDUzMTExIiwicm1zVG9rZW4iOiAiIiwic2l0ZSI6ICJFTiIsInNvdXJjZSI6ICJvbmxpbmVhcGkiLCJ0b2tlbiI6IG51bGwsInVpZCI6ICJEMjIzNzI1OTY5OCIsInZlcnNpb24iOiAiMS4wIn0sIm1lcmNoYW50IjogeyJmcm9tVXJsIjogbnVsbCwibWVyY2hhbnRJZCI6ICIyMDAyNzQiLCJub3RpZnlVcmwiOiAiaHR0cDovL3dzLmZsaWdodC5pYnUuZmF0OC5xYS5udC5jdHJpcGNvcnAuY29tL2ludGxmbGlnaHRhcGkvanNvbi9HYUZsaWdodFBheW1lbnROb3RpZnlGb3JPbmxpbmVJbnRsIiwicmVjYWxsVXJsIjogIkZsaWdodC5PcmRlci5QYXltZW50RXhlY3V0aW9uV1MuTm90aWZ5UGF5bWVudFJlc3VsdCIsInJldHVyblVybCI6ICIvL3d3dy5mYXQ1NS5xYS5udC50cmlwY29ycC5jb20vZmxpZ2h0cy9jb21wbGV0ZSJ9LCJvcmRlciI6IHsiZXhjaGFuZ2VSYXRlIjogIjEiLCJhdXRvQXBwbHlCaWxsIjogIjEiLCJvcmRlckFtb3VudCI6IDk4LCJvcmRlckN1cnJlbmN5IjogIkNOWSIsIm9yZGVySWQiOiAiMTk3ODEwMCIsIm9yZGVyU3ViVGl0bGUiOiAiIiwib3JkZXJUaXRsZSI6ICLmtYvor5Xkuqflk4EiLCJwYXlEZWFkTGluZSI6ICIyMDE5MDIxODIyNTQ1NSJ9LCJwYXltZW50VHlwZSI6IHsib3B0VHlwZSI6IDAsInBheVN1YlRpdGxlIjogbnVsbCwicGF5U3ViVHlwZSI6IDEsInBheVRpdGxlIjogbnVsbCwicGF5VHlwZSI6IDEsInBheWVlIjogMX19");
        jwsResponseType.setSignature(
                "kMOK-dGFa-bGJAbIVWzRVGxWurubOH9tbf0v-6nEnC00NU5oFksZbFX-zw1BsnoLwk4UYbDLvmthq368DL9V38qhuP1tnkannGXrtPdFmB6ruAIi2wyaom-Oq93Ep7PWqb4954pfpfEFDrgm7NJQKJfvcAIlCGDqTH4kTrcWrTI");
        new Expectations(){
            {
                thirdpartyCardInfoManagementServiceProxy.getPayCardInfos(this.withAny((JwsRequestType) this.any));
                result = jwsResponseType;
            }
        };
        value = cardInfoManagementService.getSinglePayCardInfo(encryptCardNo);
        Assert.assertEquals(StringUtils.EMPTY, value);
    }
}
