package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.exam.QueryValidityExamRequestType;
import com.ctrip.dcs.driver.domain.exam.QueryValidityExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;


public class QueryValidityExamEngineTest extends TestBase {
  @Tested
  QueryValidityExamEngine queryValidityExamEngine;

  @Injectable
  CommonMultipleLanguages commonMultipleLanguages;

  @Test
  public void test(){
    List<FromGuideExamInfo> validityGuideExamInfoList = getValidityGuideExamInfoList();
    new Expectations(){{
      domainBeanFactory.guideExamConfig().queryValidityExam();
      result = validityGuideExamInfoList;

      commonMultipleLanguages.getContent("CJSHARKEY");
      result = "初级笔试";

      commonMultipleLanguages.getContent("ZJSHARKEY");
      result = "中级笔试";
    }};
    QueryValidityExamResponseType execute =
        queryValidityExamEngine.execute(new QueryValidityExamRequestType());
    Assert.assertEquals(execute.responseResult.returnCode,"200");
  }

  private List<FromGuideExamInfo> getValidityGuideExamInfoList(){
    List<FromGuideExamInfo> list = new ArrayList<>();
    FromGuideExamInfo ddxdcj = new FromGuideExamInfo();
    ddxdcj.setStartTime("2023-10-01 00:00:00");
    ddxdcj.setEndTime("2024-12-31 23:59:59");
    ddxdcj.setExamId("00003");
    ddxdcj.setDeptId("DDXDCJ2024");
    ddxdcj.setParentDeptId("DDXDCJ");
    ddxdcj.setDeptName("初级笔试");
    ddxdcj.setDeptSharkey("CJSHARKEY");
    ddxdcj.setStartTimeSecond(1696089600000L);

    FromGuideExamInfo ddxdzj = new FromGuideExamInfo();
    ddxdzj.setStartTime("2023-10-01 00:00:00");
    ddxdzj.setEndTime("2024-12-31 23:59:59");
    ddxdzj.setExamId("00002");
    ddxdzj.setDeptId("DDXDZJ2024");
    ddxdzj.setParentDeptId("DDXDZJ");
    ddxdzj.setDeptName("中级笔试");
    ddxdzj.setDeptSharkey("ZJSHARKEY");
    ddxdzj.setStartTimeSecond(1696089600000L);
    list.add(ddxdcj);
    list.add(ddxdzj);
    return list;
  }

}
