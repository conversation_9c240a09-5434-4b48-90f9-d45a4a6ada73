package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsVerifyEventDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.TmsVerifyEventPO;
import com.ctrip.dcs.driver.domain.verify.DriverIsNeedVerifyRequestType;
import com.ctrip.dcs.driver.domain.verify.DriverIsNeedVerifyResponseType;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


public class QueryDriverIsNeedVerifyEngineUnitTest extends TestBase {

    @Tested
    QueryDriverIsNeedVerifyEngine queryDriverIsNeedVerifyEngine;

    @Injectable
    private TmsVerifyEventDao tmsVerifyEventDao;

    @Test
    public void test() {
        DriverIsNeedVerifyRequestType requestType = new DriverIsNeedVerifyRequestType();
        requestType.driverId = 1L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(DriverIsNeedVerifyResponseType.class).init();

        DriverIsNeedVerifyResponseType responseType = queryDriverIsNeedVerifyEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
        Assert.assertEquals(1, responseType.verifyFlag.intValue());

        List<TmsVerifyEventPO> verifyEvents = new ArrayList<>();
        TmsVerifyEventPO tmsVerifyEventPO = new TmsVerifyEventPO();
        tmsVerifyEventPO.setVerifyStartTime(Timestamp.valueOf(LocalDateTime.now().minusHours(1)));
        tmsVerifyEventPO.setId(0L);
        tmsVerifyEventPO.setVerifySourceId(1L);
        tmsVerifyEventPO.setVerifySourceType(2);
        tmsVerifyEventPO.setDrvLocLong(0d);
        tmsVerifyEventPO.setDrvLocLat(0d);
        tmsVerifyEventPO.setVerifyFlag(2);
        tmsVerifyEventPO.setVerifyTime(Timestamp.valueOf(LocalDateTime.now()));
        tmsVerifyEventPO.setVerifyResultCode(Strings.EMPTY);
        tmsVerifyEventPO.setFailReason(Strings.EMPTY);
        tmsVerifyEventPO.setDrvImei(Strings.EMPTY);
        tmsVerifyEventPO.setVerifyReasonStatus(1);
        tmsVerifyEventPO.setVerifyEndTime(Timestamp.valueOf(LocalDateTime.now()));
        tmsVerifyEventPO.setVerifyType(1);
        tmsVerifyEventPO.setVerifyStatus(1);
        tmsVerifyEventPO.setNoticeTimes(0);
        tmsVerifyEventPO.setDrvLocCsys(Strings.EMPTY);
        tmsVerifyEventPO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.now()));
        tmsVerifyEventPO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
        tmsVerifyEventPO.setCreateUser(Strings.EMPTY);
        tmsVerifyEventPO.setModifyUser(Strings.EMPTY);

        tmsVerifyEventPO.getId();
        tmsVerifyEventPO.getVerifySourceId();
        tmsVerifyEventPO.getVerifySourceType();
        tmsVerifyEventPO.getDrvLocLong();
        tmsVerifyEventPO.getDrvLocLat();
        tmsVerifyEventPO.getVerifyFlag();
        tmsVerifyEventPO.getVerifyTime();
        tmsVerifyEventPO.getVerifyResultCode();
        tmsVerifyEventPO.getFailReason();
        tmsVerifyEventPO.getDrvImei();
        tmsVerifyEventPO.getVerifyReasonStatus();
        tmsVerifyEventPO.getVerifyStartTime();
        tmsVerifyEventPO.getVerifyEndTime();
        tmsVerifyEventPO.getVerifyType();
        tmsVerifyEventPO.getVerifyStatus();
        tmsVerifyEventPO.getNoticeTimes();
        tmsVerifyEventPO.getDrvLocCsys();
        tmsVerifyEventPO.getDatachangeCreatetime();
        tmsVerifyEventPO.getDatachangeLasttime();
        tmsVerifyEventPO.getCreateUser();
        tmsVerifyEventPO.getModifyUser();
        verifyEvents.add(tmsVerifyEventPO);
        new Expectations(){
            {
                tmsVerifyEventDao.findNeedVerifyEvents(requestType.driverId);
                result = verifyEvents;
            }
        };
        responseType = queryDriverIsNeedVerifyEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
        Assert.assertEquals(2, responseType.verifyFlag.intValue());

        tmsVerifyEventPO.setVerifyStartTime(null);
        new Expectations(){
            {
                tmsVerifyEventDao.findNeedVerifyEvents(requestType.driverId);
                result = verifyEvents;
            }
        };
        responseType = queryDriverIsNeedVerifyEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
        Assert.assertEquals(1, responseType.verifyFlag.intValue());

        tmsVerifyEventPO.setVerifyFlag(2);
        tmsVerifyEventPO.setVerifyStartTime(Timestamp.valueOf(LocalDateTime.now().plusHours(1)));
        new Expectations(){
            {
                tmsVerifyEventDao.findNeedVerifyEvents(requestType.driverId);
                result = verifyEvents;
            }
        };
        responseType = queryDriverIsNeedVerifyEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
        Assert.assertEquals(1, responseType.verifyFlag.intValue());
    }
}
