package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.account.domain.common.DriverDomainErrorCode;
import com.ctrip.dcs.driver.account.domain.value.PhoneCheckVoiceCallTask;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory;
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import com.ctrip.model.SubmitVoiceCodeCallRequestType;
import com.ctrip.model.SubmitVoiceCodeCallResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;


public class SubmitVoiceCodeCallEngineTest extends TestBase {

    @Tested
    SubmitVoiceCodeCallEngine submitVoiceCodeCallEngine;

    @Injectable
    private PhoneCheckTaskTable phoneCheckTaskTable;
    @Injectable
    private PhoneCheckTaskFactory phoneCheckTaskFactory;
    @Injectable
    private DriverTable driverTable;
    @Injectable
    private PhoneCheckService phoneCheckService;
    @Injectable
    private LockService lockService;

    @Test
    public void test_executeSuc() {
        SubmitVoiceCodeCallRequestType requestType = getRequestType();

        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(SubmitVoiceCodeCallResponseType.class).init();

        DriverEntity driver = fetchDriverEntity();
        new Expectations(){
            {
//                phoneCheckTaskTable.queryMany((PhoneCheckTaskEntity) any);
//                result = null;

                driverTable.queryByPk(requestType.getDriverId());
                result = driver;

                phoneCheckService.ivrCall((PhoneCheckTaskEntity) any);
                result = true;
            }
        };

        SubmitVoiceCodeCallResponseType responseType = submitVoiceCodeCallEngine.doExecute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }

    private SubmitVoiceCodeCallRequestType getRequestType() {
        SubmitVoiceCodeCallRequestType requestType = new SubmitVoiceCodeCallRequestType();
        requestType.driverId = 1111L;
        requestType.phoneNo = "18899989898";
        requestType.countryCode = "86";
        requestType.verificationCode = "233344";
        return requestType;
    }

    @Test
    public void test_executeFail1() {
        SubmitVoiceCodeCallRequestType requestType = getRequestType();

        List<PhoneCheckTaskEntity> taskTableList = new ArrayList<>();
        taskTableList.add(getTask());
//        new Expectations() {
//            {
//                phoneCheckTaskTable.queryMany((PhoneCheckTaskEntity) any);
//                result = taskTableList;
//            }
//        };
        SubmitVoiceCodeCallResponseType responseType = submitVoiceCodeCallEngine.doExecute(requestType);
        Assert.assertEquals(DriverDomainErrorCode.VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrCode(), responseType.responseResult.returnCode);
    }

    @Test
    public void test_executeFail2() {
        SubmitVoiceCodeCallRequestType requestType = getRequestType();
        new Expectations() {
            {
//                phoneCheckTaskTable.queryMany((PhoneCheckTaskEntity) any);
//                result = null;

                driverTable.queryByPk(requestType.getDriverId());
                result = null;
            }
        };
        SubmitVoiceCodeCallResponseType responseType = submitVoiceCodeCallEngine.doExecute(requestType);
        Assert.assertEquals(DriverDomainErrorCode.VoiceCodeError.DRIVER_ACCOUNT_NOT_EXIST.getErrCode(), responseType.responseResult.returnCode);
    }

    @Test
    public void test_executeFail3() {
        SubmitVoiceCodeCallRequestType requestType = getRequestType();

        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(SubmitVoiceCodeCallResponseType.class).init();

        DriverEntity driver = fetchDriverEntity();
        new Expectations(){
            {
                driverTable.queryByPk(requestType.getDriverId());
                result = driver;

                phoneCheckService.ivrCall((PhoneCheckTaskEntity) any);
                result = false;
            }
        };

        SubmitVoiceCodeCallResponseType responseType = submitVoiceCodeCallEngine.doExecute(requestType);
        Assert.assertEquals(DriverDomainErrorCode.VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrCode(), responseType.responseResult.returnCode);
    }

    private PhoneCheckTaskEntity getTask() {
        PhoneCheckTaskEntity task = new PhoneCheckTaskEntity();
        task.setTaskState(0);
        task.setTaskType(2);
        task.setDriverId(1111L);
        task.setPhoneNumber("18899989898");
        task.setPhonePrefix("86");
        PhoneCheckVoiceCallTask checkVoiceCallTask = PhoneCheckVoiceCallTask.builder()
                .driverCityId(21L)
                .verificationCode("233344")
                .driverLanguages(Arrays.asList("EN", "CN"))
                .build();
        task.setTaskContent(JsonUtil.toString(checkVoiceCallTask));
        return task;
    }

    private DriverEntity fetchDriverEntity() {
        DriverEntity driver = new DriverEntity();
        driver.setDriverId(1111L);
        driver.setPhonePrefix("86");
        driver.setPhoneNumber("18899989898");
        driver.setDriverLanguage("EN");
        driver.setDriverName("张三");
        driver.setSupplierId(2333444L);
        return driver;
    }

    @Test
    public void test_onExecuted() {
        SubmitVoiceCodeCallRequestType requestType = new SubmitVoiceCodeCallRequestType();
        requestType.driverId = 1111L;
        requestType.phoneNo = "18899989898";
        requestType.countryCode = "86";
        requestType.verificationCode = "233344";
        SubmitVoiceCodeCallResponseType responseType = new SubmitVoiceCodeCallResponseType();
        ResponseResult responseResult = new ResponseResult();
        responseResult.setReturnCode("200");
        responseType.setResponseResult(responseResult);
        submitVoiceCodeCallEngine.onExecuted(requestType, responseType);
        Assert.assertEquals("18899989898", requestType.getPhoneNo());
        Assert.assertEquals("86", requestType.getCountryCode());
    }

}
