package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.domain.honour.CommemorationObjectImpl;
import com.ctrip.dcs.driver.value.honour.CommemorationObject;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;


public class QueryDirectorCommemorationEngineUnitTest extends TestBase {

    @Tested
    QueryDirectorCommemorationEngine queryDirectorCommemorationEngine;

    @Test
    public void testCommemorationObject() {
        DriverInfo driverInfo = bulidSimpleDriverInfo();

        new Expectations(){
            {
                domainBeanFactory.honourFormConfig().isShowCommemoration();
                result = true;

                domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverInfo.driverId);
                result = driverInfo;

                domainBeanFactory.archCoreInfoService().decryptIdCard(driverInfo.drvIdcard);
                result = "320682199211091188";
            }
        };

        CommemorationObject commemorationObject =
                new CommemorationObjectImpl.Builder(driverInfo.driverId, 1,
                        this.domainBeanFactory).build();
        Assert.assertEquals(commemorationObject.activeDayType(), 1);

        driverInfo.onlineTime = LocalDateTime.now().minusYears(4).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        new Expectations(){
            {
                domainBeanFactory.honourFormConfig().isShowCommemoration();
                result = true;

                domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverInfo.driverId);
                result = driverInfo;

                domainBeanFactory.archCoreInfoService().decryptIdCard(driverInfo.drvIdcard);
                result = "320682199211091188";
            }
        };
        commemorationObject =
                new CommemorationObjectImpl.Builder(driverInfo.driverId,1,
                        this.domainBeanFactory).build();
        Assert.assertEquals(commemorationObject.activeDayType(), 2);
    }
}
