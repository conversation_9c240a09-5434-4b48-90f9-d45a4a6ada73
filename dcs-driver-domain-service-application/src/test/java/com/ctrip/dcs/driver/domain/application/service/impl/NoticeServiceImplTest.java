package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.redis.NoticeRedisLogic;
import com.ctrip.dcs.driver.domain.application.redis.obj.NoticeContentDTO;
import com.ctrip.dcs.driver.domain.application.redis.obj.NoticeIdDTO;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverNoticeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverNoticeRecordTargetDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverNoticeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeUserModel;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.igt.framework.common.language.Language;
import com.ctrip.igt.framework.common.language.LanguageContext;
import mockit.Capturing;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * Created by <AUTHOR> on 2023/3/8 14:49
 */

public class NoticeServiceImplTest {

  @Tested
  NoticeServiceImpl noticeService;

  @Injectable
  DriverNoticeRecordDao driverNoticeRecordDao;

  @Injectable
  DriverNoticeRecordTargetDao driverNoticeRecordTargetDao;

  @Injectable
  NoticeRedisLogic noticeRedisLogic;

  @Injectable
  private DriverMessageServiceProxy driverMessageServiceProxy;

  @Injectable
  private ExecutorService CommonThreadPool;

  @Injectable
  CityRepository cityRepository;

  @Capturing
  City city;

  @Test
  public void testQueryNotice() {
    String startDate = "2023-03-08 00:00:00";
    String endDate = "2099-04-08 00:00:00";
    List<NoticeIdDTO> noticeIdDTOS = new ArrayList<>();
    NoticeIdDTO idDTO0 = new NoticeIdDTO();
    idDTO0.setNoticeId(11L);
    idDTO0.setIdType(1);
    idDTO0.setStartDate(startDate);
    idDTO0.setEndDate(endDate);
    idDTO0.setVehicleTypes("117");
    idDTO0.setProductType("1,2,3");
    idDTO0.setDriverStatus(2);
    idDTO0.setCountryIds(",0,");
    noticeIdDTOS.add(idDTO0);

    NoticeContentDTO contentDTO = new NoticeContentDTO();
    contentDTO.setNoticeId(11L);
    contentDTO.setStartDate(startDate);
    contentDTO.setEndDate(endDate);
    contentDTO.setContent("123");
    contentDTO.setContentEn("test");
    contentDTO.setTitle("测试");
    contentDTO.setTitleEn("test2");
    new Expectations() {
      {
        noticeRedisLogic.getValidNotice();
        result = noticeIdDTOS;
        noticeRedisLogic.isMyNotice(11L, this.anyString);
        result = true;
        noticeRedisLogic.getNoticeContent(11L);
        result = contentDTO;
        noticeRedisLogic.isNeedSendMail(11L, this.anyString);
        result = false;
      }
    };
    LanguageContext.setLanguage(Language.newBuilder().withLocaleCode("zh-cn").build());
    NoticeUserModel user1 = NoticeUserModel.newBuilder()
            .withUserId(1958L).withUserType(1)
            .withCityId(2L).withProductType("1").withStatus(1).build();
    NoticeModel noticeModel = noticeService.queryNotice(user1);
    Assert.assertTrue(noticeModel.getExist());

    noticeIdDTOS.clear();
    idDTO0.setIdType(2);
    noticeIdDTOS.add(idDTO0);
    new Expectations() {
      {
        noticeRedisLogic.getValidNotice();
        result = noticeIdDTOS;
      }
    };
    user1 = NoticeUserModel.newBuilder()
        .withUserId(1958L).withUserType(1)
        .withCityId(2L).withProductType("1").withStatus(1).withVehicleType(117L).build();
    noticeModel = noticeService.queryNotice(user1);
    Assert.assertTrue(noticeModel.getExist());

    noticeIdDTOS.clear();
    idDTO0.setUserType("1");
    idDTO0.setIdType(2);
    noticeIdDTOS.add(idDTO0);
    new Expectations() {
      {
        noticeRedisLogic.getValidNotice();
        result = noticeIdDTOS;
      }
    };
    user1 = NoticeUserModel.newBuilder()
            .withUserId(1958L).withUserType(1)
            .withCityId(2L).withProductType("4").withStatus(1).withVehicleType(117L).build();
    noticeModel = noticeService.queryNotice(user1);
    Assert.assertTrue(noticeModel.getExist());

    noticeIdDTOS.clear();
    idDTO0.setUserType("2");
    idDTO0.setIdType(2);
    noticeIdDTOS.add(idDTO0);
    new Expectations() {
      {
        noticeRedisLogic.getValidNotice();
        result = noticeIdDTOS;
      }
    };
    user1 = NoticeUserModel.newBuilder()
            .withUserId(1958L).withUserType(2)
            .withCityId(2L).withProductType("4").withStatus(1).withVehicleType(117L).build();
    noticeModel = noticeService.queryNotice(user1);
    Assert.assertTrue(noticeModel.getExist());

    noticeIdDTOS.clear();
    idDTO0.setUserType("3");
    idDTO0.setIdType(2);
    noticeIdDTOS.add(idDTO0);
    new Expectations() {
      {
        noticeRedisLogic.getValidNotice();
        result = noticeIdDTOS;
      }
    };
    user1 = NoticeUserModel.newBuilder()
            .withUserId(1958L).withUserType(41)
            .withCityId(2L).withProductType("4").withStatus(1).withVehicleType(117L).build();
    noticeModel = noticeService.queryNotice(user1);
    Assert.assertTrue(noticeModel.getExist());

    user1 = NoticeUserModel.newBuilder()
            .withUserId(1958L).withUserType(44)
            .withCityId(2L).withProductType("4").withStatus(1).withVehicleType(117L).build();
    noticeModel = noticeService.queryNotice(user1);
    Assert.assertTrue(noticeModel.getExist());
  }

  @Test
  public void testNoticeUpdate() {
    DriverNoticeRecordPO recordPO = new DriverNoticeRecordPO();
    recordPO.setId(1L);
    recordPO.setNoticeConditionType(1);
    recordPO.setNoticeStatus(2);
    new Expectations(){
      {
        driverNoticeRecordDao.findNoticeById(1L);
        result = recordPO;
        driverNoticeRecordDao.updateSendStatus(withAny(0), withAny(0L));
        result = 1;
        noticeRedisLogic.updateNoticeContent(withAny(new DriverNoticeRecordPO()));
        result = true;
      }
    };
    noticeService.noticeUpdate(1L);
    List<DriverNoticeRecordPO> recordPOS = new ArrayList<>();
    DriverNoticeRecordPO recordPO1 = new DriverNoticeRecordPO();
    recordPOS.add(recordPO1);
    new Expectations(){
      {
        driverNoticeRecordDao.queryActiveNotices(withAny(new Date()));
        result = recordPOS;
        noticeRedisLogic.updateValidNotice(withAny(new ArrayList<>()));
        result = true;
        noticeRedisLogic.cacheNoticeTarget(withAny(1L), withAny(new ArrayList<>()), withAny(Timestamp.valueOf(LocalDateTime.now())));
        result = true;
      }
    };
    noticeService.noticeUpdate(1L);
  }
}
