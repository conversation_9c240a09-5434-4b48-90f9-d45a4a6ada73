package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.enums.BankCardResultEnum;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.dcs.driver.gateway.PaymentRouterRepository;
import com.ctrip.dcs.driver.gateway.YeePayRepository;
import com.ctrip.dcs.driver.value.bank.BankCardResultModel;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBankCardRecordDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankCardRecordPO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.domain.account.BindAccountBankCardRequestType;
import com.ctrip.dcs.driver.domain.account.BindAccountBankCardResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidResponseType;
import com.ctrip.igt.ResponseResult;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;

import java.util.Arrays;
import java.util.List;

public class BindAccountBankCardEngineTest  {

    @Tested
    BindAccountBankCardEngine bindAccountBankCardEngine;

    @Injectable
    AccountBankCardRecordDao accountBankCardRecordDao;

    @Injectable
    CardInfoManagementRepository cardInfoManagementService;

    @Injectable
    AccountService accountService;

    @Injectable
    LockService lockService;

    @Injectable
    DriverAccountConfig driverAccountConfig;

    @Injectable
    YeePayRepository rhbSettlementBillServiceProxy;

    @Injectable
    PaymentRouterRepository paymentRouterApiService;

    @Injectable
    DriverWalletInfoConfig driverWalletInfoConfig;

    @Test
    public void bindAccountBankCard() throws Exception {
        BindAccountBankCardResponseType responseType = bindAccountBankCardEngine.execute(new BindAccountBankCardRequestType());
        Assert.assertEquals("500", responseType.responseResult.returnCode);

        BindAccountBankCardRequestType requestType = new BindAccountBankCardRequestType();
        requestType.uid = "uid";
        requestType.bankCardNo = "bankCardNo";
        requestType.bankPhoneNumber = "bankPhoneNumber";
        requestType.driverId = 1L;

        new Expectations() {{
            accountService.getAccountInfoByUID(this.anyString);
            result = null;
        }};
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("624", responseType.responseResult.returnCode);

        new Expectations() {{
            accountService.getAccountInfoByUID(this.anyString);
            result = new AccountInfoDTO();

            accountBankCardRecordDao.queryAccountBindingBankCard(this.anyString,this.anyString);
            result = new AccountBankCardRecordPO();
        }};
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("621", responseType.responseResult.returnCode);

        new Expectations() {{
            accountBankCardRecordDao.queryAccountBindingBankCard(this.anyString,this.anyString);
            result = null;

            paymentRouterApiService.queryBankChannelInfo(this.anyString, this.anyString,false);
            result = null;
        }};
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("500", responseType.responseResult.returnCode);

        BankCardResultModel bankCardResultModel = new BankCardResultModel();
        bankCardResultModel.setResultCode(BankCardResultEnum.CARD_NO_FAILED);
        new Expectations(){
            {
                paymentRouterApiService.queryBankChannelInfo(this.anyString, this.anyString,false);
                result = bankCardResultModel;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("602", responseType.responseResult.returnCode);

        bankCardResultModel.setResultCode(BankCardResultEnum.CARD_TYPE_FAILED);
        new Expectations(){
            {
                paymentRouterApiService.queryBankChannelInfo(this.anyString, this.anyString,false);
                result = bankCardResultModel;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("601", responseType.responseResult.returnCode);

        bankCardResultModel.setResultCode(BankCardResultEnum.OK);
        new Expectations(){
            {
                paymentRouterApiService.queryBankChannelInfo(this.anyString, this.anyString,false);
                result = bankCardResultModel;

                rhbSettlementBillServiceProxy.checkDriverBandCardValid(this.anyLong, this.anyString, this.anyString, this.anyString, this.anyString, this.anyString);
                result = null;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("610", responseType.responseResult.returnCode);

        CheckDriverBandCardValidResponseType cardValidResponseType = new CheckDriverBandCardValidResponseType();
        new Expectations(){
            {
                rhbSettlementBillServiceProxy.checkDriverBandCardValid(this.anyLong, this.anyString, this.anyString, this.anyString, this.anyString, this.anyString);
                result = cardValidResponseType;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("500", responseType.responseResult.returnCode);

        List<String> errorList = Arrays.asList("CFC101006", "CFC101007", "CFC101008", "CFC101009", "CFC101010", "CFC101011",
                "CFC101012", "CFC101016", "CFC102028", "CFC102017", "CFC102020", "CFC102021",
                "CFC101002", "CFC102003", "CFC101003", "CFC102004", "CFC102005", "CFC102006",
                "CFC102007", "CFC102008", "CFC102009", "CFC101057", "CFC102002", "CFC102003");
        errorList.forEach(e -> {
            CheckDriverBandCardValidResponseType cardValidResponseType1 = new CheckDriverBandCardValidResponseType();
            cardValidResponseType1.responseResult = new ResponseResult();
            cardValidResponseType1.responseResult.returnCode = "500";
            cardValidResponseType1.businessCode = e;
                    new Expectations(){
                        {
                            rhbSettlementBillServiceProxy.checkDriverBandCardValid(this.anyLong, this.anyString, this.anyString, this.anyString, this.anyString, this.anyString);
                            result = cardValidResponseType1;

                            driverWalletInfoConfig.getSettlementResultCode(this.anyString);
                            result = "6017";
                        }
                    };
            BindAccountBankCardResponseType responseType1 = bindAccountBankCardEngine.bindBankCard(requestType);
        });

        cardValidResponseType.responseResult = new ResponseResult();
        cardValidResponseType.responseResult.returnCode = "200";
        cardValidResponseType.businessCode = Strings.EMPTY;
        cardValidResponseType.validBankCard = 0;
        new Expectations(){
            {
                rhbSettlementBillServiceProxy.checkDriverBandCardValid(this.anyLong, this.anyString, this.anyString, this.anyString, this.anyString, this.anyString);
                result = cardValidResponseType;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("617", responseType.responseResult.returnCode);

        cardValidResponseType.validBankCard = 1;
        new Expectations(){
            {
                rhbSettlementBillServiceProxy.checkDriverBandCardValid(this.anyLong, this.anyString, this.anyString, this.anyString, this.anyString, this.anyString);
                result = cardValidResponseType;

                cardInfoManagementService.createCardInfo(this.anyString, this.anyString);
                result = Strings.EMPTY;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("622", responseType.responseResult.returnCode);

        new Expectations(){
            {
                cardInfoManagementService.createCardInfo(this.anyString, this.anyString);
                result = "createCardInfo";

                accountBankCardRecordDao.insert(this.withAny((AccountBankCardRecordPO) this.any));
                result = 0;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("623", responseType.responseResult.returnCode);

        new Expectations(){
            {
                accountBankCardRecordDao.insert(this.withAny((AccountBankCardRecordPO) this.any));
                result = 1;
            }
        };
        responseType = bindAccountBankCardEngine.bindBankCard(requestType);
        Assert.assertEquals("200", responseType.responseResult.returnCode);
    }
}
