package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.common.CommonExamSSOUtils;
import com.ctrip.dcs.driver.domain.exam.QuerySSOUrlRequestType;
import com.ctrip.dcs.driver.domain.exam.QuerySSOUrlResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class QuerySSOUrlEngineTest  extends TestBase {
  @Tested
  QuerySSOUrlEngine querySSOUrlEngine;

  @Injectable
  CommonExamSSOUtils commonExamSSOUtils;

  @Test
  public void testQuerySSOUrl(){
    QuerySSOUrlRequestType requestType = new QuerySSOUrlRequestType();
    requestType.setExamAccountId("guide0001111");
    new Expectations(){{
      domainBeanFactory.commonExamSSOUtils();
      result = commonExamSSOUtils;

      domainBeanFactory.examInterfaceConfig().getOauthDomainName();
      result = "https://t-qyweixin-api.exexm.com";

      domainBeanFactory.examInterfaceConfig().getOauthUrl();
      result = "/api/oauth/route/test3/embed?code=%s";

      commonExamSSOUtils.encryptByAES("guide0001111");
      result = "encrypt0001111";

      commonExamSSOUtils.encode("encrypt0001111");
      result = "encode0001111";
    }};
    QuerySSOUrlResponseType execute = querySSOUrlEngine.execute(requestType);
    Assert.assertEquals("https://t-qyweixin-api.exexm.com/api/oauth/route/test3/embed?code=encode0001111",execute.getOauthUrl());
  }
}
