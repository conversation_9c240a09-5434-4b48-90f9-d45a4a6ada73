package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.TestBase;
import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;
import qunar.tc.schedule.MockParameter;
import qunar.tc.schedule.Parameter;


public class HonourScheduleLogicTest extends TestBase {

    @Tested
    HonourScheduleLogic honourScheduleLogic;

    @Test
    public void syncHonourInfoFromBIDB() {
        honourScheduleLogic.syncHonourInfoFromBIDB(null);
    }

    @Test
    public void syncHRankingInfoFromBIDB() {
        honourScheduleLogic.syncHRankingInfoFromBIDB(null);
    }

    @Test
    public void weekRankUpdateNotice() {
        honourScheduleLogic.weekRankUpdateNotice(null);
    }

    @Test
    public void monthRankUpdateNotice() {
        honourScheduleLogic.monthRankUpdateNotice(null);
    }

    @Test
    public void refreshDomainCache() {
        Parameter parameter1 = new MockParameter("{\"type\":\"1\",\"driverIds\":\"1000004,1000005\"}");
        honourScheduleLogic.refreshDomainCache(parameter1);

        Parameter parameter2 = new MockParameter("{\"type\":\"2\",\"cityIds\":\"1,2\"}");
        honourScheduleLogic.refreshDomainCache(parameter2);

        Parameter parameter3 = new MockParameter("{\"type\":\"2\"}");
        honourScheduleLogic.refreshDomainCache(parameter3);
    }
}
