package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import com.ctrip.dcs.driver.domain.setting.SaveDriverPushConfigRequestType;
import com.ctrip.dcs.driver.domain.setting.SaveDriverPushConfigResponseType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class SaveDriverPushConfigEngineUnitTest extends TestBase {

    @Tested
    SaveDriverPushConfigEngine saveDriverPushConfigEngine;

    @Injectable
    private DriverOrderPushConfigDao driverOrderPushConfigDao;

    @Injectable
    private PushConfigRedisLogic pushConfigRedisLogic;

    @Test
    public void test() {
        SaveDriverPushConfigRequestType requestType = new SaveDriverPushConfigRequestType();
        requestType.driverId = 1000004L;
        requestType.orderPushStatus = true;
        requestType.drvOrderDistance = 20;
        requestType.drvIntendVehicleType = "[\"117\",\"118\"]";
        requestType.orderTyps = "[\"1617\",\"1718\",\"1717\"]";
        requestType.serviceTimeTo = "22:00";
        requestType.serviceTimeFrom = "09:30";
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(SaveDriverPushConfigResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;
        driverInfo.driverPhone = "13812345678";
        new Expectations(){
            {
                driverOrderPushConfigDao.findOne(requestType.driverId);
                result = null;

                driverOrderPushConfigDao.insert(this.withAny(new DriverOrderPushConfigPO()));
                result = 1;
            }
        };
        SaveDriverPushConfigResponseType responseType = saveDriverPushConfigEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);

        new Expectations(){
            {
                driverOrderPushConfigDao.findOne(requestType.driverId);
                result = new DriverOrderPushConfigPO();

                driverOrderPushConfigDao.update(this.withAny(new DriverOrderPushConfigPO()));
                result = 1;
            }
        };
        responseType = saveDriverPushConfigEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
