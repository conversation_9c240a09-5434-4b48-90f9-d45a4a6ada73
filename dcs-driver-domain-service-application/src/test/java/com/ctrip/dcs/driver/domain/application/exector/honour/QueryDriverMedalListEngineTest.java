package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.honour.QueryDriverMedalListRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryDriverMedalListResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalRankingPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormHonourWallDriverMedalInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import com.google.common.reflect.TypeToken;
import mockit.Expectations;
import mockit.Mocked;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.*;
import java.util.concurrent.ExecutorService;


public class QueryDriverMedalListEngineTest extends TestBase {

    @Tested
    QueryDriverMedalListEngine queryDriverMedalListEngine;

    @Test
    public void execute(@Mocked ExecutorService executorService) {
        DriverInfo driverInfo = bulidDriverInfo();
        QueryDriverMedalListRequestType requestType = new QueryDriverMedalListRequestType();
        requestType.setDriverId(driverInfo.driverId);
        requestType.setType(1);
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverMedalListResponseType.class).init();

        List<FormHonourWallDriverMedalInfo> honourWallMedalInfo = new ArrayList<>();
        FormHonourWallDriverMedalInfo formHonourWallDriverMedalInfo = new FormHonourWallDriverMedalInfo();
        formHonourWallDriverMedalInfo.setDriverRank(10);
        formHonourWallDriverMedalInfo.setTotalRank(20);
        formHonourWallDriverMedalInfo.setLight(false);
        formHonourWallDriverMedalInfo.setCode("honour");
        honourWallMedalInfo.add(formHonourWallDriverMedalInfo);

        FormHonourWallDriverMedalInfo goldMedal = new FormHonourWallDriverMedalInfo();
        goldMedal.setDriverRank(10);
        goldMedal.setTotalRank(20);
        goldMedal.setLight(false);
        goldMedal.setCode("goldMedal");
        honourWallMedalInfo.add(goldMedal);

        List<MedalBasicInfoModel> medalBasicInfoModels = JacksonUtil.deserialize(
                "[{\"type\":\"GOODCAR\",\"medalCode\":\"goodaction\",\"bimedalType\":\"cnzj_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1,\"medalGradeMin\":1,\"medalGradeMax\":50},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments50\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":50,\"medalGradeMin\":50,\"medalGradeMax\":100},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments100\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":100,\"medalGradeMin\":100,\"medalGradeMax\":150},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments150\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":150,\"medalGradeMin\":150,\"medalGradeMax\":200},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments200\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":200,\"medalGradeMin\":200,\"medalGradeMax\":300},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments300\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":300,\"medalGradeMin\":300,\"medalGradeMax\":500},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments500\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":500,\"medalGradeMin\":500,\"medalGradeMax\":800},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments800\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":800,\"medalGradeMin\":800,\"medalGradeMax\":1000},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1000\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1000,\"medalGradeMin\":1000,\"medalGradeMax\":1200},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1200\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1200,\"medalGradeMin\":1200,\"medalGradeMax\":1500},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments1500\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":1500,\"medalGradeMin\":1500,\"medalGradeMax\":2000},{\"type\":\"GOODCOMMENTS\",\"medalCode\":\"comments2000\",\"bimedalType\":\"good_comment_medal\",\"bimedalGrade\":2000,\"medalGradeMin\":2000,\"medalGradeMax\":9999999999},{\"type\":\"GOODMAP\",\"medalCode\":\"goodaction\",\"bimedalType\":\"hdtrlz_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders1\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":1,\"medalGradeMin\":1,\"medalGradeMax\":50},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders50\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":50,\"medalGradeMin\":50,\"medalGradeMax\":100},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders100\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":100,\"medalGradeMin\":100,\"medalGradeMax\":200},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders200\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":200,\"medalGradeMin\":200,\"medalGradeMax\":500},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders500\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":500,\"medalGradeMin\":500,\"medalGradeMax\":1000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders1000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":1000,\"medalGradeMin\":1000,\"medalGradeMax\":1500},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders1500\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":1500,\"medalGradeMin\":1500,\"medalGradeMax\":2000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders2000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":2000,\"medalGradeMin\":2000,\"medalGradeMax\":3000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders3000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":3000,\"medalGradeMin\":3000,\"medalGradeMax\":5000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders5000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":5000,\"medalGradeMin\":5000,\"medalGradeMax\":8000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders8000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":8000,\"medalGradeMin\":8000,\"medalGradeMax\":10000},{\"type\":\"FINISHORDERS\",\"medalCode\":\"orders10000\",\"bimedalType\":\"order_cnt_medal\",\"bimedalGrade\":10000,\"medalGradeMin\":10000,\"medalGradeMax\":9999999999},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1,\"medalGradeMin\":1,\"medalGradeMax\":50},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays50\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":50,\"medalGradeMin\":50,\"medalGradeMax\":100},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays100\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":100,\"medalGradeMin\":100,\"medalGradeMax\":200},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays200\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":200,\"medalGradeMin\":200,\"medalGradeMax\":300},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays300\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":300,\"medalGradeMin\":300,\"medalGradeMax\":500},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays500\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":500,\"medalGradeMin\":500,\"medalGradeMax\":800},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays800\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":800,\"medalGradeMin\":800,\"medalGradeMax\":1000},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1000\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1000,\"medalGradeMin\":1000,\"medalGradeMax\":1500},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1500\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1500,\"medalGradeMin\":1500,\"medalGradeMax\":1800},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays1800\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":1800,\"medalGradeMin\":1800,\"medalGradeMax\":2000},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays2000\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":2000,\"medalGradeMin\":2000,\"medalGradeMax\":2500},{\"type\":\"SERVICEDAYS\",\"medalCode\":\"servicedays2500\",\"bimedalType\":\"order_day_medal\",\"bimedalGrade\":2500,\"medalGradeMin\":2500,\"medalGradeMax\":9999999999},{\"type\":\"GOODMANNER\",\"medalCode\":\"goodaction\",\"bimedalType\":\"tdhfwj_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999},{\"type\":\"GOODLUGGAGE\",\"medalCode\":\"goodaction\",\"bimedalType\":\"zdbnxl_medal\",\"bimedalGrade\":30,\"medalGradeMin\":30,\"medalGradeMax\":9999999999}]",
                new TypeToken<List<MedalBasicInfoModel>>(){}.getType());

        AdmPrdTrhDriverHonorInfoPO biDriverMedalInfo = new AdmPrdTrhDriverHonorInfoPO();
        biDriverMedalInfo.setOrderDay(1L);
        biDriverMedalInfo.setOrderDayMedalGrade(0);
        biDriverMedalInfo.setOrderCnt(1L);
        biDriverMedalInfo.setOrderCntMedalGrade(0);
        biDriverMedalInfo.setGoodComment(1L);
        biDriverMedalInfo.setGoodCommentMedalGrade(0);
        biDriverMedalInfo.setTdhfwjTagMedal(1);
        biDriverMedalInfo.setCnzjTagMedal(1);
        biDriverMedalInfo.setHdtrlzTagMedal(1);
        biDriverMedalInfo.setZdbnxlTagMedal(1);
        biDriverMedalInfo.setMedalGetCnt(10L);

        GoldMedalDriverModel goldMedalDriverModel = new GoldMedalDriverModel();
        goldMedalDriverModel.setType("goldMedal");
        goldMedalDriverModel.setLight(true);
        goldMedalDriverModel.setMonthTime("202311");
        goldMedalDriverModel.setTotalCount(3);

        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverInfo.driverId);
                result = driverInfo;

                domainBeanFactory.honourFormConfig().isShowMedal();
                result = true;

                domainBeanFactory.honourRedisLogic().getDriverMedalCount(driverInfo.driverId);
                result = -1;

                domainBeanFactory.honourDBDataService().queryDriverMedalCount(driverInfo.driverId);
                result = 10;

                domainBeanFactory.honourFormConfig().getHonourWallMedalCount(driverInfo.driverId);
                result = 2;

                domainBeanFactory.honourFormConfig().getHonourWallMedalInfo(driverInfo.driverId, false, this.withAny((GoldMedalDriverModel) this.any));
                result = honourWallMedalInfo;

                domainBeanFactory.honourRedisLogic().queryCurrentMedalBasicInfo();
                result = null;

                domainBeanFactory.medalInfoService().queryBasicMedalInfo();
                result = medalBasicInfoModels;

                domainBeanFactory.honourRedisLogic().getDriverGoldMedalInfo(driverInfo.driverId);
                result = null;

                domainBeanFactory.medalInfoService().getGoldMedalDriverInfo(driverInfo.driverId);
                result = goldMedalDriverModel;
            }
        };

        QueryDriverMedalListResponseType responseType = queryDriverMedalListEngine.execute(requestType);
        Assert.assertEquals(responseType.responseResult.returnCode, "200");

        biDriverMedalInfo.setOrderDay(1L);
        biDriverMedalInfo.setOrderDayMedalGrade(50);
        biDriverMedalInfo.setOrderCnt(1L);
        biDriverMedalInfo.setOrderCntMedalGrade(50);
        biDriverMedalInfo.setGoodComment(1L);
        biDriverMedalInfo.setGoodCommentMedalGrade(50);
        biDriverMedalInfo.setTdhfwjTagMedal(1);
        biDriverMedalInfo.setCnzjTagMedal(1);
        biDriverMedalInfo.setHdtrlzTagMedal(1);
        biDriverMedalInfo.setZdbnxlTagMedal(1);
        biDriverMedalInfo.setMedalGetCnt(10L);

        Map<String, DimPrdTrhDriverMedalRankingPO> medalRankingPOMap = new HashMap<>();
        DimPrdTrhDriverMedalRankingPO rankingPO = new DimPrdTrhDriverMedalRankingPO();
        rankingPO.setMedalGradeGetRank(100L);
        rankingPO.setGradeGetDrvCnt(200L);
        medalRankingPOMap.put("servicedays1", rankingPO);
        requestType.setType(2);
        new Expectations(){
            {
                domainBeanFactory.honourFormConfig().getHonourWallMedalInfo(driverInfo.driverId, true, this.withAny((GoldMedalDriverModel) this.any));
                result = honourWallMedalInfo;

                domainBeanFactory.honourDBDataService().queryDriverMedalRankInfo(driverInfo.driverId);
                result = medalRankingPOMap;

                domainBeanFactory.honourDBDataService().queryNoticedMedalList(driverInfo.driverId);
                result = new ArrayList<>();

                domainBeanFactory.honourRedisLogic().getDriverGoldMedalInfo(driverInfo.driverId);
                result = goldMedalDriverModel;

                domainBeanFactory.executorService();
                result = executorService;
            }
        };
        responseType = queryDriverMedalListEngine.execute(requestType);
        Assert.assertEquals(responseType.responseResult.returnCode, "200");
    }
}
