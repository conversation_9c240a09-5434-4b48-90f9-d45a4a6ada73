package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.exam.GuideTagExamDTO;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class ExamRedisLogicTest {
  @Tested
  ExamRedisLogic examRedisLogic;

  @Injectable
  DirectorRedis directorRedis;

  @Test
  public void testExamRedisLogic(){
    String token = "testToken";
    long seconde = 200L;
    String deptList = "[\"DDXDCJ2024\",\"DDXDZJ2024\"]";
    examRedisLogic.saveAccessToken(token,seconde);
    examRedisLogic.saveExamDeptList(deptList);
    new Expectations(){
      {
        examRedisLogic.getAccessToken();
        result = token;
      }
    };
    String accessToken = examRedisLogic.getAccessToken();
    Assert.assertEquals(accessToken, "testToken");
  }

  @Test
  public void testGetGuideTagExam(){
    examRedisLogic.deleteGuidePassedExamInfo(1947L);
    examRedisLogic.saveGuidePassedExamInfo(1947L,"");
    new Expectations(){{
      directorRedis.get("guide_passed_exam_info:1947");
      result = "{\n" + "    \"examIsPass\":true,\n" + "    \"examList\":[\n" + "        {\n" + "            \"examType\":\"DDXDCJ\",\n" + "            \"completeTime\":\"2023-12-03 12:12:12\",\n" + "            \"score\":99.10\n" + "        }\n" + "    ]\n" + "}";
    }};
    GuideTagExamDTO guideTagExam = examRedisLogic.getGuideTagExam(1947L);
    Assert.assertTrue(guideTagExam.isExamIsPass());
  }

}
