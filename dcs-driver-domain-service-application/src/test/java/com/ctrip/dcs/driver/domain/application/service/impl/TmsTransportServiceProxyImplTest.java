package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.transport.TmsTransportServiceProxy;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.dcs.tms.transport.api.regulation.OldDriverInfo;
import com.ctrip.dcs.tms.transport.api.regulation.QueryHistoryDrvDataRequestType;
import com.ctrip.dcs.tms.transport.api.regulation.QueryHistoryDrvDataResponseType;
import com.ctrip.dcs.tms.transport.api.resource.driver.DrvBase;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOAResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public class TmsTransportServiceProxyImplTest extends TestBase {

    @Tested
    TmsTransportServiceProxyImpl tmsTransportServiceProxyImpl;

    @Injectable
    private TmsTransportServiceProxy tmsTransportServiceProxy;

    @Test
    public void queryDriver() {
        DriverInfo driverInfo = super.bulidSimpleDriverInfo();
        DriverInfoSOARequestType requestType = new DriverInfoSOARequestType();
        requestType.driverIds = String.valueOf(driverInfo.driverId);

        DriverInfoSOAResponseType responseType = new DriverInfoSOAResponseType();
        responseType.driverList = Collections.singletonList(driverInfo);
        new Expectations(){
            {
                tmsTransportServiceProxy.queryDriver(requestType);
                result = responseType;
            }
        };
        DriverInfo driverInfoResult = tmsTransportServiceProxyImpl.queryDriver(driverInfo.driverId);
        Assert.assertNotNull(driverInfoResult);
    }

    @Test
    public void queryDriverList() {
        long cityId = 2L;
        DriverInfo driverInfo = super.bulidSimpleDriverInfo();
        QueryDriver4BaseSOARequestType requestType = new QueryDriver4BaseSOARequestType();
        requestType.cityIdList = Collections.singletonList(cityId);
        requestType.drvStatusList = Arrays.asList(1,2);
        requestType.proLineList = Collections.singletonList(1);
        QueryDriver4BaseSOAResponseType responseType = new QueryDriver4BaseSOAResponseType();
        DrvBase drvBase = new DrvBase();
        drvBase.setDrvId(driverInfo.driverId);
        drvBase.setProLineIdList(Collections.singletonList(1));
        responseType.data = Collections.singletonList(drvBase);
        new Expectations(){
            {
                tmsTransportServiceProxy.queryDriver4Base(requestType);
                result = responseType;
            }
        };
        List<Long> result = tmsTransportServiceProxyImpl.queryDriverList(cityId);
        Assert.assertEquals(result.get(0), driverInfo.driverId);
    }

    @Test
    public void queryOldDriver(){
        String driverPhone = "driverPhone";

        QueryHistoryDrvDataRequestType requestType = new QueryHistoryDrvDataRequestType();
        requestType.drvPhone = driverPhone;
        QueryHistoryDrvDataResponseType responseType = new QueryHistoryDrvDataResponseType();
        responseType.infoList = new ArrayList<>();
        responseType.infoList.add(new OldDriverInfo());
        new Expectations(){
            {
                tmsTransportServiceProxy.queryHistoryDrvData(requestType);
                result = responseType;
            }
        };

        OldDriverInfo oldDriverInfo = tmsTransportServiceProxyImpl.queryOldDriver(driverPhone);
        Assert.assertNotNull(oldDriverInfo);
    }
}


