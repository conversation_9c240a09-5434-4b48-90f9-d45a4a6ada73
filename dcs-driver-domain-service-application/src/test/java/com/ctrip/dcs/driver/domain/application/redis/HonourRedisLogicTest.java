package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.honour.CustomerHonourMedalInfoDTO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankCityDataModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public class HonourRedisLogicTest {
    @Tested
    HonourRedisLogic honourRedisLogic;

    @Injectable
    DirectorRedis directorRedis;
    
    @Test
    public void test(){

        long driverId = 123456L;
        long cityId = 2L;
        honourRedisLogic.queryCityRankData(cityId, true);
        honourRedisLogic.saveDriverRankData(driverId, true, new RankCityDataModel());


        new Expectations(){
            {
                directorRedis.get(String.format("ranklist:%s:%s", cityId, 1));
                result = JacksonUtil.serialize(Arrays.asList(new RankCityDataModel()));
            }
        };
        honourRedisLogic.batchNotieRankUpdate(Collections.singletonList(driverId));
        honourRedisLogic.cleartNoticeRankUpdate(driverId);
        honourRedisLogic.getCurrentRankInfo();
        honourRedisLogic.getCurrentRankLikeCount(driverId, cityId, 1);
        honourRedisLogic.getDriverMedalCount(driverId);
        honourRedisLogic.isNoticeRankUpdate(driverId);
        List<RankCityDataModel> rankCityDataModelList = honourRedisLogic.queryCityRankData(cityId, true);
        honourRedisLogic.queryCurrentMedalBasicInfo();
        honourRedisLogic.saveCurrentMedalBasicInfo(new ArrayList<>());
        honourRedisLogic.saveCurrentMedalBasicInfo(Collections.singletonList(new MedalBasicInfoModel()));
        honourRedisLogic.saveCurrentRankInfo(new RankOriginalDataModel());
        honourRedisLogic.saveDriverMedalCount(driverId, 1L);
        honourRedisLogic.saveDriverRankData(driverId, true, new RankCityDataModel());
        honourRedisLogic.updateCityRankData(driverId, Arrays.asList(new RankCityDataModel()),true);
        honourRedisLogic.queryDriverRankData(driverId, true);
        honourRedisLogic.updateDriverRankLikeCount(cityId, driverId, 1, 1);
        honourRedisLogic.saveDriverGoldMedalInfo(driverId, new GoldMedalDriverModel());
        honourRedisLogic.getDriverGoldMedalInfo(driverId);
        honourRedisLogic.getDriverMedalCount(driverId);
        honourRedisLogic.getDriverCustomerMedalInfo(driverId);
        new Expectations(){
            {
                directorRedis.get("customermedalinfo:123456");
                result = "1";
            }
        };
        honourRedisLogic.getDriverCustomerMedalInfo(driverId);
        new Expectations(){
            {
                directorRedis.get("customermedalinfo:123456");
                result = JacksonUtil.serialize(Collections.singletonList(new CustomerHonourMedalInfoDTO()));
            }
        };
        honourRedisLogic.getDriverCustomerMedalInfo(driverId);
        honourRedisLogic.saveDriverCustomerMedalInfo(driverId, Collections.emptyList());
        honourRedisLogic.saveDriverCustomerMedalInfo(driverId, new ArrayList<>());
        Assert.assertTrue(rankCityDataModelList.size() > 0);
    }
}
