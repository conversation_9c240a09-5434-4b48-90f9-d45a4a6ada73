package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.UpdateAccountParam;
import com.ctrip.dcs.driver.domain.account.UpdateAccountRequestType;
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType;
import com.ctrip.igt.ResponseResult;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;


public class UpdateAccountEngineTest {

    @Tested
    UpdateAccountEngine updateAccountEngine;

    @Injectable
    AccountService accountService;

    @Test
    public void bindAccountBankCard()  {
        AccountInfoDTO account = new AccountInfoDTO();
        account.setIdentityDTOList(new ArrayList<>());
        account.setWithdrawStatus(null);
        AccountDetailConvert.convert(account);
        account.setWithdrawStatus(1);
        AccountDetailConvert.convert(account);

        UpdateAccountResponseType updateAccountResponseType = new UpdateAccountResponseType();
        updateAccountResponseType.responseResult = new ResponseResult();
        updateAccountResponseType.responseResult.returnCode = "200";
        new Expectations() {{
            accountService.updateAccount(this.withAny((UpdateAccountParam) this.any));
            result = updateAccountResponseType;
        }};

        UpdateAccountRequestType requestType = new UpdateAccountRequestType();
        UpdateAccountResponseType responseType = updateAccountEngine.execute(requestType);
        Assert.assertEquals("200", responseType.responseResult.returnCode);
    }
}
