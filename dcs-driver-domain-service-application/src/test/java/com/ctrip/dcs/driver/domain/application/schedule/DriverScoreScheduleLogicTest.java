package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.redis.PointInfoRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverPointRestfulShowInfoDao;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import qunar.tc.schedule.MockParameter;
import qunar.tc.schedule.Parameter;


public class DriverScoreScheduleLogicTest extends TestBase {

    @Tested
    DriverScoreScheduleLogic driverScoreScheduleLogic;

    @Injectable
    PointInfoRedisLogic pointInfoRedisLogic;

    @Injectable
    DriverPointRestfulShowInfoDao driverPointRestfulShowInfoDao;

    @Test
    public void updateDriverScorePopInfo() {
        driverScoreScheduleLogic.updateDriverScorePopInfo(null);
        Assert.assertTrue(true);

        Parameter parameter = new MockParameter("{}");
        driverScoreScheduleLogic.updateDriverScorePopInfo(parameter);
        Assert.assertTrue(true);

        parameter = new MockParameter("{\"driverIdList\":\"0\"}");
        driverScoreScheduleLogic.updateDriverScorePopInfo(parameter);
        Assert.assertTrue(true);

        parameter = new MockParameter("{\"driverIdList\":\"1000004\",\"popType\":\"0\"}");
        driverScoreScheduleLogic.updateDriverScorePopInfo(parameter);
        Assert.assertTrue(true);

        parameter = new MockParameter("{\"driverIdList\":\"1000004\",\"popType\":\"1\"}");
        driverScoreScheduleLogic.updateDriverScorePopInfo(parameter);
        Assert.assertTrue(true);

        parameter = new MockParameter("{\"driverIdList\":\"1000004\",\"popType\":\"2\"}");
        driverScoreScheduleLogic.updateDriverScorePopInfo(parameter);
        Assert.assertTrue(true);

        parameter = new MockParameter("{\"driverIdList\":\"1000004,1000005\",\"popType\":\"2\"}");
        driverScoreScheduleLogic.updateDriverScorePopInfo(parameter);
        Assert.assertTrue(true);

        new Expectations(){
            {
                driverPointRestfulShowInfoDao.findOne(this.withAny(anyLong));
                result = null;
            }
        };
        driverScoreScheduleLogic.updateDriverScorePopInfo(parameter);
        Assert.assertTrue(true);
    }
}
