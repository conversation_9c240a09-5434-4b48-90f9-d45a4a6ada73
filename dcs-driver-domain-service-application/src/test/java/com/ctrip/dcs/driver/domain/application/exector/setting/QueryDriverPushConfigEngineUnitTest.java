package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import com.ctrip.dcs.driver.domain.setting.DriverPushConfigInfoDTO;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPushConfigRequestType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPushConfigResponseType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.Arrays;
import java.util.Collections;
import java.util.UUID;


public class QueryDriverPushConfigEngineUnitTest extends TestBase {

    @Tested
    QueryDriverPushConfigEngine queryDriverPushConfigEngine;

    @Injectable
    private DriverOrderPushConfigDao driverOrderPushConfigDao;

    @Injectable
    private PushConfigRedisLogic pushConfigRedisLogic;

    @Injectable
    DirectorRedis directorRedis;

    @Test
    public void test() {
        QueryDriverPushConfigRequestType requestType = new QueryDriverPushConfigRequestType();
        requestType.driverIdList = Collections.singletonList(1000004L);
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverPushConfigResponseType.class).init();

        DriverOrderPushConfigPO driverOrderPushConfigPO = new DriverOrderPushConfigPO();
        driverOrderPushConfigPO.setId(1000004L);
        driverOrderPushConfigPO.setServiceTime("{\"from\":\"00:00\",\"to\":\"23:59\"}");
        driverOrderPushConfigPO.setDrvIntendVehicleType("[\"117\",\"118\"]");
        driverOrderPushConfigPO.setDrvOrderDistance(20);
        driverOrderPushConfigPO.setOrderPushStatus(true);
        driverOrderPushConfigPO.setOrderTyps("[\"1617\",\"1618\",\"1717\",\"1718\",\"1719\"]");

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;
        driverInfo.driverPhone = "13812345678";
        new Expectations(){
            {
                pushConfigRedisLogic.queryDriverPushConfig(requestType.driverIdList.get(0));
                result = null;

                driverOrderPushConfigDao.findOne(requestType.driverIdList.get(0));
                result = driverOrderPushConfigPO;
            }
        };

        QueryDriverPushConfigResponseType responseType = queryDriverPushConfigEngine.execute(requestType);
        Assert.assertEquals(1, responseType.pushInfoListList.size());

        new Expectations(){
            {
                pushConfigRedisLogic.queryDriverPushConfig(requestType.driverIdList.get(0));
                result = JacksonUtil.deserialize(
                "{\"serviceTimeFrom\":\"00:00\",\"serviceTimeTo\":\"23:59\",\"orderTyps\":[5,4,2,1,0],\"orderPushStatus\":true,\"drvOrderDistance\":20,\"drvIntendVehicleTypes\":[117,118]}"
                , DriverPushConfigInfoDTO.class);
            }
        };
        responseType = queryDriverPushConfigEngine.execute(requestType);
        Assert.assertEquals(1, responseType.pushInfoListList.size());

        requestType.driverIdList = Arrays.asList(1000004L, 1000005L);
        responseType = queryDriverPushConfigEngine.execute(requestType);
        Assert.assertEquals(0, responseType.pushInfoListList.size());

        requestType.driverIdList = Collections.emptyList();
        responseType = queryDriverPushConfigEngine.execute(requestType);
        Assert.assertEquals(0, responseType.pushInfoListList.size());
    }
}
