package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideInfoModel;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import java.util.ArrayList;
import java.util.List;


public class GuideInfoDBDataServiceImplTest {
  @Tested
  GuideInfoDBDataServiceImpl guideInfoDBDataService;

  @Injectable
  GuideInfoDao guideInfoDao;

  @Test
  public void testGuideInfoDBDataService(){
    GuideInfoModel guideInfoModel = testQueryMaxGuideId();
    testQueryGuideInfoListByGuideId();
    Assert.assertEquals(guideInfoModel.getGuideId(), new Long(1947));
  }

  private void testQueryGuideInfoListByGuideId(){
    new Expectations(){
      {
        guideInfoDao.queryGuideInfoListByGuideId(1800L,2000L);
        result = getGuideInfoList();
      }
    };
    guideInfoDBDataService.queryGuideInfoListByGuideId(1800L,2000L);
  }

  private GuideInfoModel testQueryMaxGuideId(){
    new Expectations(){
      {
        guideInfoDao.queryMaxGuideIdInfo();
        result = getGuideInfoList();
      }
    };
    GuideInfoModel guideInfoModel = guideInfoDBDataService.queryMaxGuideId();
    return guideInfoModel;
  }

  private List<GuideInfoPO> getGuideInfoList(){
    List<GuideInfoPO> list = new ArrayList<>();
    GuideInfoPO guideInfoPO = new GuideInfoPO();
    guideInfoPO.setGuideId(1947L);
    list.add(guideInfoPO);
    return list;
  }
}
