package com.ctrip.dcs.driver.domain.application.exector.notice;

import com.ctrip.dcs.driver.domain.application.service.NoticeService;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeUserModel;
import com.ctrip.dcs.driver.domain.notice.NoticeUserDTO;
import com.ctrip.dcs.driver.domain.notice.QueryNoticeRequestType;
import com.ctrip.dcs.driver.domain.notice.QueryNoticeResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

/**
 * Created by <AUTHOR> on 2023/3/8 18:38
 */

public class QueryNoticeEngineTest {

  @Tested
  QueryNoticeEngine queryNoticeEngine;

  @Injectable
  private NoticeService noticeService;

  @Test
  public void execute() {
    QueryNoticeRequestType requestType = new QueryNoticeRequestType();
    NoticeUserDTO userDTO = new NoticeUserDTO();
    userDTO.setUserId(1000004L);
    userDTO.setUserType(1);
    userDTO.setStatus(2);
    userDTO.setCityId(2L);
    userDTO.setProductType("1,2");
    userDTO.setVehicleType(117L);
    requestType.setUser(userDTO);
    NoticeModel noticeModel = NoticeModel.newBuilder().withExist(true).withStatus(2).build();
    new Expectations(){
      {
        noticeService.queryNotice(withAny(NoticeUserModel.newBuilder().build()));
        result = noticeModel;
      }
    };
    QueryNoticeResponseType responseType = queryNoticeEngine.execute(requestType);
    Assert.assertNotNull(responseType);
    NoticeModel noticeModel1 = NoticeModel.newBuilder().withExist(false).build();
    new Expectations(){
      {
        noticeService.queryNotice(withAny(NoticeUserModel.newBuilder().build()));
        result = noticeModel1;
      }
    };
    responseType = queryNoticeEngine.execute(requestType);
    Assert.assertNotNull(responseType);

    new Expectations(){
      {
        noticeService.queryNotice(withAny(NoticeUserModel.newBuilder().build()));
        result = null;
      }
    };
    responseType = queryNoticeEngine.execute(requestType);
    Assert.assertNotNull(responseType);
  }
}
