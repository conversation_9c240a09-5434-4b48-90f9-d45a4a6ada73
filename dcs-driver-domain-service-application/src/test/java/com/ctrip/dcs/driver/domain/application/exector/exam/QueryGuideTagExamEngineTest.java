package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.exam.GuideTagExamDTO;
import com.ctrip.dcs.driver.domain.exam.QueryGuideTagExamRequestType;
import com.ctrip.dcs.driver.domain.exam.QueryGuideTagExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class QueryGuideTagExamEngineTest extends TestBase {
  @Tested
  QueryGuideTagExamEngine queryGuideTagExamEngine;

  @Injectable
  LockService lockService;

  @Test
  public void test(){
    QueryGuideTagExamRequestType requestType = new QueryGuideTagExamRequestType();
    requestType.setGuideId(1971L);
    ServiceExecuteContext.newContext()
        .withRequest(requestType)
        .withGlobalTraceId(UUID.randomUUID().toString())
        .withResponseClass(QueryGuideTagExamResponseType.class).init();
    GuideTagExamDTO guideTagExamCache = new GuideTagExamDTO();
    new Expectations(){
      {
        domainBeanFactory.examRedisLogic().getGuideTagExam(1971L);
        result = guideTagExamCache;
      }
    };
    QueryGuideTagExamResponseType execute = queryGuideTagExamEngine.execute(requestType);
    Assert.assertEquals(execute.responseResult.returnCode,"200");


    new Expectations(){
      {
        domainBeanFactory.examRedisLogic().getGuideTagExam(1971L);
        result = null;
      }
    };
    QueryGuideTagExamResponseType execute1 = queryGuideTagExamEngine.execute(requestType);
    Assert.assertEquals(execute1.responseResult.returnCode,"200");
  }
}
