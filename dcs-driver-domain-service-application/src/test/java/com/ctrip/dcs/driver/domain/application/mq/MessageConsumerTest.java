package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.redis.PointInfoRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.NoticeService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverPointRestfulShowInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;


public class MessageConsumerTest {
    @Tested
    MessageConsumer messageConsumer;

    @Injectable
    DriverPointRestfulShowInfoDao driverPointRestfulShowInfoDao;

    @Injectable
    PointInfoRedisLogic pointInfoRedisLogic;

    @Injectable
    NoticeService noticeService;

    @Injectable
    LockService lockService;

    @Test
    public void onPointPopCallback(){
        LoggerContext.newContext().init();

        Message message = new BaseMessage();
        message.setProperty("driverId", Strings.EMPTY);
        messageConsumer.onPointPopCallback(message);
        Assert.assertTrue(true);

        message.setProperty("popType", Strings.EMPTY);
        messageConsumer.onPointPopCallback(message);
        Assert.assertTrue(true);

        message.setProperty("driverId", 1000004L);
        message.setProperty("popType", 1);
        new Expectations(){
            {
                driverPointRestfulShowInfoDao.findOne(1000004L);
                result = null;
            }
        };
        messageConsumer.onPointPopCallback(message);
        Assert.assertTrue(true);

        message.setProperty("popType", 2);
        new Expectations(){
            {
                driverPointRestfulShowInfoDao.findOne(1000004L);
                result = new DriverPointRestfulShowInfoPO();
            }
        };
        messageConsumer.onPointPopCallback(message);
        Assert.assertTrue(true);
    }

    @Test
    public void onManualPushNotice(){
        LoggerContext.newContext().init();
        Message message = new BaseMessage();
        message.setProperty("messageType", "2");
        message.setProperty("data", "1234");
        messageConsumer.onManualPushNotice(message);
    }
}
