package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


public class RankInfoServiceImplTest extends TestBase {

    @Tested
    RankInfoServiceImpl rankInfoServiceImpl;

    @Injectable
    private DriverMessageServiceProxy driverMessageServiceProxy;

    @Test
    public void updateRankListForSchedule() {
        long cityId = 2L;
        AdmPrdTrhDriverHonorInfoPO admPrdTrhDriverHonorInfoPO = new AdmPrdTrhDriverHonorInfoPO();
        admPrdTrhDriverHonorInfoPO.setBatchTime(1L);
        List<RankOriginalDataModel> rankOriginalDataModels = new ArrayList<>();
        RankOriginalDataModel rankOriginalDataModel1 = new RankOriginalDataModel();
        rankOriginalDataModel1.setCityId(cityId);
        rankOriginalDataModel1.setDataTimeMonth("2022-08");
        rankOriginalDataModel1.setDataTime("2022-08-08 17:57:00");
        rankOriginalDataModel1.setDataTimeWeek("2022-08-08");
        rankOriginalDataModel1.setBatchTime(1L);
        rankOriginalDataModel1.setDrvId(1000004L);
        rankOriginalDataModel1.setDrvName("driver");
        rankOriginalDataModel1.setMonthOrderCnt(1);
        rankOriginalDataModel1.setMonthRanking(1);
        rankOriginalDataModel1.setWeekOrderCnt(1);
        rankOriginalDataModel1.setWeekRanking(1);
        rankOriginalDataModels.add(rankOriginalDataModel1);
        RankOriginalDataModel rankOriginalDataModel2 = new RankOriginalDataModel();
        rankOriginalDataModel2.setCityId(cityId);
        rankOriginalDataModel2.setDataTimeMonth("2022-08");
        rankOriginalDataModel2.setDataTime("2022-08-08 17:57:00");
        rankOriginalDataModel2.setDataTimeWeek("2022-08-08");
        rankOriginalDataModel2.setBatchTime(1L);
        rankOriginalDataModel2.setDrvId(1000005L);
        rankOriginalDataModel2.setDrvName("driver");
        rankOriginalDataModel2.setMonthOrderCnt(2);
        rankOriginalDataModel2.setMonthRanking(2);
        rankOriginalDataModel2.setWeekOrderCnt(1);
        rankOriginalDataModel2.setWeekRanking(1);
        rankOriginalDataModels.add(rankOriginalDataModel2);

        RankOriginalDataModel rankOriginalDataModel3 = new RankOriginalDataModel();
        rankOriginalDataModel3.setCityId(cityId);
        rankOriginalDataModel3.setDataTimeMonth("2022-08");
        rankOriginalDataModel3.setDataTime("2022-08-08 17:57:00");
        rankOriginalDataModel3.setDataTimeWeek("2022-08-08");
        rankOriginalDataModel3.setBatchTime(1L);
        rankOriginalDataModel3.setDrvId(3452182L);
        rankOriginalDataModel3.setDrvName("driver");
        rankOriginalDataModel3.setMonthOrderCnt(3);
        rankOriginalDataModel3.setMonthRanking(3);
        rankOriginalDataModel3.setWeekOrderCnt(2);
        rankOriginalDataModel3.setWeekRanking(2);
        rankOriginalDataModels.add(rankOriginalDataModel3);

        RankOriginalDataModel rankOriginalDataModel4 = new RankOriginalDataModel();
        rankOriginalDataModel4.setCityId(cityId);
        rankOriginalDataModel4.setDataTimeMonth("2022-08");
        rankOriginalDataModel4.setDataTime("2022-08-08 17:57:00");
        rankOriginalDataModel4.setDataTimeWeek("2022-08-08");
        rankOriginalDataModel4.setBatchTime(1L);
        rankOriginalDataModel4.setDrvId(347607L);
        rankOriginalDataModel4.setDrvName("driver");
        rankOriginalDataModel4.setMonthOrderCnt(3);
        rankOriginalDataModel4.setMonthRanking(3);
        rankOriginalDataModel4.setWeekOrderCnt(2);
        rankOriginalDataModel4.setWeekRanking(2);
        rankOriginalDataModels.add(rankOriginalDataModel4);
        new Expectations(){
            {
                honourFormConfig.getRankOpenCity();
                result = Collections.singletonList(cityId);

                honourDBDataService.queryMaxBatchDataInfo();
                result = admPrdTrhDriverHonorInfoPO;

                honourDBDataService.queryCityRankList(admPrdTrhDriverHonorInfoPO.getBatchTime(), cityId, true);
                result = rankOriginalDataModels;

                honourDBDataService.queryCityRankList(admPrdTrhDriverHonorInfoPO.getBatchTime(), cityId, false);
                result = rankOriginalDataModels;
            }
        };
        rankInfoServiceImpl.updateRankListForSchedule();
    }

    @Test
    public void updateRankListForManual() {
        long cityId = 2L;
        long cityId1 = 1L;
        new Expectations(){
            {
                honourFormConfig.getRankOpenCity();
                result = Arrays.asList(cityId, cityId1);
            }
        };
        rankInfoServiceImpl.updateRankListForManual(Collections.singletonList(cityId));
    }

    @Test
    public void sendRankUpdateNotice() {
        long cityId = 2L;
        new Expectations(){
            {
                honourFormConfig.getRankOpenCity();
                result = Collections.singletonList(cityId);

                tmsTransportServiceProxy.queryDriverList(cityId);
                result = Arrays.asList(10000004L, 1000005L);
            }
        };
        rankInfoServiceImpl.sendRankUpdateNotice(true);
    }
}


