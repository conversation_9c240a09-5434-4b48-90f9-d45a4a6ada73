package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.finance.QueryDriverFinanceInfoRequestType;
import com.ctrip.dcs.driver.domain.finance.QueryDriverFinanceInfoResponseType;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.supply.driver.dto.Balance;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.frt.product.soa.DriverBasicInfoType;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Mocked;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;
import java.util.concurrent.ExecutorService;


public class QueryDriverFinanceInfoEngineUnitTest extends TestBase {

    @Tested
    QueryDriverFinanceInfoEngine queryDriverFinanceInfoEngine;

    @Test
    public void test(@Mocked ExecutorService executorService, @Mocked FinanceObject financeObject) {
        QueryDriverFinanceInfoRequestType requestType = new QueryDriverFinanceInfoRequestType();
        requestType.driverId = 1000004L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverFinanceInfoResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;
        driverInfo.driverPhone = "13812345678";

        Balance balance = new Balance();
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;

                domainBeanFactory.executorService();
                result = executorService;
            }
        };

        QueryDriverFinanceInfoResponseType responseType = queryDriverFinanceInfoEngine.execute(requestType);
        Assert.assertNull(responseType.responseResult.returnCode);
    }

    @Test
    public void testException () {
        QueryDriverFinanceInfoRequestType requestType = new QueryDriverFinanceInfoRequestType();
        requestType.driverId = 1000004L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverFinanceInfoResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;
        driverInfo.driverPhone = "13812345678";

        Balance balance = new Balance();
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;
            }
        };

        QueryDriverFinanceInfoResponseType responseType = queryDriverFinanceInfoEngine.execute(requestType);
        Assert.assertNull(responseType.responseResult.returnCode);
    }

    @Test
    public void testDriverGuide(@Mocked FinanceObject financeObject) {
        QueryDriverFinanceInfoRequestType requestType = new QueryDriverFinanceInfoRequestType();
        requestType.driverId = 1000004L;
        requestType.source = "DriverGuide";
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverFinanceInfoResponseType.class).init();

        DriverBasicInfoType driverInfo = new DriverBasicInfoType();
        driverInfo.setDriverId(1L);
        driverInfo.setDrvPhone("13812345678");
        driverInfo.setDrvName("driver");
        driverInfo.setDrvStatus(1);

        new Expectations(){
            {
                domainBeanFactory.tourService().getDriverInfo(requestType.driverId);
                result = driverInfo;
            }
        };

        QueryDriverFinanceInfoResponseType responseType = queryDriverFinanceInfoEngine.execute(requestType);
        Assert.assertNull(responseType.responseResult.returnCode);
    }
}
