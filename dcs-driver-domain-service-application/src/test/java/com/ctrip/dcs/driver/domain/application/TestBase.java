package com.ctrip.dcs.driver.domain.application;

import com.ctrip.dcs.driver.domain.application.common.CommonCryptographicUtils;
import com.ctrip.dcs.driver.domain.application.common.CommonExamSSOUtils;
import com.ctrip.dcs.driver.domain.application.common.CommonExamSecretUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.http.HttpClient;
import com.ctrip.dcs.driver.domain.application.redis.ExamRedisLogic;
import com.ctrip.dcs.driver.domain.application.redis.HonourRedisLogic;
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.*;
import com.ctrip.dcs.driver.domain.application.service.impl.InfrastructureServiceProxyImpl;
import com.ctrip.dcs.driver.domain.application.service.impl.TmsTransportServiceProxyImpl;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.DaasGateway;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.*;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.HonourFormConfig;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideBaseInfoDTO;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import mockit.Injectable;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.ExecutorService;


public class TestBase {


    @Injectable
    public HonourFormConfig honourFormConfig;

    @Injectable
    public DomainBeanFactory domainBeanFactory;

    @Injectable
    public ArchCoreInfoRepository archCoreInfoService;
    @Injectable
    public GmsTransportDomainProxyService gmsTransportDomainProxyService;
    @Injectable
    public HonourDBDataService honourDBDataService;
    @Injectable
    private RightsRepoService rightsRepoService;
    @Injectable
    private RightsDBDataService rightsDBDataService;
    @Injectable
    private GuideApplyExamDBDataService guideApplyExamDBDataService;
    @Injectable
    private GuideExamScoreDBDataService guideExamScoreDBDataService;
    @Injectable
    private GuideInfoDBDataService guideInfoDBDataService;
    @Injectable
    private LevelConfig levelConfig;
    @Injectable
    private RightsConfig rightsConfig;
    @Injectable
    private WelfareConfig welfareConfig;
    @Injectable
    private DriverLevelGreyConfig driverLevelGreyConfig;
    @Injectable
    public ExamInterfaceConfig examInterfaceConfig;
    @Injectable
    public HttpClient httpClient;
    @Injectable
    public ExamRedisLogic examRedisLogic;
    @Injectable
    public GuideExamConfig guideExamConfig;
    @Injectable
    public GuideTagExamService guideTagExamService;
    @Injectable
    public CommonExamSecretUtils commonExamSecretUtils;
    @Injectable
    private RightsRedisLogic rightsRedisLogic;
    @Injectable
    public HonourRedisLogic honourRedisLogic;
    @Injectable
    public TmsTransportServiceProxyImpl tmsTransportServiceProxy;
    @Injectable
    public MedalInfoService medalInfoService;
    @Injectable
    public CommonExamSSOUtils commonExamSSOUtils;
    @Injectable
    public RankInfoService rankInfoService;
    @Injectable
    public CommonCryptographicUtils commonCryptographicUtils;
    @Injectable
    public HonourImgConfig honourImgConfig;
    @Injectable
    public ExecutorService CommonThreadPool;
    @Injectable
    public InfrastructureServiceProxyImpl infrastructureServiceProxy;
    @Injectable
    public FinanceConvertService financeConvertService;
    @Injectable
    public DaasGateway daasGateway;


    @Test
    public void test() {
        Assert.assertTrue(true);
    }

    public DriverInfo bulidSimpleDriverInfo() {
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.driverId = 123456L;
        driverInfo.driverName = "driver";
        driverInfo.drvIdcard = "3206FhWIwEdQR3r188";
        driverInfo.onlineTime = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        driverInfo.cityId = 2L;
        driverInfo.internalScope = 0;
        driverInfo.picUrl = "headImg";
        driverInfo.drvProductionLineCodeList = Collections.emptyList();
        return driverInfo;
    }

    public DriverInfo bulidDriverInfo() {
        DriverInfo driverInfo = new DriverInfo();
        driverInfo.driverId = 123456L;
        driverInfo.driverName = "driver";
        driverInfo.drvIdcard = "3206FhWIwEdQR3r188";
        driverInfo.onlineTime = LocalDateTime.now().minusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        driverInfo.cityId = 2L;
        driverInfo.internalScope = 0;
        driverInfo.picUrl = "headImg";
        driverInfo.drvProductionLineCodeList = Arrays.asList(1,3);

        return driverInfo;
    }

    public GuideBaseInfoDTO bulidGuideInfo() {
        GuideBaseInfoDTO baseInfoDTO = new GuideBaseInfoDTO();
        baseInfoDTO.guideId = 123L;
        baseInfoDTO.name = "guide";
        baseInfoDTO.identityCardNum = "3206FhWIwEdQR3r188";
        baseInfoDTO.onlineTime = LocalDateTime.now().minusYears(3).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        baseInfoDTO.cityId = 2L;
        baseInfoDTO.areaScope = 0;
        baseInfoDTO.headImageUrl = "img";
        return baseInfoDTO;
    }

}
