package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.setting.DriverPointRestfulPopInfoModel;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;


public class PointInfoRedisLogicTest {
    @Tested
    PointInfoRedisLogic pointInfoRedisLogic;

    @Injectable
    DirectorRedis directorRedis;
    
    @Test
    public void test(){
        long driverId = 1000004L;
        DriverPointRestfulShowInfoPO driverPointRestfulShowInfo = new DriverPointRestfulShowInfoPO();
        pointInfoRedisLogic.saveDriverPointInfo(driverId, driverPointRestfulShowInfo);

        DriverPointRestfulPopInfoModel driverPointRestfulPopInfo = new DriverPointRestfulPopInfoModel();
        driverPointRestfulPopInfo.setNovicePointGuidePop(1);
        driverPointRestfulPopInfo.setNovicePointVoidancePop(0);
        String strRedisInfo = JacksonUtil.serialize(driverPointRestfulPopInfo);
        new Expectations(){
            {
                directorRedis.get(String.format("driver:pointpop:%s", driverId));
                result = strRedisInfo;
            }
        };
        DriverPointRestfulPopInfoModel driverPointRestfulPopInfoResult = pointInfoRedisLogic.queryDriverPointInfo(driverId);
        Assert.assertEquals(1, driverPointRestfulPopInfoResult.getNovicePointGuidePop());
        Assert.assertEquals(0, driverPointRestfulPopInfoResult.getNovicePointVoidancePop());

        pointInfoRedisLogic.clearDriverPointInfo(driverId);
    }
}
