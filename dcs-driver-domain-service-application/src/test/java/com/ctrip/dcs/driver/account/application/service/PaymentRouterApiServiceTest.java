package com.ctrip.dcs.driver.account.application.service;

import com.ctrip.dcs.driver.account.domain.enums.BankCardResultEnum;
import com.ctrip.dcs.driver.gateway.PaymentRouterRepository;
import com.ctrip.dcs.driver.gateway.impl.PaymentRouterRepositoryImpl;
import com.ctrip.dcs.driver.value.bank.BankCardResultModel;
import com.ctrip.dcs.driver.gateway.impl.PaymentTokenUtils;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.payment.router.api.contract.dto.comm.BankChannelInfoDto;
import com.ctrip.payment.router.api.contract.dto.req.QueryBankChannelListRequestDto;
import com.ctrip.payment.router.api.contract.dto.rsp.QueryBankChannelListResponseDto;
import com.ctrip.payment.router.api.soa.PaymentRouterApiNonPciSoaClient;
import com.ctrip.payment.trade.infra.head.ResponseHead;
import com.ctrip.payment.trade.infra.unified.monad.UnifiedResponse;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Mocked;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;


public class PaymentRouterApiServiceTest {

    @Tested
    PaymentRouterRepositoryImpl paymentRouterApiService;

    @Injectable
    DriverWalletInfoConfig driverWalletInfoConfig;

    @Injectable
    PaymentTokenUtils paymentTokenUtils;

    @Test
    public void queryBankChannelInfo(@Mocked PaymentRouterApiNonPciSoaClient paymentRouterApiNonPciSoaClient) throws Exception {
        BankCardResultModel result = paymentRouterApiService.queryBankChannelInfo("123", "uid",false);
        Assert.assertEquals(BankCardResultEnum.ERROR.getCode(), result.getResultCode().getCode());

        UnifiedResponse<QueryBankChannelListResponseDto> responseType = new UnifiedResponse<>();
        ResponseHead responseHead = new ResponseHead();
        responseHead.setCode(200);
        responseType.setHead(responseHead);
        responseType.setBody(new QueryBankChannelListResponseDto());
        List<BankChannelInfoDto> bankChannelInfoList = new ArrayList<>();
        BankChannelInfoDto bankChannelInfo = new BankChannelInfoDto();
        bankChannelInfo.setCardType(2);
        bankChannelInfo.setBankName("test");
        bankChannelInfoList.add(bankChannelInfo);
        responseType.getBody().setBankChannelInfoList(bankChannelInfoList);
        new Expectations(){{
            driverWalletInfoConfig.getWalletPayMerid();
            result = "getWalletPayMerid";

            driverWalletInfoConfig.getWalletPayKeyid();
            result = "getWalletPayKeyid";

            paymentTokenUtils.driverPrivateEncryptKey();
            result = "driverPrivateEncryptKey";

            paymentTokenUtils.paymentPublicEncryptKey();
            result = "paymentPublicEncryptKey";

            driverWalletInfoConfig.getConnectTimeout();
            result = 3000;
            driverWalletInfoConfig.getRequestTimeout();
            result = 3000;
            driverWalletInfoConfig.getSocketTimeout();
            result = 3000;

            paymentRouterApiNonPciSoaClient.queryBankChannelList(this.withAny((QueryBankChannelListRequestDto) this.any));
            result = responseType;
        }};
        result = paymentRouterApiService.queryBankChannelInfo("123", "uid",false);
        Assert.assertEquals(2, result.getCardBankType());
        Assert.assertEquals("test", result.getCardBankName());
    }
}
