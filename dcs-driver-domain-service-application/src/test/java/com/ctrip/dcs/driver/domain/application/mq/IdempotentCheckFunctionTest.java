package com.ctrip.dcs.driver.domain.application.mq;

import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;

/**
 * Created by <AUTHOR> on 2023/3/8 19:40
 */

public class IdempotentCheckFunctionTest {

  @Tested
  IdempotentCheckFunction checkFunction;

  @Test
  public void apply() {
    Message message = new BaseMessage();
    checkFunction.apply(message);
    checkFunction.apply(null);
  }
}
