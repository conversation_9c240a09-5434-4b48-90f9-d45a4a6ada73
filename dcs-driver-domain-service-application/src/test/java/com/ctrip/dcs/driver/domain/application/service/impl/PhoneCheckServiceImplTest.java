package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskRequestType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskResponseType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitTaskStatus;
import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig;
import com.ctrip.dcs.driver.account.domain.enums.PhoneCheckTaskType;
import com.ctrip.dcs.driver.account.domain.value.PhoneCheckVoiceCallTask;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.PhoneNumberServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.VoiceCallServiceProxy;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory;
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService;
import com.ctrip.dcs.driver.domain.login.DriverLoginSuccessMessage;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import junit.framework.TestCase;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.context.ApplicationContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/15 16:11
 * @Description:
 */

public class PhoneCheckServiceImplTest  {
    @Tested
    PhoneCheckServiceImpl phoneCheckService;
    @Injectable
    private PhoneCheckTaskTable phoneCheckTaskTable;
    @Injectable
    private VoiceCallServiceProxy voiceCallServiceProxy;
    @Injectable
    private PhoneCheckConfig phoneCheckConfig;
    @Injectable
    PhoneCheckTable checkTable;
    @Injectable
    private PhoneCheckTaskFactory phoneCheckTaskFactory;
    @Injectable
    private CityRepository cityRepository;
    @Injectable
    private PhoneCheckTable phoneCheckTable;
    @Injectable
    private PhoneCheckConfig config;
    @Injectable
    private PhoneNumberServiceProxy phoneNumberService;
    @Injectable
    private DriverTable driverTable;
    @Injectable
    private CommonMultipleLanguages commonMultipleLanguages;
    @Injectable
    ArchCoreInfoServiceImpl archCoreInfoService;
    @Injectable
    private ApplicationContext context;

    @Test
    public void testIvrCall_configNull() {
        PhoneCheckTaskEntity task = fetchPhoneCheckTaskEntity();
        SubmitOutboundTaskResponseType responseType = new SubmitOutboundTaskResponseType();
        List<SubmitTaskStatus> taskListStatus = new ArrayList<>();
        SubmitTaskStatus taskStatus = new SubmitTaskStatus();
        taskStatus.setResultCode("0");
        taskListStatus.add(taskStatus);
        responseType.setTaskListStatus(taskListStatus);

        boolean success = phoneCheckService.ivrCall(task);
        Assert.assertFalse(success);
    }

    @Test
    public void testIvrCall() {
        PhoneCheckTaskEntity task = fetchPhoneCheckTaskEntity();
        SubmitOutboundTaskResponseType responseType = new SubmitOutboundTaskResponseType();
        List<SubmitTaskStatus> taskListStatus = new ArrayList<>();
        SubmitTaskStatus taskStatus = new SubmitTaskStatus();
        taskStatus.setResultCode("0");
        taskListStatus.add(taskStatus);
        responseType.setTaskListStatus(taskListStatus);
        new Expectations() {
            {
                phoneCheckConfig.getVoiceSkillFlowId("CN");
                result = "CN.flow";
//
//                phoneCheckConfig.getVoiceSpeechTemplate("CN");
//                result = "你的验证码为 {verificationCode}";

                commonMultipleLanguages.getContent("driver.voiceCode.tts.textContent", "CN");
                result = "你的验证码为 {verificationCode}";

                voiceCallServiceProxy.submitOutboundTask((SubmitOutboundTaskRequestType) any);
                result = responseType;
            }
        };
        boolean success = phoneCheckService.ivrCall(task);
        Assert.assertTrue(success);
    }

    private PhoneCheckTaskEntity fetchPhoneCheckTaskEntity() {
        PhoneCheckTaskEntity task = new PhoneCheckTaskEntity();
        task.setTaskState(0);
        task.setTaskType(2);
        task.setDriverId(1111L);
        task.setPhoneNumber("***********");
        task.setPhonePrefix("86");
        PhoneCheckVoiceCallTask checkVoiceCallTask = PhoneCheckVoiceCallTask.builder()
                .driverCityId(21L)
                .verificationCode("222333")
                .driverLanguages(Arrays.asList("CN"))
                .build();
        task.setTaskContent(JsonUtil.toString(checkVoiceCallTask));
        return task;
    }

    @Test
    public void testGeneratePhoneCheckTaskByDriver1() {
        DriverEntity driver = fetchDriverEntity();
        City city = City.builder().chineseMainland(true).build();
        new Expectations() {
            {
                cityRepository.findOne(driver.getCityId());
                result = city;
            }
        };
        phoneCheckService.generatePhoneCheckTaskByDriver(driver, null);
        Assert.assertEquals("***********", driver.getPhoneNumber());
        Assert.assertEquals("86", driver.getPhonePrefix());
    }

    private DriverEntity fetchDriverEntity() {
        DriverEntity driver = new DriverEntity();
        driver.setDriverId(1111L);
        driver.setPhonePrefix("86");
        driver.setPhoneNumber("***********");
        driver.setDriverLanguage("EN");
        driver.setDriverName("张三");
        driver.setSupplierId(2333444L);
        return driver;
    }

    @Test
    public void testGeneratePhoneCheckTaskByDriver2() {
        DriverEntity driver = fetchDriverEntity();
        new Expectations() {
            {
                phoneCheckTable.count((PhoneCheckEntity) any);
                result = 1;
            }
        };
        phoneCheckService.generatePhoneCheckTaskByDriver(driver, null);
        Assert.assertEquals("***********", driver.getPhoneNumber());
        Assert.assertEquals("86", driver.getPhonePrefix());
    }

    @Test
    public void testGeneratePhoneCheckTaskByDriver3() {
        DriverEntity driver = fetchDriverEntity();
        new Expectations() {
            {
                phoneCheckTaskTable.count((SelectSqlBuilder) any);
                result = 1;
            }
        };
        phoneCheckService.generatePhoneCheckTaskByDriver(driver, null);
        Assert.assertEquals("***********", driver.getPhoneNumber());
        Assert.assertEquals("86", driver.getPhonePrefix());
    }

    @Test
    public void testGeneratePhoneCheckTaskByDriver4() {
        DriverEntity driver = fetchDriverEntity();
        SplitNumberResponseType responseType = new SplitNumberResponseType();
        new Expectations() {
            {
                phoneNumberService.splitNumber((SplitNumberRequestType) any);
                result = responseType;
            }
        };
        phoneCheckService.generatePhoneCheckTaskByDriver(driver, null);
        Assert.assertEquals("***********", driver.getPhoneNumber());
        Assert.assertEquals("86", driver.getPhonePrefix());
    }

    @Test
    public void testGeneratePhoneCheckTaskByDriver5() {
        DriverEntity driver = fetchDriverEntity();

        PhoneCheckTaskEntity task = fetchPhoneCheckTaskEntity();

        SplitNumberResponseType responseType = new SplitNumberResponseType();
        NumberDTO numberDTO = new NumberDTO();
        numberDTO.setValid(true);
        responseType.setResult(numberDTO);
        new Expectations() {
            {
                phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.IVR_CALL,null);
                result = task;

                phoneNumberService.splitNumber((SplitNumberRequestType) any);
                result = responseType;
            }
        };
        phoneCheckService.generatePhoneCheckTaskByDriver(driver, null);
        Assert.assertEquals("***********", driver.getPhoneNumber());
        Assert.assertEquals("86", driver.getPhonePrefix());
    }

    @Test
    public void testUpdateDriverPhoneCheckRes() {
        DriverLoginSuccessMessage message = new DriverLoginSuccessMessage();
        message.setLoginType(3);
        message.setDriverId(1111L);
        message.setCountryCode("86");
        message.setLoginAccount("***********");

        DriverEntity driver = fetchDriverEntity();

        new Expectations() {
            {
                driverTable.queryByPk(message.getDriverId());
                result = driver;

                context.getBean(PhoneCheckService.class);
                result = phoneCheckService;
            }
        };
        phoneCheckService.updateDriverPhoneCheckRes(message);
        Assert.assertEquals("***********", driver.getPhoneNumber());
        Assert.assertEquals("86", driver.getPhonePrefix());
    }

    @Test
    public void testUpdateDriverPhoneCheckRes2() {
        DriverLoginSuccessMessage message = new DriverLoginSuccessMessage();
        message.setLoginType(3);
        message.setDriverId(1111L);
        message.setCountryCode("86");
        message.setLoginAccount("***********");

        new Expectations() {
            {
                driverTable.queryByPk(message.getDriverId());
                result = null;
            }
        };
        phoneCheckService.updateDriverPhoneCheckRes(message);
        Assert.assertEquals("***********", message.getLoginAccount());
        Assert.assertEquals("86", message.getCountryCode());
    }
    @Test
    public void testUpdateDriverPhoneCheckRes4() {
        DriverLoginSuccessMessage message = new DriverLoginSuccessMessage();
        message.setLoginType(5);
        message.setDriverId(1111L);
        message.setCountryCode("86");
        message.setLoginAccount("***********");

        phoneCheckService.updateDriverPhoneCheckRes(message);
        Assert.assertEquals("***********", message.getLoginAccount());
        Assert.assertEquals("86", message.getCountryCode());
    }

    @Test
    public void testUpdateDriverPhoneCheckRes5() {
        DriverLoginSuccessMessage message = new DriverLoginSuccessMessage();
        message.setLoginType(3);
        message.setDriverId(1111L);
        message.setCountryCode("86");
        message.setLoginAccount("***********");

        DriverEntity driver = fetchDriverEntity();

        City city = City.builder().chineseMainland(true).build();
        new Expectations() {
            {
                driverTable.queryByPk(message.getDriverId());
                result = driver;

                cityRepository.findOne(driver.getCityId());
                result = city;
            }
        };
        phoneCheckService.updateDriverPhoneCheckRes(message);
        Assert.assertEquals("***********", message.getLoginAccount());
        Assert.assertEquals("86", message.getCountryCode());
    }

    @Test
    public void testUpdateDriverPhoneCheckRes3() {
        DriverLoginSuccessMessage message = new DriverLoginSuccessMessage();
        message.setLoginType(3);
        message.setDriverId(1111L);
        message.setCountryCode("86");
        message.setLoginAccount("***********");

        DriverEntity driver = fetchDriverEntity();

        new Expectations() {
            {
                driverTable.queryByPk(message.getDriverId());
                result = driver;

                checkTable.queryOne((PhoneCheckEntity) any);
                result = null;
                context.getBean(PhoneCheckService.class);
                result = phoneCheckService;
            }
        };
        phoneCheckService.updateDriverPhoneCheckRes(message);
        Assert.assertEquals("***********", message.getLoginAccount());
        Assert.assertEquals("86", message.getCountryCode());
    }
}
