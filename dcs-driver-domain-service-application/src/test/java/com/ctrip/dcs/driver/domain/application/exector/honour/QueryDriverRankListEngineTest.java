package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.honour.QueryDriverRankListRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryDriverRankListResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankCityDataModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


public class QueryDriverRankListEngineTest extends TestBase {

    @Tested
    QueryDriverRankListEngine queryDriverRankListEngine;

    @Test
    public void execute() {
        DriverInfo driverInfo = bulidDriverInfo();
        QueryDriverRankListRequestType requestType = new QueryDriverRankListRequestType();
        requestType.setDriverId(driverInfo.driverId);
        requestType.setPageNo(1);
        requestType.setPageSize(50);
        requestType.setType(1);
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverRankListResponseType.class).init();

        RankOriginalDataModel rankOriginalDataModel = new RankOriginalDataModel();
        rankOriginalDataModel.setCityId(driverInfo.cityId);
        rankOriginalDataModel.setBatchTime(1L);
        rankOriginalDataModel.setDataTime("2022-08-08 16:59:00");
        rankOriginalDataModel.setDataTimeWeek("2022-08-08");
        rankOriginalDataModel.setDataTimeMonth("202208");

        List<RankCityDataModel> cacheRankList = new ArrayList<>();
        RankCityDataModel rankCityDataModel0 = new RankCityDataModel();
        rankCityDataModel0.setDrvId(driverInfo.driverId);
        rankCityDataModel0.setDrvName("driver");
        rankCityDataModel0.setOrderCnt(2);
        cacheRankList.add(rankCityDataModel0);

        RankCityDataModel rankCityDataModel1 = new RankCityDataModel();
        rankCityDataModel1.setDrvId(654321L);
        rankCityDataModel1.setDrvName("driver1");
        rankCityDataModel1.setOrderCnt(1);
        cacheRankList.add(rankCityDataModel1);
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverInfo.driverId);
                result = driverInfo;

                domainBeanFactory.honourFormConfig().isRankOpenCity(driverInfo.cityId);
                result = true;

                domainBeanFactory.honourRedisLogic().getCurrentRankInfo();
                result = rankOriginalDataModel;

                domainBeanFactory.commonCryptographicUtils().encryptByDES(this.withAny(anyString));
                result = "refs";

                domainBeanFactory.honourRedisLogic()
                        .queryCityRankData(driverInfo.cityId, requestType.type == 1);
                result = cacheRankList;

            }
        };
        new Expectations(){
            {

                domainBeanFactory.honourRedisLogic().queryDriverRankData(driverInfo.driverId,true);
                result = null;
            }
        };
        queryDriverRankListEngine.execute(requestType);

        QueryDriverRankListResponseType responseType = queryDriverRankListEngine.execute(requestType);
        Assert.assertEquals(responseType.rankList.size(), 2);

        requestType.setType(2);
        new Expectations(){
            {
                domainBeanFactory.honourRedisLogic()
                        .queryCityRankData(driverInfo.cityId, requestType.type == 1);
                result = cacheRankList;
            }
        };
        responseType = queryDriverRankListEngine.execute(requestType);
        Assert.assertEquals(responseType.rankList.size(), 2);
    }
}
