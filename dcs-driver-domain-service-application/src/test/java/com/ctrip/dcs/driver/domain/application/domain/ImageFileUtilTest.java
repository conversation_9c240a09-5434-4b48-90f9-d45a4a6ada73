package com.ctrip.dcs.driver.domain.application.domain;

import com.ctrip.dcs.driver.domain.application.util.ImageFileUtil;
import com.ctrip.tour.driver.utility.log.LogHelper;
import com.dianping.cat.Cat;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.FluentCaseInsensitiveStringsMap;
import com.ning.http.client.Response;
import com.ning.http.client.cookie.Cookie;
import com.ning.http.client.uri.Uri;
import lombok.SneakyThrows;
import mockit.Mock;
import mockit.MockUp;
import org.junit.Assert;
import org.junit.Test;
import qunar.hc.QunarAsyncClient;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.util.Base64;
import java.util.List;

public class ImageFileUtilTest {

    @Test
    public void test_getImageTypeFromBuf() {
        byte[] buf = {(byte) 0xFF, (byte) 0xD8, (byte) 0xFF};
        String type = ImageFileUtil.getImageTypeFromBuf(buf);
        Assert.assertEquals("jpg", type);

        buf = new byte[]{(byte) 0x89, 0x50, 0x4E, 0x47};
        type = ImageFileUtil.getImageTypeFromBuf(buf);
        Assert.assertEquals("png", type);
    }

    @SneakyThrows
    @Test
    public void test_compressImage() {
        int[] cmp = {1};
        new MockUp<BigDecimal>() {
            @Mock
            public int compareTo(BigDecimal val) {
                return cmp[0];
            }
        };
        String base64Str = "iVBORw0KGgoAAAANSUhEUgAAAAIAAAABCAIAAAB7QOjdAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAEXRFWHRTb2Z0d2FyZQBTbmlwYXN0ZV0Xzt0AAAAPSURBVAiZY/z//z8DAwMADv8C/zo6KlgAAAAASUVORK5CYII=";
        byte[] output = ImageFileUtil.compressImage(Base64.getDecoder().decode(base64Str), "png", BigDecimal.valueOf(1024d * 500));
        Assert.assertTrue(output.length > 0);

        cmp[0] = -1;
        output = ImageFileUtil.compressImage(Base64.getDecoder().decode(base64Str), "png", BigDecimal.valueOf(1024d * 500));
        Assert.assertTrue(output.length > 0);
    }

    @SneakyThrows
    @Test
    public void test_downloadImage() {
        new MockUp<Cat>() {
            @Mock
            public static void logEvent(String type, String name) {}
        };
        new MockUp<LogHelper>() {
            @Mock
            public static void info(String title, String message) {}
        };
        boolean[] hasRes = {false};
        new MockUp<QunarAsyncClient>() {
            @Mock
            public ListenableFuture<Response> get(final String url) throws IOException {
                if (!hasRes[0]) {
                    return Futures.immediateFuture(null);
                }
                return Futures.immediateFuture(new Response() {
                    @Override
                    public int getStatusCode() {
                        return 0;
                    }

                    @Override
                    public String getStatusText() {
                        return "";
                    }

                    @Override
                    public byte[] getResponseBodyAsBytes() throws IOException {
                        return new byte[0];
                    }

                    @Override
                    public ByteBuffer getResponseBodyAsByteBuffer() throws IOException {
                        return null;
                    }

                    @Override
                    public InputStream getResponseBodyAsStream() throws IOException {
                        return null;
                    }

                    @Override
                    public String getResponseBodyExcerpt(int maxLength, String charset) throws IOException {
                        return "";
                    }

                    @Override
                    public String getResponseBody(String charset) throws IOException {
                        return "";
                    }

                    @Override
                    public String getResponseBodyExcerpt(int maxLength) throws IOException {
                        return "";
                    }

                    @Override
                    public String getResponseBody() throws IOException {
                        return "";
                    }

                    @Override
                    public Uri getUri() {
                        return null;
                    }

                    @Override
                    public String getContentType() {
                        return "";
                    }

                    @Override
                    public String getHeader(String name) {
                        return "";
                    }

                    @Override
                    public List<String> getHeaders(String name) {
                        return List.of();
                    }

                    @Override
                    public FluentCaseInsensitiveStringsMap getHeaders() {
                        return null;
                    }

                    @Override
                    public boolean isRedirected() {
                        return false;
                    }

                    @Override
                    public List<Cookie> getCookies() {
                        return List.of();
                    }

                    @Override
                    public boolean hasResponseStatus() {
                        return false;
                    }

                    @Override
                    public boolean hasResponseHeaders() {
                        return false;
                    }

                    @Override
                    public boolean hasResponseBody() {
                        return false;
                    }
                });
            }
        };
        String fileName = "https://dimg04.c-ctrip.com/images/abc.png";
        byte[] buf = ImageFileUtil.downloadImage(fileName);
        Assert.assertNull(buf);
        hasRes[0] = true;
        buf = ImageFileUtil.downloadImage(fileName);
        Assert.assertNotNull(buf);
    }

    @SneakyThrows
    @Test
    public void test_downloadThenCompressImage() {
        byte[][] downloadImg = new byte[][]{null};
        String[] type = {null};
        boolean[] thrEx = {false};
        new MockUp<ImageFileUtil>() {
            @Mock
            public byte[] downloadImage(String fileName) throws Exception {
                return downloadImg[0];
            }
            @Mock
            public String getImageTypeFromBuf(byte[] bytes) {
                return type[0];
            }
            @Mock
            public byte[] compressImage(byte[] data, String fileType, BigDecimal maxCompressSize) throws Exception {
                if (thrEx[0]) {
                    throw new RuntimeException("mock");
                }
                return new byte[0];
            }
        };
        boolean[] logError = {false};
        new MockUp<LogHelper>() {
            @Mock
            public void error(Throwable throwable) {
                logError[0] = true;
            }
        };
        byte[] buf = ImageFileUtil.downloadThenCompressImage(null);
        Assert.assertNull(buf);
        downloadImg[0] = new byte[]{0, 0, 1, 1};
        buf = ImageFileUtil.downloadThenCompressImage(null);
        Assert.assertNull(buf);
        type[0] = "png";
        buf = ImageFileUtil.downloadThenCompressImage(null);
        Assert.assertNotNull(buf);
        thrEx[0] = true;
        buf = ImageFileUtil.downloadThenCompressImage(null);
        Assert.assertNull(buf);
        Assert.assertTrue(logError[0]);
    }

}
