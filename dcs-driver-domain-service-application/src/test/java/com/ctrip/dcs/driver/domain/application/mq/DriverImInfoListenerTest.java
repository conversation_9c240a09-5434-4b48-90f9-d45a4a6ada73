package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.DriverGreyConfig;
import com.ctrip.dcs.im.groupchat.dto.GroupChatMessageDto;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.im.interfaces.service.implus.message.ImPlusQueryImUrlRequestType;
import com.ctrip.igt.im.interfaces.service.implus.message.ImPlusQueryImUrlResponseType;
import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.base.BaseMessage;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;


public class DriverImInfoListenerTest {

    @Injectable
    DomainBeanFactory domainBeanFactory;

    @Injectable
    DirectorRedis directorRedis;

    @Injectable
    DriverGreyConfig driverGreyConfig;

    @Injectable
    ExecutorService CommonThreadPool;

    @Injectable
    DriverMessageServiceProxy driverMessageServiceProxy;

    @Injectable
    CommonMultipleLanguages commonMultipleLanguages;

    @Tested
    DriverImInfoListener driverImInfoListener;

    @Test
    public void onMessage() {
        this.driverImInfoListener.onMessage(null);
        Message message = new BaseMessage();
        this.driverImInfoListener.onMessage(message);

        message.setProperty("data1", Strings.EMPTY);
        this.driverImInfoListener.onMessage(message);

        message.setProperty("content", Strings.EMPTY);
        this.driverImInfoListener.onMessage(message);

        Map<String, Object> params = new HashMap<>();
        message.setProperty("content", "1");
        this.driverImInfoListener.onMessage(message);

        params.put("orderId", "26857165611");
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("messageBody", "im test message");
        params.put("sendRole", "2");
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("sendRole", "1");
        params.put("toRole", "1");
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("toRole", "2");
        params.put("driverId", Strings.EMPTY);
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("driverId", "notNumeric");
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("driverId", 0);
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("driverId", 3452903L);
        params.put("messageType", 99);
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("messageType", 0);
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        params.put("messageType", 1);
        message.setProperty("content", JacksonUtil.serialize(params));
        this.driverImInfoListener.onMessage(message);

        Assert.assertNotNull(message);
    }


    @Test
    public void pushMessage(){
        GroupChatMessageDto dataInfo = new GroupChatMessageDto();
        dataInfo.setDriverId("3452903");
        dataInfo.setOrderId("26857165611");
        dataInfo.setMessageBody("im test message");
        dataInfo.setMessageType(1);
        dataInfo.setGroupTitle("10-08 Singapore Airport Drop-off service group");

        new Expectations(){
            {
                directorRedis.get(String.format("version_new:%s", dataInfo.driverId));
                result = Strings.EMPTY;

                domainBeanFactory.tmsTransportServiceProxy().queryDriver(Long.parseLong(dataInfo.driverId));
                result = null;
            }
        };
        this.driverImInfoListener.pushMessage(dataInfo);

        DriverInfo driverInfo = new DriverInfo();
        driverInfo.cityId = 2L;
        driverInfo.countryId = 1L;
        new Expectations(){
            {
                directorRedis.get(String.format("version_new:%s", dataInfo.driverId));
                result = "8355#2023101900";

                domainBeanFactory.tmsTransportServiceProxy().queryDriver(Long.parseLong(dataInfo.driverId));
                result = driverInfo;

                driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, this.anyLong, this.anyLong, this.withAny((BigDecimal) this.any));
                result = true;
            }
        };
        this.driverImInfoListener.pushMessage(dataInfo);

        ImPlusQueryImUrlResponseType responseType = new ImPlusQueryImUrlResponseType();
        responseType.isSupportIM = true;
        new Expectations(){
            {
                domainBeanFactory.imServiceProxy().imPlusQueryImUrl(this.withAny((ImPlusQueryImUrlRequestType) this.any));
                result = responseType;
            }
        };
        this.driverImInfoListener.pushMessage(dataInfo);
        Assert.assertNotNull(dataInfo);

        dataInfo.setMessageBody("im test message length is over 20012345678901011121314151617181920212223242526272829303132333435363738394041424344454647484950515253545556575859606162636465666768697071727374757677787980818283848586878889909192");
        dataInfo.setMessageType(0);
        this.driverImInfoListener.pushMessage(dataInfo);

        dataInfo.setMessageType(1009);
        this.driverImInfoListener.pushMessage(dataInfo);
        Assert.assertNotNull(dataInfo);
    }
}
