package com.ctrip.dcs.driver.domain.application.redis;

import com.google.common.collect.ImmutableMap;
import credis.java.client.CacheProvider;
import mockit.Deencapsulation;
import mockit.Injectable;
import mockit.Mock;
import mockit.MockUp;
import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;
import java.util.Map;


public class DirectorRedisTest {
    @Tested
    DirectorRedis directorRedis;

    @Injectable
    private CacheProvider redisProvider;

    @Test
    public void test(){
        try {
            MockUp<CacheProvider> cacheProviderMockUp = new MockUp<CacheProvider>() {
                @Mock
                public String get(String key){
                    return "";
                }
                @Mock
                public String hget(String key, String field){
                    return "";
                }
                @Mock
                public Boolean set(String key, String value){
                    return true;
                }
                @Mock
                public Boolean hset(String key, String field, String value){
                    return true;
                }
                @Mock
                public Long ttl(String key){
                    return 12L;
                }
                @Mock
                public Boolean setnx(String key, String value){
                    return true;
                }
                @Mock
                public Boolean hmset(String key, Map<String, String> keyValues){
                    return true;
                }
            };

            Deencapsulation.setField(directorRedis, "redisProvider", cacheProviderMockUp.getMockInstance());

            directorRedis.ttl("123");
            directorRedis.type("456");
            directorRedis.get("123");
            directorRedis.hget("123", "456");
            directorRedis.set("123", "456");
            directorRedis.hset("123", "456", "123", 123);
            directorRedis.hmset("123", ImmutableMap.of("1000004", "1"), 10);
            directorRedis.setNx("123", "234", 10);

        }catch (Exception e){

        }
    }
}
