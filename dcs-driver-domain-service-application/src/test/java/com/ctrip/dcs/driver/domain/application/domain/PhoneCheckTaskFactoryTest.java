package com.ctrip.dcs.driver.domain.application.domain;

import java.time.LocalDateTime;

import org.junit.Assert;
import org.junit.Test;

import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig;
import com.ctrip.dcs.driver.account.domain.enums.PhoneCheckTaskType;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverDeviceInfoDao;
import com.ctrip.dcs.geo.domain.repository.TimeZoneRepository;

import mockit.Expectations;
import mockit.Injectable;
import mockit.Tested;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/15 17:16
 * @Description:
 */

public class PhoneCheckTaskFactoryTest  {

    @Tested
    PhoneCheckTaskFactory phoneCheckTaskFactory;

    @Injectable
    private PhoneCheckConfig config;
    @Injectable
    private TimeZoneRepository timeZoneRepository;
    @Injectable
    private DriverDeviceInfoDao driverDeviceInfoDao;

    @Test
    public void testApplyVoiceTask() {
     DriverEntity driver = fetchDriverEntity();

     PhoneCheckTaskEntity taskEntity = phoneCheckTaskFactory.applyVoiceTask(driver, "233333", "EN");
        Assert.assertTrue (taskEntity.getTaskContent().contains("EN"));
    }

    @Test
    public void testApply() {
        // 准备测试数据
        DriverEntity driver = fetchDriverEntity();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localTime = now.plusDays(1).withHour(10);
        LocalDateTime bjTime = localTime.plusHours(2);

        // 设置必要的模拟行为
        new Expectations() {{
            // 模拟driverDeviceInfoDao.findOne返回null
            driverDeviceInfoDao.findOne(anyLong);
            result = null;

            // 模拟config的行为
            config.getFirstCallDay();
            result = 1;

            config.getFirstCallHour();
            result = 10;

            // 模拟timeZoneRepository的行为
            timeZoneRepository.transform(anyLong, (LocalDateTime) any, anyLong);
            result = localTime;

            timeZoneRepository.transform(anyLong, (LocalDateTime) any, anyLong);
            result = bjTime;
        }};

        // 执行测试方法
        PhoneCheckTaskEntity taskEntity = phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.IVR_CALL, null);

        // 验证结果
        Assert.assertEquals(3, (int) taskEntity.getTaskType());
    }

    /**
     * 测试当提供planTime时的apply方法
     */
    @Test
    public void testApplyWithPlanTime() {
        // 准备测试数据
        DriverEntity driver = fetchDriverEntity();
        LocalDateTime planTime = LocalDateTime.now().plusHours(1);

        // 执行测试方法
        PhoneCheckTaskEntity taskEntity = phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.IVR_CALL, planTime);

        // 验证结果
        Assert.assertEquals(3, (int) taskEntity.getTaskType());
        Assert.assertEquals(planTime, taskEntity.getPlanTime());
    }

    /**
     * 测试当DriverDeviceInfoPO为null但有有效驱动语言时的apply方法
     */
    @Test
    public void testApplyWithValidDriverLanguage() {
        // 准备测试数据
        DriverEntity driver = fetchDriverEntity();
        driver.setDriverLanguage("FR,EN");

        // 设置模拟行为
        new Expectations() {{
            driverDeviceInfoDao.findOne(anyLong);
            result = null;
        }};

        // 执行测试方法
        PhoneCheckTaskEntity taskEntity = phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.PHONE_VERIFY, null);

        // 验证结果
        Assert.assertTrue(taskEntity.getTaskContent().contains("FR") || taskEntity.getTaskContent().contains("EN"));
    }

    /**
     * 测试当DriverDeviceInfoPO为null且无有效驱动语言时的apply方法，使用国家ID
     */
    @Test
    public void testApplyWithCountryId() {
        // 准备测试数据
        DriverEntity driver = fetchDriverEntity();
        driver.setDriverLanguage("XX"); // 无效语言
        driver.setCountryId(1L); // 中国

        // 设置模拟行为
        new Expectations() {{
            driverDeviceInfoDao.findOne(anyLong);
            result = null;
        }};

        // 执行测试方法
        PhoneCheckTaskEntity taskEntity = phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.SMS, null);

        // 验证结果
        Assert.assertTrue(taskEntity.getTaskContent().contains("CN"));
    }

    /**
     * 测试非IVR类型的apply方法
     */
    @Test
    public void testApplyNonIvr() {
        // 准备测试数据
        DriverEntity driver = fetchDriverEntity();

        // 执行测试方法 - SMS不是IVR类型
        PhoneCheckTaskEntity taskEntity = phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.SMS, null);

        // 验证结果 - 应该没有planTime
        Assert.assertNull(taskEntity.getPlanTime());
    }

    /**
     * 测试IVR类型且计算本地时间的apply方法
     */
    @Test
    public void testApplyIvrWithLocalTime() {
        // 准备测试数据
        DriverEntity driver = fetchDriverEntity();
        driver.setCityId(2L);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime localTime = now.plusDays(1).withHour(10);
        LocalDateTime bjTime = localTime.plusHours(2); // 模拟北京时间比本地时间快2小时

        // 设置模拟行为
        new Expectations() {{
            config.getFirstCallDay();
            result = 1;

            config.getFirstCallHour();
            result = 10;

            timeZoneRepository.transform(PhoneCheckTaskFactory.BJ_CITY, (LocalDateTime) any, driver.getCityId());
            result = now.plusDays(1).withHour(10);

            timeZoneRepository.transform(driver.getCityId(), (LocalDateTime) any, PhoneCheckTaskFactory.BJ_CITY);
            result = bjTime;
        }};

        // 执行测试方法
        PhoneCheckTaskEntity taskEntity = phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.IVR_CALL, null);

        // 验证结果
        Assert.assertEquals(bjTime, taskEntity.getPlanTime());
    }

 private DriverEntity fetchDriverEntity() {
  DriverEntity driver = new DriverEntity();
  driver.setDriverId(1111L);
  driver.setPhonePrefix("86");
  driver.setPhoneNumber("18899989898");
  driver.setDriverLanguage("EN");
  driver.setDriverName("张三");
  driver.setSupplierId(2333444L);
  driver.setCityId(1L);
  return driver;
 }
}
