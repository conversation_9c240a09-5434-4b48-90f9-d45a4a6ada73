package com.ctrip.dcs.driver.domain.application.domain;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.application.common.DriverLevelHelper;
import com.ctrip.dcs.driver.domain.application.service.TourService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.infrastructure.IMServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.FinanceConfig;
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig;
import mockit.Injectable;
import mockit.Tested;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.spockframework.util.Assert;


public class DomainBeanFactoryTest extends TestBase {

    @Tested
    DomainBeanFactory domainBeanFactory;

    @Injectable
    FinanceConfig financeConfig;

    @Injectable
    IMServiceProxy imServiceProxy;

    @Injectable
    TourService tourService;

    @Injectable
    DriverLevelHelper driverLevelHelper;

    @Injectable
    GeoGateway geoGateway;

    @Injectable
    TripUniversityQconfig tripUniversityQconfig;

    @Test
    public void test() {
        Assert.notNull(domainBeanFactory.archCoreInfoService());
        Assert.notNull(domainBeanFactory.commonCryptographicUtils());
        Assert.notNull(domainBeanFactory.gmsTransportDomainServiceProxy());
        Assert.notNull(domainBeanFactory.honourDBDataService());
        Assert.notNull(domainBeanFactory.honourFormConfig());
        Assert.notNull(domainBeanFactory.honourImgConfig());
        Assert.notNull(domainBeanFactory.honourRedisLogic());
        Assert.notNull(domainBeanFactory.medalInfoService());
        Assert.notNull(domainBeanFactory.rankInfoService());
        Assert.notNull(domainBeanFactory.tmsTransportServiceProxy());
        Assert.notNull(domainBeanFactory.executorService());
        Assert.notNull(domainBeanFactory.infrastructureServiceProxy());
        Assert.notNull(domainBeanFactory.financeConvertService());
        Assert.notNull(domainBeanFactory.financeConfig());
        Assert.notNull(domainBeanFactory.imServiceProxy());
        Assert.notNull(domainBeanFactory.tourService());
        Assert.notNull(domainBeanFactory.driverLevelHelper());
        Assert.notNull(domainBeanFactory.geoGateway());
    }
}
