package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.TestBase;
import com.ctrip.dcs.driver.domain.finance.FinanceWithdrawRequestType;
import com.ctrip.dcs.driver.domain.finance.FinanceWithdrawResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import mockit.Expectations;
import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.UUID;


public class FinanceWithdrawEngineUnitTest extends TestBase {

    @Tested
    FinanceWithdrawEngine financeWithdrawEngine;

    @Test
    public void test() {
        FinanceWithdrawRequestType requestType = new FinanceWithdrawRequestType();
        requestType.driverId = 1000004L;
        ServiceExecuteContext.newContext()
                .withRequest(requestType)
                .withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(FinanceWithdrawResponseType.class).init();

        DriverInfo driverInfo = bulidSimpleDriverInfo();
        driverInfo.status = 1;
        driverInfo.driverPhone = "13812345678";
        new Expectations(){
            {
                domainBeanFactory.tmsTransportServiceProxy().queryDriver(requestType.driverId);
                result = driverInfo;

                domainBeanFactory.financeConvertService().driverWithdraw(this.withAny((WithdrawModel) this.any));
                result = FinanceResultEnum.OK.getCode();
            }
        };

        FinanceWithdrawResponseType responseType = financeWithdrawEngine.execute(requestType);
        Assert.assertEquals(FinanceResultEnum.OK.getCode(), responseType.responseResult.returnCode);
    }
}
