package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.application.redis.AppControlRedisLogic
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverAppGuidanceControlDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverAppGuidanceControlPO
import com.google.common.collect.ImmutableMap
import com.google.common.collect.Lists
import spock.lang.Specification

class DriverAppControlServiceImplTest extends Specification {

    def dao = Mock(DriverAppGuidanceControlDao.class)
    def cache = Mock(AppControlRedisLogic.class)
    def driverAppControlServiceImpl = new DriverAppControlServiceImpl(
            dao: dao,
            cache: cache
    )

    def testQueryDriverAppGuidance() {
        given:
        cache.getGuidance(_) >> cacheMap
        dao.findMany(_, _) >> guidanceList

        when:
        def result = driverAppControlServiceImpl.queryDriverAppGuidance("", guidanceTypes)

        then:
        b == result.getOrDefault("HOME_SCORE", false)

        where:
        b     | cacheMap                              | guidanceTypes                    | guidanceList
        true  | Collections.emptyMap()                | Lists.newArrayList("HOME_SCORE") | Lists.newArrayList(new DriverAppGuidanceControlPO(guidanceType: "HOME_SCORE"))
        false | Collections.emptyMap()                | Lists.newArrayList("HOME_SCORE") | Collections.emptyList()
        true  | ImmutableMap.of("HOME_SCORE", "true") | Lists.newArrayList("HOME_SCORE") | Collections.emptyList()
    }

    def testSaveDriverAppGuidance() {
        when:
        driverAppControlServiceImpl.saveDriverAppGuidance("123", "HOME_SCORE")

        then:
        1 * dao.insert(_)
    }
}
