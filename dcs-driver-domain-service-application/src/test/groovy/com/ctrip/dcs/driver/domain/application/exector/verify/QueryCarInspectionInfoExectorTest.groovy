package com.ctrip.dcs.driver.domain.application.exector.verify

import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverCarInspectionRecordDO
import com.ctrip.dcs.driver.domain.task.QueryCarInspectionInfoRequestType
import com.ctrip.dcs.driver.domain.task.QueryCarInspectionInfoResponseType
import spock.lang.Specification

class QueryCarInspectionInfoExectorTest extends Specification {

    QueryCarInspectionInfoExector executor
    DriverTaskAdapter driverTaskAdapter = Mock(DriverTaskAdapter)

    def setup() {
        executor = new QueryCarInspectionInfoExector(driverTaskAdapter: driverTaskAdapter)
    }

    def "querycarinspectioninfotest"() {
        given:
        def request = new QueryCarInspectionInfoRequestType(driverId:123456L, taskId: "TASK_001")
        def record1 = new DriverCarInspectionRecordDO(
                taskStepKey: "KEY_1",
                taskStepValue: "VALUE_1"
        )
        def record2 = new DriverCarInspectionRecordDO(
                taskStepKey: "KEY_2",
                taskStepValue: "VALUE_2"
        )

        when:
        def response = executor.execute(request)

        then:
        1 * driverTaskAdapter.queryCarInspectionInfoList("TASK_001") >> [record1, record2]

        response.carInspectionList.size() == 2
        response.carInspectionList*.taskStepKey == ["KEY_1", "KEY_2"]
        response.carInspectionList*.taskStepValue == ["VALUE_1", "VALUE_2"]
        response.responseResult.returnCode == "200"
    }

    def "querycarinspectioninfotest.norecords"() {
        given:
        def request = new QueryCarInspectionInfoRequestType(driverId:123456L, taskId: "TASK_002")

        when:
        QueryCarInspectionInfoResponseType response = executor.execute(request)

        then:
        1 * driverTaskAdapter.queryCarInspectionInfoList("TASK_002") >> []

        response.carInspectionList.isEmpty()
        response.responseResult.returnCode == "200"
    }
}