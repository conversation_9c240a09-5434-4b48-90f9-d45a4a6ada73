package com.ctrip.dcs.driver.domain.application.common

import spock.lang.Specification

class DriverLevelHelperTest extends Specification {

    DriverLevelHelper driverLevelHelper

    def setup() {
        driverLevelHelper = new DriverLevelHelper()
    }

    def "getDriverLevelName"(){

        when:
        def res1 = driverLevelHelper.getDriverLevelName(1, "aa")

        then:
        res1 == "aa"
    }

}
