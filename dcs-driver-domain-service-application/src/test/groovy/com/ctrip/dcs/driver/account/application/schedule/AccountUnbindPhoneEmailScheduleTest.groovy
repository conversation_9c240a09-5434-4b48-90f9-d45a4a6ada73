package com.ctrip.dcs.driver.account.application.schedule

import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLogType
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.dcs.go.log.Log
import org.junit.platform.commons.logging.Logger
import org.springframework.test.util.ReflectionTestUtils
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

class AccountUnbindPhoneEmailScheduleTest extends Specification {

    def schedule = new AccountUnbindPhoneEmailSchedule()
    def mockUserCenter = Mock(UserCenterAccountGateway)
    def mockLogHelper = Mock(AccountChangeLogHelper)

    def setup() {
        ReflectionTestUtils.setField(schedule, 'userCenterAccountGateway', mockUserCenter)
        ReflectionTestUtils.setField(schedule, 'accountChangeLogHelper', mockLogHelper)

    }



    @Unroll
    def "单用户解绑#type测试: 当操作#result时记录日志"() {
        given:
        def param = new MockParameter('{"uidList":"1001","type":"' + type + '"}')
        mockUserCenter."unBind${type.capitalize()}"('1001', 'System') >> result

        when:
        schedule.onExecute(param)

        then:
        (result ? 1 : 0) * mockLogHelper.saveLog(
                { AccountChangeLog log ->
                    log.type == expectedLogType &&
                            log.operator == 'System' &&
                            log.uid == '1001'
                }
        )

        where:
        type    | result | expectedLogType
        'phone' | true   | AccountChangeLogType.UNBIND_PHONE
        'phone' | false  | AccountChangeLogType.UNBIND_PHONE
        'email' | true   | AccountChangeLogType.UNBIND_EMAIL
        'email' | false  | AccountChangeLogType.UNBIND_EMAIL
    }

    def "批量用户处理测试: 验证多个uid顺序执行并独立处理异常"() {
        given:
        def param = new MockParameter('{"uidList":"1001,1002,1003","type":"phone"}')
        when:
        schedule.onExecute(param)

        then:
        //
        3 * mockUserCenter.unBindPhone(_, 'System') >> true >> { throw new RuntimeException() } >> false
        1 * mockLogHelper.saveLog(_) // 只有1001成功
    }

}
