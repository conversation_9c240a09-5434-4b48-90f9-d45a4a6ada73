package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.arch.distlock.DLock
import com.ctrip.arch.distlock.DistributedLockService
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.util.concurrent.TimeUnit

class DriverRightsReverseListenerTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    DomainBeanFactory domainBeanFactory=Mock(DomainBeanFactory)
    @Mock
    LockService lockService =Mock(LockService)
    @Mock
    DrivRightsDao drivRightsDao =Mock(DrivRightsDao)
    @Mock
    DrivRightsRecordDao rightsRecordDao =Mock(DrivRightsRecordDao)
    DistributedLockService distributedLockService =Mock(DistributedLockService)
    @InjectMocks
    DriverRightsReverseListener driverRightsReverseListener

    def setup() {
        lockService =new LockService(distributedLockService: distributedLockService)
        domainBeanFactory = new DomainBeanFactory()
        driverRightsReverseListener = new DriverRightsReverseListener(domainBeanFactory: domainBeanFactory,lockService:lockService,drivRightsDao:drivRightsDao,rightsRecordDao:rightsRecordDao)
        DLock dLock = Mock(DLock)
        distributedLockService.getLock("dcs_driver_use_rights:1_1")>> dLock
        dLock.tryLock(1000L, TimeUnit.MILLISECONDS)>>true
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.setProperty("content", data);
        given:
        rightsRecordDao.queryDriverRecordsBySupplyOrderId(1l, 1, "881834338813162")>>recordList
        drivRightsDao.queryDriverRights(1l,"2023-09")>>rightsList

        when:
        driverRightsReverseListener.onMessage(message)

        then:
        message !=null
        where:
        data | rightsList | recordList
        "" | null | null
        "{\"driverId\":1,\"rightsType\":1,\"userOrderId\":\"365992299926\",\"purchaseOrderId\":null,\"supplyOrderId\":\"881834338813162\",\"punishOrderId\":null,\"money\":9,\"date\":\"2023-09\"}" | [new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 0, extend: "extend"),new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 3, extend: "extend"),new DrivRightsPO(rightsType: 1, useCount: 1, rightsStatus: 0, extend: "{\"welfareLimit\":40,\"welfareUse\":0,\"welfareFreeze\":0}")] | [new DrivRightsRecordPO(isDeleted: 0)]
        "{\"driverId\":1,\"rightsType\":1,\"userOrderId\":\"365992299926\",\"purchaseOrderId\":null,\"supplyOrderId\":\"881834338813162\",\"punishOrderId\":null,\"money\":9,\"date\":\"2023-09\"}" | [new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 0, extend: "extend"),new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 3, extend: "extend"),new DrivRightsPO(rightsType: 1, useCount: 1, rightsStatus: 0, extend: "{\"welfareLimit\":40,\"welfareUse\":0,\"welfareFreeze\":0}")] | []
        "{\"driverId\":1,\"rightsType\":1,\"userOrderId\":\"365992299926\",\"purchaseOrderId\":null,\"supplyOrderId\":\"881834338813162\",\"punishOrderId\":null,\"money\":9,\"date\":\"2023-09\"}" | [new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 0, extend: "extend"),new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 3, extend: "extend"),new DrivRightsPO(rightsType: 1, useCount: 0, rightsStatus: 0, extend: "{\"welfareLimit\":40,\"welfareUse\":0,\"welfareFreeze\":0}")] | []
        "{\"driverId\":1,\"rightsType\":1,\"userOrderId\":\"365992299926\",\"purchaseOrderId\":null,\"supplyOrderId\":\"881834338813162\",\"punishOrderId\":null,\"money\":9,\"date\":\"2023-09\"}" | [new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 0, extend: "extend"),new DrivRightsPO(rightsType: 0, useCount: 0, rightsStatus: 3, extend: "extend"),new DrivRightsPO(rightsType: 2, useCount: 0, rightsStatus: 0, extend: "extend")] | []
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme