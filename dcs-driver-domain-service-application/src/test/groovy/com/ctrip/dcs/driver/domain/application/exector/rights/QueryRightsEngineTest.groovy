package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.rights.QueryRightsRequestType
import com.ctrip.dcs.driver.domain.rights.QueryRightsResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import spock.lang.Specification

class QueryRightsEngineTest extends Specification {
    QueryRightsEngine queryRightsEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsConfig rightsConfig = Mock(RightsConfig)
    LevelConfig levelConfig = Mock(LevelConfig)
    RightsRepoServiceImpl rightsRepoService = Mock(RightsRepoServiceImpl)


    def setup() {
        domainBeanFactory = new DomainBeanFactory(levelConfig: levelConfig, rightsConfig: rightsConfig, rightsRepoService: rightsRepoService)
        queryRightsEngine = new QueryRightsEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryRightsResponseType.class).init()
        domainBeanFactory.rightsRepoService() >> rightsRepoService
        rightsRepoService.queryDriverRights(1L, Arrays.asList(7), "2023-06") >> model
        rightsRepoService.queryDriverLevel(1, "2023-06") >> new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0)
        when:
        QueryRightsResponseType result = queryRightsEngine.execute(request)

        then:
        result != null
        where:
        request                                                                                 | model                                                                                                                                                                   || size
        new QueryRightsRequestType(driverId: 1, date: "2023-06", rightsTypes: Arrays.asList(7)) | [new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 7, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "{\"vacationLimit\":2,\"vacationUse\":0}")] || 0
        new QueryRightsRequestType(driverId: 1, date: "2023-06", rightsTypes: Arrays.asList(1)) | [new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 1, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "{\"welfareLimit\":50,\"welfareUse\":1001}")] || 0
        new QueryRightsRequestType(driverId: 1, date: "2023-06", rightsTypes: Arrays.asList(2)) | [new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 2, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "{\"weekUseCount\":1,\"useTime\":\"2023-06-13 18:07:37\"}")] || 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme