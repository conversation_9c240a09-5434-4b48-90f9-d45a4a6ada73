package com.ctrip.dcs.driver.trip.university.application.service.impl

import com.ctrip.dcs.driver.account.domain.service.AccountService
import com.ctrip.dcs.driver.account.infrastructure.value.AccountIdentityDTO
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.http.HttpClient
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis
import com.ctrip.dcs.driver.domain.infrastructure.adapter.transport.TmsTransportServiceProxy
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO
import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityDriverIdentityEnum
import com.ctrip.dcs.driver.trip.university.infrastructure.gateway.TripUniversityGateway
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityFailedLogDTO
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenDTO
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenRequestDTO

import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverForDisCardDTO
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityUserData
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityUserDataResponse
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXLDTO
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXLUserDTO
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXingLiUserResult
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXingLiUserResultResponse
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhixlUserResponseDTO
import com.ctrip.dcs.tms.transport.api.model.QueryDrvCodeByDrvIdSOAResponseType
import com.ctriposs.baiji.exception.BaijiRuntimeException
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ExecutorService

class TripUniversityServiceImplTest extends Specification {
    def testObj = new TripUniversityServiceImpl()
    def tripUniversityGateway = Mock(TripUniversityGateway)
    def directorRedis = Mock(DirectorRedis)
    def domainBeanFactory = Mock(DomainBeanFactory)
    def executorService = Mock(ExecutorService)
    def accountService = Mock(AccountService)
    def driverOrderPushConfigDao = Mock(DriverOrderPushConfigDao)
    def tmsTransportServiceProxy = Mock(TmsTransportServiceProxy)
    def httpClient = Mock(HttpClient)

    def setup() {

        testObj.tmsTransportServiceProxy = tmsTransportServiceProxy
        testObj.driverOrderPushConfigDao = driverOrderPushConfigDao
        testObj.domainBeanFactory = domainBeanFactory
        testObj.directorRedis = directorRedis
        testObj.executorService = executorService
        testObj.accountService = accountService
        testObj.tripUniversityGateway = tripUniversityGateway
    }

    @Unroll
    def "syncDriverInfoToTripUniversityTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityGateway.saveSyncFailedLog(_) >> true
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(tripUniversityGrayDrvIdList: [-1L])
        driverOrderPushConfigDao.findOne(_) >> new DriverOrderPushConfigPO(drvLanguage: "drvLanguage", drvId: 1L)


        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getDrvId(_, _) >> 1L
        spy.queryAccountBaseInfo(_, _) >> new AccountInfoDTO(identityDTOList: [findDriver])
        spy.setBatchZhiXingLiUser(_) >> new ZhixlUserResponseDTO(code: 0)
        spy.getZhiXingLiUserResult(_) >> new ZhiXingLiUserResultResponse(code: 0, success: true, data: new ZhiXingLiUserResult(current_status: "current_status", error_msg: ["String"]))
        spy.populateDriverDTO(_, _) >> {}
        spy.logAndReportEvent(_, _) >> {}
        spy.getMaxRetryCount() >> maxRetryCount
        when:
        def result = spy.syncDriverInfoToTripUniversity(driverDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverDTO                                                                                                            |maxRetryCount| findDriver  || expectedResult
        new TripUniversityDriverDTO(failedLogId: 1L, retryCount: 0, uid: "uid", drvId: 1L, driverLanguage: "driverLanguage") |0| new AccountIdentityDTO()  || false
        new TripUniversityDriverDTO(failedLogId: 1L, retryCount: 0, uid: "uid", drvId: 1L, driverLanguage: "driverLanguage") |1| new AccountIdentityDTO()  || false
    }

    @Unroll
    def "syncDriverInfoToTripUniversityWithSwitchTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityGateway.saveSyncFailedLog(_) >> true
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(tripUniversityGrayDrvIdList: tripUniversityGrayDrvIdList)
        driverOrderPushConfigDao.findOne(_) >> new DriverOrderPushConfigPO(drvLanguage: "drvLanguage", drvId: 1L)


        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getDrvId(_, _) >> 1L
        spy.queryAccountBaseInfo(_, _) >> accountInfo
        spy.setBatchZhiXingLiUser(_) >> new ZhixlUserResponseDTO(code: 0)
        spy.getZhiXingLiUserResult(_) >> new ZhiXingLiUserResultResponse(code: 0, success: true, data: new ZhiXingLiUserResult(current_status: "current_status", error_msg: ["String"]))
        spy.populateDriverDTO(_, _) >> {}
        spy.logAndReportEvent(_, _) >> {}
        spy.getMaxRetryCount() >> maxRetryCount
        when:
        def result = spy.syncDriverInfoToTripUniversity(driverDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        tripUniversityGrayDrvIdList|driverDTO                        |maxRetryCount| findDriver                |accountInfo|| expectedResult
        [2L]|new TripUniversityDriverDTO(failedLogId: 1L, drvId: 1L) |0            | new AccountIdentityDTO() |new AccountInfoDTO(identityDTOList: [findDriver])|| false
        [-1L]|new TripUniversityDriverDTO(failedLogId: 1L, drvId: 1L) |0            | new AccountIdentityDTO() |null|| false
        [-1L]|new TripUniversityDriverDTO(failedLogId: 1L, drvId: 1L) |0            | new AccountIdentityDTO() |new AccountInfoDTO(identityDTOList: [])|| false
    }

    @Unroll
    def "logAndReportEventTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.logAndReportEvent(event, driverDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverDTO                                          | event   || expectedResult
        new TripUniversityDriverDTO(uid: "uid", drvId: 1L) | "event" || null
    }

    @Unroll
    def "getMaxRetryCountTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig()

        when:
        def result = testObj.getMaxRetryCount()

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        expectedResult << 5
    }

    @Unroll
    def "populateDriverDTOTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getDriverId(_) >> 1L
        when:
        def result = spy.populateDriverDTO(driverDTO, accountInfoDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverDTO                                                                                                                                                      | accountInfoDTO                                                                                                                             || expectedResult
        new TripUniversityDriverDTO(uid: "uid", drvId: 1L, name: "name", idCardNo: "idCardNo", countryCode: "countryCode", phoneNumber: "phoneNumber", email: "email") | new AccountInfoDTO(uid: "uid", name: "name", idCardNo: "idCardNo", countryCode: "countryCode", phoneNumber: "phoneNumber", email: "email") || null
    }

    @Unroll
    def "queryAccountBaseInfoTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        accountService.getAccountInfoByUID(_) >> new AccountInfoDTO()
        accountService.getAccountByDriverId(_) >> new AccountInfoDTO()

        when:
        def result = testObj.queryAccountBaseInfo(driverId, uid)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        uid   | driverId || expectedResult
        "uid" | 1L       || new AccountInfoDTO()
    }

    @Unroll
    def "getAccessTokenTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(zhixlTokenMap:"{}")

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getDrvId(_, _) >> 1L
        spy.queryDrvCodeByDrvId(_) >> "queryDrvCodeByDrvIdResponse"
        when:
        def result = spy.getAccessToken(requestDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestDTO                                || expectedResult
        new TripUniversityAccessTokenRequestDTO() || new TripUniversityAccessTokenDTO(accessToken:"queryDrvCodeByDrvIdResponse", tenantId: "ctrip")

    }

    @Unroll
    def "getDrvIdTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        accountService.getAccountInfoByUID(_) >> new AccountInfoDTO(identityDTOList: [new AccountIdentityDTO()])

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getDriverId(_) >> 1L
        when:
        def result = spy.getDrvId(uid, drvId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        uid   | drvId || expectedResult
        "uid" | 1L    || 1L
    }

    @Unroll
    def "getDriverIdTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getDriverId(accountInfoDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        accountInfoDTO                                                                                        || expectedResult
        new AccountInfoDTO(identityDTOList: [new AccountIdentityDTO(source: "source", sourceId: "sourceId")]) || -1L
    }

    @Unroll
    def "updateTripUniversityAccountAsyncTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.updateTripUniversityAccountAsync(requestDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestDTO                                         || expectedResult
        new TripUniversityDriverDTO(uid: "uid", drvId: 1L) || null
    }

    @Unroll
    def "queryDrvCodeByDrvIdTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        directorRedis.get(_) >> "getResponse"
        directorRedis.set(_, _, _) >> Boolean.TRUE
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig()
        tmsTransportServiceProxy.queryDrvCodeByDrvId(_) >> new QueryDrvCodeByDrvIdSOAResponseType(data: "data")

        when:
        def result = testObj.queryDrvCodeByDrvId(drvId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvId || expectedResult
        1L    || "getResponse"
    }

    @Unroll
    def "queryDrvIdByCodeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        directorRedis.get(_) >> "getResponse"

        when:
        def result = testObj.queryDrvIdByCode(drvCode)

        then: "验证返回结果里属性值是否符合预期"
        result.getData() == expectedResult
        where: "表格方式验证多种分支调用场景"
        drvCode   || expectedResult
        "drvCode" || "getResponse"
    }

    /**
     * 外部http请求，无法mock，直接测试异常
     * @return
     */
    @Unroll
    def "getCtripUniveTokenTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.httpClient() >> new HttpClient()
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(zhixlTokenUrl: "zhixlTokenUrl")

        when:
        def result = testObj.getCtripUniveToken()

        then: "验证返回结果里属性值是否符合预期"
        def ex = thrown(BaijiRuntimeException);
        ex.getMessage() == expectedResult
        where: "表格方式验证多种分支调用场景"
        test | expectedResult
        "a"  | "java.lang.RuntimeException: Json Error"
    }

    @Unroll
    def "getParamsTest"() {
        given: "设定相关方法入参"
        when:
        def result = TripUniversityServiceImpl.getParams(paramsMap)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        paramsMap            || expectedResult
        ["String": "String"] || "String=String"
    }

    @Unroll
    def "setBatchZhiXingLiUserTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.httpClient() >> httpClient
        httpClient.post(_, _) >> httpResponse
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(zhixlApiBatchCreateUserInterfaceUrl: "zhixlApiBatchCreateUserInterfaceUrl")

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getCtripUniveToken() >> "getCtripUniveTokenResponse"
        when:
        def result = spy.setBatchZhiXingLiUser(zhiXLDTOList)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        httpResponse                        | zhiXLDTOList     || expectedResult
        "{\"code\":200, \"success\": true}" | [new ZhiXLDTO()] || new ZhixlUserResponseDTO(code:200, success: true)
    }

    @Unroll
    def "getZhiXingLiUserTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.httpClient() >> httpClient
        httpClient.post(_, _) >> httpResponse

        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(zhixlApiInterfaceUrl: "zhixlApiInterfaceUrl")

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getCtripUniveToken() >> "getCtripUniveTokenResponse"
        when:
        def result = spy.getZhiXingLiUser(uid)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        httpResponse| uid         || expectedResult
        "{\"code\":200, \"success\": true}"| "guide1234" || new TripUniversityUserDataResponse(code:200, success: true)
    }

    @Unroll
    def "getZhiXingLiUserResultTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.httpClient() >> httpClient
        httpClient.get(_) >> httResponse
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(zhixlBatchUserResultApiInterfaceUrl: "zhixlBatchUserResultApiInterfaceUrl")

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getCtripUniveToken() >> "getCtripUniveTokenResponse"
        when:
        def result = spy.getZhiXingLiUserResult(dto)

        then: "验证返回结果里属性值是否符合预期"
        result.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        httResponse|dto                                                                                  || expectedResult
        "{\"code\":200, \"success\": true}"|new ZhixlUserResponseDTO(code: 200, success: true, respon_id: "respon_id", msg: "msg") || true
    }

    @Unroll
    def "grayControlTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig()

        when:
        def result = testObj.grayControl(driverDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverDTO                              || expectedResult
        new TripUniversityDriverDTO(drvId: 1L) || true
    }

    @Unroll
    def "getIdentityEnumTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getIdentityEnum(accountInfoDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        accountInfoDTO                                                                  || expectedResult
        new AccountInfoDTO(identityDTOList: [new AccountIdentityDTO(source: "Driver")]) || TripUniversityDriverIdentityEnum.DRIVER
    }

    @Unroll
    def "getZhiXingLiUserResultWithRetryTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(batchUserResultSleepMilliseconds: 2, batchUserResultWaitMilliseconds: 1)

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getZhiXingLiUserResult(_) >> new ZhiXingLiUserResultResponse(data: new ZhiXingLiUserResult(current_status: "current_status"))
        when:
        def result = spy.getZhiXingLiUserResultWithRetry2(responseDTO)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        responseDTO                                      || expectedResult
        new ZhixlUserResponseDTO(respon_id: "respon_id") || new ZhiXingLiUserResultResponse(data: new ZhiXingLiUserResult(current_status: "current_status"))
    }

    @Unroll
    def "getZhiXingLiUserResultWithRetryWithTimeoutTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig(batchUserResultSleepMilliseconds: 1, batchUserResultWaitMilliseconds: 1)

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getZhiXingLiUserResult(_) >> new ZhiXingLiUserResultResponse( code: 200, success: true, data: new ZhiXingLiUserResult(current_status: "current_status"))
        when:
        def result = spy.getZhiXingLiUserResultWithRetry2(responseDTO)

        then: "验证返回结果里属性值是否符合预期"
        result.code == expectedResult
        where: "表格方式验证多种分支调用场景"
        responseDTO                                      || expectedResult
        new ZhixlUserResponseDTO(respon_id: "respon_id") || -998
    }

    @Unroll
    def "saveSyncFailedLogTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityGateway.saveSyncFailedLog(_) >> true

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.convert(_, _, _) >> new DriverSyncDrvInfoToTripUniversityFailedLogDTO()
        when:
        def result = spy.saveSyncFailedLog(driverDTO, code, message)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        code | driverDTO                     | message   || expectedResult
        0    | new TripUniversityDriverDTO() | "message" || null
    }

    @Unroll
    def "convert2ZhixlDTOTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getDepartId(_) >> "getDepartIdResponse"
        when:
        def result = spy.convert2ZhixlDTO(driverDTO)

        then: "验证返回结果里属性值是否符合预期"
        ((ZhiXLUserDTO)result.data).id == expectedResult
        where: "表格方式验证多种分支调用场景"
        driverDTO                                                                          || expectedResult
        new TripUniversityDriverDTO(drvId: 1L, identityEnum: TripUniversityDriverIdentityEnum.DRIVER) || "card1"
    }

    @Unroll
    def "getDepartIdTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        domainBeanFactory.tripUniversityQconfig() >> new TripUniversityQconfig()

        when:
        def result = testObj.getDepartId(identityEnum)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        identityEnum                            || expectedResult
        TripUniversityDriverIdentityEnum.DRIVER || "ZTYV0"
    }

    @Unroll
    def "convertTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.convert(driverDTO, code, message)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        code | driverDTO                                                                          | message   || expectedResult
        0    | new TripUniversityDriverDTO(failedLogId: 1L, retryCount: 0, uid: "uid", drvId: 1L) | "message" || new DriverSyncDrvInfoToTripUniversityFailedLogDTO(id: 1L, uid: "uid", drvId: 1L, retryCount: 1, createUser: "system", modifyUser: "system", failedReasonCode: 0, failedReasonDetail: "message")
    }

    @Unroll
    def "updateDrvPhoneAndEmailForDuplicateProcessTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getZhiXingLiUser(_) >> new TripUniversityUserDataResponse(data: [new TripUniversityUserData(mobile_phone: "mobile_phone", email: "email")], success: true)
        spy.setBatchZhiXingLiUser(_) >> new ZhixlUserResponseDTO()
        when:
        def result = spy.updateDrvPhoneAndEmailForDuplicateProcess(dto)

        then: "验证返回结果里属性值是否符合预期"
        result.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        dto                                                                                               || expectedResult
        new TripUniversityDriverForDisCardDTO(drvId: "drvId", phoneNumber: "phoneNumber", email: "email") || true
    }
}
