package com.ctrip.dcs.driver.account.application.cache

import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit

class AccountUDLCacheTest extends Specification {

    AccountUDLCache cache
    DriverAccountConfig config

    def setup() {
        config = new DriverAccountConfig()
        config.setUdLCacheMaxCount(2)
        config.setUdLCacheTimeOut(1) // 1 second timeout

        cache = new AccountUDLCache()
        cache.driverAccountConfig = config
        cache.initCache() // Manually trigger @PostConstruct logic
    }

    // 其他测试方法保持不变...

    def "保存有效数据到sourceId缓存"() {
        given: "准备测试数据"
        def account1 = AccountUDLDo.builder().sourceId("s1").uid("u1").build()
        def account2 = AccountUDLDo.builder().sourceId("s2").uid("u2").build()

        when: "保存两个账户对象"
        cache.saveAccountUDLBySourceId([account1, account2])

        then: "缓存中能正确获取保存的对象"
        cache.sourceId_cache.get("s1") == account1
        cache.sourceId_cache.get("s2") == account2
    }

    @Unroll
    def "通过sourceId查询缓存: #caseDesc"() {
        given: "初始化缓存数据"
        cache.saveAccountUDLBySourceId(initialData)

        when: "执行查询操作"
        def result = cache.getAccountUDLBySourceId(queryIds)

        then: "验证查询结果"
        result.size() == expectedSize
        result*.sourceId.containsAll(expectedIds)

        where:
        caseDesc                 | initialData                                                                 | queryIds   | expectedSize | expectedIds
        "完全匹配"               | [AccountUDLDo.builder().sourceId("s1").uid("u1").build()]                  | ["s1"]     | 1            | ["s1"]
        "部分匹配"               | [AccountUDLDo.builder().sourceId("s1").uid("u1").build()]                  | ["s1","s2"]| 1            | ["s1"]
        "无匹配"                 | [AccountUDLDo.builder().sourceId("s1").uid("u1").build()]                  | ["s3"]     | 0            | []
        "空查询返回空结果"       | []                                                                          | []         | 0            | []
    }

    def "保存并查询uid缓存"() {
        given: "准备测试数据"
        def account1 = AccountUDLDo.builder().uid("u1").sourceId("s1").build()
        def account2 = AccountUDLDo.builder().uid("u2").sourceId("s2").build()

        when: "保存并查询数据"
        cache.saveAccountUDLByUid([account1, account2])
        def result = cache.getAccountUDLByUid(["u1", "u3"])

        then: "验证查询结果"
        result.size() == 1
        result[0].uid == "u1"
    }

    def "LRU缓存淘汰策略验证"() {
        given: "初始化三个测试对象"
        def account1 = AccountUDLDo.builder().sourceId("s1").uid("u1").build()
        def account2 = AccountUDLDo.builder().sourceId("s2").uid("u2").build()
        def account3 = AccountUDLDo.builder().sourceId("s3").uid("u3").build()

        when: "填充缓存并触发淘汰"
        cache.saveAccountUDLBySourceId([account1, account2]) // Fill cache
        cache.getAccountUDLBySourceId(["s1"])               // Access s1 to update LRU
        cache.saveAccountUDLBySourceId([account3])          // Add third element

        then: "验证淘汰结果"
        cache.sourceId_cache.size() == 2
        cache.sourceId_cache.get("s2") == null  // Should be evicted
        cache.sourceId_cache.get("s1") != null
        cache.sourceId_cache.get("s3") != null
    }

    def "缓存自动过期验证"() {
        given: "创建测试对象并保存"
        def account = AccountUDLDo.builder().sourceId("s1").uid("u1").build()
        cache.saveAccountUDLBySourceId([account])

        when: "等待超过缓存超时时间"
        TimeUnit.MILLISECONDS.sleep(1100)

        then: "验证缓存条目已过期"
        cache.getAccountUDLBySourceId(["s1"]).isEmpty()
    }

    def "并发保存操作线程安全"() {
        given: "创建多个线程并发操作"
        def account1 = AccountUDLDo.builder().sourceId("s1").uid("u1").build()
        def account2 = AccountUDLDo.builder().sourceId("s2").uid("u2").build()

        when: "使用多线程并发保存"
        def t1 = Thread.start { cache.saveAccountUDLBySourceId([account1]) }
        def t2 = Thread.start { cache.saveAccountUDLBySourceId([account2]) }
        t1.join()
        t2.join()

        then: "验证缓存状态一致性"
        cache.sourceId_cache.size() == 2
        cache.sourceId_cache.get("s1") == account1
        cache.sourceId_cache.get("s2") == account2
    }

    def "缓存最大容量限制"() {
        given: "设置缓存容量为1"
        config.setUdLCacheMaxCount(1)
        cache.initCache()

        when: "保存两个对象"
        def account1 = AccountUDLDo.builder().sourceId("s1").uid("u1").build()
        def account2 = AccountUDLDo.builder().sourceId("s2").uid("u2").build()
        cache.saveAccountUDLBySourceId([account1])
        cache.saveAccountUDLBySourceId([account2])

        then: "验证只保留最后一个对象"
        cache.sourceId_cache.size() == 1
        cache.sourceId_cache.get("s1") == null
        cache.sourceId_cache.get("s2") == account2
    }
}
