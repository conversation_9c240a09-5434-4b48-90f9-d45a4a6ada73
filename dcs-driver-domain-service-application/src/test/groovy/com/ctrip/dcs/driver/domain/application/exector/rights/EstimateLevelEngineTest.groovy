package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.common.DriverLevelHelper
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.DriverPointModel
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.dcs.driver.domain.rights.EstimateLevelRequestType
import com.ctrip.dcs.driver.domain.rights.EstimateLevelResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import spock.lang.Specification

import java.time.LocalDateTime

class EstimateLevelEngineTest extends Specification {
    EstimateLevelEngine estimateLevelEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsRedisLogic rightsRedisLogic = Mock(RightsRedisLogic)
    GeoGateway geoGateway = Mock(GeoGateway)
    DriverLevelHelper driverLevelHelper = Mock(DriverLevelHelper)


    def setup() {
        domainBeanFactory = new DomainBeanFactory(rightsRedisLogic: rightsRedisLogic, geoGateway: geoGateway, driverLevelHelper: driverLevelHelper)
        estimateLevelEngine = new EstimateLevelEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {
        def request = new EstimateLevelRequestType( driverId: 1, cityId: 1)

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(EstimateLevelResponseType.class).init()
        domainBeanFactory.rightsRedisLogic.getDriverPointSnapshot([1l], LocalDateTimeUtils.todayStr())>> Collections.emptyList()
        domainBeanFactory.rightsRedisLogic.getDriverPointSnapshot([1l], LocalDateTimeUtils.yesterDayStr())>> [new DriverPointModel(1l,"1",1l,1,1 as BigDecimal,1 as BigDecimal, 1 as BigDecimal, 3 as BigDecimal,10,9,1 as BigDecimal,2 as BigDecimal,1 as BigDecimal, 10)]
        domainBeanFactory.geoGateway().getLocalCurrentTime(_) >> LocalDateTime.now()
        when:
        EstimateLevelResponseType result = estimateLevelEngine.execute(request)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme