package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import java.sql.Timestamp
import java.text.SimpleDateFormat

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

class RightsDBDataServiceImplTest extends Specification {
    @Mock
    DrivRightsDao drivRightsDao
    @Mock
    DrivLevelDao drivLevelDao
    @Mock
    DrivRightsRecordDao drivRightsRecordDao
    @Mock
    SimpleDateFormat sdf
    @InjectMocks
    RightsDBDataServiceImpl rightsDBDataServiceImpl

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test query Driver Rights"() {
        given:
        when(drivRightsDao.queryDriverRights(anyLong(), anyString())).thenReturn(po)

        when:
        List<RightsModel> result = rightsDBDataServiceImpl.queryDriverRights(0l, "monthIdx")

        then:
        result != null
        where:
        po                                                                                                                                                                                                                                                                                                                                                                                            || size
        null                                                                                                                                                                                                                                                                                                                                                                                          || 0
        [new DrivRightsPO(id: 1l, rightsConfigId: 1l, rightsName: "rightsName", rightsDesc: "rightsDesc", cityId: 1l, monthIdx: "monthIdx", drivId: 1l, drivLevel: 0, rightsType: 0, useLimit: 0, useCount: 0, rightsStatus: 0, rightsStratTime: new Timestamp(1686636458573L), rightsEndTime: new Timestamp(1686636458573L), extend: "extend", datachangeCreatetime: new Timestamp(1686636458573L))] || 1
    }

    def "test query Driver Records"() {
        given:
        when(drivRightsRecordDao.queryDriverRecords(anyLong(), any(), any())).thenReturn([new DrivRightsRecordPO(rightsType: 0,isDeleted:0, datachangeCreatetime: new Timestamp(1686636458573L))])

        when:
        List<RightsRecordModel> result = rightsDBDataServiceImpl.queryDriverRecords(0l, "2023-06-01 00:00:00", "2023-06-29 14:40:20", [0])

        then:
        result != null
    }

    def "test query Driver RecordsBYDriver"() {
        given:
        when(drivRightsRecordDao.queryDriverRecordsByDriver(1l,7)).thenReturn([new DrivRightsRecordPO(rightsType: 0,isDeleted:0, datachangeCreatetime: new Timestamp(1686636458573L))])

        when:
        List<RightsRecordModel> result = rightsDBDataServiceImpl.querDriverRecordsByDriver(7,1l)

        then:
        result != null
    }

    def "test query Driver Level"() {
        given:
        when(drivLevelDao.queryDriverLevel(anyLong(), anyString())).thenReturn(po)

        when:
        LevelModel result = rightsDBDataServiceImpl.queryDriverLevel(0l, "monthIdx")

        then:
        true

        where:
        po                                                                                                                                                                       || size
        new DrivLevelPO(levelConfigId: 1l, levelName: "levelName", cityId: 1l, monthIdx: "monthIdx", drivId: 1l, drivLevel: 0, drivPoint: "0", drivActivity: "1", drivRank: "2") || 1
        null                                                                                                                                                                     || 0
    }

    def "test save Rights Records"() {
        when:
        def res = rightsDBDataServiceImpl.saveRightsRecords(new DrivRightsRecordPO())

        then:
        res == null
    }

    def "test update Driver Rights"() {
        when:
        def res = rightsDBDataServiceImpl.updateDriverRights(new DrivRightsPO())

        then:
        res == null
    }

    def "test query Driver Records By Id"() {
        given:
        when(drivRightsRecordDao.queryDriverRecordsByRightsId(anyLong())).thenReturn([new DrivRightsRecordPO(datachangeCreatetime: new Timestamp(1686636458573L),isDeleted:0)])

        when:
        List<RightsRecordModel> result = rightsDBDataServiceImpl.queryDriverRecordsById(1l)

        then:
        result !=null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme