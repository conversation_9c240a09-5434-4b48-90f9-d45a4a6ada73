package com.ctrip.dcs.driver.domain.application.helper

import cn.hutool.core.date.LocalDateTimeUtil
import com.ctrip.dcs.driver.domain.application.service.DriverDeviceInfoService
import com.ctrip.dcs.driver.domain.infrastructure.model.device.DriverDeviceInfoModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BizVoipGrayQConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfiguration
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import java.time.temporal.ChronoUnit

import static org.mockito.Mockito.when

class DriverDeviceManagerTest extends Specification {

    @Mock
    private DriverDeviceInfoService driverDeviceInfoService;

    @Mock
    private BusinessConfiguration configuration;

    @InjectMocks
    DriverDeviceManager manager

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "fromUserRequest"() {
        given:
        when(configuration.getBizVoipGrayQConfig()).thenReturn(new BizVoipGrayQConfig(appIds: ["123"]))

        when:
        def response = manager.fromUserRequest()

        then:
        response == false
    }

    def "activeForVoip"() {
        given:
        when(driverDeviceInfoService.queryDeviceInfo(1L)).thenReturn(new DriverDeviceInfoModel(activeTime: LocalDateTimeUtil.now()))
        when(driverDeviceInfoService.queryDeviceInfo(2L)).thenReturn(new DriverDeviceInfoModel(activeTime: LocalDateTimeUtil.now().minusMinutes(20)))
        when(configuration.getBizVoipGrayQConfig()).thenReturn(new BizVoipGrayQConfig(bizGrayDriverDeviceActiveIntervalMinutes: 10))
        expect:
        result == manager.activeForVoip(driverId)
        where:
        result | driverId
        true   | 1L
        false  | 2L
    }

    def "active"() {
        given:
        when(driverDeviceInfoService.queryDeviceInfo(1L)).thenReturn(new DriverDeviceInfoModel(activeTime: LocalDateTimeUtil.now()))
        when(driverDeviceInfoService.queryDeviceInfo(2L)).thenReturn(new DriverDeviceInfoModel(activeTime: LocalDateTimeUtil.now().minusSeconds(20)))
        expect:
        result == manager.active(driverId, configIntervalTime, timeUnit)
        where:
        result | driverId | configIntervalTime | timeUnit
        true   | 1L       | 10                 | ChronoUnit.SECONDS
        false  | 2L       | 10                 | ChronoUnit.SECONDS
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme