package com.ctrip.dcs.driver.domain.application.exector


import com.ctrip.dcs.driver.domain.application.exector.gray.QueryDriverBizGraySwitchEngine
import com.ctrip.dcs.driver.domain.application.helper.DriverDeviceManager
import com.ctrip.dcs.driver.domain.application.service.DriverBizGrayService
import com.ctrip.dcs.driver.domain.gray.QueryDriverBizGraySwitchRequestType
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyBoolean
import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class QueryDriverBizGraySwitchEngineTest extends Specification {

    @Mock
    DriverBizGrayService driverBizGrayService;

    @Mock
    DriverDeviceManager driverDeviceManager;

    @InjectMocks
    QueryDriverBizGraySwitchEngine bizGraySwitchEngine

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        Long driverId = 1000004L
        String bizKey = "voip"

        def request = new QueryDriverBizGraySwitchRequestType(
                driverId: driverId, bizKey: bizKey)
        given:
        when(driverBizGrayService.queryBizGraySwitch(anyString(), anyLong(),anyString(),anyLong(),anyString(),anyString(), anyBoolean())).thenReturn(false)
        when(driverDeviceManager.activeForVoip(anyLong())).thenReturn(true)
        when(driverDeviceManager.driverDeviceActiveCheckForVoip()).thenReturn(true)
        when(driverDeviceManager.fromUserRequest()).thenReturn(true)
        when:
        def response = bizGraySwitchEngine.execute(request)

        then:
        with(response) {
            result == false
        }
    }


    def "test execute with ignore ver"() {
        Long driverId = 1000004L
        String bizKey = "safe_point"

        def request = new QueryDriverBizGraySwitchRequestType(
                driverId: driverId, bizKey: bizKey, ignoreVersion: true)
        given:
        when(driverBizGrayService.queryBizGraySwitch(anyString(), anyLong(),anyString(),anyLong(),anyString(),anyString(), anyBoolean())).thenReturn(false)
        when(driverDeviceManager.activeForVoip(anyLong())).thenReturn(true)
        when(driverDeviceManager.driverDeviceActiveCheckForVoip()).thenReturn(true)
        when(driverDeviceManager.fromUserRequest()).thenReturn(true)
        when:
        def response = bizGraySwitchEngine.execute(request)

        then:
        with(response) {
            result == false
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme