package com.ctrip.dcs.driver.account.application.schedule

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.dianping.cat.Cat
import org.springframework.test.util.ReflectionTestUtils
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

/*
作者：pl.yang
创建时间：2025/4/7-上午11:18-2025
*/

class AccountBaseInfoCheckScheduleTest extends Specification {
    def accountBaseInfoDao = Mock(AccountBaseInfoDao)
    def userCenterAccountGateway = Mock(UserCenterAccountGateway)
    def directorRedis = Mock(DirectorRedis)
    def driverAccountConfig = Mock(DriverAccountConfig)

    def schedule = new AccountBaseInfoCheckSchedule(
            accountBaseInfoDao: accountBaseInfoDao,
            userCenterAccountGateway: userCenterAccountGateway,
            directorRedis: directorRedis,
            driverAccountConfig: driverAccountConfig
    )

    def setup() {
        driverAccountConfig.getJobSleepMills() >> 0 // 默认不睡眠
    }

    // 场景1: 传入uidList参数时处理指定UID
    def "当传入uidList参数时应该处理指定用户"() {
        given: "准备包含3个UID的参数"
        def param = new MockParameter("{\n" +
                "  \"uidList\": \"uid1,uid2,uid3\"\n" +
                "}")
        def poList = [
                new AccountBaseInfoPO(uid: "uid1"),
                new AccountBaseInfoPO(uid: "uid2"),
                new AccountBaseInfoPO(uid: "uid3")
        ]

        when: "执行定时任务"
        schedule.onExecute(param)

        then: "验证按UID查询并批量检查"
        1 * accountBaseInfoDao.batchQueryByUID(["uid1", "uid2", "uid3"]) >> poList
        3 * userCenterAccountGateway.getAccountByUid(_) >> new AccountInfoResponseType()
        0 * directorRedis.set(_, _, _)
    }

    // 场景2: 传入startId参数时从指定位置开始
    def "当传入startId参数时应该从指定ID开始分页"() {
        given: "准备startId参数"
        def param = new MockParameter("{\"startId\": \"100\"}")
        def firstPage = (1..2).collect { new AccountBaseInfoPO(id: 100 + it) }
        def emptyPage = []

        when: "执行定时任务"
        schedule.onExecute(param)

        then: "验证分页逻辑"
        1 * accountBaseInfoDao.batchQueryByPage(100L, 100) >> firstPage
        1 * accountBaseInfoDao.batchQueryByPage(102L, 100) >> emptyPage
        1 * directorRedis.set("dcs.driver.account.base.info.check.job.progress", 102L, 2592000)
    }
    // 场景6: 检查字段冲突的不同情况
    // 修复后的场景6测试
    @Unroll
    def "当#field字段存在冲突时应记录#expectedEvent事件"() {
        given:
        def po = new AccountBaseInfoPO(
                uid: "testUid",
                countryCode: "CN",
                phoneNumber: "***********",
                email: "<EMAIL>"
        )

        def remoteAccount = new AccountInfoResponseType().tap {
            countryCode = "CN"
            phoneNumber = "***********"
            email = "<EMAIL>"
        }

        if (field != "_") {
            remoteAccount."set${field}"(conflictValue)
        }

        when:
        schedule.batchCheck([po])

        then:
        1 * userCenterAccountGateway.getAccountByUid("testUid") >> remoteAccount

        where:
        field           | conflictValue     | expectedEvent
        "CountryCode"   | "US"              | "CountryCodeConflict"
        "PhoneNumber"   | "***********"     | "PhoneCodeConflict"
        "Email"         | "conflict@ctrip"  | "EmailConflict"
        "_"             | "_"               | "Finish"
    }
}

