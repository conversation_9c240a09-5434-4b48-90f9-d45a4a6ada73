package com.ctrip.dcs.driver.account.application.exector

import com.ctrip.dcs.driver.account.application.convert.QueryAccountCurrentlyBoundBankCardConvert
import com.ctrip.dcs.driver.account.application.exector.QueryAccountCurrentlyBoundBankCardEngine
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo
import com.ctrip.dcs.driver.domain.account.AccountBankCardDTO
import com.ctrip.dcs.driver.domain.account.QueryAccountCurrentlyBoundBankCardRequestType
import com.ctrip.dcs.driver.domain.account.QueryAccountCurrentlyBoundBankCardResponseType
import com.ctrip.dcs.driver.gateway.AccountBankCardRecordRepository
import com.ctrip.dcs.driver.value.BaseResult
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll


/*
作者：pl.yang
创建时间：2025/6/3-下午4:54-2025
*/

class QueryAccountCurrentlyBoundBankCardEngineTest extends Specification {
    @Subject
    QueryAccountCurrentlyBoundBankCardEngine engine = new QueryAccountCurrentlyBoundBankCardEngine()

    AccountBankCardRecordRepository recordRepository = Mock()
    QueryAccountCurrentlyBoundBankCardConvert convert = Mock()

    def setup() {
        engine.recordRepository = recordRepository
        engine.convert = convert
    }

    // 场景1: 正常获取绑定的银行卡
    def "当请求有效且银行卡存在时，应返回成功响应"() {
        given: "准备有效的请求和模拟数据"
        def request = new QueryAccountCurrentlyBoundBankCardRequestType(uid: "user123")
        def mockCardDo = AccountBankCardDo.builder().cardNo("card-123").build()
        def mockResponse = new QueryAccountCurrentlyBoundBankCardResponseType(
                accountBankCard:new AccountBankCardDTO(bankCardNo: "card-123")
        )

        when: "执行查询"
        def response = engine.execute(request)

        then: "验证正确调用并返回成功"
        1 * recordRepository.queryAccountBindingLastBankCardV2("user123") >> BaseResult.successResult(mockCardDo)
        1 * convert.convertResponse(request, mockCardDo) >> mockResponse

        and: "验证响应数据"
        response.accountBankCard.bankCardNo == "card-123"
    }

    // 场景2: 银行卡记录查询失败
    def "当银行卡记录查询失败时，应返回错误响应"() {
        given: "准备请求并模拟失败响应"
        def request = new QueryAccountCurrentlyBoundBankCardRequestType(uid: "user123")

        when: "执行查询"
        def response = engine.execute(request)

        then: "验证返回错误信息"
        1 * recordRepository.queryAccountBindingLastBankCardV2("user123") >> BaseResult.failResult("DB_ERROR", "Database failure")

        and: "验证错误响应"
        response.responseResult.returnMessage == "Database failure"
    }
}

