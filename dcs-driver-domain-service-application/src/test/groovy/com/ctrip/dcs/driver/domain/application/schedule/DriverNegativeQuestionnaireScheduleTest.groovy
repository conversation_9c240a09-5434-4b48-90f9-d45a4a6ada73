package com.ctrip.dcs.driver.domain.application.schedule

import com.ctrip.dcs.driver.domain.application.mq.DriverNegativeQuestionnaireListener
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

class DriverNegativeQuestionnaireScheduleTest extends Specification {
    def testObj = new DriverNegativeQuestionnaireSchedule()
    def listener = Mock(DriverNegativeQuestionnaireListener)

    def setup() {

        testObj.listener = listener
    }

    @Unroll
    def "processTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.process(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter           || expectedResult
        new MockParameter("{\"orderList\":\"12,23\"}") || null
    }
}
