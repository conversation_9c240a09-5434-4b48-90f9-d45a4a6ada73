package com.ctrip.dcs.driver.domain.application.exector.freeze


import com.ctrip.dcs.driver.domain.infrastructure.adapter.db.DriverFreezeAdapter
import com.ctrip.model.CreateDriverApplyFreezeRecordRequestType
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class CreateDriverApplyFreezeRecordEngineTest extends Specification {

    @Mock
    DriverFreezeAdapter driverFreezeAdapter
    @InjectMocks
    CreateDriverApplyFreezeRecordEngine driverApplyFreezeRecordEngine
    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        Long driverId = 1000004L
        def request = new CreateDriverApplyFreezeRecordRequestType(driverId: driverId)
        given:

        when:
        def response = driverApplyFreezeRecordEngine.execute(request)

        then:
        with(response) {
            responseStatus!= null
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme