package com.ctrip.dcs.driver.domain.application.exector.honour

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory

import com.ctrip.dcs.driver.domain.application.service.impl.TmsTransportServiceProxyImpl
import com.ctrip.dcs.driver.domain.honour.QueryDirectorCommemorationRequestType
import com.ctrip.dcs.driver.domain.honour.QueryDirectorCommemorationResponseType
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.HonourFormConfig
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository
import com.ctrip.dcs.tms.transport.api.model.DriverInfo
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import org.owasp.csrfguard.util.Strings
import spock.lang.Specification
import spock.lang.Unroll

class QueryDirectorCommemorationEngineTest extends Specification {

    QueryDirectorCommemorationEngine queryDirectorCommemorationEngine
    DomainBeanFactory defDomainBeanFactory = Mock(DomainBeanFactory)
    HonourFormConfig defHonourFormConfig = Mock(HonourFormConfig)
    TmsTransportServiceProxyImpl defTmsTransportServiceProxy = Mock(TmsTransportServiceProxyImpl)
    ArchCoreInfoRepository defArchCoreInfoService = Mock(ArchCoreInfoRepository)

    def setup() {
        queryDirectorCommemorationEngine = new QueryDirectorCommemorationEngine(domainBeanFactory : defDomainBeanFactory)
    }

    @Unroll
    def "test execute"() {
        Long defDirectorId = 1000004L
        Long defDirectorType = 1;

        def request = new QueryDirectorCommemorationRequestType(
                directorId: defDirectorId, directorType: defDirectorType)

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDirectorCommemorationResponseType.class).init()

        given:
        defDomainBeanFactory.honourFormConfig() >> defHonourFormConfig
        defHonourFormConfig.isShowCommemoration() >> true
        defDomainBeanFactory.tmsTransportServiceProxy() >> defTmsTransportServiceProxy
        defTmsTransportServiceProxy.queryDriver(defDirectorId) >> new DriverInfo()
        defDomainBeanFactory.archCoreInfoService() >> defArchCoreInfoService
        defArchCoreInfoService.decryptIdCard(any() as String) >> "320682199211091188"

        when:
        def response = queryDirectorCommemorationEngine.execute(request)

        then:
        with(response) {
            type == 0
            activeDayCount == 0
            activeDayType == 0
            directorName == Strings.EMPTY
            directorName == Strings.EMPTY
            image == Strings.EMPTY
        }
   }
}
