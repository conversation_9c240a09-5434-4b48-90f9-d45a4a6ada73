package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.account.AccountDetailDTO
import com.ctrip.dcs.driver.domain.account.QueryAccountByDriverIdResponseType
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.DriverDomainServiceProxy
import spock.lang.Specification

class DriverAccountServiceImplTest extends Specification {
    def driverDomainServiceProxy = Mock(DriverDomainServiceProxy)

    def service = new DriverAccountServiceImpl(
            driverDomainServiceProxy: driverDomainServiceProxy
    )

    def testGetUid() {
        given:
        driverDomainServiceProxy.queryAccountByDriverId(_) >> responseType

        when:
        def result = service.getUid(1L)


        then:
        uid == result
        where:
        responseType                                 | uid
        null                                         | ""
        new QueryAccountByDriverIdResponseType()     | ""
        new QueryAccountByDriverIdResponseType(
                accountDetail: new AccountDetailDTO(
                        uid: "123"
                )
        )                                            | "123"
    }
}
