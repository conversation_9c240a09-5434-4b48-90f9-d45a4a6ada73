package com.ctrip.dcs.driver.domain.application.exector.account

import com.ctrip.dcs.driver.domain.account.QueryAccountProcessRequestType
import com.ctrip.dcs.driver.domain.account.QueryAccountProcessResponseType
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverAccountParamConfig
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.Mockito.when

class QueryAccountProcessEngineTest extends Specification {
    @Mock
    DriverAccountParamConfig driverAccountParamConfig
    @InjectMocks
    QueryAccountProcessEngine queryAccountProcessEngine

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        def req = new QueryAccountProcessRequestType(cityId: 1l,driverId: 1l)
        given:
        when(driverAccountParamConfig.isGrey(anyLong(), anyLong())).thenReturn(true)

        when:
        QueryAccountProcessResponseType result = queryAccountProcessEngine.execute(req)

        then:
        result.responseResult.returnCode == "200"
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme