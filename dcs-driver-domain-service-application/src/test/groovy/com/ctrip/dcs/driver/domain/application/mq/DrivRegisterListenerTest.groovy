package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.domain.application.service.DriverLevelService
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDAlDao
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverLevelGreyConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.google.common.collect.Lists
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.time.LocalDateTime

class DrivRegisterListenerTest extends Specification {
    DriverLevelGreyConfig driverLevelGreyConfig = Mock(DriverLevelGreyConfig)
    LevelConfig levelConfig = Mock(LevelConfig)
    DriverLevelService driverLevelService = Mock(DriverLevelService)
    DrivLevelDAlDao drivLevelDAlDao = Mock(DrivLevelDAlDao)
    GeoGateway geoGateway = Mock(GeoGateway)
    DrivRegisterListener drivRegisterListener

    def setup() {
        drivRegisterListener = new DrivRegisterListener()
        drivRegisterListener.driverLevelGreyConfig = driverLevelGreyConfig
        drivRegisterListener.levelConfig = levelConfig
        drivRegisterListener.drivLevelDAlDao = drivLevelDAlDao
        drivRegisterListener.geoGateway = geoGateway
        drivRegisterListener.driverLevelService = driverLevelService
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.setProperty("dataChange", "12");
        message.setProperty("proLineList", "1");

        given:
        driverLevelGreyConfig.isInGrey(_) >> true
        geoGateway.getLocalCurrentTime(_) >> LocalDateTime.now()
        levelConfig.getCityDriverLevel(_) >> Lists.newArrayList()
        driverLevelService.getDefaultForm(_, _) >> new FormLevelInfo(id: 1L, levelName: "aaa", level: 100)
        driverLevelService.getDefaultForm(_, _) >> new FormLevelInfo(id: 1L, levelName: "aaa", level: 100)

        when:
        drivRegisterListener.onMessage(message)

        then:
        1 * drivLevelDAlDao.insertDuplicateUpdate(_, _) >> 1
    }
}