package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.Mockito.when

class DrivLevelListenerTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    DrivRightsDao drivRightsDao
    @Mock
    RightsConfig rightsConfig
    @Mock
    RightsRedisLogic rightsRedisLogic
    @InjectMocks
    DrivLevelListener drivLevelListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.setProperty("dataChange", data);

        given:
        when(rightsConfig.getRightsInfo(anyLong(), anyInt())).thenReturn([new FormRightsInfo(0l, 0, "rightsName", "rightsDesc", [null], [null], "cityIdsStr", [1l], 0, 0, "extend")])

        when:
        drivLevelListener.onMessage(message)

        then:
        true
        where:
        data || res
        "{\"otterParseTime\":1686741080032,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"driv_rights\"},\"otterSendTime\":1686741080033,\"beforeColumnList\":[],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"897\"},{\"isNull\":false,\"name\":\"rights_config_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"rights_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"福利金名称\"},{\"isNull\":false,\"name\":\"rights_desc\",\"isKey\":false,\"isUpdated\":true,\"value\":\"福利金简介\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"month_idx\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06\"},{\"isNull\":false,\"name\":\"driv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1233\"},{\"isNull\":false,\"name\":\"driv_level\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"rights_type\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"use_limit\",\"isKey\":false,\"isUpdated\":true,\"value\":\"99\"},{\"isNull\":false,\"name\":\"use_count\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"rights_status\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"rights_strat_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06-01 00:00:00\"},{\"isNull\":false,\"name\":\"rights_end_time\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06-30 23:59:59\"},{\"isNull\":false,\"name\":\"extend\",\"isKey\":false,\"isUpdated\":true,\"value\":\"{\\\"welfareLimit\\\":50,\\\"welfareUse\\\":0}\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06-14 19:11:20.004\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06-14 19:11:20.004\"}],\"tableName\":\"driv_rights\"}"||true
        "{\"otterParseTime\":1686709988969,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"driv_level\"},\"otterSendTime\":1686709988971,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"147\"},{\"isNull\":false,\"name\":\"level_config_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"level_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"青铜司机\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"month_idx\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-06\"},{\"isNull\":false,\"name\":\"driv_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"105\"},{\"isNull\":false,\"name\":\"driv_level\",\"isKey\":false,\"isUpdated\":false,\"value\":\"3\"},{\"isNull\":false,\"name\":\"driv_point\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0.00\"},{\"isNull\":false,\"name\":\"driv_activity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0.00\"},{\"isNull\":false,\"name\":\"driv_rank\",\"isKey\":false,\"isUpdated\":false,\"value\":\"7\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-06-13 11:44:09.534\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-06-13 11:44:45.080\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"147\"},{\"isNull\":false,\"name\":\"level_config_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"level_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"青铜司机\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2\"},{\"isNull\":false,\"name\":\"month_idx\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-06\"},{\"isNull\":false,\"name\":\"driv_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"105\"},{\"isNull\":false,\"name\":\"driv_level\",\"isKey\":false,\"isUpdated\":true,\"value\":\"4\"},{\"isNull\":false,\"name\":\"driv_point\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0.00\"},{\"isNull\":false,\"name\":\"driv_activity\",\"isKey\":false,\"isUpdated\":false,\"value\":\"0.00\"},{\"isNull\":false,\"name\":\"driv_rank\",\"isKey\":false,\"isUpdated\":false,\"value\":\"7\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-06-13 11:44:09.534\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06-14 10:33:08.959\"}],\"tableName\":\"driv_level\"}"|| true
        "{\"otterParseTime\":1686741079712,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcstransportdb\",\"tableName\":\"driv_level\"},\"otterSendTime\":1686741079724,\"beforeColumnList\":[],\"eventType\":\"INSERT\",\"schemaName\":\"dcstransportdb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":true,\"value\":\"333\"},{\"isNull\":false,\"name\":\"level_config_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"level_name\",\"isKey\":false,\"isUpdated\":true,\"value\":\"青铜司机\"},{\"isNull\":false,\"name\":\"city_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"month_idx\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06\"},{\"isNull\":false,\"name\":\"driv_id\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1233\"},{\"isNull\":false,\"name\":\"driv_level\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"driv_point\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"driv_activity\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"driv_rank\",\"isKey\":false,\"isUpdated\":true,\"value\":\"\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06-14 19:11:19.697\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-06-14 19:11:19.697\"}],\"tableName\":\"driv_level\"}"||true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme