package com.ctrip.dcs.driver.domain.application.helper

import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfiguration
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverDeviceQConfig
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class DriverDeviceInfoLimitedRefresherTest extends Specification {
    @Mock
    DirectorRedis redisClient

    @Mock
    BusinessConfiguration config

    @Mock
    DriverDeviceQConfig driverDeviceConfig

    @InjectMocks
    DriverDeviceInfoLimitedRefresher refresher

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "allowRefresh"() {
        given:
        when(config.getDriverDeviceConfig()).thenReturn(driverDeviceConfig)
        when(config.getDriverDeviceConfig().getInfoRefreshIntervalSeconds()).thenReturn(10L)
        when(redisClient.get("driver_device_info_refresh_1#10001")).thenReturn("222")
        when(redisClient.set(anyString(), anyString(), anyLong())).thenReturn(Boolean.TRUE)
        expect:
        result == refresher.allowRefresh(driverId, appid)
        where:
        result | driverId | appid
        false  | 1L       | "10001"
        true   | 2L       | "10001"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme