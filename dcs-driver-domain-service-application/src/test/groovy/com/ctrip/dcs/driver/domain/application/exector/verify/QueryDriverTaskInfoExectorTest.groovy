package com.ctrip.dcs.driver.domain.application.exector.verify

import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO
import com.ctrip.dcs.driver.domain.task.DriverTaskStatus
import com.ctrip.dcs.driver.domain.task.QueryDriverTaskInfoRequestType
import spock.lang.Specification
import spock.lang.Subject

import java.time.LocalDateTime

class QueryDriverTaskInfoExectorTest extends Specification {

    @Subject
    QueryDriverTaskInfoExector executor = new QueryDriverTaskInfoExector()

    DriverTaskAdapter driverTaskAdapter = Mock()

    def setup() {
        executor.driverTaskAdapter = driverTaskAdapter
    }

    def "querydrivertaskinfoexectortest.driverOrderId"() {
        given:
        def request = new QueryDriverTaskInfoRequestType(driverId: 123L, driverOrderId: "ORDER_001")
        def taskRecord = validTaskRecord()

        when:
        def response = executor.execute(request)

        then:
        1 * driverTaskAdapter.queryDriverTaskByDriverOrderId(123L, "ORDER_001") >> taskRecord
        response.task != null
        response.task.taskId == "TASK_001"
        response.responseResult.returnCode == "200"
    }

    def "querydrivertaskinfoexectortest.taskId"() {
        given:
        def request = new QueryDriverTaskInfoRequestType(driverId: 123L, taskId: "TASK_001")
        def taskRecord = validTaskRecord()

        when:
        def response = executor.execute(request)

        then:
        1 * driverTaskAdapter.queryDriverTaskByTaskId(123L, "TASK_001") >> taskRecord
        response.task != null
        response.task.taskId == "TASK_001"
        response.responseResult.returnCode == "200"
    }

    def "querydrivertaskinfoexectortest.novalidtask"() {
        given:
        def request = new QueryDriverTaskInfoRequestType(driverId: 123L, taskId: "TASK_001")

        when:
        def response = executor.execute(request)

        then:
        1 * driverTaskAdapter.queryDriverTaskByTaskId(123L, "TASK_001") >> null
        response.task == null
        response.responseResult.returnCode == "200"
    }

    def "querydrivertaskinfoexectortest.checkTaskValidreturnfalse"() {
        given:
        def invalidTaskRecord = invalidTaskRecord()

        expect:
        !executor.checkTaskValid(invalidTaskRecord)
    }

    def "querydrivertaskinfoexectortest.convertToTaskInfoDTO"() {
        given:
        def taskRecord = validTaskRecord()

        when:
        def result = executor.convertToTaskInfoDTO(taskRecord)

        then:
        result.taskId == "TASK_001"
        result.taskStatus == DriverTaskStatus.CREATE
        result.customerOrderId == "C_ORDER_001"
        result.driverOrderId == "ORDER_001"
        result.carId == 1024996
    }

    private DriverTaskRecordDO validTaskRecord() {
        new DriverTaskRecordDO(
                driverId: 123L,
                taskId: "TASK_001",
                driverOrderId: "ORDER_001",
                customerOrderId: "C_ORDER_001",
                customerOrderCarId: 1024996,
                taskStatus: DriverTaskStatusEnum.CREATE,
                dataChangeCreateTime: LocalDateTime.now().minusHours(23)
        )
    }

    private DriverTaskRecordDO invalidTaskRecord() {
        new DriverTaskRecordDO(
                driverId: 123L,
                taskId: "TASK_001",
                driverOrderId: "ORDER_001",
                customerOrderId: "C_ORDER_001",
                customerOrderCarId: 1024996,
                taskStatus: DriverTaskStatusEnum.EXPIRE,
                dataChangeCreateTime: LocalDateTime.now().minusHours(25)
        )
    }
}