package com.ctrip.dcs.driver.domain.application.schedule

import com.ctrip.dcs.driver.domain.application.common.CommonExamSecretUtils
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.domain.exam.ApplyExamObjectImpl
import com.ctrip.dcs.driver.domain.application.domain.exector.SaveApplyExamExecutor
import com.ctrip.dcs.driver.domain.application.exector.rights.UseRightsEngine
import com.ctrip.dcs.driver.domain.application.http.HttpClient
import com.ctrip.dcs.driver.domain.application.redis.ExamRedisLogic
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic
import com.ctrip.dcs.driver.domain.application.service.GuideApplyExamDBDataService
import com.ctrip.dcs.driver.domain.application.service.GuideInfoDBDataService
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService
import com.ctrip.dcs.driver.domain.application.service.impl.GuideApplyExamDBDataServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideApplyExamDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideExamScoreDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDAlDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DriverPointTotalInfoDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DriverPointTotalInfoPO
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.RightsReissueDto
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideInfoModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.DriverPointModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverLevelGreyConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.ExamInterfaceConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.GuideExamConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Before
import org.junit.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.schedule.MockParameter
import qunar.tc.schedule.TaskHolder
import spock.lang.Specification

import java.sql.Timestamp
import java.time.LocalDate

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

class GuideExamScheduleLogicTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    GuideApplyExamDao guideApplyExamDao = Mock(GuideApplyExamDao);
    @Mock
    GuideExamScoreDao guideExamScoreDao = Mock(GuideExamScoreDao);
    @Mock
    GuideApplyExamDBDataService guideApplyExamDBDataService = Mock(GuideApplyExamDBDataService);
    @Mock
    GuideInfoDBDataService guideInfoDBDataService = Mock(GuideInfoDBDataService);
    @Mock
    ExamRedisLogic examRedisLogic = Mock(ExamRedisLogic);
    @Mock
    GuideExamConfig guideExamConfig = Mock(GuideExamConfig);
    @Mock
    ExamInterfaceConfig examInterfaceConfig = Mock(ExamInterfaceConfig);
    @Mock
    LockService lockService = Mock(LockService);
    @Mock
    DomainBeanFactory domainBeanFactory
    @Mock
    HttpClient httpClient = Mock(HttpClient)
    @Mock
    CommonExamSecretUtils commonExamSecretUtils = Mock(CommonExamSecretUtils)
    @InjectMocks
    GuideExamScheduleLogic guideExamScheduleLogic


    def setup() {
        domainBeanFactory = new DomainBeanFactory(guideExamConfig: guideExamConfig, guideApplyExamDBDataService: guideApplyExamDBDataService, examInterfaceConfig: examInterfaceConfig, examRedisLogic: examRedisLogic, httpClient: httpClient,commonExamSecretUtils:commonExamSecretUtils,guideInfoDBDataService:guideInfoDBDataService)
        guideApplyExamDBDataService = new GuideApplyExamDBDataServiceImpl(guideApplyExamDao: guideApplyExamDao)
        guideExamScheduleLogic = new GuideExamScheduleLogic(domainBeanFactory:domainBeanFactory,guideExamScoreDao:guideExamScoreDao,guideApplyExamDao:guideApplyExamDao,lockService:lockService);
    }

    def "query exam dept list"(){
        given:
        domainBeanFactory.guideApplyExamDBDataService().queryApplyFailedRecords(0, 1000) >> [new GuideApplyExamModel(id: 1l, guideId: 2129l, examAccountId: "guide0002129", guideName: "测试", account: "***********", applySubject: "DDXDCJ2024", applyTime: "2023-11-13 07:12:12", timeZone: new BigDecimal("8"), subjectName: "中级笔试", applyResult: 1, examIsPassed: 1, callExamSuccess: 1, datachangeCreatetime: "2023-11-15 09:12:13")]
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.queryValidityExamMap() >> getValidityGuideExamInfoMap()
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examRedisLogic.getAccessToken() >> "y2HAwYy5xDFCclBdLCDfEA__"
        examInterfaceConfig.getRootDeptId() >> "DDXD"
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getQueryDeptInfoUrl() >> "/api/getDeptList?access_token=%s"
        httpClient.post("https://t-open-api.exexm.com/api/getDeptList?access_token=y2HAwYy5xDFCclBdLCDfEA__",
                "{\"codes\":[\"DDXD\"],\"currentPage\":1,\"exFields\":[\"ContainsChild\"],\"pageSize\":100}") >> "{\"code\":200,\"msg\":\"ok\",\"data\":[{\"Id\":\"9990120222556416\",\"Code\":\"DDXDZJ2024\",\"Name\":\"当地向导中级2024\",\"Abbreviation\":\"\",\"DepartmentLevelId\":null,\"DepartmentLevelCode\":null,\"DepartmentLevelName\":null,\"IsCompany\":0,\"IsStore\":0,\"ParentId\":\"9990117658133504\",\"ParentCode\":\"DDXDZJ\",\"ParentName\":\"当地向导中级\",\"LegalPersonId\":null,\"LegalPersonCode\":null,\"LegalPersonName\":null,\"AreaId\":null,\"AreaCode\":null,\"AreaName\":null,\"ExpireFrom\":0,\"ExpireTo\":99999999,\"Tags\":\"\",\"Remark\":\"\",\"CustomerOpenId\":\"\",\"CompanyId\":14,\"ModifyTime\":20231031153914},{\"Id\":\"9990118793217792\",\"Code\":\"DDXDCJ2024\",\"Name\":\"当地向导初级2024\",\"Abbreviation\":\"\",\"DepartmentLevelId\":null,\"DepartmentLevelCode\":null,\"DepartmentLevelName\":null,\"IsCompany\":0,\"IsStore\":0,\"ParentId\":\"9990116265033984\",\"ParentCode\":\"DDXDCJ\",\"ParentName\":\"当地向导初级\",\"LegalPersonId\":null,\"LegalPersonCode\":null,\"LegalPersonName\":null,\"AreaId\":null,\"AreaCode\":null,\"AreaName\":null,\"ExpireFrom\":0,\"ExpireTo\":99999999,\"Tags\":\"\",\"Remark\":\"\",\"CustomerOpenId\":\"\",\"CompanyId\":14,\"ModifyTime\":20231031153851},{\"Id\":\"9990117658133504\",\"Code\":\"DDXDZJ\",\"Name\":\"当地向导中级\",\"Abbreviation\":\"\",\"DepartmentLevelId\":null,\"DepartmentLevelCode\":null,\"DepartmentLevelName\":null,\"IsCompany\":0,\"IsStore\":0,\"ParentId\":\"9990114508145664\",\"ParentCode\":\"DDXD\",\"ParentName\":\"当地向导部门\",\"LegalPersonId\":null,\"LegalPersonCode\":null,\"LegalPersonName\":null,\"AreaId\":null,\"AreaCode\":null,\"AreaName\":null,\"ExpireFrom\":0,\"ExpireTo\":99999999,\"Tags\":\"\",\"Remark\":\"\",\"CustomerOpenId\":\"\",\"CompanyId\":14,\"ModifyTime\":20231031153830},{\"Id\":\"9990116265033984\",\"Code\":\"DDXDCJ\",\"Name\":\"当地向导初级\",\"Abbreviation\":\"\",\"DepartmentLevelId\":null,\"DepartmentLevelCode\":null,\"DepartmentLevelName\":null,\"IsCompany\":0,\"IsStore\":0,\"ParentId\":\"9990114508145664\",\"ParentCode\":\"DDXD\",\"ParentName\":\"当地向导部门\",\"LegalPersonId\":null,\"LegalPersonCode\":null,\"LegalPersonName\":null,\"AreaId\":null,\"AreaCode\":null,\"AreaName\":null,\"ExpireFrom\":0,\"ExpireTo\":99999999,\"Tags\":\"\",\"Remark\":\"\",\"CustomerOpenId\":\"\",\"CompanyId\":14,\"ModifyTime\":20231031153811},{\"Id\":\"9990114508145664\",\"Code\":\"DDXD\",\"Name\":\"当地向导部门\",\"Abbreviation\":\"\",\"DepartmentLevelId\":null,\"DepartmentLevelCode\":null,\"DepartmentLevelName\":null,\"IsCompany\":0,\"IsStore\":0,\"ParentId\":\"\",\"ParentCode\":null,\"ParentName\":null,\"LegalPersonId\":null,\"LegalPersonCode\":null,\"LegalPersonName\":null,\"AreaId\":null,\"AreaCode\":null,\"AreaName\":null,\"ExpireFrom\":0,\"ExpireTo\":99999999,\"Tags\":\"\",\"Remark\":\"\",\"CustomerOpenId\":\"\",\"CompanyId\":14,\"ModifyTime\":**************}],\"currentPage\":1,\"pageSize\":100,\"totalCount\":5,\"totalPage\":1}"

        when:
        Boolean result = guideExamScheduleLogic.queryExamDeptList(null)

        then:
        result == null
    }

    def "retry apply exam"(){
        given:
        domainBeanFactory.guideApplyExamDBDataService().countCallExamFailed() >> 1L
        domainBeanFactory.guideApplyExamDBDataService().queryCallExamFailedRecords(0, 1000) >> [new GuideApplyExamModel(id: 1l, guideId: 2129l, examAccountId: "guide0002129", guideName: "测试", account: "***********", applySubject: "DDXDCJ2024", applyTime: "2023-11-13 07:12:12", timeZone: new BigDecimal("8"), subjectName: "中级笔试", applyResult: 1, examIsPassed: 1, callExamSuccess: 1, datachangeCreatetime: "2023-11-15 09:12:13")]
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.queryValidityExamMap() >> getValidityGuideExamInfoMap()
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examRedisLogic.getAccessToken() >> "y2HAwYy5xDFCclBdLCDfEA__"
        examInterfaceConfig.getRootDeptId() >> "DDXD"
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getChangeUserInfoUrl() >> "/api/dataexchange/user?access_token=%s"
        httpClient.post("https://t-api.exexm.com/api/dataexchange/user?access_token=y2HAwYy5xDFCclBdLCDfEA__",
                "{\"fields\":[\"id\",\"user_name\",\"dept_id\"],\"data\":{\"id\":\"guide0002129\",\"user_name\":\"测试\",\"dept_id\":\"DDXDCJ2024\"}}") >> "{\"code\":200,\"msg\":\"\",\"Message\":\"\",\"data\":{\"success\":true,\"action\":0,\"id\":\"10006807694674944\",\"msg\":\"修改成功\"},\"success\":true,\"is_encrypt\":false}"

        when:
        Boolean result = guideExamScheduleLogic.retryApplyExam(null)

        then:
        result == null
    }

    def "test query user info empty"() {
        given:
        domainBeanFactory.guideApplyExamDBDataService().queryApplyFailedRecords(0, 1000) >> []

        when:
        Boolean result = guideExamScheduleLogic.queryUserInfo(new MockParameter())

        then:
        result == null
    }

    def "test query user info"() {
        given:
        domainBeanFactory.guideApplyExamDBDataService().queryApplyFailedRecords(0, 1000) >> [new GuideApplyExamModel(id: 1l, guideId: 2129l, examAccountId: "guide0002129", guideName: "测试", account: "***********", applySubject: "DDXDCJ2024", applyTime: "2023-11-13 07:12:12", timeZone: new BigDecimal("8"), subjectName: "中级笔试", applyResult: 1, examIsPassed: 1, callExamSuccess: 1, datachangeCreatetime: "2023-11-15 09:12:13")]
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.queryValidityExamMap() >> getValidityGuideExamInfoMap()
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examRedisLogic.getAccessToken() >> "y2HAwYy5xDFCclBdLCDfEA__"
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getQueryUserInfoUrl() >> "/api/GetUserInfo?access_token=%s&__v=2.0"
        httpClient.post("https://t-open-api.exexm.com/api/GetUserInfo?access_token=y2HAwYy5xDFCclBdLCDfEA__&__v=2.0","{\"user_id\":\"guide0002129\"}") >> "{\"code\":200,\"msg\":\"\",\"data\":[{\"user_id\":\"guide0002129\",\"user_name\":\"测试\",\"mobile_phone\":\"\",\"wx_unionid\":\"\",\"customer_openid\":\"\",\"dept_code\":\"DDXDCJ2024\",\"dept_name\":\"当地向导初级2024\"}],\"success\":true}"

        when:
        Boolean result = guideExamScheduleLogic.queryUserInfo(new MockParameter())

        then:
        result == null
    }

    def "query exam score info"(){
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.getQueryExamScoreGuideExamInfoList() >> getValidityGuideExamInfoList()
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examRedisLogic.getAccessToken() >> "y2HAwYy5xDFCclBdLCDfEA__"
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getQueryUserExamInfoUrl() >> "/api/GetUserExamInfo?access_token=%s&__v=2.0"
        httpClient.post("https://t-open-api.exexm.com/api/GetUserExamInfo?access_token=y2HAwYy5xDFCclBdLCDfEA__&__v=2.0",
                "{\"user_list\":[\"guide0002129\"],\"dept_code_list\":[\"DDXDCJ2024\"],\"exam_id\":\"00003\"}") >> "{\"code\":200,\"msg\":\"\",\"data\":[{\"user_id\":\"guide0002129\",\"user_name\":\"chalice\",\"dept_id\":\"9990118793217792\",\"dept_code\":\"DDXDCJ2024\",\"dept_name\":\"当地向导初级2024\",\"post_id\":\"\",\"post_name\":\"\",\"position_id\":null,\"position_name\":null,\"position_level_id\":null,\"position_level_name\":null,\"exam_id\":\"00003\",\"exam_name\":\"测试习题\",\"submit_time\":********100101,\"spent_time\":34,\"score\":42.86,\"is_passed\":0,\"type\":0,\"src_id\":\"\",\"src_name\":\"\",\"create_time\":********105820,\"course_from_type\":99,\"course_from_id\":\"\",\"examination_id\":\"****************\",\"examination_name\":\"测试习题\",\"customer_openid\":\"\"}],\"success\":true}";

        domainBeanFactory.guideApplyExamDBDataService().queryUpassedBetweenIds(0L,200L) >> getUpassedApplyExamModels()
        when:
        Boolean result = guideExamScheduleLogic.queryExamScoreInfo(new MockParameter("{\"minId\":\"0\",\"maxId\":\"200\"}"))

        then:
        result == null
    }

    def "query exam score info passed"(){
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.getQueryExamScoreGuideExamInfoList() >> getValidityGuideExamInfoList()
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examRedisLogic.getAccessToken() >> "y2HAwYy5xDFCclBdLCDfEA__"
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getQueryUserExamInfoUrl() >> "/api/GetUserExamInfo?access_token=%s&__v=2.0"
        httpClient.post("https://t-open-api.exexm.com/api/GetUserExamInfo?access_token=y2HAwYy5xDFCclBdLCDfEA__&__v=2.0",
                "{\"user_list\":[\"guide0002129\"],\"dept_code_list\":[\"DDXDCJ2024\"],\"exam_id\":\"00003\"}") >> "{\"code\":200,\"msg\":\"\",\"data\":[{\"user_id\":\"guide0002129\",\"user_name\":\"chalice\",\"dept_id\":\"9990118793217792\",\"dept_code\":\"DDXDCJ2024\",\"dept_name\":\"当地向导初级2024\",\"post_id\":\"\",\"post_name\":\"\",\"position_id\":null,\"position_name\":null,\"position_level_id\":null,\"position_level_name\":null,\"exam_id\":\"00003\",\"exam_name\":\"测试习题\",\"submit_time\":********100101,\"spent_time\":34,\"score\":42.86,\"is_passed\":1,\"type\":0,\"src_id\":\"\",\"src_name\":\"\",\"create_time\":********105820,\"course_from_type\":99,\"course_from_id\":\"\",\"examination_id\":\"****************\",\"examination_name\":\"测试习题\",\"customer_openid\":\"\"}],\"success\":true}";

        domainBeanFactory.guideApplyExamDBDataService().queryUpassedBetweenIds(0L,200L) >> getUpassedApplyExamModels()
        when:
        Boolean result = guideExamScheduleLogic.queryExamScoreInfo(new MockParameter("{\"minId\":\"0\",\"maxId\":\"200\"}"))

        then:
        result == null
    }

    def "query exam score info by user"(){
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.getQueryExamScoreGuideExamInfoList() >> getValidityGuideExamInfoList()
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examRedisLogic.getAccessToken() >> "y2HAwYy5xDFCclBdLCDfEA__"
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getQueryUserExamInfoUrl() >> "/api/GetUserExamInfo?access_token=%s&__v=2.0"
        httpClient.post("https://t-open-api.exexm.com/api/GetUserExamInfo?access_token=y2HAwYy5xDFCclBdLCDfEA__&__v=2.0",
                "{\"user_list\":[\"guide0002129\"],\"dept_code_list\":[\"DDXDCJ2024\"],\"begin_date\":\"********\",\"end_date\":\"********\"}") >> "{\"code\":200,\"msg\":\"\",\"data\":[{\"user_id\":\"guide0002129\",\"user_name\":\"chalice\",\"dept_id\":\"9990118793217792\",\"dept_code\":\"DDXDCJ2024\",\"dept_name\":\"当地向导初级2024\",\"post_id\":\"\",\"post_name\":\"\",\"position_id\":null,\"position_name\":null,\"position_level_id\":null,\"position_level_name\":null,\"exam_id\":\"00003\",\"exam_name\":\"测试习题\",\"submit_time\":********100101,\"spent_time\":34,\"score\":42.86,\"is_passed\":1,\"type\":0,\"src_id\":\"\",\"src_name\":\"\",\"create_time\":********105820,\"course_from_type\":99,\"course_from_id\":\"\",\"examination_id\":\"****************\",\"examination_name\":\"测试习题\",\"customer_openid\":\"\"}],\"success\":true}";

        domainBeanFactory.guideApplyExamDBDataService().queryUpassedBetweenIds(0L,200L) >> getUpassedApplyExamModels()
        when:
        Boolean result = guideExamScheduleLogic.queryExamScoreInfoByUser(new MockParameter("{\"examAccountId\":\"guide0002129\",\"applySubject\":\"DDXDCJ2024\",\"beginDate\":\"********\",\"endDate\":\"********\"}"))

        then:
        result == null
    }

    def "query exam score info by user passed"(){
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.getQueryExamScoreGuideExamInfoList() >> getValidityGuideExamInfoList()
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examRedisLogic.getAccessToken() >> "y2HAwYy5xDFCclBdLCDfEA__"
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getQueryUserExamInfoUrl() >> "/api/GetUserExamInfo?access_token=%s&__v=2.0"
        httpClient.post("https://t-open-api.exexm.com/api/GetUserExamInfo?access_token=y2HAwYy5xDFCclBdLCDfEA__&__v=2.0",
                "{\"user_list\":[\"guide0002129\"],\"dept_code_list\":[\"DDXDCJ2024\"],\"begin_date\":\"********\",\"end_date\":\"********\"}") >> "{\"code\":200,\"msg\":\"\",\"data\":[{\"user_id\":\"guide0002129\",\"user_name\":\"chalice\",\"dept_id\":\"9990118793217792\",\"dept_code\":\"DDXDCJ2024\",\"dept_name\":\"当地向导初级2024\",\"post_id\":\"\",\"post_name\":\"\",\"position_id\":null,\"position_name\":null,\"position_level_id\":null,\"position_level_name\":null,\"exam_id\":\"00003\",\"exam_name\":\"测试习题\",\"submit_time\":********100101,\"spent_time\":34,\"score\":42.86,\"is_passed\":1,\"type\":0,\"src_id\":\"\",\"src_name\":\"\",\"create_time\":********105820,\"course_from_type\":99,\"course_from_id\":\"\",\"examination_id\":\"****************\",\"examination_name\":\"测试习题\",\"customer_openid\":\"\"}],\"success\":true}";

        domainBeanFactory.guideApplyExamDBDataService().queryUpassedBetweenIds(0L,200L) >> getUpassedApplyExamModels()
        domainBeanFactory.guideApplyExamDBDataService().queryGuideApplyExamByAccountAndSubject("guide0002129","DDXDCJ2024") >> getGuideApplyExamModel1()
        when:
        Boolean result = guideExamScheduleLogic.queryExamScoreInfoByUser(new MockParameter("{\"examAccountId\":\"guide0002129\",\"applySubject\":\"DDXDCJ2024\",\"beginDate\":\"********\",\"endDate\":\"********\"}"))

        then:
        result == null
    }

    def "query exam access token"(){
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        domainBeanFactory.httpClient() >> httpClient
        domainBeanFactory.commonExamSecretUtils() >> commonExamSecretUtils
        commonExamSecretUtils.secret() >> "junittestsecret"
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examInterfaceConfig.getOpenapiDomainName() >> "https://t-open-api.exexm.com"
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getOpenAuthInterfaceUrl() >> "/api/oauth/token?tenantid=%s&secret=%s"
        examInterfaceConfig.getTenantid() >> "hq"
        httpClient.get("https://t-sso.exexm.com/api/oauth/token?tenantid=hq&secret=junittestsecret")>> "{\"code\":200,\"msg\":\"ok\",\"data\":{\"access_token\":\"P4cYP7oh*sDYWj6ryaMBRA__\",\"expires_in\":618.0},\"success\":true}"
        when:
        Boolean result = guideExamScheduleLogic.queryExamAccessToken(null)

        then:
        result == null
    }

    def "preheat guide tag exam cache"(){
        given:
        domainBeanFactory.guideInfoDBDataService().queryMaxGuideId() >> new GuideInfoModel(guideId: 100L)
        domainBeanFactory.guideInfoDBDataService().queryGuideInfoListByGuideId(0L,200L) >> [new GuideInfoModel(guideId: 100L)]

        when:
        Boolean result = guideExamScheduleLogic.preheatGuideTagExamCache(null);

        then:
        result == null
    }

    def "preheat guide tag exam cache by param"(){
        given:
        domainBeanFactory.guideInfoDBDataService().queryMaxGuideId() >> new GuideInfoModel(guideId: 100L)
        domainBeanFactory.guideInfoDBDataService().queryGuideInfoListByGuideId(0L,200L) >> [new GuideInfoModel(guideId: 100L)]

        when:
        Boolean result = guideExamScheduleLogic.preheatGuideTagExamCache(new MockParameter("{\"minGuideId\":\"0\",\"maxGuideId\":\"100\"}"));

        then:
        result == null
    }

    private List<GuideApplyExamModel> getUpassedApplyExamModels(){
        List<GuideApplyExamModel> guideApplyExamModels = new ArrayList<>();
        GuideApplyExamModel cjModel = getGuideApplyExamModel1();
        guideApplyExamModels.add(cjModel);
        return  guideApplyExamModels;
    }

    private GuideApplyExamModel getGuideApplyExamModel1() {
        GuideApplyExamModel model = new GuideApplyExamModel();
        model.setExamAccountId("guide0002129");
        model.setGuideId(2129L);
        model.setAccount("***********");
        model.setSubjectName("初级笔试");
        model.setApplyResult(0);
        model.setExamIsPassed(0);
        model.setCallExamSuccess(0);
        model.setGuideName("测试");
        model.setApplySubject("DDXDCJ2024");
        return model;
    }

    private List<FromGuideExamInfo> getValidityGuideExamInfoList(){
        List<FromGuideExamInfo> list = new ArrayList<>();
        list.add(getDDXDCJ());
        list.add(getDDXDZJ());
        return list;
    }

    private FromGuideExamInfo getDDXDCJ(){
        FromGuideExamInfo ddxdcj = new FromGuideExamInfo();
        ddxdcj.setStartTime("2023-10-01 00:00:00");
        ddxdcj.setEndTime("2024-12-31 23:59:59");
        ddxdcj.setExamId("00003");
        ddxdcj.setDeptId("DDXDCJ2024");
        ddxdcj.setParentDeptId("DDXDCJ");
        ddxdcj.setDeptName("初级笔试");
        ddxdcj.setStartTimeSecond(1696089600000L);
        return ddxdcj;
    }

    private FromGuideExamInfo getDDXDZJ(){
        FromGuideExamInfo ddxdzj = new FromGuideExamInfo();
        ddxdzj.setStartTime("2023-10-01 00:00:00");
        ddxdzj.setEndTime("2024-12-31 23:59:59");
        ddxdzj.setExamId("00002");
        ddxdzj.setDeptId("DDXDZJ2024");
        ddxdzj.setParentDeptId("DDXDZJ");
        ddxdzj.setDeptName("中级笔试");
        ddxdzj.setStartTimeSecond(1696089600000L);
        return ddxdzj;
    }

    private Map<String, FromGuideExamInfo> getValidityGuideExamInfoMap() {
        Map<String,FromGuideExamInfo> map = new HashMap<>();
        map.put("DDXDCJ2024",getDDXDCJ());
        map.put("DDXDZJ2024",getDDXDZJ());
        return map;
    }

}
//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
