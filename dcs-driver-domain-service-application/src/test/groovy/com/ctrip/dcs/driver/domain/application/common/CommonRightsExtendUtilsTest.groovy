package com.ctrip.dcs.driver.domain.application.common

import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import spock.lang.Specification

class CommonRightsExtendUtilsTest extends Specification {

    def "test get Reassign Use Count"() {
        when:
        int result = CommonRightsExtendUtils.getReassignUseCount(extend)

        then:
        result == count
        where:
        extend                                                     || count
        "{\"weekUseCount\":1,\"useTime\":\"2022-06-13 18:07:37\"}" || 0
        ""                                                         || 0
    }

    def "test get Welfare Total"() {
        when:
        BigDecimal result = CommonRightsExtendUtils.getWelfareTotal(extend)

        then:
        result == money
        where:
        extend                                      || money
        "{\"welfareLimit\":50,\"welfareUse\":1001}" || 1001 as BigDecimal
        ""                                          || 0 as BigDecimal
    }

    def "test build Extend"() {
        when:
        String result = CommonRightsExtendUtils.buildExtend(rightsType, "100")

        then:
        result == extend

        where:
        rightsType || extend
        1          || "{\"welfareLimit\":100,\"welfareUse\":0,\"welfareFreeze\":0}"
        0          || ""
        7          || "{\"vacationLimit\":100,\"vacationUse\":0}"
    }

    def "test build Extend For Update"() {
        when:
        String result = CommonRightsExtendUtils.buildExtendForUpdate(model, 10 as BigDecimal,0)

        then:
        result !=null

        where:
        model                                                                                                                                                                                      || extend
        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 1, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "{\"welfareLimit\":50,\"welfareUse\":1001}") || "{\"welfareLimit\":50,\"welfareUse\":1011}"
        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 2, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "{\"welfareLimit\":50,\"welfareUse\":1001}") || "{\"weekUseCount\":1,\"useTime\":\"2023-06-14 18:00:56\"}"
        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 3, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "{\"welfareLimit\":50,\"welfareUse\":1001}") || ""
        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 7, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "{\"welfareLimit\":50,\"welfareUse\":1001}") || "{\"vacationLimit\":2,\"vacationUse\":0}"
    }

    def "test_buildExtendForWelfareFreeze"() {
        when:
        String result = CommonRightsExtendUtils.buildExtendForWelfareFreeze(extend, 10 as BigDecimal)

        then:
        result !=null

        where:
        extend || _
        "{\"welfareLimit\":50,\"welfareUse\":1011}" || _
        "" || _
    }

    def "test_buildExtendForReverse"() {
        when:
        String result = CommonRightsExtendUtils.buildExtendForReverse(extend, 10 as BigDecimal)

        then:
        result !=null

        where:
        extend || _
        "{\"welfareLimit\":50,\"welfareUse\":1011}" || _
        "" || _
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme