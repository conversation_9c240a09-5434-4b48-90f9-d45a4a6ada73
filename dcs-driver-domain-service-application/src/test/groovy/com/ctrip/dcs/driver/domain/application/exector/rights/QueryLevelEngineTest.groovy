package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.rights.QueryLevelRequestType
import com.ctrip.dcs.driver.domain.rights.QueryLevelResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import spock.lang.Specification

class QueryLevelEngineTest extends Specification {
    QueryLevelEngine queryLevelEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsConfig rightsConfig = Mock(RightsConfig)
    LevelConfig levelConfig = Mock(LevelConfig)
    RightsRepoServiceImpl rightsRepoService = Mock(RightsRepoServiceImpl)


    def setup() {
        domainBeanFactory = new DomainBeanFactory(levelConfig: levelConfig, rightsConfig: rightsConfig, rightsRepoService: rightsRepoService)
        queryLevelEngine = new QueryLevelEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {
        def request = new QueryLevelRequestType(driverId: 1, date: "2023-06")

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryLevelResponseType.class).init()
        domainBeanFactory.rightsRepoService() >> rightsRepoService
        rightsRepoService.queryDriverLevel(1, "2023-06") >> new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0)
        when:
        QueryLevelResponseType result = queryLevelEngine.execute(request)

        then:
        result != null
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme