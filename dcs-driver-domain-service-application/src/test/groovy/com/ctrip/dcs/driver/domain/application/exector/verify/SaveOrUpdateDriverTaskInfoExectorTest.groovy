package com.ctrip.dcs.driver.domain.application.exector.verify

import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO
import com.ctrip.dcs.driver.domain.task.DriverTaskInfoDTO
import com.ctrip.dcs.driver.domain.task.DriverTaskStatus
import com.ctrip.dcs.driver.domain.task.SaveOrUpdateDriverTaskInfoRequestType
import spock.lang.Specification
import spock.lang.Subject

class SaveOrUpdateDriverTaskInfoExectorTest extends Specification {

    @Subject
    SaveOrUpdateDriverTaskInfoExector executor = new SaveOrUpdateDriverTaskInfoExector()
    DriverTaskAdapter driverTaskAdapter = Mock()

    def setup() {
        executor.driverTaskAdapter = driverTaskAdapter
    }

    def "savedrivertaskinfoexectortest"() {
        given:
        def request = createRequest(DriverTaskStatusEnum.CREATE)
        driverTaskAdapter.insertTaskRecord(_) >> 1

        when:
        def response = executor.execute(request)

        then:
        response.responseResult.returnCode == "200"
    }

    def "savedrivertaskinfoexectortest.finiished"() {
        given:
        def request = createRequest(DriverTaskStatusEnum.FINISHED)

        when:
        def response = executor.execute(request)

        then:
        1 * driverTaskAdapter.updateTaskStatus("TASK_001", DriverTaskStatusEnum.FINISHED)
        response.responseResult.returnCode == "200"
    }

    def "savedrivertaskinfoexectortest.otherstatuses"() {
        given:
        def request = createRequest(DriverTaskStatusEnum.EXPIRE)

        when:
        def response = executor.execute(request)

        then:
        0 * driverTaskAdapter.insertTaskRecord(_)
        0 * driverTaskAdapter.updateTaskStatus(_, _)
        response.responseResult.returnCode == "200"
    }

    def "convertToDriverTaskRecord should map all fields correctly"() {
        given:
        def request = createRequest(DriverTaskStatusEnum.CREATE)

        when:
        def result = executor.convertToDriverTaskRecord(request)

        then:
        with(result) {
            driverId == 123L
            taskId == "TASK_001"
            taskStatus == DriverTaskStatusEnum.CREATE
            customerOrderId == "C_ORDER_001"
            driverOrderId == "D_ORDER_001"
            customerOrderCarId == 1024
            taskPeriodWorkTime == "TASK_001" // 根据代码逻辑，这里可能存在问题需要确认
        }
    }

    private SaveOrUpdateDriverTaskInfoRequestType createRequest(DriverTaskStatusEnum status) {
        new SaveOrUpdateDriverTaskInfoRequestType(
                driverId: 123L,
                task: new DriverTaskInfoDTO(
                        taskId: "TASK_001",
                        taskStatus: DriverTaskStatus.findByValue(status.value),
                        customerOrderId: "C_ORDER_001",
                        driverOrderId: "D_ORDER_001",
                        carId: 1024
                )
        )
    }

}