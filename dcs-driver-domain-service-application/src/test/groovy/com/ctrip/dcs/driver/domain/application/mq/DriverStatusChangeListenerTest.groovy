package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.account.domain.service.AccountService
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverAccountParamConfig
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.platform.commons.util.StringUtils
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class DriverStatusChangeListenerTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    UserCenterAccountGateway accounService
    @Mock
    DriverAccountParamConfig driverAccountParamConfig
    @Mock
    private AccountService accountService;
    @Mock
    private AccountChangeLogHelper accountChangeLogHelper;
    @Mock
    private DriverAccountConfig driverAccountConfig;
    @InjectMocks
    DriverStatusChangeListener driverStatusChangeListener

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message3"() {
        Message message = new BaseMessage();
        message.addTag("tag_driverstate_offline")
        given:
        when(accounService.unfreezeAccount(anyString())).thenReturn(true)
        when(driverAccountParamConfig.isGrey(anyLong(), anyLong())).thenReturn(true)

        when:
        driverStatusChangeListener.onMessage(message)
        message.addTag("tag_driverstate_online")
        driverStatusChangeListener.onMessage(message)


        then:
        StringUtils.isBlank(message.getStringProperty("content"))
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.addTag("tag_driverstate_online")
        message.addTag("tag_driverstate_offline")
        driverStatusChangeListener.onMessage(message)
        message.setProperty("content", "{\"uid\":\"TODG_1nnt0mql7e66\",\"modifyUser\":\"虞家荣\",\"drvId\":3455166,\"cityId\":359}");
        given:
        when(accounService.unfreezeAccount(anyString())).thenReturn(true)
        when(driverAccountParamConfig.isGrey(anyLong(), anyLong())).thenReturn(true)

        when:
        driverStatusChangeListener.onMessage(message)

        then:
        StringUtils.isNotBlank(message.getStringProperty("content"))
    }

    def "test on Message1"() {
        Message message = new BaseMessage();
        message.addTag("tag_driverstate_offline")
        message.setProperty("content", "{\"uid\":\"TODG_1nnt0mql7e66\",\"modifyUser\":\"虞家荣\",\"drvId\":3455166,\"cityId\":359}");
        given:
        when(accounService.unfreezeAccount(anyString())).thenReturn(true)
        when(driverAccountParamConfig.isGrey(anyLong(), anyLong())).thenReturn(false)

        when:
        driverStatusChangeListener.onMessage(message)
        message.addTag("tag_driverstate_online")
        driverStatusChangeListener.onMessage(message)

        then:
        StringUtils.isNotBlank(message.getStringProperty("content"))
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
