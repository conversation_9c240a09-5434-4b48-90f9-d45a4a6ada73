package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig
import com.ctrip.dcs.driver.domain.rights.QueryDriverIdByRightsRequestType
import com.ctrip.dcs.driver.domain.rights.QueryDriverIdByRightsResponseType
import com.ctrip.igt.PaginatorDTO
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyInt
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class QueryDriverIdByRightsEngineTest extends Specification {
    @Mock
    DrivRightsDao dao
    @Mock
    SystemQConfig systemQConfig
    @InjectMocks
    QueryDriverIdByRightsEngine queryDriverIdByRightsEngine

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        def request = new QueryDriverIdByRightsRequestType(rightsType: 1,date:"2021-09-10",paginator: new PaginatorDTO(1,2))

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryDriverIdByRightsResponseType.class).init()
        given:
        when(dao.countByRightsAndMonth(anyInt(), anyString())).thenReturn(1l)
        when(systemQConfig.getString("close_rights")).thenReturn("2")

        when:
        queryDriverIdByRightsEngine.validate(new AbstractValidator<>(QueryDriverIdByRightsRequestType.class))
        QueryDriverIdByRightsResponseType result = queryDriverIdByRightsEngine.execute(request)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme