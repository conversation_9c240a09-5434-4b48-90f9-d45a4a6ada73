package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.account.application.schedule.PhoneCheckTaskGenerateScheduler
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverLostContactTable
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.DriverOrderServiceProxy
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig
import com.ctrip.dcs.self.order.query.api.QueryOrderListResponseType
import com.ctrip.dcs.self.order.query.dto.BaseDetail
import com.ctrip.dcs.self.order.query.dto.OrderDetail
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

class DriverNegativeQuestionnaireListenerTest extends Specification {
    def testObj = new DriverNegativeQuestionnaireListener()
    def driverOrderServiceProxy = Mock(DriverOrderServiceProxy)
    def phoneCheckTaskGenerateScheduler = Mock(PhoneCheckTaskGenerateScheduler)
    def driverLostContactTable = Mock(DriverLostContactTable)
    def systemQConfig = Mock(SystemQConfig)


    def setup() {

        testObj.phoneCheckTaskGenerateScheduler = phoneCheckTaskGenerateScheduler
        testObj.driverOrderServiceProxy = driverOrderServiceProxy
        testObj.driverLostContactTable = driverLostContactTable
        testObj.systemQConfig = systemQConfig
    }

    @Unroll
    def "onMessageTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.doProcessOrder(_) >> {}
        systemQConfig.getString("ivr_switch") >> ""
        when:
        def result = spy.onMessage(message)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        message                                                                                                                                   || expectedResult
        new BaseMessage(attrs: ["data": "{\"sceneCode\":\"DcsSpecialQuestion\",\"orderId\":\"1658097486875309\",\"uid\":\"_TIUS1j5dsfrokyn4\"}"]) || null
    }

    @Unroll
    def "doProcessOrderTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        driverLostContactTable.insert(_) >> 0
        driverOrderServiceProxy.queryOrderList(_) >> new QueryOrderListResponseType(orderList: [new OrderDetail(baseDetail: new BaseDetail(drvId: 1L))])

        when:
        def result = testObj.doProcessOrder(orderId)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        orderId   || expectedResult
        "orderId" || null
    }
}
