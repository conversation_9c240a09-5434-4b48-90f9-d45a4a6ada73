package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType
import com.ctrip.dcs.driver.account.application.cache.AccountInfoCache
import com.ctrip.dcs.driver.account.application.cache.AppPushMessageCache
import com.ctrip.dcs.driver.convert.AccountInfoConvert
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository
import com.ctrip.dcs.driver.value.account.AccountUidInfo
import com.ctrip.dcs.driver.account.application.dto.RegisterAccountResult
import com.ctrip.dcs.driver.account.application.service.impl.AccountServiceImpl
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.PhoneNumberServiceProxy
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.GlobalIdRecordDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.GlobalIdRecordPO
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper
import com.ctrip.dcs.driver.account.infrastructure.gateway.EmailServiceGateway
import com.ctrip.dcs.driver.account.infrastructure.gateway.QunarAccountGateway
import com.ctrip.dcs.driver.account.infrastructure.qconfig.ChineseConfig
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.account.infrastructure.qconfig.EmailNotifyConfig
import com.ctrip.dcs.driver.account.infrastructure.value.AccountIdentityDTO
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO
import com.ctrip.dcs.driver.account.infrastructure.value.RegisterNewAccountParam
import com.ctrip.dcs.driver.account.infrastructure.value.UpdateAccountParam
import com.ctrip.dcs.driver.account.infrastructure.value.emil.EmailConfigInfo
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic

import com.ctrip.dcs.driver.domain.application.service.TmsTransportProxyService
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA
import com.ctrip.igt.framework.common.exception.BizException
import com.google.common.collect.Lists
import org.apache.commons.lang3.tuple.Triple
import qunar.tc.qmq.Message
import qunar.tc.qmq.producer.MessageProducerProvider
import spock.lang.Specification

class AccountServiceTest extends Specification {

    AccountServiceImpl accountService

    UserCenterAccountGateway userCenterAccountGateway = Mock(UserCenterAccountGateway)
    AccountMapperDao accountMapperDao = Mock(AccountMapperDao)
    AccountBaseInfoDao accountBaseInfoDao = Mock(AccountBaseInfoDao)
    GlobalIdRecordDao globalIdRecordDao = Mock(GlobalIdRecordDao)
    QunarAccountGateway qunarAccountGateway = Mock(QunarAccountGateway)
    AccountInfoCache accountInfoCache = Mock(AccountInfoCache)
    DriverAccountConfig driverAccountConfig = Mock(DriverAccountConfig)
    ArchCoreInfoRepository archCoreInfoService = Mock(ArchCoreInfoRepository)
    LockService lockService = Mock(LockService)
    TmsTransportProxyService tmsTransportProxyService = Mock(TmsTransportProxyService)
    ChineseConfig chineseConfig = Mock(ChineseConfig)
    EmailNotifyConfig emailNotifyConfig = Mock(EmailNotifyConfig)
    EmailServiceGateway emailServiceGateway = Mock(EmailServiceGateway)
    AccountChangeLogHelper accountChangeLogHelper = Mock(AccountChangeLogHelper)
    AppPushMessageCache appPushMessageCache = Mock(AppPushMessageCache)
    CommonMultipleLanguages commonMultipleLanguages = GroovyMock(CommonMultipleLanguages)
    PushConfigRedisLogic pushConfigRedisLogic = Mock(PushConfigRedisLogic)
    TripUniversityService tripUniversityService = Mock(TripUniversityService)
    MessageProducerProvider messageProducerProvider = Mock(MessageProducerProvider)
    Message message = Mock(Message)
    PhoneNumberServiceProxy phoneNumberService = Mock(PhoneNumberServiceProxy)
    //accountInfoConvert
    AccountInfoConvert accountInfoConvert = new AccountInfoConvert()

    def setup() {
        accountService = new AccountServiceImpl()
        accountService.userCenterAccountGateway = userCenterAccountGateway;
        accountService.accountMapperDao = accountMapperDao;
        accountService.accountBaseInfoDao = accountBaseInfoDao;
        accountService.globalIdRecordDao = globalIdRecordDao;
        accountService.qunarAccountGateway = qunarAccountGateway;
        accountService.accountInfoCache = accountInfoCache;
        accountService.driverAccountConfig = driverAccountConfig;
        accountService.archCoreInfoService = archCoreInfoService;
        accountService.lockService = lockService;
        accountService.tmsTransportProxyService = tmsTransportProxyService;
        accountService.chineseConfig = chineseConfig;
        accountService.emailNotifyConfig = emailNotifyConfig;
        accountService.emailServiceGateway = emailServiceGateway;
        accountService.accountChangeLogHelper = accountChangeLogHelper;
        accountService.appPushMessageCache = appPushMessageCache;
        accountService.commonMultipleLanguages = commonMultipleLanguages;
        accountService.pushConfigRedisLogic = pushConfigRedisLogic;
        accountService.tripUniversityService = tripUniversityService
        accountService.messageProducerProvider = messageProducerProvider
        accountService.phoneNumberService = phoneNumberService
        accountService.accountInfoConvert = accountInfoConvert
    }

    def "doRegister_checkPayoneerAccountIdFailed1"() {
        given:
        accountBaseInfoDao.queryByUID(_) >> new AccountBaseInfoPO(uid: "111")
        accountBaseInfoDao.queryByMobilePhone(_,_) >> new AccountBaseInfoPO(uid: "111")
        accountBaseInfoDao.queryByPayoneerAccountId(_) >> new AccountBaseInfoPO(uid: "222")

        when:
        accountService.doRegister(new RegisterNewAccountParam(payoneerAccountId: "2345"))

        then:
        def e1 = thrown(BizException)
        e1.code == "1517105"

    }

    def "doRegister_checkPayoneerAccountIdFailed2"() {
        given:
        accountBaseInfoDao.queryByUID(_) >> new AccountBaseInfoPO(uid: "111")
        accountBaseInfoDao.queryByMobilePhone(_,_) >> new AccountBaseInfoPO(uid: "111")
        accountBaseInfoDao.queryByPayoneerAccountId(_) >> new AccountBaseInfoPO(uid: "222")

        when:
        accountService.doRegister(new RegisterNewAccountParam(countryCode: "86", phoneNumber: "99999", payoneerAccountId: "2345"))

        then:
        def e2 = thrown(BizException)
        e2.code == "1517105"

    }

    def "checkUidUserCenterState"() {
        given:
        userCenterAccountGateway.unfreezeAccount(_) >>> [false, true]

        when:
        def res1 = accountService.checkUidUserCenterState()
        def res2 = accountService.checkUidUserCenterState("123")
        def res3 = accountService.checkUidUserCenterState("123")

        then:
        res1
        !res2
        res3
    }

    def "doUpdateAccount"() {
        given:
        messageProducerProvider.generateMessage(_) >> message
        AccountBaseInfoPO accountBaseInfo = new AccountBaseInfoPO()
        UpdateAccountParam param = new UpdateAccountParam(source: "DriverGuide", uid: "fj023jf0", name: "zc", idCardNo: "j20f", email: "jf02", countryCode: "85", phoneNumber: "29349", payoneerAccountId: "jf0290-", isOversea: true, ppmAccountId: "jf020f")

        when:
        UpdateAccountResponseType res = accountService.doUpdateAccount(accountBaseInfo, param)

        then:
        res != null
        res.getResponseResult().getReturnCode() == "200"
        1 * accountInfoCache.delAccount(_)
        1 * accountBaseInfoDao.queryByPayoneerAccountId(_) >> null
        1 * userCenterAccountGateway.bindEmail(_,_,_) >> true
        1 * userCenterAccountGateway.bindMobilePhone(_,_,_,_) >> true
        1 * accountMapperDao.queryByUid(_) >> Lists.newArrayList()
        1 * driverAccountConfig.getAccountUpdateQmqDelaySeconds() >> 2
        1 * messageProducerProvider.sendMessage(_)
    }

    def "doUpdateAccountBindEmailError"() {
        AccountBaseInfoPO accountBaseInfo = new AccountBaseInfoPO()
        UpdateAccountParam param = new UpdateAccountParam(source: "DriverGuide", uid: "fj023jf0", name: "zc", idCardNo: "j20f", email: "jf02", countryCode: "85", phoneNumber: "29349", payoneerAccountId: "jf0290-", isOversea: true, ppmAccountId: "jf020f")

        when:
        UpdateAccountResponseType res = accountService.doUpdateAccount(accountBaseInfo, param)

        then:
        res != null
        res.getResponseResult().getReturnCode() == "1117102"
        1 * accountBaseInfoDao.queryByPayoneerAccountId(_) >> null
        1 * userCenterAccountGateway.bindEmail(_,_,_) >> false
    }

    def "doUpdateAccountBindPhoneError"() {
        AccountBaseInfoPO accountBaseInfo = new AccountBaseInfoPO()
        UpdateAccountParam param = new UpdateAccountParam(source: "DriverGuide", uid: "fj023jf0", name: "zc", idCardNo: "j20f", email: "jf02", countryCode: "85", phoneNumber: "29349", payoneerAccountId: "jf0290-", isOversea: true, ppmAccountId: "jf020f")

        when:
        UpdateAccountResponseType res = accountService.doUpdateAccount(accountBaseInfo, param)

        then:
        res != null
        res.getResponseResult().getReturnCode() == "1117102"
        1 * accountBaseInfoDao.queryByPayoneerAccountId(_) >> null
        1 * userCenterAccountGateway.bindEmail(_,_,_) >> true
        1 * userCenterAccountGateway.bindMobilePhone(_,_,_,_) >> false
    }

    def "checkUpdatePhoneAndEmail"(){

        when:
        def res1 = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam())
        def res2 = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam(email: "<EMAIL>"))
        def res3 = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam(email: "<EMAIL>"))
        def res4 = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam(email: "<EMAIL>", countryCode: "86", phoneNumber: "123"))
        def res5 = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam(email: "<EMAIL>", countryCode: "66", phoneNumber: "123"))
        def res6 = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam(email: "<EMAIL>", countryCode: "86", phoneNumber: "345"))
        def res7 = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam(email: "<EMAIL>", countryCode: "66", phoneNumber: "345"))

        then:
        1 * userCenterAccountGateway.queryAccountUidByEmail(_) >> new AccountUidInfo("aaa", "udl")
        3 * userCenterAccountGateway.queryAccountUidByPhone(_, _) >> new AccountUidInfo("aaa", "udl")
        res1
        res2
        res3
        res4
        res5
        res6
        res7
    }

    def "checkUpdatePhoneAndEmail_emailExist"() {
        when:
        def res = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", email: "<EMAIL>"), new UpdateAccountParam(email: "<EMAIL>"))

        then:
        1 * userCenterAccountGateway.queryAccountUidByEmail(_) >> new AccountUidInfo("bbb", "udl")
        def e = thrown(BizException)
        e.getCode() == "1517107"
    }

    def "checkUpdatePhoneAndEmail_phoneExist"() {
        when:
        def res = accountService.checkUpdatePhoneAndEmail(new AccountBaseInfoPO(uid: "aaa", countryCode: "86", phoneNumber: "123"), new UpdateAccountParam(countryCode: "66", phoneNumber: "345"))

        then:
        1 * userCenterAccountGateway.queryAccountUidByPhone(_, _) >> new AccountUidInfo("bbb", "udl")
        def e = thrown(BizException)
        e.getCode() == "1517106"
    }



    def "getAccountInfoByName"() {
        given:
        accountBaseInfoDao.queryByName(_) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountBaseInfoPO(uid: "123"))]
        accountInfoCache.getAccount(_) >> new AccountInfoDTO(uid: "123")

        when:
        def res1 = accountService.getAccountInfoByName("")
        def res2 = accountService.getAccountInfoByName("123")
        def res3 = accountService.getAccountInfoByName("123")

        then:
        res1.size() == 0
        res2.size() == 0
        res3.size() == 1
    }

    def "getAccountInfoByIdCard"() {
        given:
        accountBaseInfoDao.queryByIdCardNo(_) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountBaseInfoPO(uid: "123"))]
        accountInfoCache.getAccount(_) >> new AccountInfoDTO(uid: "123", countryCode: "86", phoneNumber: "131xxxx1234")
        archCoreInfoService.encryptIdCard(_) >> "xxx"

        when:
        def res1 = accountService.getAccountInfoByIdCard("")
        def res2 = accountService.getAccountInfoByIdCard("123")
        def res3 = accountService.getAccountInfoByIdCard("123")

        then:
        res1.size() == 0
        res2.size() == 0
        res3.size() == 1
        res3.get(0).countryCode == "86"
        res3.get(0).phoneNumber == "131xxxx1234"
    }


    def "getAccountInfoByMobilePhone"() {
        given:
        userCenterAccountGateway.queryAccountUidByPhone(_, _) >>> [null, new AccountUidInfo("123", "udl")]
        accountInfoCache.getAccount(_) >> new AccountInfoDTO(uid: "123")
        archCoreInfoService.encryptByType(_, _) >> "fkjaldsfjl"

        when:
        def res1 = accountService.getAccountInfoByMobilePhone("","143")
        def res2 = accountService.getAccountInfoByMobilePhone("123", "*********")
        def res3 = accountService.getAccountInfoByMobilePhone("123", "*********")

        then:
        res1 == null
        res2 == null
        res3 != null
        res3.getUid() == "123"
    }

    def "getAccountInfoByEmail"() {
        given:
        accountBaseInfoDao.queryByEmail(_) >>> [null, new AccountBaseInfoPO(uid: "123")]
        accountInfoCache.getAccount(_) >> new AccountInfoDTO(uid: "123")
        archCoreInfoService.encryptByType(_, _) >> "fkjaldsfjl"

        when:
        def res1 = accountService.getAccountInfoByEmail("")
        def res2 = accountService.getAccountInfoByEmail("123")
        def res3 = accountService.getAccountInfoByEmail("123")

        then:
        res1 == null
        res2 == null
        res3 != null
        res3.getUid() == "123"
    }

    def "getAccountInfoByPayoneer"() {
        given:
        accountBaseInfoDao.queryByPayoneerAccountId(_) >>> [null, new AccountBaseInfoPO(uid: "123")]
        accountInfoCache.getAccount(_) >> new AccountInfoDTO(uid: "123")

        when:
        def res1 = accountService.getAccountInfoByPayoneer("")
        def res2 = accountService.getAccountInfoByPayoneer("123")
        def res3 = accountService.getAccountInfoByPayoneer("123")

        then:
        res1 == null
        res2 == null
        res3 != null
        res3.getUid() == "123"
    }

    def "batchGetAccountInfoByUID"() {
        given:
        driverAccountConfig.getBatchQueryAccountCountLimit() >> 100
        accountInfoCache.batchGetAccount(_) >> Lists.newArrayList(new AccountInfoDTO())
        accountBaseInfoDao.batchQueryByUID(_) >> Lists.newArrayList(new AccountBaseInfoPO())
        accountMapperDao.batchQueryByUid(_) >> Lists.newArrayList()

        when:
        def res1 = accountService.batchGetAccountInfoByUID(Lists.newArrayList())
        def res2 = accountService.batchGetAccountInfoByUID(Lists.newArrayList("123", "345"))

        then:
        1 * accountInfoCache.batchSaveAccount(_, _)
        res1.size() == 0
        res2.size() == 2

    }

    def "getAccountByDriverId"() {
        given:
        accountMapperDao.batchQueryBySource(_, _ as String) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "DriverGuide", sourceId: "123", uid: "123"), new AccountMapperPO(source: "Driver", sourceId: "123", isValid: 1, uid: "123"))]
        accountInfoCache.getAccount(_) >> new AccountInfoDTO(identityDTOList: Lists.newArrayList(new AccountIdentityDTO(), new AccountIdentityDTO()))

        when:
        def res1 = accountService.getAccountByDriverId(null)
        def res2 = accountService.getAccountByDriverId(-1)
        def res3 = accountService.getAccountByDriverId(1)
        def res4 = accountService.getAccountByDriverId(2)

        then:
        res1 == null
        res2 == null
        res3 ==  null
        res4 != null
        res4.getIdentityDTOList().size() == 2
    }

    def "batchGetAccountByDriverId"() {
        given:
        accountMapperDao.batchQueryBySource(_, _) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "DriverGuide", sourceId: "123", uid: "123"), new AccountMapperPO(source: "Driver", sourceId: "123", isValid: 1, uid: "123"))]
        accountInfoCache.getAccount(_) >> new AccountInfoDTO(identityDTOList: Lists.newArrayList(new AccountIdentityDTO(), new AccountIdentityDTO()))
        accountInfoCache.batchGetAccount(_) >> [new AccountInfoDTO()]
        driverAccountConfig.getBatchQueryAccountCountLimit() >> 100

        when:
        def res1 = accountService.batchGetAccountByDriverId(Lists.newArrayList())
        def list1 = Lists.newArrayList()
        list1.add(null)
        list1.add(null)
        def res2 = accountService.batchGetAccountByDriverId(list1)
        def res3 = accountService.batchGetAccountByDriverId(Lists.newArrayList(1L,2L,3L))
        def res4 = accountService.batchGetAccountByDriverId(Lists.newArrayList(1L,2L,3L))

        then:
        res1.size() == 0
        res2.size() == 0
        res3.size() == 0
        res4.size() == 1
    }

    def "batchGetAccountByDriverId_driverIdTooMany"() {
        given:
        driverAccountConfig.getBatchQueryAccountCountLimit() >> 1

        when:
        def res1 = accountService.batchGetAccountByDriverId(Lists.newArrayList(1L,2L))

        then:
        def e = thrown(BizException)
        e.getCode() == "1517104"
    }

    def "getIdentityByUid"() {
        given:
        accountMapperDao.queryByUid(_) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "DriverGuide", sourceId: "123"), new AccountMapperPO(source: "Driver", sourceId: "123", isValid: 1))]

        when:
        def res1 = accountService.getIdentityByUid(null)
        def res2 = accountService.getIdentityByUid("")
        def res3 = accountService.getIdentityByUid("1234")
        def res4 = accountService.getIdentityByUid("1324")

        then:
        res1.size() == 0
        res2.size() == 0
        res3.size() == 0
        res4.size() == 2
    }

    def "getIdentityByDriverId"() {
        given:
        accountMapperDao.batchQueryBySource(_, _) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "DriverGuide", sourceId: "123"), new AccountMapperPO(source: "Driver", sourceId: "123", isValid: 1))]

        when:
        def res1 = accountService.getIdentityByDriverId(null)
        def res2 = accountService.getIdentityByDriverId(-1)
        def res3 = accountService.getIdentityByDriverId(1)
        def res4 = accountService.getIdentityByDriverId(2)

        then:
        res1.size() == 0
        res2.size() == 0
        res3.size() == 0
        res4.size() == 2
    }

    def "updateAccountIdentityState"() {
        given:
        accountMapperDao.queryByUid(_) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "DriverGuide", sourceId: "1234", isValid: 1))]
        appPushMessageCache.getDriverVersion(_) >> 123

        when:
        accountService.updateAccountIdentityState("", AccountSouceEnum.DRIVER_GUIDE, false)
        accountService.updateAccountIdentityState("123", AccountSouceEnum.DRIVER_GUIDE, false)
        accountService.updateAccountIdentityState("123", AccountSouceEnum.DRIVER_GUIDE, false)

        then:
        1 * accountMapperDao.update(_)
        1 * accountChangeLogHelper.saveLog(_)
        1 * accountInfoCache.delAccount(_)
    }

    def "refreshAccountCache"() {
        given:
        accountBaseInfoDao.queryByUID(_) >>> [null, new AccountBaseInfoPO()]
        driverAccountConfig.getAccountInfoRedisCacheSeconds() >> 3600
        accountMapperDao.queryByUid(_) >> Lists.newArrayList()

        when:
        accountService.refreshAccountCache("")
        accountService.refreshAccountCache("")

        then:
        1 * accountInfoCache.delAccount(_)
        1 * accountInfoCache.saveAccount(_, _)
    }

    def "reCalcDriverIdentityState"() {
        given:
        accountMapperDao.queryByUid(_) >>> [null, Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "DriverGuide", sourceId: "123"), new AccountMapperPO(source: "Driver", sourceId: "123", isValid: 1))]

        when:
        accountService.reCalcDriverIdentityState("123")
        accountService.reCalcDriverIdentityState("123")
        accountService.reCalcDriverIdentityState("123")

        then:
        1 * tmsTransportProxyService.queryDriverDetail(_) >> null
    }

    def "splitPhoneNumber"() {
        given:
        phoneNumberService.splitNumber(_) >>> [null, new SplitNumberResponseType(), new SplitNumberResponseType( result: new NumberDTO(countryCode: "86", bodyNumber: "123456"))]

        when:
        def res1 = accountService.splitPhoneNumber("86", "1234")
        def res2 = accountService.splitPhoneNumber("86", "1234")
        def res3 = accountService.splitPhoneNumber("86", "1234")

        then:
        res1 == null
        res2 == null
        res3 != null
        res3.bodyNumber == "123456"
    }

    def "calcDriverState"() {
        given:
        tmsTransportProxyService.queryDriverDetail(_) >>> [null, new QueryDrvDetailDTOSOA(drvStatus: 3), new QueryDrvDetailDTOSOA(drvStatus: 2, proLineList: Lists.newArrayList(3)), new QueryDrvDetailDTOSOA(drvStatus: 2, proLineList: Lists.newArrayList(1, 2, 3))]
        driverAccountConfig.getDgTransferDriverProLine() >> 3

        when:
        accountService.calcDriverState(new AccountMapperPO(sourceId: "123"))
        accountService.calcDriverState(new AccountMapperPO(sourceId: "123"))
        accountService.calcDriverState(new AccountMapperPO(sourceId: "123"))
        accountService.calcDriverState(new AccountMapperPO(sourceId: "123"))

        then:
        2 * accountMapperDao.update(_ as AccountMapperPO) >> 1
        2 * accountInfoCache.delAccount(_)
        2 * accountChangeLogHelper.saveLog(_) >> true
    }

    def "repeatRegisterUpdateAccount"() {
        given:
        emailNotifyConfig.getRegisterAccountInfoConflictConfig() >> null
        userCenterAccountGateway.bindEmail(_, _) >> null

        AccountBaseInfoPO accountBaseInfo1 = new AccountBaseInfoPO()
        AccountBaseInfoPO accountBaseInfo2 = new AccountBaseInfoPO(name: "12", idCardNo: "45", email: "79", payoneerAccountId: "00", isOversea: 0, ppmAccountId: "aaa")
        RegisterNewAccountParam param = new RegisterNewAccountParam(name: "123", idCardNo: "456", email: "789", payoneerAccountId: "000", isOversea: false, source: "Driver", ppmAccountId: "bbb")

        AccountBaseInfoPO accountBaseInfo3 = new AccountBaseInfoPO(name: "12", idCardNo: "45", email: "79", payoneerAccountId: "00", isOversea: 0, ppmAccountId: "aaa")
        RegisterNewAccountParam param3 = new RegisterNewAccountParam(name: "123", idCardNo: "456", email: "789", payoneerAccountId: "000", isOversea: false, source: "Driver", ppmAccountId: "bbb")


        when:
        def res1 = accountService.repeatRegisterUpdateAccount(accountBaseInfo1, param)
        //之前
        def res2 = accountService.repeatRegisterUpdateAccount(accountBaseInfo2, param)
        def res3 = accountService.repeatRegisterUpdateAccount(accountBaseInfo3, param3)

        then:
        res1
        !res2

    }

    //拋出之前是境内，现在是境外的异常
    def "repeatRegisterUpdateAccount_ex"() {
        given:
        emailNotifyConfig.getRegisterAccountInfoConflictConfig() >> null
        userCenterAccountGateway.bindEmail(_, _) >> null


        AccountBaseInfoPO accountBaseInfo3 = new AccountBaseInfoPO(name: "12", idCardNo: "45", email: "79", payoneerAccountId: "00", isOversea: 0, ppmAccountId: "aaa")
        RegisterNewAccountParam param3 = new RegisterNewAccountParam(name: "123", idCardNo: "456", email: "789", payoneerAccountId: "000", isOversea: true, source: "Driver", ppmAccountId: "bbb")


        when:
        accountService.repeatRegisterUpdateAccount(accountBaseInfo3, param3)
        then:
        thrown(BizException)


    }

    def "sendRegisterConflictEmail"() {
        given:
        emailNotifyConfig.getRegisterAccountInfoConflictConfig() >>> [null, new EmailConfigInfo(receiverEmails: "<EMAIL>", contentTemplate: "%s-%s")]

        when:
        accountService.sendRegisterConflictEmail("", Lists.newArrayList())
        accountService.sendRegisterConflictEmail("231", Lists.newArrayList(Triple.of("a", "b", "c")))

        then:
        1 * emailServiceGateway.sendEmail(_)
    }

    def "createNewAccount"() {

        given:
        userCenterAccountGateway.registerNewAccount(_) >> RegisterAccountResult.builder().uid("1431234").build();
        accountChangeLogHelper.saveLog(_) >> true

        when:
        def res = accountService.createNewAccount(new RegisterNewAccountParam(), null)

        then:
        res != null
        res.getUid() == "1431234"

    }

    def "checkPayoneerAccountId"() {
        given:
        accountBaseInfoDao.queryByPayoneerAccountId(_) >> new AccountBaseInfoPO(uid: "1234")

        when:
        accountService.checkPayoneerAccountId("123", "ng003g-")

        then:
        def ex = thrown(BizException)
        ex.code == "1517105"
    }

    def "checkPhone"() {
        given:
        accountMapperDao.batchQueryBySource(_, _) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "Driver", sourceId: "123"))]
        accountBaseInfoDao.queryByUID(_) >>> [null, new AccountBaseInfoPO(countryCode: "86", phoneNumber: "123")]

        when:
        accountService.checkPhone(new RegisterNewAccountParam(source: "Guide"))
        accountService.checkPhone(new RegisterNewAccountParam(source: "DriverGuide"))
        accountService.checkPhone(new RegisterNewAccountParam(source: "DriverGuide"))
        accountService.checkPhone(new RegisterNewAccountParam(source: "DriverGuide"))
        accountService.checkPhone(new RegisterNewAccountParam(source: "DriverGuide", sourceId: "123", countryCode: "86", phoneNumber: "1234"))

        then:
        def ex = thrown(BizException)
        ex.code == "1517103"
    }

    def "checkSourceId"() {

        when:
        accountService.checkSourceId(Lists.newArrayList(), null)
        accountService.checkSourceId(Lists.newArrayList(new AccountMapperPO()), new RegisterNewAccountParam(source: "Guide"))
        accountService.checkSourceId(Lists.newArrayList(new AccountMapperPO(source: "Driver", sourceId: "123")), new RegisterNewAccountParam(source: "DriverGuide", sourceId: "234"))

        then:
        def ex = thrown(BizException)
        ex.code == "1517102"

    }

    def "buildAccountRegisterParam"() {
        when:
        def res = accountService.buildAccountRegisterParam(new RegisterNewAccountParam(source: "DriverGuide", sourceId: "234"))

        then:
        res.getSourceId() == "234"
    }

    def "buildAccountMapper"() {
        when:
        def res = accountService.buildAccountMapper("1", "CN_CSP", "Driver", "34", true)

        then:
        res != null
        res.getUid() == "1"
    }


    def "saveAccount"() {

        when:
        accountService.saveAccount(new AccountBaseInfoPO(), new AccountMapperPO(source: "Driver", sourceId: "999"))
        accountService.saveAccount(new AccountBaseInfoPO(id: 111L), new AccountMapperPO(id: 222L, source: "Driver", sourceId: "43435"))

        then:
        2 * globalIdRecordDao.delete(_ as GlobalIdRecordPO) >> 1
        1 * accountBaseInfoDao.insert(_ as AccountBaseInfoPO) >> 1
        1 * accountBaseInfoDao.update(_ as AccountBaseInfoPO) >> 1
        1 * accountMapperDao.insert(_ as AccountMapperPO) >> 1
        1 * accountMapperDao.update(_ as AccountMapperPO) >> 1


    }


    def "buildAccountInfoDTO"() {
        when:
        def res = accountService.buildAccountInfoDTO(new AccountBaseInfoPO(uid: "111"), Lists.newArrayList(new AccountMapperPO(sourceId: "1234", source: "Driver", isValid: 1)))

        then:
        res != null
        res.getUid() == "111"
    }

    def "preProcessParam0"() {
        given:
        archCoreInfoService.encryptByType(_, _) >>> ["j0gijogj", "nigh", "f82ig0"]

        RegisterNewAccountParam param = new RegisterNewAccountParam(phoneNumber: "242", email: "jg02", idCardNo: "jf020")
        when:
        accountService.preProcessParam(param)

        then:
        param.getPhoneNumber() == "j0gijogj"
        param.getEmail() == "nigh"
        param.getIdCardNo() == "f82ig0"
    }

    def "preProcessParam"() {
        given:
        archCoreInfoService.encryptByType(_, _) >>> ["j0gijogj", "nigh", "f82ig0"]

        UpdateAccountParam param = new UpdateAccountParam(phoneNumber: "242", email: "jg02", idCardNo: "jf020")
        when:
        accountService.preProcessParam(param)

        then:
        param.getPhoneNumber() == "j0gijogj"
        param.getEmail() == "nigh"
        param.getIdCardNo() == "f82ig0"
    }

    def "sendDriverOfflineAppPush"() {
        given:
        appPushMessageCache.getDriverVersion(_) >> BigDecimal.valueOf(8379L)
        driverAccountConfig.getSupportDriverGuideVersion() >> BigDecimal.valueOf(8379L)
        commonMultipleLanguages.getContent(_, _) >> "fasf"

        when:
        accountService.sendDriverOfflineAppPush(new AccountMapperPO(source: "DriverGuide", sourceId: "32423", isValid: 0))

        then:
        1 * pushConfigRedisLogic.getDriverLanguage(_) >> "zh_CN"
    }

    def "sendAccountBaseInfoChangeMessage"() {
        given:
        messageProducerProvider.generateMessage(_) >> message

        when:
        accountService.sendAccountBaseInfoChangeMessage("1234", "Driver", Lists.newArrayList())

        then:
        1 * driverAccountConfig.getAccountUpdateQmqDelaySeconds() >> 2
        1 * messageProducerProvider.sendMessage(_)
    }

    def "sendAccountBaseInfoChangeMessage2"() {
        given:
        messageProducerProvider.generateMessage(_) >> message

        when:
        accountService.sendAccountBaseInfoChangeMessage("1234", "Driver", Lists.newArrayList())

        then:
        1 * driverAccountConfig.getAccountUpdateQmqDelaySeconds() >> 0
        1 * messageProducerProvider.sendMessage(_)
    }
}
