package com.ctrip.dcs.driver.domain.application.exector.verify

import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverCarInspectionRecordDO
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO
import com.ctrip.dcs.driver.domain.task.CarInspectionInfoDTO
import com.ctrip.dcs.driver.domain.task.DriverTaskStatus
import com.ctrip.dcs.driver.domain.task.QueryDriverTaskInfoRequestType
import com.ctrip.dcs.driver.domain.task.SaveOrUpdateCarInspectionInfoRequestType
import com.ctrip.dcs.driver.domain.task.SaveOrUpdateCarInspectionInfoResponseType
import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification
import spock.lang.Subject

import java.sql.Timestamp
import java.time.LocalDateTime

class SaveOrUpdateCarInspectionInfoExectorTest extends Specification {

    @Subject
    SaveOrUpdateCarInspectionInfoExector executor = new SaveOrUpdateCarInspectionInfoExector()

    DriverTaskAdapter driverTaskAdapter = Mock()

    def setup() {
        executor.driverTaskAdapter = driverTaskAdapter
    }

    def "saveorupdatecarinspectioninfoexectortest"() {
        given:
        def request = new SaveOrUpdateCarInspectionInfoRequestType(
                driverId: 123L,
                taskId: "TASK_001",
                carInspectionList: [
                        new CarInspectionInfoDTO(
                                taskStepKey: "KEY_1",
                                taskStepValue: "VALUE_1"
                        ),
                        new CarInspectionInfoDTO(
                                taskStepKey: "KEY_2",
                                taskStepValue: "VALUE_2"
                        )
                ]
        )

        when:
        def response = executor.execute(request)

        then:
        driverTaskAdapter.updateCarInspectionInfo({ List<DriverCarInspectionRecordDO> records ->
            records.size() == 2 &&
                    records.every { it.driverId == 123L && it.taskId == "TASK_001" } &&
                    records*.taskStepKey == ["KEY_1", "KEY_2"] &&
                    records*.taskStepValue == ["VALUE_1", "VALUE_2"]
        } as List<DriverCarInspectionRecordDO>)

        response.responseResult.returnCode == "200"
    }

    def "saveorupdatecarinspectioninfoexectortest.emptylist"() {
        given:
        def request = new SaveOrUpdateCarInspectionInfoRequestType(
                driverId: 123L,
                taskId: "TASK_001",
                carInspectionList: []
        )

        when:
        def response = executor.execute(request)

        then:
        1 * driverTaskAdapter.updateCarInspectionInfo([])

        response.responseResult.returnCode == "200"
    }

    def "saveorupdatecarinspectioninfoexectortest.toCarInspectionRecordList"() {
        given:
        def request = new SaveOrUpdateCarInspectionInfoRequestType(
                driverId: 456L,
                taskId: "TASK_002",
                carInspectionList: [
                        new CarInspectionInfoDTO(
                                taskStepKey: "ENGINE",
                                taskStepValue: "OK"
                        )
                ]
        )

        when:
        def result = executor.toCarInspectionRecordList(request)

        then:
        result.size() == 1
        with(result[0]) {
            driverId == 456L
            taskId == "TASK_002"
            taskStepKey == "ENGINE"
            taskStepValue == "OK"
        }
    }

    def "saveorupdatecarinspectioninfoexectortest.emptylis"() {
        given:
        def request = new SaveOrUpdateCarInspectionInfoRequestType(
                carInspectionList: null
        )

        when:
        def result = executor.toCarInspectionRecordList(request)

        then:
        CollectionUtils.isEmpty(result)
    }
}