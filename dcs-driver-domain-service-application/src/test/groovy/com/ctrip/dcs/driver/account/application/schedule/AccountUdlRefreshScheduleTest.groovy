package com.ctrip.dcs.driver.account.application.schedule

import com.ctrip.dcs.driver.account.application.service.RefreshUdlService
import com.ctrip.igt.framework.common.clogging.Logger
import com.ctrip.igt.framework.common.clogging.LoggerContext
import org.junit.platform.commons.logging.LoggerFactory
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll
import spock.util.mop.ConfineMetaClassChanges

@ConfineMetaClassChanges([LoggerContext, LoggerFactory])
class AccountUdlRefreshScheduleTest extends Specification {
    def refreshUdlService = Mock(RefreshUdlService)
    def logger = Mock(Logger)

    def schedule = new AccountUdlRefreshSchedule(
            refreshUdlService: refreshUdlService
    )

    def setup() {
//        LoggerContext.metaClass.static.newContext = { ->
//            Mock(LoggerContext).with {
//                withGlobalTraceId(_) >> it
//            }
//        }
//        LoggerFactory.metaClass.static.getLogger = { _ -> logger }
//        schedule.logger = logger
    }

    // 场景1: 存在uidList参数时执行指定用户刷新
    def "当传入uidList参数时应执行指定用户刷新"() {
        given: "准备包含uidList的JSON参数"
        def param = new MockParameter('''{
            "uidList": "uid1,uid2"
        }''')

        when: "执行定时任务"
        schedule.onExecute(param)

        then: "验证调用指定用户刷新"
        1 * refreshUdlService.refreshUdlByUid(_,_)
        0 * refreshUdlService.refreshAllUdl(_)
    }

    // 场景2: 存在refreshALLUdl=true时执行全量刷新
    def "当refreshALLUdl为true时应执行全量刷新"() {
        given: "准备全量刷新JSON参数"
        def param = new MockParameter('''{
            "refreshALLUdl": "true"
        }''')

        when: "执行定时任务"
        schedule.onExecute(param)

        then: "验证调用全量刷新"
        1 * refreshUdlService.refreshAllUdl(param)
        0 * refreshUdlService.refreshUdlByUid(_)
    }

    // 场景3: 同时存在uidList和refreshALLUdl时优先处理uidList
    def "当同时存在uidList和refreshALLUdl时应优先处理uidList"() {
        given: "准备包含两个参数的JSON"
        def param = new MockParameter('''{
            "uidList": "uid3",
            "refreshALLUdl": "true"
        }''')

        when: "执行定时任务"
        schedule.onExecute(param)

        then: "仅执行指定用户刷新"
        1 * refreshUdlService.refreshUdlByUid(_,"uid3")
        0 * refreshUdlService.refreshAllUdl(_)
    }

    // 场景4: 无有效参数时不执行任何操作
    def "当无有效参数时应不执行任何刷新"() {
        given: "准备空JSON参数"
        def param = new MockParameter('''{}''')

        when: "执行定时任务"
        schedule.onExecute(param)
        boolean result = true

        then: "不执行任何刷新操作"
        0 * refreshUdlService._
        assert result
    }


    // 场景6: 参数值为空字符串时按无效处理
    @Unroll
    def "当参数#paramName值为空时应视为无效参数"() {
        given: "准备空值JSON参数"
        def jsonContent = """{"${paramName}": ""}"""
        def param = new MockParameter(jsonContent)

        when: "执行定时任务"
        schedule.onExecute(param)

        then: "不执行任何刷新操作"
        0 * refreshUdlService._

        where:
        paramName << ["uidList", "refreshALLUdl"]
    }

    // 场景7: 全量刷新参数为无效布尔值时跳过
    def "当refreshALLUdl为非布尔值时应跳过全量刷新"() {
        given: "准备无效布尔JSON参数"
        def param = new MockParameter('''{
            "refreshALLUdl": "yes"
        }''')

        when: "执行定时任务"
        schedule.onExecute(param)
        boolean result = true

        then: "不执行任何刷新"
        0 * refreshUdlService._
        assert result
    }
}

