package com.ctrip.dcs.driver.domain.application.exector.account

import com.ctrip.dcs.driver.account.domain.service.AccountService
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.domain.account.RegisterAccountRequestType
import com.ctrip.dcs.driver.domain.account.RegisterAccountResponseType
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class RegisterAccountEngineTest extends Specification {
    @Mock
    UserCenterAccountGateway accounService
    @Mock
    AccountMapperDao accountMapperDao
    @Mock
    DriverAccountConfig driverAccountConfig
    @Mock
    AccountService accountService;
    @InjectMocks
    RegisterAccountEngine registerAccountEngine

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(accounService.queryAccountUid(any())).thenReturn(null)
        when(driverAccountConfig.getEnableNewRegisterProgressSwitch()).thenReturn(0)

        when:
        RegisterAccountResponseType result = registerAccountEngine.execute(Mock(RegisterAccountRequestType))

        then:
        result.responseResult.returnCode == "200"
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
