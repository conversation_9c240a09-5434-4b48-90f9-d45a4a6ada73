package com.ctrip.dcs.driver.account.application.service

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountChangeLogDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.dianping.cat.Cat
import qunar.tc.schedule.MockParameter
import qunar.tc.schedule.Parameter
import qunar.tc.schedule.TaskHolder
import qunar.tc.schedule.TaskMonitor
import spock.lang.Specification
import spock.lang.Unroll

/*
作者：pl.yang
创建时间：2025/4/7-下午3:35-2025
*/

class RefreshUdlServiceTest extends Specification {
    RefreshUdlService refreshUdlService = new RefreshUdlService()

    // Mock dependencies
    AccountBaseInfoDao accountBaseInfoDao = Mock()
    AccountMapperDao accountMapperDao = Mock()
    AccountChangeLogDao accountChangeLogDao = Mock()
    UserCenterAccountGateway userCenterAccountGateway = Mock()
    DirectorRedis directorRedis = Mock()
    DriverAccountConfig driverAccountConfig = Mock()
    TaskMonitor taskMonitor = Mock()

    def setup() {
        refreshUdlService.accountBaseInfoDao = accountBaseInfoDao
        refreshUdlService.accountMapperDao = accountMapperDao
        refreshUdlService.accountChangeLogDao = accountChangeLogDao
        refreshUdlService.userCenterAccountGateway = userCenterAccountGateway
        refreshUdlService.directorRedis = directorRedis
        refreshUdlService.driverAccountConfig = driverAccountConfig

        driverAccountConfig.getJobSleepMills() >> 0 // Disable sleep for testing
    }

    def cleanup() {
    }

    // Test cases for refreshUdlByUid
    def "testRefreshUdlByUid when valid uids should update successfully"() {
        given:
        def uids = "uid1,uid2"
        def account1 = new AccountInfoResponseType(udl: "udl1")
        def account2 = new AccountInfoResponseType(udl: "udl2")

        when:
        refreshUdlService.refreshUdlByUid(Mock(Parameter){
            it.getString("dbList")>>"account_base_info"
        },uids)

        then:
        1 * userCenterAccountGateway.getAccountByUid("uid1") >> account1
        1 * userCenterAccountGateway.getAccountByUid("uid2") >> account2
        1 * accountBaseInfoDao.updateUdl("udl1", "uid1")
        1 * accountMapperDao.updateUdl("udl1", "uid1")
        1 * accountChangeLogDao.updateUdl("udl1", "uid1")
        1 * accountBaseInfoDao.updateUdl("udl2", "uid2")
        1 * accountMapperDao.updateUdl("udl2", "uid2")
        1 * accountChangeLogDao.updateUdl("udl2", "uid2")
    }

    def "testRefreshUdlByUid when account not exists should log warning"() {
        given:
        def uids = "invalidUid"

        when:
        refreshUdlService.refreshUdlByUid(Mock(Parameter),uids)

        then:
        1 * userCenterAccountGateway.getAccountByUid("invalidUid") >> null
        0 * accountBaseInfoDao.updateUdl(_, _)
    }

    def "testRefreshUdlByUid when udl is empty should skip update"() {
        given:
        def uids = "emptyUid"
        def account = new AccountInfoResponseType(udl: null)

        when:
        refreshUdlService.refreshUdlByUid(Mock(Parameter),uids)

        then:
        1 * userCenterAccountGateway.getAccountByUid("emptyUid") >> account
        0 * accountBaseInfoDao.updateUdl(_, _)
    }

    def "testRefreshUdlByUid when exception occurs should catch and log"() {
        given:
        def uids = "errorUid"

        when:
        refreshUdlService.refreshUdlByUid(Mock(Parameter),uids)

        then:
        1 * userCenterAccountGateway.getAccountByUid("errorUid") >> { throw new RuntimeException("DB error") }
        0 * accountBaseInfoDao.updateUdl(_, _)
    }



    def "testRefreshAllUdl when startId from parameter should use it"() {
        given:
        def param = new MockParameter("{\"startId\": \"100\"}")

        when:
        refreshUdlService.refreshAllUdl(param)

        then:
        1 * accountBaseInfoDao.batchQueryByPage(100, 100) >> []
        0 * directorRedis.get(_)
    }



    def "testRefreshAllUdl when exception occurs should continue processing"() {
        given:
        def param = new MockParameter()
        def po1 = new AccountBaseInfoPO(id: 1L, uid: "uid1")
        def po2 = new AccountBaseInfoPO(id: 2L, uid: "uid2")

        when:
        refreshUdlService.refreshAllUdl(param)

        then:
        1 * accountBaseInfoDao.batchQueryByPage(0, 100) >> [po1, po2]
        1 * userCenterAccountGateway.getAccountByUid("uid1") >> { throw new RuntimeException() }
        1 * userCenterAccountGateway.getAccountByUid("uid2") >> new AccountInfoResponseType(udl: "udl2")
    }

    def "testRefreshAllUdl when no udl found should skip update"() {
        given:
        def param = new MockParameter()
        def po = new AccountBaseInfoPO(id: 1L, uid: "uid1")
        def account = new AccountInfoResponseType(udl: null)

        when:
        refreshUdlService.refreshAllUdl(param)

        then:
        1 * accountBaseInfoDao.batchQueryByPage(0, 100) >> [po]
        1 * userCenterAccountGateway.getAccountByUid("uid1") >> account
        0 * accountBaseInfoDao.updateUdl(_, _)
    }

    def "testRefreshAllUdl when account not found should log warning"() {
        given:
        def param = new MockParameter()
        def po = new AccountBaseInfoPO(id: 1L, uid: "invalidUid")

        when:
        refreshUdlService.refreshAllUdl(param)

        then:
        1 * accountBaseInfoDao.batchQueryByPage(0, 100) >> [po]
        1 * userCenterAccountGateway.getAccountByUid("invalidUid") >> null
        0 * accountBaseInfoDao.updateUdl(_, _)
    }

    @Unroll
    def "testGetStartId when #scenario should return #expected"() {
        given:
        def param = new MockParameter()
        if (paramStartId != null) {
            String json = "{\"startId\": \"" + paramStartId + "\"}";
            param = new MockParameter(json)
        }
        directorRedis.get(_) >> redisValue
        when:
        def result = refreshUdlService.getStartId(param)

        then:

        result == expected

        where:
        scenario                        | paramStartId | redisValue | expected
        "parameter has startId"        | "100"        | null       | 100L
        "redis has value"              | null         | "50"       | 50L
        "no parameter or redis value"  | null         | null       | 0L
    }
}

