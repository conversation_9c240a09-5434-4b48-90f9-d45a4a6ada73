package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.application.common.DriverLevelHelper
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations

import java.time.LocalDateTime
import java.time.Month
import static org.mockito.Mockito.*

class RightsRepoServiceImplTest extends Specification {
    @Mock
    RightsDBDataService rightsDBDataService
    @Mock
    RightsRedisLogic rightsRedisLogic
    @Mock
    DriverLevelHelper driverLevelHelper
    @InjectMocks
    RightsRepoServiceImpl rightsRepoServiceImpl

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test query Driver Rights"() {
        given:
        when(rightsDBDataService.queryDriverRights(anyLong(), anyString())).thenReturn(db)
        when(rightsRedisLogic.getDrivRights(anyLong(), anyString())).thenReturn(cache)

        when:
        List<RightsModel> result = rightsRepoServiceImpl.queryDriverRights(0l, [0], "monthIdx")

        then:
        true
        where:
        cache                                                                                                                                                                   | db                                                                                                                                                                      || size
        [new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 0, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "extend")] | null                                                                                                                                                                    || 1
        null                                                                                                                                                                    | null                                                                                                                                                                    || 0
        null                                                                                                                                                                    | [new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 0, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "extend")] || 1
    }

    def "test query Driver Records"() {
        given:
        when(rightsDBDataService.queryDriverRecords(anyLong(), anyString(), anyString(), any())).thenReturn([new RightsRecordModel(1l, 1l, 0, "rightsName", 0, "levelName", "userOrderId", "purchaseOrderId", "supplyOrderId", "punishOrderId", "money", LocalDateTime.of(2023, Month.JUNE, 13, 13, 48, 43))])

        when:
        List<RightsRecordModel> result = rightsRepoServiceImpl.queryDriverRecords(0l, "useStartDate", "useEndDate", [0])

        then:
        result !=null
    }

    def "test query Driver Level1"() {
        given:
//        when(rightsDBDataService.queryDriverLevel(anyLong(), anyString())).thenReturn(db)
        when(rightsRedisLogic.getDrivLevel(anyLong(), anyString())).thenReturn(new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0))
        when(driverLevelHelper.getDriverLevelName(anyInt(), anyString())).thenReturn("aaa")

        when:
        LevelModel result = rightsRepoServiceImpl.queryDriverLevel(0l, "monthIdx")

        then:
        result != null
        result.getLevelName() == "aaa"
    }

    def "test query Driver Level2"() {
        given:
        when(rightsDBDataService.queryDriverLevel(anyLong(), anyString())).thenReturn(new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0))
//        when(rightsRedisLogic.getDrivLevel(anyLong(), anyString())).thenReturn(new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0))
        when(driverLevelHelper.getDriverLevelName(anyInt(), anyString())).thenReturn("aaa")

        when:
        LevelModel result = rightsRepoServiceImpl.queryDriverLevel(0l, "monthIdx")

        then:
        result != null
        result.getLevelName() == "aaa"
    }

    def "test save Rights Records"() {
        when:
        def res = rightsRepoServiceImpl.saveRightsRecords(new DrivRightsRecordPO())

        then:
        res == null
    }

    def "test update Driver Rights"() {
        when:
        def res = rightsRepoServiceImpl.updateDriverRights(new DrivRightsPO())

        then:
        res == null
    }

    def "test query Driver Records By Id"() {
        given:
        when(rightsDBDataService.queryDriverRecordsById(anyLong())).thenReturn([new RightsRecordModel(1l, 1l, 0, "rightsName", 0, "levelName", "userOrderId", "purchaseOrderId", "supplyOrderId", "punishOrderId", "money", LocalDateTime.of(2023, Month.JUNE, 13, 13, 48, 43))])

        when:
        List<RightsRecordModel> result = rightsRepoServiceImpl.queryDriverRecordsById(1l)

        then:
        result !=null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme