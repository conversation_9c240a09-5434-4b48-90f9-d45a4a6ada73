package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType
import com.ctrip.basebiz.account.service.soa.AccountMetaData
import com.ctrip.dcs.driver.value.account.AccountUidInfo
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.domain.application.common.CommonLogger
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.AccountLoginApiInternalServiceProxy
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.AccountManagerApiServiceProxy
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.AccountServiceProxy
import com.ctrip.dcs.driver.domain.infrastructure.dto.account.AccountBaseDto
import com.ctrip.igt.framework.common.exception.BizException
import com.ctrip.soa.platform.accountapiserverinternal.v1.RegisterByMobilePhoneRequestType
import com.ctrip.soa.platform.accountapiserverinternal.v1.RegisterByMobilePhoneResponseType
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.BindEmailResponseType
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.UnfreezeAccountByUidResponseType
import spock.lang.Specification
import spock.lang.Unroll

class UserCenterAccountRepositoryImplTest extends Specification {
    AccountServiceProxy accountServiceProxy = Mock(AccountServiceProxy)
    AccountManagerApiServiceProxy accountManagerApiServiceProxy = Mock(AccountManagerApiServiceProxy)
    AccountLoginApiInternalServiceProxy accountLoginApiInternalServiceProxy = Mock(AccountLoginApiInternalServiceProxy)
    DriverAccountConfig driverAccountConfig = Mock(DriverAccountConfig)
    CommonLogger logger = Mock(CommonLogger)
    UserCenterAccountGatewayImpl gateway = new UserCenterAccountGatewayImpl(
            accountServiceProxy: accountServiceProxy,
            accountManagerApiServiceProxy: accountManagerApiServiceProxy,
            accountLoginApiInternalServiceProxy: accountLoginApiInternalServiceProxy,
            driverAccountConfig: driverAccountConfig,
            logger: logger

    )


    def "test query Account Uid"() {
        given:
        accountServiceProxy.getAccountByMobilePhone(_) >> res;
        accountServiceProxy.getAccountByEmail(_) >> res
        driverAccountConfig.isEnableQueryDiffSubSystem(_) >> isEnableQueryDiffSubSystem

        when:
        AccountUidInfo result = gateway.queryAccountUid(accountDto)

        then:
        result.getUid() == "1"

        where:
        accountDto                                                                 | isEnableQueryDiffSubSystem | res
        //只查询境内系统
        new AccountBaseDto(accountType: 2, email: "<EMAIL>")                      | false                      | new AccountInfoResponseType(returnCode: 0, uid: "1")
        new AccountBaseDto(accountType: 1, phoneNumber: "1234", countryCode: "86") | false                      | new AccountInfoResponseType(returnCode: 0, uid: "1")
        //查询境外系统
        new AccountBaseDto(accountType: 1, phoneNumber: "1234", countryCode: "86") | true                       | new AccountInfoResponseType(returnCode: 0, uid: "1")

    }
    // 测试queryAccountUidByPhone方法
    def "testQueryAccountUidByPhone-当手机号存在时返回正确UID"() {
        given: "配置查询不同子系统"
        driverAccountConfig.isEnableQueryDiffSubSystem() >> true

        and: "模拟境外子系统返回空，境内子系统返回有效响应"
        def validResponse = new AccountInfoResponseType(uid: "123", udl: "mainland")
        1 * accountServiceProxy.getAccountByMobilePhone(_) >> validResponse

        when: "执行手机号查询"
        def result = gateway.queryAccountUidByPhone("86", "***********")

        then: "验证返回结果"
        result.uid == "123"
        result.udl == "mainland"
    }

    def "testQueryAccountUidByEmail-当邮箱存在时返回正确UID"() {
        given: "配置查询不同子系统"
        driverAccountConfig.isEnableQueryDiffSubSystem() >> true

        and: "模拟境外子系统返回空，境内子系统返回有效响应"
        def validResponse = new AccountInfoResponseType(uid: "456", udl: "mainland")
        1 * accountServiceProxy.getAccountByEmail(_) >> validResponse

        when: "执行邮箱查询"
        def result = gateway.queryAccountUidByEmail("<EMAIL>")

        then: "验证返回结果"
        result.uid == "456"
        result.udl == "mainland"
    }

    // 测试getAccountByUid方法
    def "testGetAccountByUid-当服务返回成功时获取账户信息"() {
        given: "模拟成功响应"
        def response = new AccountInfoResponseType(returnCode: 0)
        1 * accountServiceProxy.getAccountByUid(_) >> response

        when: "执行UID查询"
        def result = gateway.getAccountByUid("123")

        then: "验证返回结果"
        result == response
    }

    // 测试registerNewAccount方法
    def "testRegisterNewAccount-手机注册成功并绑定邮箱"() {
        given: "配置账户参数"
        def accountDto = new AccountBaseDto(
                accountType: 1,
                countryCode: "86",
                phoneNumber: "***********",
                bindAccountType: 2,
                email: "<EMAIL>",
                oversea: false
        )

        and: "模拟注册成功"
        accountLoginApiInternalServiceProxy.registerByMobilePhone(_) >>
                new RegisterByMobilePhoneResponseType(returnCode: 0, uid: "newUid")

        and: "模拟绑定成功"
        accountManagerApiServiceProxy.bindEmail(_) >> new BindEmailResponseType(returnCode: 0)

        when: "执行注册"
        def result = gateway.registerNewAccount(accountDto)

        then: "验证注册结果"
        result.success
        result.uid == "newUid"
    }

    // 测试bindEmail方法
    def "testBindEmail-当绑定成功时返回true"() {
        given: "模拟查询accessCode"
        gateway.getAccountByUid(_) >> new AccountInfoResponseType(
                accountMetaData: new AccountMetaData(subSystemId: 90023)
        )

        and: "模拟绑定成功"
        accountManagerApiServiceProxy.bindEmail(_) >> new BindEmailResponseType(returnCode: 0)

        when: "执行邮箱绑定"
        def result = gateway.bindEmail("uid123", "<EMAIL>", "operator")

        then: "验证返回true"
        assert result
    }

    // 测试unBindPhone方法
    def "testUnBindPhone-当解绑失败时抛出异常"() {
        given: "模拟查询accessCode"
        gateway.getAccountByUid(_) >> new AccountInfoResponseType(
                accountMetaData: new AccountMetaData(subSystemId: 90023)
        )

        and: "模拟服务异常"
        accountManagerApiServiceProxy.unbindMobilePhone(_) >> { throw new Exception("service down") }

        when: "执行手机解绑"
        gateway.unBindPhone("uid123", "operator")

        then: "验证抛出预期异常"
        thrown(BizException)
    }

    // 测试registerAccountByMobilePhone方法
    @Unroll
    def "testRegisterAccountByMobilePhone-根据配置选择正确的accessCode: #scenario"() {
        given: "配置城市ID和海外标志"
        driverAccountConfig.getRegisterToDiffSubSystemCityIds() >> cityConfig
        def accountDto = new AccountBaseDto(
                countryCode: "86",
                phoneNumber: "***********",
                cityId: cityId,
                oversea: oversea
        )


        when: "执行手机注册"
        def result = gateway.registerAccountByMobilePhone(accountDto)

        then: "验证使用的accessCode"
        result.accessCode == expectedAccessCode
//        1 * accountLoginApiInternalServiceProxy.registerByMobilePhone({ it.getAccessCode() == expectedAccessCode}) >> Mock(RegisterByMobilePhoneResponseType) {
//            it.getReturnCode() >> 0
//        }
        1 * accountLoginApiInternalServiceProxy.registerByMobilePhone(_) >> {
            args ->
                try {
                    RegisterByMobilePhoneRequestType requestType = args[0] as RegisterByMobilePhoneRequestType
                    assert requestType.getAccessCode() == expectedAccessCode
                    return new RegisterByMobilePhoneResponseType(returnCode: 0, uid: "newUid")
                } catch (Exception e) {
                    e.printStackTrace()
                }
                return null;

        }

        where:
        scenario               | cityConfig | cityId | oversea | expectedAccessCode
        "全量开启海外注册"     | "-1"       | 1      | true    | "XDOVERSEASOAAUTHENTICATE"
        "特定城市开启海外注册" | "2,3"      | 2      | true    | "XDOVERSEASOAAUTHENTICATE"
        "境内注册"             | "2,3"      | 1      | false   | "XDSOAAUTEHNTICATE"
    }

    // 测试unfreezeAccount方法
    def "testUnfreezeAccount-当账户已解冻时返回true"() {
        given: "模拟返回已解冻状态码"
        accountManagerApiServiceProxy.unfreezeAccountByUid(_) >>
                new UnfreezeAccountByUidResponseType(returnCode: 30038)

        when: "执行解冻操作"
        def result = gateway.unfreezeAccount("uid123")

        then: "验证返回true"
        assert result
    }

    // 其他方法类似，需要补充测试场景...

    // 辅助方法
    private AccountBaseDto createValidAccountDto() {
        new AccountBaseDto(
                accountType: 1,
                countryCode: "86",
                phoneNumber: "***********",
                operator: "system"
        )
    }
}

