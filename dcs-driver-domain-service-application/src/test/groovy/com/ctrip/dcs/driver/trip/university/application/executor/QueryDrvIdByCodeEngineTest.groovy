package com.ctrip.dcs.driver.trip.university.application.executor

import com.ctrip.dcs.driver.domain.trip.university.QueryDrvIdByCodeRequestType
import com.ctrip.dcs.driver.domain.trip.university.QueryDrvIdByCodeResponseType
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import com.ctrip.igt.framework.common.result.Result
import spock.lang.Specification
import spock.lang.Unroll

class QueryDrvIdByCodeEngineTest extends Specification {
    def testObj = new QueryDrvIdByCodeEngine()
    def tripUniversityService = Mock(TripUniversityService)

    def setup() {

        testObj.tripUniversityService = tripUniversityService
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityService.queryDrvIdByCode(_) >> Result.Builder.<String>newResult().success().build()

        when:
        def result = testObj.execute(requestType)

        then: "验证返回结果里属性值是否符合预期"
        result.responseResult.success == expectedResult
        where: "表格方式验证多种分支调用场景"
        requestType                                         || expectedResult
        new QueryDrvIdByCodeRequestType(drvCode: "drvCode") || true
    }

}
