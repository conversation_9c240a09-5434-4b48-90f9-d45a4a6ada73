package com.ctrip.dcs.driver.account.application.mq

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverTable
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService
import com.ctrip.dcs.driver.domain.login.DriverLoginSuccessMessage
import com.ctrip.dcs.luna.JsonUtil
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.Before
import org.junit.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage

import java.time.LocalDateTime

import static org.mockito.Mockito.when

class DriverApproveMessageListenerTest {
    @Mock
    Logger LOGGER
    @Mock
    private DriverTable driverTable
    @Mock
    private PhoneCheckService phoneCheckService
    @InjectMocks
    DriverApproveMessageListener messageListener

    @InjectMocks
    DriverLoginEventListener driverLoginEventListener

    @Before
    void setUp() {
        MockitoAnnotations.initMocks(this)
        PowerMockito.mockStatic(LocalDateTime.class)
    }

    @Test
    void test_driverApproveListener() {
        Message msg = new BaseMessage()
        msg.setProperty("sourceId", "1111")
        msg.setProperty("sourceType", 1)

        DriverEntity driver = new DriverEntity()
        driver.setDriverId(1111L)
        driver.setPhonePrefix("86")
        driver.setPhoneNumber("18899989898")
        driver.setDriverLanguage("EN")
        driver.setDriverName("张三")
        driver.setSupplierId(2333444L)

        when(driverTable.queryByPk(driver.getDriverId())).thenReturn(driver)
//        doNothing().when(phoneCheckService.generatePhoneCheckTaskByDriver(driver))

        messageListener.driverApproveListener(msg)
        assert (true)
    }


    @Test
    void login_onMessage() {
        Message msg = new BaseMessage()
        DriverLoginSuccessMessage loginSuccessMessage = new DriverLoginSuccessMessage()
        loginSuccessMessage.setLoginType(4)
        loginSuccessMessage.setDriverId(1111L)
        loginSuccessMessage.setCountryCode("86")
        loginSuccessMessage.setLoginAccount("***********")
        msg.setProperty("data", JsonUtil.serialize(loginSuccessMessage))

        driverLoginEventListener.onMessage(msg)
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme