package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.impl.RightsDBDataServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordByDriverRequestType
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordByDriverResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import org.mockito.InjectMocks
import spock.lang.Specification

import java.time.LocalDateTime
import java.time.Month

class QueryRightsRecordByDriverEngineTest extends Specification {
    @InjectMocks
    QueryRightsRecordByDriverEngine queryRightsRecordByDriverEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsConfig rightsConfig = Mock(RightsConfig)
    LevelConfig levelConfig = Mock(LevelConfig)
    RightsDBDataServiceImpl dataService = Mock(RightsDBDataServiceImpl)

    def setup() {
        domainBeanFactory = new DomainBeanFactory(levelConfig: levelConfig, rightsConfig: rightsConfig, rightsDBDataService: dataService)
        queryRightsRecordByDriverEngine = new QueryRightsRecordByDriverEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {

        ServiceExecuteContext.newContext().withRequest(new QueryRightsRecordByDriverRequestType(driverId: 1l,rightsType: 7)).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryRightsRecordByDriverResponseType.class).init()

        when:
        dataService.querDriverRecordsByDriver(7,1)>>data
        QueryRightsRecordByDriverResponseType result = queryRightsRecordByDriverEngine.execute(new QueryRightsRecordByDriverRequestType(driverId: 1l,rightsType: 7))

        then:
        result != null
        where:
        data|| res
        null||_
        [new RightsRecordModel(1l, 1l, 0, "rightsName", 0, "levelName", "userOrderId", "purchaseOrderId", "supplyOrderId", "punishOrderId", "money", LocalDateTime.of(2023, Month.JUNE, 13, 13, 48, 43))] || _
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme