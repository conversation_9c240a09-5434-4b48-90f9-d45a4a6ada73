package com.ctrip.dcs.driver.account.application.exector

import com.ctrip.dcs.driver.account.application.convert.QueryAccountBankCardByDriverIdConvert
import com.ctrip.dcs.driver.account.application.service.AccountBankCardService
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo
import com.ctrip.dcs.driver.domain.account.AccountBankCardDTO
import com.ctrip.dcs.driver.domain.account.QueryAccountBankCardByDriverIdRequestType
import com.ctrip.dcs.driver.domain.account.QueryAccountBankCardByDriverIdResponseType
import com.ctrip.dcs.driver.value.BaseResult
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import spock.lang.Specification
import spock.lang.Subject
import spock.lang.Unroll

/*
作者：pl.yang
创建时间：2025/6/3-下午2:45-2025
*/

class QueryAccountBankCardByDriverIdEngineTest extends Specification {
    def QueryAccountBankCardByDriverIdEngine = new QueryAccountBankCardByDriverIdEngine()

    @Subject
    QueryAccountBankCardByDriverIdEngine engine

    AccountBankCardService accountBankCardService = Mock()
    QueryAccountBankCardByDriverIdConvert convert = Mock()

    def setup() {
        engine = new QueryAccountBankCardByDriverIdEngine()
        engine.accountBankCardService = accountBankCardService
        engine.queryAccountBankCardByDriverIdConvert = convert
    }

    // 场景1: 服务层返回失败结果
    def "当银行卡服务返回失败时，应返回失败响应"() {
        given: "模拟服务层返回失败"
        def request = new QueryAccountBankCardByDriverIdRequestType(driverId: 123L)
        accountBankCardService.queryAccountBankCard(123L, null) >> BaseResult.failResult("500", "Service error")

        when: "执行查询"
        QueryAccountBankCardByDriverIdResponseType response = engine.execute(request)

        then: "验证失败响应"
        response.getResponseResult().getReturnMessage() == "Service error"
    }

    // 场景2: 查询结果为空
    def "当查询结果为空时，应返回'未找到'错误"() {
        given: "模拟服务层返回空数据"
        def request = new QueryAccountBankCardByDriverIdRequestType(driverId: 123L)
        accountBankCardService.queryAccountBankCard(123L, null) >> BaseResult.successResult(null)

        when: "执行查询"
        def response = engine.execute(request)

        then: "验证未找到错误"
        response.getResponseResult().getReturnMessage() == "AccountBankCardDo not query"
    }

    // 场景3: 请求指定银行卡ID但与查询结果不匹配
    def "当请求指定银行卡ID但与查询结果不匹配时，应返回失败"() {
        given: "创建请求并模拟服务返回"
        def request = new QueryAccountBankCardByDriverIdRequestType(
                driverId: 123L,
                bankCardId: "req-card-456"
        )

        def mockData = Stub(AccountBankCardDo) {
            getCardNo() >> "actual-card-789"
        }
        accountBankCardService.queryAccountBankCard(123L, "req-card-456") >> BaseResult.successResult(mockData)

        when: "执行查询"
        def response = engine.execute(request)

        then: "验证银行卡ID不匹配失败"
        response.getResponseResult().getReturnCode()!="200"
    }

    // 场景4: 转换后银行卡号为空
    def "当转换后银行卡号为空时，应返回'银行卡号为空'错误"() {
        given: "模拟服务返回有效数据但转换失败"
        def request = new QueryAccountBankCardByDriverIdRequestType(driverId: 123L)
        def mockData =AccountBankCardDo.builder().cardNo("card-123").build()

        accountBankCardService.queryAccountBankCard(123L, null) >> BaseResult.successResult(mockData)
        convert.convertResponse(request, mockData) >> new QueryAccountBankCardByDriverIdResponseType(
                accountBankCard: new AccountBankCardDTO(bankCardNo: null)
        )

        when: "执行查询"
        def response = engine.execute(request)

        then: "验证银行卡号空错误"
        response.getResponseResult().getReturnMessage() == "bankCardNo is null"
    }

    // 场景5: 仅提供司机ID查询成功
    def "当仅提供司机ID且查询成功时，应返回有效银行卡信息"() {
        given: "模拟仅司机ID的成功查询"
        def request = new QueryAccountBankCardByDriverIdRequestType(driverId: 123L)
        def mockData =AccountBankCardDo.builder().cardNo("card-123").build()
        def expectedResponse = new QueryAccountBankCardByDriverIdResponseType(
                accountBankCard: new AccountBankCardDTO(bankCardNo: "card-123")
        )

        accountBankCardService.queryAccountBankCard(123L, null) >> BaseResult.successResult(mockData)
        convert.convertResponse(request, mockData) >> expectedResponse

        when: "执行查询"
        def response = engine.execute(request)

        then: "验证成功响应"
        response.accountBankCard.bankCardNo == "card-123"
    }

    // 场景6: 同时提供司机ID和银行卡ID查询成功
    def "当同时提供司机ID和银行卡ID且查询成功时，应返回指定银行卡信息"() {
        given: "模拟带银行卡ID的成功查询"
        def request = new QueryAccountBankCardByDriverIdRequestType(
                driverId: 123L,
                bankCardId: "card-456"
        )
        def mockData =AccountBankCardDo.builder().cardNo("card-456").build()
        def expectedResponse = new QueryAccountBankCardByDriverIdResponseType(
                accountBankCard: new AccountBankCardDTO(bankCardNo: "card-456")
        )

        accountBankCardService.queryAccountBankCard(123L, "card-456") >> BaseResult.successResult(mockData)
        convert.convertResponse(request, mockData) >> expectedResponse

        when: "执行查询"
        def response = engine.execute(request)

        then: "验证成功响应"
        response.accountBankCard.bankCardNo == "card-456"
    }


}

