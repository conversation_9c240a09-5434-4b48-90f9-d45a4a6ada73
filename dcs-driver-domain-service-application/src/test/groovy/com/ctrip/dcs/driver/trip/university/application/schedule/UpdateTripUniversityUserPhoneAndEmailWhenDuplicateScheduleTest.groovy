package com.ctrip.dcs.driver.trip.university.application.schedule

import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig
import com.trip.dcs.driver.application.common.MockExecutorService
import org.mockito.Mockito
import qunar.tc.qschedule.common.pojo.ScheduleBaseMessage
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ExecutorService

class UpdateTripUniversityUserPhoneAndEmailWhenDuplicateScheduleTest extends Specification {
    def testObj = new UpdateTripUniversityUserPhoneAndEmailWhenDuplicateSchedule()
    def tripUniversityService = Mock(TripUniversityService)
    def accountMapperDao = Mock(AccountMapperDao)
    def executorService = Mock(ExecutorService)
    def tripUniversityQconfig = Mock(TripUniversityQconfig)

    def setup() {

        testObj.accountMapperDao = accountMapperDao
        testObj.tripUniversityQconfig = tripUniversityQconfig
        testObj.executorService = executorService
        testObj.tripUniversityService = tripUniversityService
        testObj.executorService = new MockExecutorService()
    }

    @Unroll
    def "onExecuteTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityService.updateDrvPhoneAndEmailForDuplicateProcess(_) >> null
        tripUniversityQconfig.getSyncBatchSize() >> 1

        when:
        def result = testObj.onExecute(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"drvIds\": \"134,124\"}") || null
    }
}
