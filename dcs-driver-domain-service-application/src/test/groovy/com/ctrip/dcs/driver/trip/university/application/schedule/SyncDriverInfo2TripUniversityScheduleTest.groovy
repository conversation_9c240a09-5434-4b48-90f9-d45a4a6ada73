package com.ctrip.dcs.driver.trip.university.application.schedule

import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig
import com.trip.dcs.driver.application.common.MockExecutorService
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ExecutorService

class SyncDriverInfo2TripUniversityScheduleTest extends Specification {
    def testObj = new SyncDriverInfo2TripUniversitySchedule()
    def tripUniversityService = Mock(TripUniversityService)
    def accountBaseInfoDao = Mock(AccountBaseInfoDao)
    def accountMapperDao = Mock(AccountMapperDao)
    def executorService = Mock(ExecutorService)
    def tripUniversityQconfig = Mock(TripUniversityQconfig)

    def setup() {

        testObj.accountMapperDao = accountMapperDao
        testObj.accountBaseInfoDao = accountBaseInfoDao
        testObj.tripUniversityQconfig = tripUniversityQconfig
        testObj.executorService = executorService
        testObj.tripUniversityService = tripUniversityService
        testObj.executorService = new MockExecutorService()

    }

    @Unroll
    def "onExecuteTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityService.syncDriverInfoToTripUniversity(_) >> true
        accountBaseInfoDao.batchQueryByUID(_) >> [new AccountBaseInfoPO(id: 1L, uid: "uid")]
        2 * accountBaseInfoDao.batchQueryPageByDateRange(_, _, _, _) >> [new AccountBaseInfoPO(id: 1L, uid: "uid")] >> []
        accountMapperDao.batchQueryBySource(_, _) >> [new AccountMapperPO(uid: "uid")]
        tripUniversityQconfig.getSyncBatchSize() >> 1

        when:
        def result = testObj.onExecute(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"drvIds\":\"1\"}") || null
    }

    @Unroll
    def "onExecuteByDateTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityService.syncDriverInfoToTripUniversity(_) >> true
        accountBaseInfoDao.batchQueryByUID(_) >> [new AccountBaseInfoPO(id: 1L, uid: "uid")]
        2 * accountBaseInfoDao.batchQueryPageByDateRange(_, _, _, _) >> [new AccountBaseInfoPO(id: 1L, uid: "uid")] >> []
        accountMapperDao.batchQueryBySource(_, _) >> [new AccountMapperPO(uid: "uid")]
        tripUniversityQconfig.getSyncBatchSize() >> 1

        when:
        def result = testObj.onExecute(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"type\":\"byDate\", \"from\": \"2024-10-10 : 11:00:00\", \"to\": \"2024-10-10 : 11:00:00\"}") || null
    }

    @Unroll
    def "onExecuteByDrvIdsTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityService.syncDriverInfoToTripUniversity(_) >> true
        accountBaseInfoDao.batchQueryByUID(_) >> [new AccountBaseInfoPO(id: 1L, uid: "uid")]
        accountMapperDao.batchQueryBySource(_, _ as List) >> [new AccountMapperPO(uid: "uid")]
        tripUniversityQconfig.getSyncBatchSize() >> 1

        when:
        def result = testObj.onExecute(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"type\":\"byDrvIds\", \"drvIds\": \"134,124\"}") || null
    }
}
