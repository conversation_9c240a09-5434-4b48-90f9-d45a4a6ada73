package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.account.infrastructure.constant.Constants
import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfig
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO
import qunar.tc.qmq.MessageProducer
import qunar.tc.qmq.Message
import spock.lang.Specification
import spock.lang.Subject

import java.util.concurrent.TimeUnit

class DriverOrderStatusChangeListenerTest extends Specification {
    @Subject
    DriverOrderStatusChangeListener listener = new DriverOrderStatusChangeListener()

    DriverTaskAdapter driverTaskAdapter = Mock()
    MessageProducer messageProducer = Mock()
    BusinessConfig businessConfig = Mock()

    def setup() {
        listener.driverTaskAdapter = driverTaskAdapter
        listener.messageProducer = messageProducer
        listener.businessConfig = businessConfig
    }

    def "onDriverOrderCanceled should update status and send message when valid task exists"() {
        given:
        def message = Mock(Message)
        def taskRecord = activeTaskRecord()

        when:
        listener.onDriverOrderCanceled(message)

        then:
        1 * message.getLongProperty("driverId") >> 123L
        1 * message.getStringProperty("driverOrderId") >> "ORDER_001"
        1 * driverTaskAdapter.queryDriverTaskByDriverOrderId(123L, "ORDER_001") >> taskRecord
        1 * driverTaskAdapter.updateTaskStatus("TASK_001", DriverTaskStatusEnum.CANCEL)
//        1 * listener.pushTaskStatusMessage("4084", taskRecord)
    }

    def "onCarInspectionNotice should expire task and send message"() {
        given:
        def message = Mock(Message)
        def taskRecord = activeTaskRecord()

        when:
        listener.onCarInspectionNotice(message)

        then:
        1 * message.getLongProperty("driverId") >> 123L
        1 * message.getStringProperty("driverOrderId") >> "ORDER_001"
        1 * driverTaskAdapter.queryDriverTaskByDriverOrderId(123L, "ORDER_001") >> taskRecord
        1 * driverTaskAdapter.updateTaskStatus("TASK_001", DriverTaskStatusEnum.EXPIRE)
//        1 * listener.pushTaskStatusMessage("4085", taskRecord)
    }

    def "getValidDriverTaskInfo should return null for invalid message"() {
        expect:
        listener.getValidDriverTaskInfo(message) == null

        where:
        message               | _
        null                  | _
        invalidDriverIdMsg()  | _
        invalidOrderIdMsg()   | _
    }

    private DriverTaskRecordDO activeTaskRecord() {
        new DriverTaskRecordDO(
                driverId: 123L,
                taskId: "TASK_001",
                driverOrderId: "ORDER_001",
                customerOrderId: "C_ORDER_001",
                taskStatus: DriverTaskStatusEnum.CREATE
        )
    }

    private Message invalidDriverIdMsg() {
        Mock(Message) {
            getLongProperty("driverId") >> 0L
            getStringProperty("driverOrderId") >> "ORDER_001"
        }
    }

    private Message invalidOrderIdMsg() {
        Mock(Message) {
            getLongProperty("driverId") >> 123L
            getStringProperty("driverOrderId") >> null
        }
    }
}
