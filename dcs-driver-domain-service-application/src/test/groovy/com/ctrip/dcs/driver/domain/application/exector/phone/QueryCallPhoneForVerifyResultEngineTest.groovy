package com.ctrip.dcs.driver.domain.application.exector.phone

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable
import com.ctrip.dcs.driver.domain.application.covert.QueryCallPhoneForVerifyResultConvert
import com.ctrip.dcs.driver.domain.application.service.QueryCallPhoneForVerifyResultService
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType
import spock.lang.Specification

class QueryCallPhoneForVerifyResultEngineTest extends Specification {
    PhoneCheckTaskTable phoneCheckTaskTable = Mock(PhoneCheckTaskTable)
    QueryCallPhoneForVerifyResultConvert convert = new QueryCallPhoneForVerifyResultConvert()
    QueryCallPhoneForVerifyResultService service = new QueryCallPhoneForVerifyResultService(phoneCheckTaskTable: phoneCheckTaskTable)

    QueryCallPhoneForVerifyResultEngine engine = new QueryCallPhoneForVerifyResultEngine(
            convert: convert,
            service: service
    )

    def "QueryCallPhoneForVerifyResult"() {
        def request = new QueryCallPhoneForVerifyResultRequestType(callTaskId: 1234)
        given:
        phoneCheckTaskTable.queryByPk(_) >> new PhoneCheckTaskEntity(callHangupCode: "200")
        when:
        def response = engine.execute(request)
        then:
        response.getCallResultStatus() == "200"
    }
}