package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.domain.application.schedule.DriverLevelScheduleLogic
import org.junit.Test
import org.junit.Before
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.slf4j.Logger
import static org.mockito.Mockito.*

class DrivPointFinishListenerTest {
    @Mock
    Logger logger
    @Mock
    DriverLevelScheduleLogic driverLevelScheduleLogic
    @InjectMocks
    DrivPointFinishListener drivPointFinishListener

    @Before
    void setUp() {
        MockitoAnnotations.initMocks(this)
    }

    @Test
    void testOnMessage() {
        when(driverLevelScheduleLogic.getCity()).thenReturn([1l])

        drivPointFinishListener.onMessage(null)
    }

    @Test
    void testOnMessageError() {
        when(driverLevelScheduleLogic.getCity()).thenThrow(NullPointerException.class)
        drivPointFinishListener.onMessage(null)
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme