package com.ctrip.dcs.driver.trip.university.application.schedule

import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import com.ctrip.dcs.driver.trip.university.infrastructure.gateway.TripUniversityGateway
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityFailedLogDTO
import com.ctrip.dcs.go.log.Log
import com.trip.dcs.driver.application.common.MockExecutorService
import qunar.tc.schedule.MockParameter
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ExecutorService

class SyncDriverInfo2TripUniversityByFailedLogScheduleTest extends Specification {
    def testObj = new SyncDriverInfo2TripUniversityByFailedLogSchedule()
    def log = Mock(Log)
    def tripUniversityGateway = Mock(TripUniversityGateway)
    def tripUniversityService = Mock(TripUniversityService)
    def executorService = Mock(ExecutorService)
    def tripUniversityQconfig = Mock(TripUniversityQconfig)

    def setup() {

        testObj.tripUniversityQconfig = tripUniversityQconfig
        testObj.log = log
        testObj.executorService = executorService
        testObj.tripUniversityService = tripUniversityService
        testObj.tripUniversityGateway = tripUniversityGateway
        testObj.executorService = new MockExecutorService()

    }

    @Unroll
    def "onExecuteTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        2 * tripUniversityGateway.querySyncFailedLog(_) >> [new DriverSyncDrvInfoToTripUniversityFailedLogDTO(id: 1L, uid: "uid", drvId: 1L, retryCount: 0)] >> []
        tripUniversityService.syncDriverInfoToTripUniversity(_) >> true
        tripUniversityQconfig.getSyncBatchSize() >> 1

        when:
        def result = testObj.onExecute(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"drvIds\":\"1\"}") || null
    }

    @Unroll
    def "onExecuteByDateTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        2 * tripUniversityGateway.querySyncFailedLog(_) >> [new DriverSyncDrvInfoToTripUniversityFailedLogDTO(id: 1L, uid: "uid", drvId: 1L, retryCount: 0)] >> []
        tripUniversityService.syncDriverInfoToTripUniversity(_) >> true
        tripUniversityQconfig.getSyncBatchSize() >> 1

        when:
        def result = testObj.onExecute(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"type\":\"byDate\", \"from\": \"2024-10-10 : 11:00:00\", \"to\": \"2024-10-10 : 11:00:00\"}") || null
    }

    @Unroll
    def "onExecuteByDrvIdsTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        2 * tripUniversityGateway.querySyncFailedLog(_) >> [new DriverSyncDrvInfoToTripUniversityFailedLogDTO(id: 1L, uid: "uid", drvId: 1L, retryCount: 0)] >> []
        tripUniversityService.syncDriverInfoToTripUniversity(_) >> true
        tripUniversityQconfig.getSyncBatchSize() >> 1

        when:
        def result = testObj.onExecute(parameter)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        parameter || expectedResult
        new MockParameter("{\"type\":\"byDrvIds\", \"drvIds\": \"134,124\"}") || null
    }
}
