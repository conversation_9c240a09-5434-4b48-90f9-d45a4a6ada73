package com.ctrip.dcs.driver.domain.application.exector.account

import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO
import com.ctrip.dcs.driver.account.application.exector.ParsePhoneNumberEngine
import com.ctrip.dcs.driver.account.domain.service.AccountService
import com.ctrip.dcs.driver.domain.account.ParsePhoneNumberRequestType
import com.ctrip.dcs.driver.domain.account.TelphoneNumberType
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository
import spock.lang.Specification

class ParsePhoneNumberEngineTest extends Specification {

    ParsePhoneNumberEngine parsePhoneNumberEngine
    AccountService accountService = Mock(AccountService)
    ArchCoreInfoRepository archCoreInfoService = Mock(ArchCoreInfoRepository)

    def setup() {
        parsePhoneNumberEngine = new ParsePhoneNumberEngine()
        parsePhoneNumberEngine.accountService = accountService;
        parsePhoneNumberEngine.archCoreInfoService = archCoreInfoService;
    }

    def "test execute"() {
        given:
        accountService.splitPhoneNumber(_, _) >> splitResult
        archCoreInfoService.decryptByType(_, _) >> decryptedSplitNumber
        archCoreInfoService.encryptByType(_, _) >> encryptedSplitNumber
        archCoreInfoService.isEncrypt(_, _) >> [true,true,true,true,true,false]

        when:
        def res = parsePhoneNumberEngine.execute(new ParsePhoneNumberRequestType(countryCode: requestCountryCode, number: requestNumber, phoneType: requestType))


        then:
        res.getResponseResult().getReturnCode() == respCode
        res.getCounryCode() == respCountryCode
        res.getNumber() == respNumber
        res.isValidNumber() == respValidNubmer

        where:
        desc                              || requestCountryCode || requestNumber   || requestType               || splitResult                                                                                          || respCode  || respCountryCode || respNumber    || decryptedSplitNumber || encryptedSplitNumber || respValidNubmer
        "splitFailed"                     || "86"               || "111"           || TelphoneNumberType.ALL    || null                                                                                                 || "1117106" || null            || null          || ""                   || ""                   || false
        "normal"                          || "86"               || "12345678901"   || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "12345678901", valid: true, mobile: true) || "200"     || "86"            || "12345678901" || "12345678901"        || "12345678901"        || true
        "startWith0"                      || "86"               || "012345678901"  || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "12345678901", valid: true, mobile: true) || "200"     || "86"            || "12345678901" || "12345678901"        || "12345678901"        || false
        "startWithCountryCode"            || "86"               || "8612345678901" || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "12345678901", valid: true, mobile: true) || "200"     || "86"            || "12345678901" || "12345678901"        || "12345678901"        || false
        "invalidPhone"                    || "86"               || "123"           || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "123", valid: false, mobile: false)       || "200"     || "86"            || "123"         || "123"                || "123"                || false
        "invalidPhoneWith0"               || "86"               || "0123"          || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "123", valid: false, mobile: false)       || "200"     || "86"            || "123"         || "123"                || "123"                || false
        "invalidPhoneWithCountryCode"     || "86"               || "86123"         || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "123", valid: false, mobile: false)       || "200"     || "86"            || "123"         || "123"                || "123"                || false
        "normal-fixedPhone"               || "86"               || "2163723500"    || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "21", bodyNumber: "63723500", valid: true, mobile: false) || "200"     || "86"            || "2163723500"  || "63723500"           || "2163723500"         || true
        "startWith0-fixedPhone"           || "86"               || "02163723500"   || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "21", bodyNumber: "63723500", valid: true, mobile: false) || "200"     || "86"            || "2163723500"  || "63723500"           || "2163723500"         || false
        "startWithCountryCode-fixedPhone" || "86"               || "862163723500"  || TelphoneNumberType.ALL    || new NumberDTO(countryCode: "86", cityCode: "21", bodyNumber: "63723500", valid: true, mobile: false) || "200"     || "86"            || "2163723500"  || "63723500"           || "2163723500"         || false
        "mobileType-fixedPhone"           || "86"               || "2163723500"    || TelphoneNumberType.MOBILE || new NumberDTO(countryCode: "86", cityCode: "21", bodyNumber: "63723500", valid: true, mobile: false) || "200"     || "86"            || "2163723500"  || "63723500"           || "2163723500"         || false
        "mobileType-mobile"               || "86"               || "12345678901"   || TelphoneNumberType.MOBILE || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "12345678901", valid: true, mobile: true) || "200"     || "86"            || "12345678901" || "12345678901"        || "12345678901"        || true
        "mobileType-mobileWith0"          || "86"               || "012345678901"  || TelphoneNumberType.MOBILE || new NumberDTO(countryCode: "86", cityCode: "", bodyNumber: "12345678901", valid: true, mobile: true) || "200"     || "86"            || "12345678901" || "12345678901"        || "12345678901"        || false


    }
}
