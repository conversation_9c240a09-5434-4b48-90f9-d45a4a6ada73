package com.ctrip.dcs.driver.domain.application.exector.phone

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable
import com.ctrip.dcs.driver.domain.application.covert.CreatePhoneCheckConvert
import com.ctrip.dcs.driver.domain.application.service.CreatePhoneCheckService
import com.ctrip.model.CreatePhoneCheckRequestType
import spock.lang.Specification

class CreatePhoneCheckEngineTest extends Specification {
    PhoneCheckTable phoneCheckTable = Mock(PhoneCheckTable)
    CreatePhoneCheckConvert convert = new CreatePhoneCheckConvert()
    CreatePhoneCheckService service = new CreatePhoneCheckService(phoneCheckTable: phoneCheckTable)

    CreatePhoneCheckEngine engine = new CreatePhoneCheckEngine(
            convert: convert,
            service: service
    )

    def "CreatePhoneCheck"() {
        def request = new CreatePhoneCheckRequestType()
        given:
        phoneCheckTable.insert(_) >> 1
        when:
        def response = engine.execute(request)
        then:
        response.result == true
    }
}