package com.ctrip.dcs.driver.trip.university.application.mq

import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification
import spock.lang.Unroll

class PushDataToCtripUniversityListenerTest extends Specification {
    def testObj = new PushDataToCtripUniversityListener()
    def tripUniversityService = Mock(TripUniversityService)

    def setup() {

        testObj.tripUniversityService = tripUniversityService
    }

    @Unroll
    def "pushDataToTripUniversityListenerTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityService.syncDriverInfoToTripUniversity(_) >> true

        when:
        def result = testObj.pushDataToTripUniversityListener(message)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        message || expectedResult
        new BaseMessage(attrs : ["drvId": "123"]) || null
        new BaseMessage(attrs : ["drvId": "0", "uid": null]) || null
    }
}
