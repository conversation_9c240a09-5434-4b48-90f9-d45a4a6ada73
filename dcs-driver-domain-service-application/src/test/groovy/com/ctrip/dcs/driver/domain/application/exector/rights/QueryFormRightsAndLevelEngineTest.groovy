package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.common.DriverLevelHelper
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsChoiceLevelInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsIntroduceInfo
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.rights.QueryFormRightsAndLevelRequestType
import com.ctrip.dcs.driver.domain.rights.QueryFormRightsAndLevelResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import com.google.common.collect.Lists
import spock.lang.Specification

class QueryFormRightsAndLevelEngineTest extends Specification {
    QueryFormRightsAndLevelEngine queryFormRightsAndLevelEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsConfig rightsConfig = Mock(RightsConfig)
    DriverLevelHelper driverLevelHelper = Mock(DriverLevelHelper)
    LevelConfig levelConfig= Mock(LevelConfig)


    def setup() {
        domainBeanFactory = new DomainBeanFactory(levelConfig: levelConfig,rightsConfig: rightsConfig, driverLevelHelper: driverLevelHelper)
        queryFormRightsAndLevelEngine = new QueryFormRightsAndLevelEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {
        def request = new QueryFormRightsAndLevelRequestType( cityId: 1)

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryFormRightsAndLevelResponseType.class).init()

        given:
        domainBeanFactory.levelConfig()>>levelConfig
        domainBeanFactory.rightsConfig()>>rightsConfig
        Map<Long, List<FormLevelInfo>> cityLevelMap = new HashMap<>();
        cityLevelMap.put(1L,[new FormLevelInfo(0l, 0, "levelName", "cityIdsStr", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0, 0, 0, [1l])])
        levelConfig.cityLevelMap=cityLevelMap
        levelConfig.getCityDriverLevel(_) >> Lists.newArrayList(new FormLevelInfo(0l, 0, "levelName", "cityIdsStr", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0, 0, 0, [1l]))

        Map<Long, List<FormRightsInfo>> cityRightsMap = new HashMap<>()
        Map<Integer, List<Long>> levelRightsMap = new HashMap<>();
        levelRightsMap.put(0,Arrays.asList(0,1,2))
        List<FormRightsInfo> list=[new FormRightsInfo(0l, 0, "rightsName", "rightsDesc", [new FormRightsIntroduceInfo("1","2")], [new FormRightsChoiceLevelInfo(0, 0, 0, "extend")], "cityIdsStr", [1l], 0, 0, "extend")]
        cityRightsMap.put(1L,list)
        rightsConfig.cityRightsMap=cityRightsMap
        rightsConfig.levelRightsMap=levelRightsMap
        driverLevelHelper.getDriverLevelName(_, _) >> "aaa"

        when:
        QueryFormRightsAndLevelResponseType result = queryFormRightsAndLevelEngine.execute(request)

        then:
        result != null
        result.getLevels().size() == 1
        result.getLevels().get(0).getLevelName() == "aaa"
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme