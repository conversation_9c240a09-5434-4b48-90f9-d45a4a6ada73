package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverLevelGreyConfig
import com.ctrip.dcs.driver.domain.rights.CheckVacationRightsRequestType
import com.ctrip.dcs.driver.domain.rights.CheckVacationRightsResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import spock.lang.Specification

class CheckVacationRightsEngineTest extends Specification {
    CheckVacationRightsEngine checkVacationRightsEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsRepoServiceImpl repoService = Mock(RightsRepoServiceImpl)
    DriverLevelGreyConfig driverLevelGreyConfig= Mock(DriverLevelGreyConfig)


    def setup() {
        domainBeanFactory = new DomainBeanFactory(driverLevelGreyConfig: driverLevelGreyConfig)
        checkVacationRightsEngine = new CheckVacationRightsEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {
        def request = new CheckVacationRightsRequestType( driverId: 1, cityId: 1)

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(CheckVacationRightsResponseType.class).init()

        given:
        domainBeanFactory.driverLevelGreyConfig() >> driverLevelGreyConfig
        driverLevelGreyConfig.canUseVacationRights(1)>>true
        when:
        def expectedResult = checkVacationRightsEngine.execute(request)

        then:
        expectedResult.canUse == true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme