package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.arch.distlock.DLock
import com.ctrip.arch.distlock.DistributedLockService
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig
import com.ctrip.dcs.driver.domain.rights.CheckRightsRequestType
import com.ctrip.dcs.driver.domain.rights.CheckRightsResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.TimeUnit

class CheckRightsEngineTest extends Specification {
    CheckRightsEngine checkRightsEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsRepoServiceImpl repoService = Mock(RightsRepoServiceImpl)
    DistributedLockService distributedLockService =Mock(DistributedLockService)
    LockService lockService =Mock(LockService)
    SystemQConfig systemQConfig=Mock(SystemQConfig)

    def setup() {
        lockService =new LockService(distributedLockService: distributedLockService)
        domainBeanFactory = new DomainBeanFactory(rightsRepoService: repoService)
        checkRightsEngine = new CheckRightsEngine(domainBeanFactory: domainBeanFactory,lockService:lockService,systemQConfig:systemQConfig)
        DLock dLock = Mock(DLock)
        distributedLockService.getLock("dcs_driver_use_rights:1_7")>> dLock
        dLock.tryLock(1000L, TimeUnit.MILLISECONDS)>>true
    }

    @Unroll
    def "test execute"() {
        def request = new CheckRightsRequestType(
                driverId: 1, date: "2023-06", rightsType: 7)

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(CheckRightsResponseType.class).init()

        given:
        domainBeanFactory.rightsRepoService() >> repoService
        repoService.queryDriverRights(1L, Arrays.asList(7), "2023-06") >> [new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 7, 0, 0, 0, "2022-06-13 14:07:38", "2099-06-13 14:07:38", "2023-06-13 14:07:38", "{\"vacationLimit\":2,\"vacationUse\":0}")]
        systemQConfig.getString("close_rights")>>"2"
        when:
        def expectedResult = checkRightsEngine.execute(request)


        then:
        expectedResult.canUse == true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme