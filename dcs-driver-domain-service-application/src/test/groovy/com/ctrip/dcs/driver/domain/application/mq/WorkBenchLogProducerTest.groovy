package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class WorkBenchLogProducerTest extends Specification {
    @Mock
    Logger LOGGER
    @InjectMocks
    WorkBenchLogProducer workBenchLogProducer

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test send Message"() {
        when:
        WorkBenchLogProducer.sendMessage(WorkBenchLogProducer.buildMessase(condition))

        then:
        res == 1
        where:
        condition || res
        new UseRightsCondition(1l, 0,"name" ,"123", "123", "123", "123", 0 as BigDecimal,"") || 1
        new UseRightsCondition(1l, 1,"name" ,"123", "123", "123", "123", 0 as BigDecimal,"") || 1
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme