package com.ctrip.dcs.driver.domain.application.domain.exector

import com.ctrip.dcs.driver.domain.application.common.CommonExamSecretUtils
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.domain.exam.ApplyExamObjectImpl
import com.ctrip.dcs.driver.domain.application.http.HttpClient
import com.ctrip.dcs.driver.domain.application.redis.ExamRedisLogic
import com.ctrip.dcs.driver.domain.application.service.GuideApplyExamDBDataService
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService
import com.ctrip.dcs.driver.domain.application.service.impl.GuideApplyExamDBDataServiceImpl
import com.ctrip.dcs.driver.domain.application.service.impl.RightsDBDataServiceImpl
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideApplyExamDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideApplyExamPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.ExamInterfaceConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.GuideExamConfig
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.dcs.driver.value.exam.ApplyExamObject
import com.ctrip.dcs.driver.value.rights.DriverRightsObject
import com.ctrip.igt.framework.common.clogging.Logger
import mockit.Mocked
import org.mockito.InjectMocks
import org.mockito.Mock
import spock.lang.Specification

import java.sql.Timestamp
import java.util.concurrent.ExecutorService

class SaveApplyExamExecutorTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    ApplyExamObject owner
    @Mock
    DomainBeanFactory domainBeanFactory
    @Mock
    GuideExamConfig guideExamConfig = Mock(GuideExamConfig);
    @Mock
    GuideApplyExamDBDataService guideApplyExamDBDataService = Mock(GuideApplyExamDBDataService);
    @Mock
    GuideApplyExamDao guideApplyExamDao =Mock(GuideApplyExamDao)
    @Mock
    ExecutorService executorService = Mock(ExecutorService)
    @Mock
    ExamInterfaceConfig examInterfaceConfig = Mock(ExamInterfaceConfig)
    @Mock
    ExamRedisLogic examRedisLogic = Mock(ExamRedisLogic)
    @Mock
    HttpClient httpClient = Mock(HttpClient)
    @Mock
    CommonExamSecretUtils commonExamSecretUtils = Mock(CommonExamSecretUtils)

    @InjectMocks
    SaveApplyExamExecutor saveApplyExamExecutor

    def setup() {
        domainBeanFactory = new DomainBeanFactory(guideExamConfig: guideExamConfig,guideApplyExamDBDataService:guideApplyExamDBDataService,executorService:executorService,examInterfaceConfig:examInterfaceConfig,examRedisLogic:examRedisLogic,httpClient:httpClient,commonExamSecretUtils:commonExamSecretUtils)
        owner = new ApplyExamObjectImpl(domainBeanFactory,new GuideApplyExamModel(guideId:2129L,examAccountId:"guide0002129",applySubject:"DDXDCJ2024",guideName:"何海龙"));
        saveApplyExamExecutor = new SaveApplyExamExecutor(owner,domainBeanFactory)
        guideApplyExamDBDataService = new GuideApplyExamDBDataServiceImpl(guideApplyExamDao: guideApplyExamDao)
    }

    def "passed"() {
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.getExamInfoByDeptId("DDXDCJ2024") >>new FromGuideExamInfo("DDXDCJ2024","初级笔试","2023-11-01 00:00:00","2099-12-31 23:59:59",*************,*************,"DDXDCJ","00003","guidetrainingcenter.registrationinformationform.elementary")

        domainBeanFactory.guideApplyExamDBDataService().queryGuideApplyExamByAccount("guide0002129") >>[new GuideApplyExamModel(id: 1l, guideId: 2129l, examAccountId: "guide0002129", guideName: "测试", account: "***********", applySubject: "DDXDCJ2024", applyTime: "2023-11-13 07:12:12", timeZone: new BigDecimal("8"), subjectName: "中级笔试", applyResult: 1, examIsPassed: 1, callExamSuccess: 1, datachangeCreatetime: "2023-11-15 09:12:13")]
        when:
        Boolean result = saveApplyExamExecutor.doWork(null)

        then:
        result == Boolean.TRUE
    }

    def "have not apply"() {
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.getExamInfoByDeptId("DDXDCJ2024") >>new FromGuideExamInfo("DDXDCJ2024","初级笔试","2023-11-01 00:00:00","2099-12-31 23:59:59",*************,*************,"DDXDCJ","00003","guidetrainingcenter.registrationinformationform.elementary")

        domainBeanFactory.guideApplyExamDBDataService().queryGuideApplyExamByAccount("guide0002129") >>[]

        domainBeanFactory.executorService() >> executorService;
        when:
        Boolean result = saveApplyExamExecutor.doWork(null)

        then:
        result == Boolean.FALSE
    }

    def "applied not passed"() {
        given:
        domainBeanFactory.guideExamConfig() >> guideExamConfig
        guideExamConfig.getExamInfoByDeptId("DDXDCJ2024") >>new FromGuideExamInfo("DDXDCJ2024","初级笔试","2023-11-01 00:00:00","2099-12-31 23:59:59",*************,*************,"DDXDCJ","00003","guidetrainingcenter.registrationinformationform.elementary")

        domainBeanFactory.guideApplyExamDBDataService().queryGuideApplyExamByAccount("guide0002129") >>[new GuideApplyExamModel(id: 1l, guideId: 2129l, examAccountId: "guide0002129", guideName: "测试", account: "***********", applySubject: "DDXDCJ2024", applyTime: "2023-11-13 07:12:12", timeZone: new BigDecimal("8"), subjectName: "中级笔试", applyResult: 1, examIsPassed: 0, callExamSuccess: 1, datachangeCreatetime: "2023-11-15 09:12:13")]

        domainBeanFactory.executorService() >> executorService;
        when:
        Boolean result = saveApplyExamExecutor.doWork(null)

        then:
        result == Boolean.FALSE
    }

    def "call exam data"() {
        given:
        domainBeanFactory.examInterfaceConfig() >> examInterfaceConfig
        examInterfaceConfig.getApiDomainName() >> "https://t-api.exexm.com"
        examInterfaceConfig.getChangeUserInfoUrl() >> "/api/dataexchange/user?access_token=%s"

        domainBeanFactory.examRedisLogic() >> examRedisLogic
        examRedisLogic.getAccessToken() >> ""

        examInterfaceConfig.getSsoDomainName() >> "https://t-sso.exexm.com"
        examInterfaceConfig.getOpenAuthInterfaceUrl() >> "/api/oauth/token?tenantid=%s&secret=%s"
        domainBeanFactory.commonExamSecretUtils() >> commonExamSecretUtils
        commonExamSecretUtils.secret() >> "QtHvZrpfWFzuCmw6"
        examInterfaceConfig.getTenantid() >> "hq"

        domainBeanFactory.httpClient() >> httpClient
        httpClient.get("https://t-sso.exexm.com/api/oauth/token?tenantid=hq&secret=QtHvZrpfWFzuCmw6") >> "{\"code\":200,\"msg\":\"ok\",\"data\":{\"access_token\":\"gERBqz5GhyWjodk4KP-FKA__\",\"expires_in\":6921.0},\"success\":true}\n"

        httpClient.post("https://t-api.exexm.com/api/dataexchange/user?access_token=testtoken","{\"fields\":[\"id\",\"user_name\",\"dept_id\"],\"data\":{\"id\":\"guide0002129\",\"user_name\":\"何海龙\",\"dept_id\":\"DDXDCJ2024\"}}")>> "{\"code\":200,\"msg\":\"\",\"Message\":\"\",\"data\":{\"success\":true,\"action\":2,\"id\":\"9994492060832256\",\"msg\":\"修改成功\"},\"success\":true,\"is_encrypt\":false}\n"
        when:
        Boolean result = saveApplyExamExecutor.callExamDataExchange()

        then:
        result == null
    }


}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
