package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.infrastructure.adapter.tour.ProductBasicServiceProxy
import com.ctrip.frt.product.soa.DriverInfoResponseType
import com.ctrip.frt.product.soa.DriverInfoType
import com.ctrip.igt.framework.common.exception.BizException
import org.assertj.core.util.Lists
import spock.lang.Specification

class TourServiceImplTest extends Specification {
    def productBasicServiceProxy = Mock(ProductBasicServiceProxy)

    def tourService = new TourServiceImpl(
            productBasicServiceProxy: productBasicServiceProxy
    )

    def "getDriverInfo"() {
        given:
        productBasicServiceProxy.getDriverInfo(_) >> responseType

        when:
        def info = tourService.getDriverInfo(1L)

        then:
        result == info != null
        where:
        responseType                                                                         | result
        null                                                                                 | false
        new DriverInfoResponseType()                                                         | false
        new DriverInfoResponseType(driverInfoList: Lists.newArrayList(new DriverInfoType())) | true
    }

    def "getDriverInfo_exp"() {
        given:
        productBasicServiceProxy.getDriverInfo(_) >> { throw new Exception("error") }

        when:
        def info = tourService.getDriverInfo(1L)

        then:
        info == null
    }
}
