package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.domain.application.redis.ExamRedisLogic
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDAlDao
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverLevelGreyConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyLong
import static org.mockito.Mockito.when

class GuideApplyExamListenerTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    ExamRedisLogic examRedisLogic = Mock(ExamRedisLogic);
    @InjectMocks
    GuideApplyExamListener guideApplyExamListener

    def setup() {
        guideApplyExamListener = new GuideApplyExamListener(examRedisLogic:examRedisLogic);
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.setProperty("dataChange", "{\"otterParseTime\":*************,\"orderKeyInfo\":{\"pks\":[],\"schemaName\":\"dcsguidesupplydb\",\"tableName\":\"guide_apply_exam\"},\"otterSendTime\":*************,\"beforeColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"71\"},{\"isNull\":false,\"name\":\"guide_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2297\"},{\"isNull\":false,\"name\":\"exam_account_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"guide0002297\"},{\"isNull\":false,\"name\":\"guide_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"笑川桑\"},{\"isNull\":false,\"name\":\"account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"},{\"isNull\":false,\"name\":\"apply_subject\",\"isKey\":false,\"isUpdated\":false,\"value\":\"DDXDCJ2024\"},{\"isNull\":false,\"name\":\"apply_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-11-15 14:07:07\"},{\"isNull\":false,\"name\":\"time_zone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"8.0\"},{\"isNull\":false,\"name\":\"subject_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"初级笔试\"},{\"isNull\":false,\"name\":\"apply_result\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"exam_is_passed\",\"isKey\":false,\"isUpdated\":true,\"value\":\"0\"},{\"isNull\":false,\"name\":\"call_exam_success\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-11-15 14:07:07.236\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-12-04 19:40:57.523\"}],\"eventType\":\"UPDATE\",\"schemaName\":\"dcsguidesupplydb\",\"afterColumnList\":[{\"isNull\":false,\"name\":\"id\",\"isKey\":true,\"isUpdated\":false,\"value\":\"71\"},{\"isNull\":false,\"name\":\"guide_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2297\"},{\"isNull\":false,\"name\":\"exam_account_id\",\"isKey\":false,\"isUpdated\":false,\"value\":\"guide0002297\"},{\"isNull\":false,\"name\":\"guide_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"笑川桑\"},{\"isNull\":false,\"name\":\"account\",\"isKey\":false,\"isUpdated\":false,\"value\":\"<EMAIL>\"},{\"isNull\":false,\"name\":\"apply_subject\",\"isKey\":false,\"isUpdated\":false,\"value\":\"DDXDCJ2024\"},{\"isNull\":false,\"name\":\"apply_time\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-11-15 14:07:07\"},{\"isNull\":false,\"name\":\"time_zone\",\"isKey\":false,\"isUpdated\":false,\"value\":\"8.0\"},{\"isNull\":false,\"name\":\"subject_name\",\"isKey\":false,\"isUpdated\":false,\"value\":\"初级笔试\"},{\"isNull\":false,\"name\":\"apply_result\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"exam_is_passed\",\"isKey\":false,\"isUpdated\":true,\"value\":\"1\"},{\"isNull\":false,\"name\":\"call_exam_success\",\"isKey\":false,\"isUpdated\":false,\"value\":\"1\"},{\"isNull\":false,\"name\":\"datachange_createtime\",\"isKey\":false,\"isUpdated\":false,\"value\":\"2023-11-15 14:07:07.236\"},{\"isNull\":false,\"name\":\"datachange_lasttime\",\"isKey\":false,\"isUpdated\":true,\"value\":\"2023-12-04 20:34:06.686\"}],\"tableName\":\"guide_apply_exam\"}");

        given:

        when:
        Boolean result =  guideApplyExamListener.onMessage(message)

        then:
        result == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
