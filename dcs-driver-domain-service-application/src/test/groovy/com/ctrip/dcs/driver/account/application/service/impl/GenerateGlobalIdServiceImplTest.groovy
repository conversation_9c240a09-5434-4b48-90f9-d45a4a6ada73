package com.ctrip.dcs.driver.account.application.service.impl


import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.GlobalIdRecordDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.GlobalIdRecordPO

import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository
import spock.lang.Specification
import spock.lang.Subject


/*
作者：pl.yang
创建时间：2025/4/3-下午5:15-2025
*/

class GenerateGlobalIdServiceImplTest extends Specification {
    def accountBaseInfoDao = Mock(AccountBaseInfoDao)
    def userCenterAccountGateway = Mock(UserCenterAccountGateway)
    def accountMapperDao = Mock(AccountMapperDao)
    def globalIdRecordDao = Mock(GlobalIdRecordDao)
    def archCoreInfoService = Mock(ArchCoreInfoRepository)
    def lockService = Mock(LockService)
    def driverAccountConfig = Mock(DriverAccountConfig)

    @Subject
    def service = new GenerateGlobalIdServiceImpl(
            accountBaseInfoDao: accountBaseInfoDao,
            userCenterAccountGateway: userCenterAccountGateway,
            accountMapperDao: accountMapperDao,
            globalIdRecordDao: globalIdRecordDao,
            archCoreInfoService: archCoreInfoService,
            lockService: lockService,
            driverAccountConfig: driverAccountConfig
    )

    def setup() {
        driverAccountConfig.getAccountLockWaitMills() >> 5000
    }

    def "测试generateGlobalId-已有司机身份"() {
        given: "手机号对应的UID已有司机身份"
        def source = "Driver"
        def countryCode = "86"
        def phoneNumber = "***********"
        def encryptedPhoneNumber = "encryptedPhone"
        def uid = "testUid"
        def accountMapperPO = new AccountMapperPO(sourceId: "1001", source: source)

        when: "调用generateGlobalId方法"
        def result = service.generateGlobalId(source, countryCode, phoneNumber)

        then: "应返回已有的司机身份ID"
        1 * archCoreInfoService.encryptByType(_, phoneNumber) >> encryptedPhoneNumber
        1 * lockService.executeInLock(_, _, _) >> { args -> args[2].call() }
        1 * accountBaseInfoDao.queryByMobilePhone(countryCode, encryptedPhoneNumber) >> new AccountBaseInfoPO(uid: uid)
        1 * accountMapperDao.queryByUid(uid) >> [accountMapperPO]
        result == 1001L
    }

    def "测试generateGlobalId-已生成过GlobalId"() {
        given: "手机号对应的UID没有司机身份，但已生成过GlobalId"
        def source = "driver"
        def countryCode = "86"
        def phoneNumber = "***********"
        def encryptedPhoneNumber = "encryptedPhone"
        def uid = "testUid"
        def globalIdRecordPO = new GlobalIdRecordPO(id: 2002L)

        when: "调用generateGlobalId方法"
        def result = service.generateGlobalId(source, countryCode, phoneNumber)

        then: "应返回已生成的GlobalId"
        1 * archCoreInfoService.encryptByType(_, phoneNumber) >> encryptedPhoneNumber
        1 * lockService.executeInLock(_, _, _) >> { args -> args[2].call() }
        1 * accountBaseInfoDao.queryByMobilePhone(countryCode, encryptedPhoneNumber) >> new AccountBaseInfoPO(uid: uid)
        1 * accountMapperDao.queryByUid(uid) >> []
        1 * globalIdRecordDao.queryByPhone(countryCode, encryptedPhoneNumber) >> globalIdRecordPO
        result == 2002L
    }

    def "测试generateGlobalId-生成新GlobalId"() {
        given: "手机号对应的UID没有司机身份且未生成过GlobalId"
        def source = "driver"
        def countryCode = "86"
        def phoneNumber = "***********"
        def encryptedPhoneNumber = "encryptedPhone"
        def uid = "testUid"
        def newGlobalIdRecordPO = new GlobalIdRecordPO(id: 3003L)

        when: "调用generateGlobalId方法"
        def result = service.generateGlobalId(source, countryCode, phoneNumber)

        then: "应生成新的GlobalId"
        1 * archCoreInfoService.encryptByType(_, phoneNumber) >> encryptedPhoneNumber
        1 * lockService.executeInLock(_, _, _) >> { args -> args[2].call() }
        1 * accountBaseInfoDao.queryByMobilePhone(countryCode, encryptedPhoneNumber) >> new AccountBaseInfoPO(uid: uid)
        1 * accountMapperDao.queryByUid(uid) >> []
        1 * globalIdRecordDao.queryByPhone(countryCode, encryptedPhoneNumber) >> null
        1 * globalIdRecordDao.insert(_) >> { args ->
            def record = args[0] as GlobalIdRecordPO
            record.id = 3003L
            return 1
        }
        result == 3003L
    }

    def "测试generateGlobalId-无UID生成新GlobalId"() {
        given: "手机号没有对应的UID且未生成过GlobalId"
        def source = "driver"
        def countryCode = "86"
        def phoneNumber = "***********"
        def encryptedPhoneNumber = "encryptedPhone"
        def newGlobalIdRecordPO = new GlobalIdRecordPO(id: 4004L)

        when: "调用generateGlobalId方法"
        def result = service.generateGlobalId(source, countryCode, phoneNumber)

        then: "应生成新的GlobalId"
        1 * archCoreInfoService.encryptByType(_, phoneNumber) >> encryptedPhoneNumber
        1 * lockService.executeInLock(_, _, _) >> { args -> args[2].call() }
        1 * accountBaseInfoDao.queryByMobilePhone(countryCode, encryptedPhoneNumber) >> null
        1 * userCenterAccountGateway.queryAccountUidByPhone(countryCode, encryptedPhoneNumber) >> null
        1 * globalIdRecordDao.queryByPhone(countryCode, encryptedPhoneNumber) >> null
        1 * globalIdRecordDao.insert(_) >> { args ->
            def record = args[0] as GlobalIdRecordPO
            record.id = 4004L
            return 1
        }
        result == 4004L
    }
}

