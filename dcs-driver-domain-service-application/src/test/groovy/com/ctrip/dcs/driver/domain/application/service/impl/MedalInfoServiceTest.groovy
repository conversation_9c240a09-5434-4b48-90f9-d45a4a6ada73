package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.application.redis.HonourRedisLogic
import com.ctrip.dcs.driver.domain.application.service.HonourDBDataService
import com.ctrip.dcs.driver.domain.infrastructure.adapter.tourai.TourAIOneServiceProxy
import com.ctrip.dcs.driver.domain.infrastructure.geateway.DaasGateway
import com.ctrip.dcs.driver.domain.infrastructure.geateway.impl.DaasGatewayImpl
import spock.lang.Specification

class MedalInfoServiceTest extends Specification {


    HonourRedisLogic honourRedisLogic = Mock(HonourRedisLogic)
    HonourDBDataService honourDBDataService = Mock(HonourDBDataService)
    DaasGateway daasGateway = Mock(DaasGateway)
    TourAIOneServiceProxy tourAIOneServiceProxy = Mock(TourAIOneServiceProxy)
    MedalInfoServiceImpl medalInfoService

    def setup() {
        medalInfoService = new MedalInfoServiceImpl()
        medalInfoService.honourRedisLogic = honourRedisLogic
        medalInfoService.honourDBDataService = honourDBDataService
        medalInfoService.daasGateway = daasGateway
        medalInfoService.tourAIOneServiceProxy = tourAIOneServiceProxy
    }

    def "getGoldMedalDriverInfo"() {

        given:
        daasGateway.queryGoldMedalDriverInfo(_) >>>[null, new DaasGatewayImpl.GoldMedalDriverEntity(driverId: 1L, goodDriver: 1, totalGoodDriverCnt: 33) ]

        when:
        def res1 = medalInfoService.getGoldMedalDriverInfo(1L)
        def res2 = medalInfoService.getGoldMedalDriverInfo(1L)

        then:
        !res1.isLight()
        res1.getTotalCount() == 0
        res2.isLight()
        res2.getTotalCount() == 33
    }


}
