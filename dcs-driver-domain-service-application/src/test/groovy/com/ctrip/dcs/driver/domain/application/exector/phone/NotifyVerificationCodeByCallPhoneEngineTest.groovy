package com.ctrip.dcs.driver.domain.application.exector.phone

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable
import com.ctrip.dcs.driver.domain.application.covert.NotifyVerificationCodeByCallPhoneConvert
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory
import com.ctrip.dcs.driver.domain.application.service.NotifyVerificationCodeByCallPhoneService
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService
import com.ctrip.model.NotifyVerificationCodeByCallPhoneRequestType
import spock.lang.Specification

class NotifyVerificationCodeByCallPhoneEngineTest extends Specification {
    PhoneCheckTaskFactory phoneCheckTaskFactory = Mock(PhoneCheckTaskFactory)
    PhoneCheckService phoneCheckService = Mock(PhoneCheckService)
    PhoneCheckTaskTable phoneCheckTaskTable = Mock(PhoneCheckTaskTable)


    NotifyVerificationCodeByCallPhoneConvert convert = new NotifyVerificationCodeByCallPhoneConvert()
    NotifyVerificationCodeByCallPhoneService service = new NotifyVerificationCodeByCallPhoneService(phoneCheckTaskFactory: phoneCheckTaskFactory,
            phoneCheckTaskTable: phoneCheckTaskTable,
            phoneCheckService: phoneCheckService)

    NotifyVerificationCodeByCallPhoneEngine engine = new NotifyVerificationCodeByCallPhoneEngine(
            convert: convert,
            service: service
    )

    def "NotifyVerificationCodeByCallPhone"() {
        def request = new NotifyVerificationCodeByCallPhoneRequestType()
        given:
        phoneCheckTaskFactory.applyVerificationCodeTask(_, _, _) >> new PhoneCheckTaskEntity(id: 1, callHangupCode: "200")
        phoneCheckService.ivrCall(_) >> true
        when:
        def response = engine.execute(request)
        then:
        response.getResponseResult().getReturnCode() == "200"
    }
}