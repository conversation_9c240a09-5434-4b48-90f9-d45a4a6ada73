package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordRequestType
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import spock.lang.Specification

class QueryRightsRecordEngineTest extends Specification {
    QueryRightsRecordEngine queryRightsRecordEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsConfig rightsConfig = Mock(RightsConfig)
    LevelConfig levelConfig = Mock(LevelConfig)
    RightsRepoServiceImpl rightsRepoService = Mock(RightsRepoServiceImpl)


    def setup() {
        domainBeanFactory = new DomainBeanFactory(levelConfig: levelConfig, rightsConfig: rightsConfig, rightsRepoService: rightsRepoService)
        queryRightsRecordEngine = new QueryRightsRecordEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {
        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryRightsRecordResponseType.class).init()
        domainBeanFactory.rightsRepoService() >> rightsRepoService
        rightsRepoService.queryDriverRecords(1, "2023-06-01 00:00:00", "2023-06-29 14:40:20", Arrays.asList(1)) >> model
        rightsRepoService.queryDriverRecordsById(1l) >> model
        when:
        QueryRightsRecordResponseType result = queryRightsRecordEngine.execute(request)

        then:
        result != null
        where:
        request                                                                                                                                                      | model                                                                                                                                                                               || size
        new QueryRightsRecordRequestType(driverId: 1, useStartDate: "2023-06-01 00:00:00", useEndDate: "2023-06-29 14:40:20", rightsTypes: Arrays.asList(1),)        | [new RightsRecordModel(1l, 1l, 0, "rightsName", 0, "levelName", "userOrderId", "purchaseOrderId", "supplyOrderId", "punishOrderId", "money", LocalDateTimeUtils.firstDayOfMonth())] || 0
        new QueryRightsRecordRequestType(id: 1l) | [new RightsRecordModel(1l, 1l, 0, "rightsName", 0, "levelName", "userOrderId", "purchaseOrderId", "supplyOrderId", "punishOrderId", "money", LocalDateTimeUtils.firstDayOfMonth())] || 0
        new QueryRightsRecordRequestType(driverId: 1, useStartDate: "2023-06-01 00:00:00", useEndDate: "2023-06-29 14:40:20", rightsTypes: Arrays.asList(1), id: 1l) | null || 0
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme