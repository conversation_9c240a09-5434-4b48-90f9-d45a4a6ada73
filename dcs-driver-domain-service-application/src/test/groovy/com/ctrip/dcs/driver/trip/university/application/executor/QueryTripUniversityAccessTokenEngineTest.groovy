package com.ctrip.dcs.driver.trip.university.application.executor

import com.ctrip.dcs.driver.domain.trip.university.QueryTripUniversityAccessTokenRequestType
import com.ctrip.dcs.driver.domain.trip.university.QueryTripUniversityAccessTokenResponseType
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenDTO
import spock.lang.Specification
import spock.lang.Unroll

class QueryTripUniversityAccessTokenEngineTest extends Specification {
    def testObj = new QueryTripUniversityAccessTokenEngine()
    def tripUniversityService = Mock(TripUniversityService)

    def setup() {

        testObj.tripUniversityService = tripUniversityService
    }

    @Unroll
    def "executeTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityService.getAccessToken(_) >> new TripUniversityAccessTokenDTO(accessToken: "accessToken", tenantId: "tenantId")

        when:
        def result = testObj.execute(request)

        then: "验证返回结果里属性值是否符合预期"
        result.accessToken == expectedResult
        where: "表格方式验证多种分支调用场景"
        request                                                              || expectedResult
        new QueryTripUniversityAccessTokenRequestType(drvId: 1L, uid: "uid") || "accessToken"
    }
}
