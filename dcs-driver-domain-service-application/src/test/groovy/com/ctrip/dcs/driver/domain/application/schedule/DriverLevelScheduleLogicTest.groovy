package com.ctrip.dcs.driver.domain.application.schedule

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.exector.rights.UseRightsEngine
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic
import com.ctrip.dcs.driver.domain.application.service.DriverLevelService
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDAlDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DriverPointTotalInfoDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DriverPointTotalInfoPO
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.RightsReissueDto
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.DriverPointModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverLevelGreyConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.igt.framework.common.clogging.Logger
import com.google.common.collect.Maps
import org.junit.Before
import org.junit.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.powermock.api.mockito.PowerMockito
import qunar.tc.schedule.MockParameter
import qunar.tc.schedule.TaskHolder

import java.sql.Timestamp
import java.time.LocalDate
import java.time.LocalDateTime

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

class DriverLevelScheduleLogicTest {
    @Mock
    Logger LOGGER
    @Mock
    DrivLevelDAlDao drivLevelDAlDao
    @Mock
    RightsDBDataService rightsDBDataService;
    @Mock
    DrivRightsDao drivRightsDao
    @Mock
    RightsRedisLogic rightsRedisLogic
    @Mock
    DrivLevelDao drivLevelDao
    @Mock
    DriverPointTotalInfoDao driverPointTotalInfoDao
    @Mock
    SystemQConfig systemQConfig
    //Field levelConfig of type LevelConfig - was not mocked since Mockito doesn't mock a Final class when 'mock-maker-inline' option is not set
    @Mock
    DriverLevelGreyConfig driverLevelGreyConfig
    //Field rightsConfig of type RightsConfig - was not mocked since Mockito doesn't mock a Final class when 'mock-maker-inline' option is not set
    @Mock
    LevelConfig levelConfig
    @Mock
    DriverLevelService driverLevelService
    @Mock
    UseRightsEngine useRightsEngine;
    @Mock
    RightsConfig rightsConfig
    @Mock
    LockService lockService
    @Mock
    DomainBeanFactory domainBeanFactory
    @Mock
    GeoGateway geoGateway
    @InjectMocks
    DriverLevelScheduleLogic driverLevelScheduleLogic

    @Before
    void setUp() {
        MockitoAnnotations.initMocks(this)
        PowerMockito.mockStatic(LocalDateTime.class)
    }

    @Test
    void testUpdateDriverLevel() {
        Map<Long, LocalDate> cityMap = new HashMap<>();
        cityMap.put(1L,LocalDate.now())
        driverLevelGreyConfig.cityMap=cityMap
        when(driverLevelService.calcDriverLevel(1L, 1L, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal)).thenReturn(new FormLevelInfo(0l, 0, "levelName", "cityIdsStr", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0, 0, 0, [1l]))
        when(drivLevelDAlDao.batchInsertDuplicateUpdate(any(), any())).thenReturn([0] as int[])
        when(driverPointTotalInfoDao.countByCityId(1)).thenReturn(33l)
        when(driverPointTotalInfoDao.query(any())).thenReturn([new DriverPointTotalInfoPO(driverId:"1")])
        when(drivLevelDao.countByCityIdAndMonthIdx(anyLong(), anyString())).thenReturn(2l)
        when(systemQConfig.getString("is_custom")).thenReturn("1")
        when(systemQConfig.getString("driver_point_snapshot_date")).thenReturn("20230620")
        when(rightsRedisLogic.getDriverPointSnapshot([1l], LocalDateTimeUtils.todayStr())).thenReturn([new DriverPointModel(1l,"1",1l,1,1 as BigDecimal,1 as BigDecimal,2 as BigDecimal, 1 as BigDecimal,10,9,1 as BigDecimal,2 as BigDecimal, 0 as BigDecimal,10)])
        when(geoGateway.getLocalCurrentTime(anyLong())).thenReturn(LocalDateTime.now())
        driverLevelScheduleLogic.updateDriverLevel(new MockParameter())
    }


    @Test
    void testPointSnapshot() {
        Map<Long, LocalDate> cityMap = new HashMap<>();
        cityMap.put(1L,LocalDate.now())
        driverLevelGreyConfig.cityMap=cityMap
        Map<Long, List<FormLevelInfo>> cityLevelMap = new HashMap<>();
        cityLevelMap.put(1L,[new FormLevelInfo(0l, 0, "levelName", "cityIdsStr", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0, 0, 0, [1l]),new FormLevelInfo(1l, 4, "levelName", "1,2", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 1, 1, 0, [1l])])
        levelConfig.cityLevelMap=cityLevelMap
        when(driverLevelService.calcDriverLevel(1, 1, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal)).thenReturn(new FormLevelInfo(0l, 0, "levelName", "cityIdsStr", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0, 0, 0, [1l]))
        when(driverPointTotalInfoDao.queryByCityIdAndLevel(1l,4)).thenReturn([new DriverPointTotalInfoPO(id:1,drivLevel:4,cityId:"2",driverId:"1",orderInfoPoint:10 as BigDecimal,rewardInfoPoint:10 as BigDecimal),new DriverPointTotalInfoPO(id:2,drivLevel:4,cityId:"2",driverId:"1",orderInfoPoint:10 as BigDecimal,rewardInfoPoint:10 as BigDecimal),new DriverPointTotalInfoPO(id:1,drivLevel:4,cityId:"2",driverId:"1",orderInfoPoint:110 as BigDecimal,rewardInfoPoint:120 as BigDecimal)])
        Map<Long, LocalDateTime> localCurrentTimeMap = Maps.newHashMap();
        localCurrentTimeMap.put(2L, LocalDateTime.now())
        when(geoGateway.getLocalCurrentTime(anyList())).thenReturn(localCurrentTimeMap)
        when(geoGateway.getLocalCurrentTime(anyLong())).thenReturn(LocalDateTime.now())
        driverLevelScheduleLogic.pointSnapshot(new MockParameter())

        driverLevelScheduleLogic.pointSnapshot(new MockParameter("{\"citys\":\"1\"}"));

        driverLevelScheduleLogic.pointSnapshot(new MockParameter("{\"citys\":\"1,2\"}"));
    }

    @Test
    void testInitDriverLevel() {
        when(driverPointTotalInfoDao.count()).thenReturn(1)

        driverLevelScheduleLogic.initDriverLevel(null)
    }

    @Test
    void testRightsReissue() {
        when(drivLevelDao.queryDriverLevel(anyLong(), anyString())).thenReturn(new DrivLevelPO(cityId: 1l, drivLevel: 0))

        driverLevelScheduleLogic.rightsReissue(new MockParameter(""))
    }

    @Test
    void testRightsCancel() {
        when(drivLevelDao.queryDriverLevel(anyLong(), anyString())).thenReturn(new DrivLevelPO(cityId: 1l, drivLevel: 0))

        driverLevelScheduleLogic.cancellRights(new MockParameter(""))
    }

    @Test
    void testBuildRecords() {
        driverLevelScheduleLogic.buildRecords([new RightsReissueDto(1l,1,10,1)], TaskHolder.getKeeper().getLogger())
        when(drivLevelDao.queryDriverLevel(anyLong(), anyString())).thenReturn(new DrivLevelPO(cityId: 1l, drivLevel: 0))
        when(rightsConfig.getRightsInfo(anyLong(), anyInt(),anyInt())).thenReturn(new FormRightsInfo())
        driverLevelScheduleLogic.buildRecords([new RightsReissueDto(1l,1,10,1)], TaskHolder.getKeeper().getLogger())
    }

    @Test
    void testUseCityKingRights() {
        Map<Long, LocalDate> cityMap = new HashMap<>();
        cityMap.put(1L,LocalDate.now())
        driverLevelGreyConfig.cityMap=cityMap
        when(driverLevelService.calcDriverLevel(1, 1, 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal)).thenReturn(new FormLevelInfo(0l, 0, "levelName", "cityIdsStr", 0 as BigDecimal, 0 as BigDecimal, 0 as BigDecimal, 0, 0, 0, [1l]))
        when(drivLevelDAlDao.batchInsertDuplicateUpdate(any(), any())).thenReturn([0] as int[])
        when(drivRightsDao.countByRightsAndMonth(anyInt(),anyString())).thenReturn(33l)
        when(drivRightsDao.query(any())).thenReturn([new DrivRightsPO(drivId: "1")])
        when(drivLevelDao.countByCityIdAndMonthIdx(anyLong(), anyString())).thenReturn(2l)
        when(rightsRedisLogic.getDriverPointSnapshot([1l], LocalDateTimeUtils.todayStr())).thenReturn([new DriverPointModel(1l,"1",1l,1,1 as BigDecimal,1 as BigDecimal,1 as BigDecimal,2 as BigDecimal,10,9,1 as BigDecimal,2 as BigDecimal,2 as BigDecimal,10)])
        when(domainBeanFactory.rightsDBDataService()).thenReturn(rightsDBDataService)
        when(rightsDBDataService.queryDriverRights(49l, LocalDateTimeUtils.monthIndexStr())).thenReturn([new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 0, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "extend")])
        when(drivRightsDao.queryDriverRights(1l, LocalDateTimeUtils.monthIndexStr()) ).thenReturn([new DrivRightsPO(id: 1l, rightsConfigId: 1l, rightsName: "rightsName", rightsDesc: "rightsDesc", cityId: 1l, monthIdx: "monthIdx", drivId: 1l, drivLevel: 0, rightsType: 3, useLimit: 0, useCount: 0, rightsStatus: 0, rightsStratTime: new Timestamp(1686636458573L), rightsEndTime: new Timestamp(1686636458573L), extend: "extend", datachangeCreatetime: new Timestamp(1686636458573L))])
        try{
            driverLevelScheduleLogic.useCityKingRights(new MockParameter())
        }catch (Exception e){

        }


        when(drivRightsDao.query(any())).thenReturn(null)
        driverLevelScheduleLogic.useCityKingRights(new MockParameter())

    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme