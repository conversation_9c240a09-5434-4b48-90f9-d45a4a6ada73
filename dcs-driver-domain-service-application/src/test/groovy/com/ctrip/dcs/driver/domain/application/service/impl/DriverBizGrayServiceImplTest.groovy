package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.application.redis.DriverAppVerRedisLogic
import com.ctrip.dcs.driver.domain.application.service.TmsTransportProxyService
import com.ctrip.dcs.driver.domain.application.service.TourService
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.DriverGreyConfig
import com.ctrip.dcs.geo.domain.repository.CityRepository
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA
import com.ctrip.frt.product.soa.DriverBasicInfoType
import com.google.common.collect.ImmutableMap
import org.apache.commons.lang3.tuple.Pair
import spock.lang.Specification

class DriverBizGrayServiceImplTest extends Specification {
    def driverGreyConfig = Mock(DriverGreyConfig)
    def driverAppVerRedisLogic = Mock(DriverAppVerRedisLogic)
    def tourService = Mock(TourService)
    def tmsTransportProxyService = Mock(TmsTransportProxyService)
    def cityRepository = Mock(CityRepository)

    def driverBizGrayService = new DriverBizGrayServiceImpl(
            driverGreyConfig: driverGreyConfig,
            driverAppVerRedisLogic: driverAppVerRedisLogic,
            tourService: tourService,
            tmsTransportProxyService: tmsTransportProxyService,
            cityRepository: cityRepository
    )

    def "queryBizGraySwitch_paramsCheck"() {
        given:
        driverGreyConfig.getConfig() >> map

        when:
        def result = driverBizGrayService.queryBizGraySwitch(bizGrayKey, driverId, "", null, null, null, false)

        then:
        result == expectedResult
        where:
        expectedResult | bizGrayKey | driverId | map
        false          | ""         | 1L       | null
        false          | "voip"     | null     | null
        false          | "voip"     | 1L       | null
        false          | "voip"     | 1L       | ImmutableMap.of("im", new DriverGreyConfig.BizGreyConfigEntity())
    }

    def "queryBizGraySwitch_match"() {
        given:
        driverGreyConfig.getConfig() >> map
        driverAppVerRedisLogic.getAppRnVer(_) >> Pair.of("8400", "2024082910")
        tourService.getDriverInfo(_) >> new DriverBasicInfoType(serviceCityId: 2L)
        tmsTransportProxyService.queryDriverDetail(_) >> new QueryDrvDetailDTOSOA(cityId: 3L)
        cityRepository.findOne(_) >> City.builder().countryId(1).build()

        when:
        def result = driverBizGrayService.queryBizGraySwitch(bizGrayKey, driverId, productLine, cityId, appVer, rnVer, false)

        then:
        result == expectedResult
        where:
        expectedResult | bizGrayKey | driverId | productLine | cityId | appVer | rnVer        | map
        true           | "voip"     | 1L       | ""          | 2L     | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "1", appVer: "8400", rnVer: "2024082900"))
        true           | "voip"     | 1L       | "DAY"       | null   | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "11", cityIds: "2", appVer: "8400", rnVer: "2024082900"))
        false          | "voip"     | 1L       | "JNT"       | null   | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "11", cityIds: "2", appVer: "8400", rnVer: "2024082900"))
        false          | "voip"     | 1L       | "POINT"     | null   | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "11", cityIds: "2", appVer: "8400", rnVer: "2024082900"))
        true           | "voip"     | 1L       | "JNT"       | null   | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "11", cityIds: "2", countryIds: "1", appVer: "8400", rnVer: "2024082900"))
        true           | "voip"     | 1L       | "JNT"       | 2L     | "8400" | "2024082910" | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "11", cityIds: "2", countryIds: "1", appVer: "8400", rnVer: "2024082900"))
        true           | "voip"     | 1L       | "JNT"       | 2L     | "8400" | "2024082910" | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "", cityIds: "", countryIds: "", appVer: "8400", rnVer: "2024082900"))
        false          | "voip"     | 1L       | ""          | 2L     | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "11"))
        false          | "voip"     | 1L       | ""          | 2L     | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "32", cityIds: "33", countryIds: "", appVer: "8400", rnVer: "2024082900"))
        false          | "voip"     | 1L       | ""          | 2L     | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "32", cityIds: "", countryIds: "435", appVer: "8400", rnVer: "2024082900"))
        false          | "voip"     | 1L       | ""          | 2L     | ""     | ""           | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "", cityIds: "", countryIds: "435", appVer: "8400", rnVer: "2024082900"))

    }

    def "queryBizGraySwitch_driverVersionIsNull"() {
        given:
        driverGreyConfig.getConfig() >> map
        driverAppVerRedisLogic.getAppRnVer(_) >> null
        tourService.getDriverInfo(_) >> new DriverBasicInfoType(serviceCityId: 2L)
        tmsTransportProxyService.queryDriverDetail(_) >> new QueryDrvDetailDTOSOA(cityId: 3L)
        cityRepository.findOne(_) >> City.builder().countryId(1).build()

        when:
        def result = driverBizGrayService.queryBizGraySwitch(bizGrayKey, driverId, productLine, cityId, appVer, rnVer, false)

        then:
        result == expectedResult
        where:
        expectedResult | bizGrayKey | driverId | productLine | cityId | appVer | rnVer | map
        false          | "voip"     | 1L       | ""          | 2L     | ""     | ""    | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "1", appVer: "8400", rnVer: "2024082900"))
        false          | "voip"     | 1L       | ""          | 2L     | ""     | ""    | ImmutableMap.of("voip", new DriverGreyConfig.BizGreyConfigEntity(driverIds: "1", appVer: "", rnVer: "2024082900"))


    }

    def "queryBizGraySwitch_ignoreVersion"() {
        given:
        driverAppVerRedisLogic.getAppRnVer(_) >> null
        tourService.getDriverInfo(_) >> new DriverBasicInfoType(serviceCityId: 2L)
        tmsTransportProxyService.queryDriverDetail(_) >> new QueryDrvDetailDTOSOA(cityId: 3L)
        cityRepository.findOne(_) >> City.builder().countryId(1).build()

        when:
        def result = driverBizGrayService.queryBizGraySwitch("safe_point", 123L, null, 1L, "aaa", "bbb", true)

        then:
        result
        3 * driverGreyConfig.getConfig() >> ImmutableMap.of("safe_point", new DriverGreyConfig.BizGreyConfigEntity(cityIds: "1,2,3", appVer: "8400", rnVer: "2024082900"))
    }

}
