package com.ctrip.dcs.driver.account.application.mq

import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountChangeLogDao
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import spock.lang.*
import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountChangeLogPO
import qunar.tc.qmq.Message


/*
作者：pl.yang
创建时间：2025/4/3-下午3:06-2025
*/

class DriverAccountChangeLogListenerTest extends Specification {
    @Subject
    DriverAccountChangeLogListener listener = new DriverAccountChangeLogListener()

    // Mock dependencies
    def accountChangeLogDao = Mock(AccountChangeLogDao)
    def accountBaseInfoDao = Mock(AccountBaseInfoDao)
    def userCenterAccountGateway = Mock(UserCenterAccountGateway)

    def setup() {
        listener.accountChangeLogDao = accountChangeLogDao
        listener.accountBaseInfoDao = accountBaseInfoDao
        listener.userCenterAccountGateway = userCenterAccountGateway
    }

    // 测试1: 正常流程 - 用户中心返回有效UDL
    def "当收到有效消息且用户中心返回UDL时，应正确插入日志"() {
        given: "准备有效消息和用户中心响应"
        def message = Mock(Message)
        def content = '{"uid":"123","type":"UPDATE_ACCOUNT","before":"old","after":"new","operator":"admin"}'
        def accountInfo = new AccountInfoResponseType(udl: "test_udl")

        when: "处理消息"
        message.getStringProperty("content") >> content
        listener.onMessage(message)

        then: "验证依赖调用和插入数据"
        1 * userCenterAccountGateway.getAccountByUid("123") >> accountInfo
        0 * accountBaseInfoDao.queryByUID(_)
        1 * accountChangeLogDao.insert(_) >> { args ->
            AccountChangeLogPO po = args[0]
            assert po.uid == "123"
            assert po.type == "UPDATE_ACCOUNT"
            assert po.changeBefore == "old"
            assert po.changeAfter == "new"
            assert po.operator == "admin"
            assert po.providerDataLocation == "test_udl"
            return 1 // 添加返回值
        }
    }

}

