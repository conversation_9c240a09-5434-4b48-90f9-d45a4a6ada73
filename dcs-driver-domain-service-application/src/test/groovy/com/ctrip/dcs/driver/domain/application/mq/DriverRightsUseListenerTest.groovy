package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.exector.rights.UseRightsEngine
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService
import com.ctrip.dcs.driver.domain.application.service.impl.RightsDBDataServiceImpl
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.sql.Timestamp

import static org.mockito.Mockito.when

class DriverRightsUseListenerTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    UseRightsEngine useRightsEngine
    @Mock
    DrivRightsDao drivRightsDao =Mock(DrivRightsDao)
    @Mock
    DrivLevelDao drivLevelDao =Mock(DrivLevelDao)
    @Mock
    DrivRightsRecordDao drivRightsRecordDao =Mock(DrivRightsRecordDao)
    RightsRepoServiceImpl repoService = Mock(RightsRepoServiceImpl)
    @Mock
    DomainBeanFactory domainBeanFactory
    @Mock
    RightsDBDataService rightsDBDataService
    @InjectMocks
    DriverRightsUseListener driverRightsUseListener

    def setup() {
        MockitoAnnotations.initMocks(this)

        rightsDBDataService = new RightsDBDataServiceImpl(drivLevelDao: drivLevelDao, drivRightsDao: drivRightsDao,drivRightsRecordDao: drivRightsRecordDao)
        domainBeanFactory = new DomainBeanFactory(rightsDBDataService: rightsDBDataService,rightsRepoService: repoService)
        driverRightsUseListener = new DriverRightsUseListener(domainBeanFactory: domainBeanFactory)
        when(drivRightsRecordDao.queryDriverRecordsBySupplyOrderId(1l, 6,LocalDateTimeUtils.monthIndexStr())).thenReturn([])
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.setProperty("content",data);
        given:
        repoService.queryDriverLevel(1l, LocalDateTimeUtils.monthIndexStr()) >>new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0)
        drivRightsDao.queryDriverRights(1l, LocalDateTimeUtils.monthIndexStr()) >>[new DrivRightsPO(id: 1l, rightsConfigId: 1l, rightsName: "rightsName", rightsDesc: "rightsDesc", cityId: 1l, monthIdx: "monthIdx", drivId: 1l, drivLevel: 0, rightsType: 6, useLimit: 0, useCount: 0, rightsStatus: 0, rightsStratTime: new Timestamp(1686636458573L), rightsEndTime: new Timestamp(1686636458573L), extend: "extend", datachangeCreatetime: new Timestamp(1686636458573L))]

        when:
        try{
            driverRightsUseListener.onMessage(message)
        }catch(Exception e){

        }


        then:
        true

        where:
        data || _
        "" ||_
        "{\"driverId\":1,\"rightsType\":6,\"userOrderId\":\"365992299926\",\"purchaseOrderId\":null,\"supplyOrderId\":\"881834338813162\",\"punishOrderId\":null,\"money\":9}" || _
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme