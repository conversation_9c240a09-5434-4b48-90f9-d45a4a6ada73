package com.ctrip.dcs.driver.domain.application.exector.account

import com.ctrip.dcs.driver.account.application.exector.QueryAccountByIdCardEngine
import com.ctrip.dcs.driver.account.domain.service.AccountService
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO
import com.ctrip.dcs.driver.domain.account.QueryAccountByIdCardRequestType
import com.google.common.collect.Lists
import spock.lang.Specification

class QueryAccountByIdCardEngineTest extends Specification {

    AccountService accountService = Mock(AccountService)

    QueryAccountByIdCardEngine queryAccountByIdCardEngine;

    def setup() {
        queryAccountByIdCardEngine = new QueryAccountByIdCardEngine()
        queryAccountByIdCardEngine.accountService = accountService
    }

    def "execute"() {
        given:
        accountService.getAccountInfoByIdCard(_) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountInfoDTO(countryCode: "86", phoneNumber: "131xxxx1234", identityDTOList: Lists.newArrayList()))]

        when:
        def res1 = queryAccountByIdCardEngine.execute(new QueryAccountByIdCardRequestType())
        def res2 = queryAccountByIdCardEngine.execute(new QueryAccountByIdCardRequestType())

        then:
        res1.getAccountDetailList().size() == 0
        res2.getAccountDetailList().size() == 1
        res2.getAccountDetailList().get(0).getCountryCode() == "86"
        res2.getAccountDetailList().get(0).getPhoneNumber() == "131xxxx1234"
    }
}
