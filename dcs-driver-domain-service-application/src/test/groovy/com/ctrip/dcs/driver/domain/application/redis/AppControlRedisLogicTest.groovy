package com.ctrip.dcs.driver.domain.application.redis

import com.google.common.collect.ImmutableMap
import org.assertj.core.util.Lists
import spock.lang.Specification

class AppControlRedisLogicTest extends Specification {
    def directorRedis = Mock(DirectorRedis.class)
    def appControlRedisLogic = new AppControlRedisLogic(
            directorRedis: directorRedis
    )

    def testGetGuidance() {
        given:
        directorRedis.hgetAll(_) >> cacheMap

        when:
        def result = appControlRedisLogic.getGuidance("123")

        then:
        size == result.size()
        where:
        size | cacheMap
        0    | null
        1    | ImmutableMap.of("HOME_SCORE", "true")
    }

    def testCacheGuidance() {
        given:
        directorRedis.hmset(_, _, _) >> true

        when:
        def result = appControlRedisLogic.cacheGuidance("123", Lists.newArrayList("HOME_SCORE"))

        then:
        result
    }
}
