package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.arch.distlock.DLock
import com.ctrip.arch.distlock.DistributedLockService
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService
import com.ctrip.dcs.driver.domain.application.service.RightsRepoService
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

import java.util.concurrent.TimeUnit

class DriverRightsVacationListenerTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    DomainBeanFactory domainBeanFactory
    @Mock
    RightsDBDataService rightsDBDataService =Mock(RightsDBDataService)
    @Mock
    DistributedLockService distributedLockService =Mock(DistributedLockService)
    RightsRepoService rightsRepoService =Mock(RightsRepoService)
    @Mock
    DrivRightsRecordDao drivRightsRecordDao=Mock(DrivRightsRecordDao)
    @Mock
    DrivRightsDao drivRightsDao =Mock(DrivRightsDao)
    @Mock
    LockService lockService
    @Mock
    DriverMessageServiceProxy driverMessageServiceProxy
    @InjectMocks
    DriverRightsVacationListener driverRightsVacationListener

    def setup() {
        lockService =new LockService(distributedLockService: distributedLockService)
        domainBeanFactory = new DomainBeanFactory(rightsDBDataService:rightsDBDataService,rightsRepoService:rightsRepoService)
        driverRightsVacationListener = new DriverRightsVacationListener(domainBeanFactory: domainBeanFactory,lockService:lockService,drivRightsRecordDao:drivRightsRecordDao,drivRightsDao:drivRightsDao,driverMessageServiceProxy:driverMessageServiceProxy)
        DLock dLock = Mock(DLock)
        distributedLockService.getLock("dcs_driver_use_rights:3452182_7")>> dLock
        dLock.tryLock(1000L, TimeUnit.MILLISECONDS)>>true
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.setProperty("content",data);
        given:
        rightsDBDataService.queryDriverRights(3452182l, LocalDateTimeUtils.monthIndexStr()) >> rightsList
        rightsRepoService.queryDriverLevel(3452182l, LocalDateTimeUtils.monthIndexStr()) >> level

        when:
        driverRightsVacationListener.onMessage(message)

        then:
        message !=null

        where :
        data || rightsList || level
        "[{\"driverId\":3452182,\"rightsType\":7,\"userOrderId\":\"36836426382\",\"supplyOrderId\":\"32588681957179421\",\"sysExpectBookTime\":\"2023-09-2809: 09: 45\"},{\"driverId\":3452182,\"rightsType\":7,\"userOrderId\":\"36843310497\",\"supplyOrderId\":\"32588936017379365\",\"sysExpectBookTime\":\"2023-09-2721: 20: 00\"}]"||[new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 0, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "{\"vacationLimit\":2,\"vacationUse\":0}")] ||new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0)
        "[{\"driverId\":3452182,\"rightsType\":7,\"userOrderId\":\"36836426382\",\"supplyOrderId\":\"32588681957179421\",\"sysExpectBookTime\":\"2023-09-2809: 09: 45\"},{\"driverId\":3452182,\"rightsType\":7,\"userOrderId\":\"36843310497\",\"supplyOrderId\":\"32588936017379365\",\"sysExpectBookTime\":\"2023-09-2721: 20: 00\"}]"||[new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 7, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "{\"vacationLimit\":2,\"vacationUse\":0}")] ||new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0)
        "[{\"driverId\":3452182,\"rightsType\":7,\"userOrderId\":\"36836426382\",\"supplyOrderId\":\"32588681957179421\",\"sysExpectBookTime\":\"2023-09-2809: 09: 45\"},{\"driverId\":3452182,\"rightsType\":7,\"userOrderId\":\"36843310497\",\"supplyOrderId\":\"32588936017379365\",\"sysExpectBookTime\":\"2023-09-2721: 20: 00\"}]"||[new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 7, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "{\"vacationLimit\":2,\"vacationUse\":0}")] ||null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme