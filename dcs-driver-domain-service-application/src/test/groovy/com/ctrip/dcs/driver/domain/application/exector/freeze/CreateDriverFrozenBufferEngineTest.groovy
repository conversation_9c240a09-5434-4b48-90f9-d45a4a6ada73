package com.ctrip.dcs.driver.domain.application.exector.freeze

import com.ctrip.dcs.driver.domain.infrastructure.adapter.db.DriverFreezeAdapter
import com.ctrip.model.CreateDriverFrozenBufferRequestType
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class CreateDriverFrozenBufferEngineTest extends Specification {

    @Mock
    DriverFreezeAdapter driverFreezeAdapter
    @InjectMocks
    CreateDriverFrozenBufferEngine driverFrozenBufferEngine

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        Long driverId = 1000004L
        def request = new CreateDriverFrozenBufferRequestType(driverId: driverId, bufferStartTime: "2025-03-26 17:18:26", bufferEndTime: "2025-03-26 17:18:26")
        given:

        when:
        def response = driverFrozenBufferEngine.execute(request)

        then:
        with(response) {
            responseStatus != null
        }
    }
}

//Generated with love by <PERSON>Me :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme