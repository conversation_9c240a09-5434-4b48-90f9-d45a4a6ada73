package com.ctrip.dcs.driver.domain.application.redis

import spock.lang.Specification

class DriverAppVerRedisLogicTest extends Specification {
    def directorRedis = Mock(DirectorRedis)

    def logic = new DriverAppVerRedisLogic(
            directorRedis: directorRedis
    )

    def testGetAppRnVer() {
        given:
        directorRedis.get("version_new:1") >> "8400#2024082900"
        directorRedis.get("version_new:2") >> "8400.2024082900"
        directorRedis.get("version_new:3") >> ""
        when:
        def result = logic.getAppRnVer(driverId)

        then:
        appVer == result.getLeft()
        rnVer == result.getRight()
        where:
        driverId | appVer | rnVer
        null     | ""     | ""
        1L       | "8400" | "2024082900"
        2L       | ""     | ""
        3L       | ""     | ""
    }
}
