package com.ctrip.dcs.driver.account.application.mq

import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable
import qunar.tc.qmq.base.BaseMessage
import spock.lang.Specification

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/22 09:59
 * @Description:
 */
class PhoneCheckCallEventListenerTest extends Specification {

  def callEventListener = new PhoneCheckCallEventListener()

  def config = Mock(PhoneCheckConfig)
  def taskTable = Mock(PhoneCheckTaskTable)
  def checkTable = Mock(PhoneCheckTable)

   def setup() {
     callEventListener.config = config
     callEventListener.taskTable = taskTable
     callEventListener.checkTable = checkTable
   }
    def "OnMessage"() {
     def msg = new BaseMessage()
     msg.setProperty("OutCallResultData", "{\"batchId\":\"23\",\"originalID\":\"G280327\",\"dialNumber\":\"*************\",\"dnisType\":\"PSTN\",\"aNI\":\"********\",\"dNIS\":\"*************\",\"callResult\":\"COMPLETED\",\"callID\":\"500974586855782511883\",\"returnCode\":\"200\",\"hangupCode\":\"500\",\"timeToAlert\":23,\"timeToAnswer\":0,\"releaseNo\":\"*************\"}")

     PhoneCheckTaskEntity task = new PhoneCheckTaskEntity()
     task.setId(23)
     task.setTaskState(100)

     given:
     config.getSkillGroupId() >> "G280322"
     config.getVoiceSkillGroupId() >> "G280327"
     taskTable.queryByPk(_) >> task

     when:
     callEventListener.onMessage(msg)
     then:
     assert (true)

     task.setTaskState(100)
     def msg1 = new BaseMessage()
     msg1.setProperty("OutCallResultData", "{\"batchId\":\"23\",\"originalID\":\"G280327\",\"dialNumber\":\"*************\",\"dnisType\":\"PSTN\",\"aNI\":\"********\",\"dNIS\":\"*************\",\"callResult\":\"UNCOMPLETED\",\"callID\":\"500974586855782511883\",\"returnCode\":\"200\",\"hangupCode\":\"500\",\"maxRetryCount\":1,\"executeCount\":1,\"releaseNo\":\"*************\"}")
     when:
     callEventListener.onMessage(msg1)
     then:
     assert (true)
    }
}
