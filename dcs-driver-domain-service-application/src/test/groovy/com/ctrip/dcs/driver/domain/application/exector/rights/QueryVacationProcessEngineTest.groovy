package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig
import com.ctrip.dcs.driver.domain.rights.QueryVacationProcessRequestType
import com.ctrip.dcs.driver.domain.rights.QueryVacationProcessResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class QueryVacationProcessEngineTest extends Specification {
    @Mock
    SystemQConfig systemQConfig
    @InjectMocks
    QueryVacationProcessEngine queryVacationProcessEngine

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        def request = new QueryVacationProcessRequestType(cityId: 1,driverId: 2)

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryVacationProcessResponseType.class).init()
        given:
        when(systemQConfig.getString(anyString())).thenReturn(config)

        when:
        queryVacationProcessEngine.validate(new AbstractValidator<>(QueryVacationProcessRequestType.class))
        QueryVacationProcessResponseType result = queryVacationProcessEngine.execute(request)

        then:
        result != null
        where:
        config | _
        "getStringResponse" | _
        "7" |_
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme