package com.ctrip.dcs.driver.domain.application.domain.exector

import com.ctrip.dcs.driver.domain.application.domain.CommonResult
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.value.rights.DriverRightsObject
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class CheckRightsCanUseExecutorTest extends Specification {
    @Mock
    DriverRightsObject owner
    @Mock
    DomainBeanFactory domainBeanFactory
    @InjectMocks
    CheckRightsCanUseExecutor checkRightsCanUseExecutor

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test do Work"() {
        when:
        def result = checkRightsCanUseExecutor.doWork(rightsModel)

        then:
        result == code
        where:
        rightsModel                                                                                                                                                                                               || code

        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 2, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "{\"weekUseCount\":1,\"useTime\":\"2023-06-13 18:07:37\"}") || true
        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 1, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "{\"welfareLimit\":50,\"welfareUse\":1001}")                || false
        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 1, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "")                || false
        new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 3, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "")                || true
    }

    def "test convert"() {
        when:
        CommonResult result = checkRightsCanUseExecutor.convert(null)

        then:
        result != null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme