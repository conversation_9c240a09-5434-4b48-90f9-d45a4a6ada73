package com.ctrip.dcs.driver.domain.application.exector.account

import com.ctrip.dcs.driver.value.account.AccountUidInfo
import com.ctrip.dcs.driver.domain.account.QueryAccountRequestType
import com.ctrip.dcs.driver.domain.account.QueryAccountResponseType
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class QueryAccountEngineTest extends Specification {
    @Mock
    UserCenterAccountGateway accounService
    @Mock
    AccountMapperDao accountMapperDao
    @Mock
    DirectorRedis directorRedis
    @InjectMocks
    QueryAccountEngine queryAccountEngine

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test execute"() {
        given:
        when(accounService.queryAccountUid(any())).thenReturn(Mock(AccountUidInfo))
        when(accountMapperDao.queryByUid(anyString())).thenReturn([new AccountMapperPO(source: "Guide", sourceId: "1"),new AccountMapperPO(source: "Driver", sourceId: "1")])
        when(directorRedis.hset(anyString(), anyString(), anyString(), anyLong())).thenReturn(Boolean.TRUE)

        when:
        QueryAccountResponseType result = queryAccountEngine.execute(Mock(QueryAccountRequestType))

        then:
        result.responseResult.returnCode == "200"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
