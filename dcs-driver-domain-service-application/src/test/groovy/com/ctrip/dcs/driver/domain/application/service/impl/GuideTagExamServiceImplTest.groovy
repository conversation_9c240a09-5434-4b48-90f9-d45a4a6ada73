package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.redis.ExamRedisLogic
import com.ctrip.dcs.driver.domain.application.service.GuideApplyExamDBDataService
import com.ctrip.dcs.driver.domain.application.service.GuideExamScoreDBDataService
import com.ctrip.dcs.driver.domain.exam.GuideExamScoreDTO
import com.ctrip.dcs.driver.domain.exam.GuideTagExamDTO
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideApplyExamDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideExamScoreDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideExamScoreModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel
import org.mockito.InjectMocks
import org.mockito.Mock
import spock.lang.Specification

import java.sql.Timestamp

import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.when

class GuideTagExamServiceImplTest extends Specification {
    @Mock
    GuideApplyExamDao guideApplyExamDao = Mock(GuideApplyExamDao);
    @Mock
    GuideExamScoreDao guideExamScoreDao = Mock(GuideExamScoreDao);
    @Mock
    ExamRedisLogic examRedisLogic = Mock(ExamRedisLogic);
    @Mock
    GuideApplyExamDBDataService guideApplyExamDBDataService = Mock(GuideApplyExamDBDataService);
    @Mock
    GuideExamScoreDBDataService guideExamScoreDBDataService = Mock(GuideExamScoreDBDataService);
    @Mock
    DomainBeanFactory domainBeanFactory
    @InjectMocks
    GuideTagExamServiceImpl guideTagExamServiceImpl

    def setup() {
        domainBeanFactory = new DomainBeanFactory(guideApplyExamDBDataService: guideApplyExamDBDataService,guideExamScoreDBDataService:guideExamScoreDBDataService,examRedisLogic: examRedisLogic)
        guideApplyExamDBDataService = new GuideApplyExamDBDataServiceImpl(guideApplyExamDao: guideApplyExamDao)
        guideExamScoreDBDataService = new GuideExamScoreDBDataServiceImpl(guideExamScoreDao: guideExamScoreDao)
        guideTagExamServiceImpl = new GuideTagExamServiceImpl(domainBeanFactory:domainBeanFactory);
    }

    def "test query guide tag Exam by cache"() {
        given:
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        examRedisLogic.getGuideTagExam(1947L) >> getGuideTagExamDTO()

        when:
        GuideTagExamDTO result = guideTagExamServiceImpl.queryAndSaveGuideTagExamDTO(1947L)

        then:
        result != null
    }

    def "test query guide tag Exam by db nodata"() {
        given:
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        examRedisLogic.getGuideTagExam(1947L) >> null
        domainBeanFactory.guideApplyExamDBDataService().queryPassedGuideApplyExamByAccount("guide0001947") >> null

        when:
        GuideTagExamDTO result = guideTagExamServiceImpl.queryAndSaveGuideTagExamDTO(1947L)

        then:
        result != null
    }

    def "test query guide tag Exam by db"() {
        given:
        domainBeanFactory.examRedisLogic() >> examRedisLogic
        examRedisLogic.getGuideTagExam(1947L) >> null
        domainBeanFactory.guideApplyExamDBDataService().queryPassedGuideApplyExamByAccount("guide0001947") >> getGuideApplyExamModel1()
        domainBeanFactory.guideExamScoreDBDataService().queryPassedGuideExamScoreInfoMapByAccount("guide0001947") >>getGuideScoreMap()
        when:
        GuideTagExamDTO result = guideTagExamServiceImpl.queryAndSaveGuideTagExamDTO(1947L)

        then:
        result != null
    }

    private Map<String,List<GuideExamScoreModel>> getGuideScoreMap(){
        List<GuideExamScoreModel> list = new ArrayList<>();
        GuideExamScoreModel guideExamScoreModel1 = new GuideExamScoreModel();
        guideExamScoreModel1.setApplySubject("DDXDCJ2024");
        guideExamScoreModel1.setCompleteTime("2023-11-30 00:00:00");
        guideExamScoreModel1.setExamAccountId("guide0001947");
        guideExamScoreModel1.setExamIsPassed(1);
        guideExamScoreModel1.setExamScore(new BigDecimal("90"));
        guideExamScoreModel1.setTimeZone(new BigDecimal("9"));

        GuideExamScoreModel guideExamScoreModel = new GuideExamScoreModel();
        guideExamScoreModel.setApplySubject("DDXDCJ2024");
        guideExamScoreModel.setCompleteTime("2023-11-30 00:00:00");
        guideExamScoreModel.setExamAccountId("guide0001947");
        guideExamScoreModel.setExamIsPassed(1);
        guideExamScoreModel.setExamScore(new BigDecimal("100"));
        guideExamScoreModel.setTimeZone(new BigDecimal("9"));
        list.add(guideExamScoreModel);
        list.add(guideExamScoreModel1);
        Map<String,List<GuideExamScoreModel>> map = new HashMap<>();
        map.put("DDXDCJ2024",list);
        return map;
    }

    private List<GuideApplyExamModel> getGuideApplyExamModel1() {
        List<GuideApplyExamModel> result = new ArrayList<>();
        GuideApplyExamModel model = new GuideApplyExamModel();
        model.setExamAccountId("guide0001947");
        model.setGuideId(1947L);
        model.setAccount("***********");
        model.setSubjectName("初级笔试");
        model.setApplyResult(1);
        model.setExamIsPassed(1);
        model.setCallExamSuccess(1);
        model.setGuideName("测试");
        model.setApplySubject("DDXDCJ2024");
        result.add(model);
        return result;
    }

    private GuideTagExamDTO getGuideTagExamDTO(){
        GuideTagExamDTO guideTagExamDTO = new GuideTagExamDTO();
        guideTagExamDTO.setExamIsPass(false);
        return guideTagExamDTO;
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
