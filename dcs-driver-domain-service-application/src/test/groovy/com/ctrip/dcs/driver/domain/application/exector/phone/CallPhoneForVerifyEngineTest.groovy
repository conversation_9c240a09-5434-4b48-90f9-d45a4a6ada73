package com.ctrip.dcs.driver.domain.application.exector.phone

import com.ctrip.dcs.driver.account.application.schedule.PhoneCheckTaskProcessScheduler
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable
import com.ctrip.dcs.driver.domain.application.covert.CallPhoneForVerifyConvert
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory
import com.ctrip.dcs.driver.domain.application.service.CallPhoneForVerifyService
import com.ctrip.model.CallPhoneForVerifyRequestType
import spock.lang.Specification

class CallPhoneForVerifyEngineTest extends Specification {
    PhoneCheckTaskFactory phoneCheckTaskFactory = Mock(PhoneCheckTaskFactory)
    PhoneCheckTaskTable phoneCheckTaskTable = Mock(PhoneCheckTaskTable)
    PhoneCheckTaskProcessScheduler scheduler = Mock(PhoneCheckTaskProcessScheduler)

    CallPhoneForVerifyConvert convert = new CallPhoneForVerifyConvert()
    CallPhoneForVerifyService service = new CallPhoneForVerifyService(phoneCheckTaskFactory: phoneCheckTaskFactory,
            phoneCheckTaskTable: phoneCheckTaskTable,
            scheduler: scheduler)

    CallPhoneForVerifyEngine engine = new CallPhoneForVerifyEngine(
            convert: convert,
            service: service
    )

    def "CallPhoneForVerify"() {
        def request = new CallPhoneForVerifyRequestType()
        given:
        phoneCheckTaskFactory.applyPhoneCallTask(_, _) >> new PhoneCheckTaskEntity(id: 1, callHangupCode: "200")
        when:
        def response = engine.execute(request)
        then:
        response.getCallTaskId() == 1
    }
}