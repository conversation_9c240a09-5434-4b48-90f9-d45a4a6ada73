package com.ctrip.dcs.driver.domain.application.redis

import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class RightsRedisLogicTest extends Specification {
    //Field gson of type Gson - was not mocked since <PERSON><PERSON><PERSON> doesn't mock a Final class when 'mock-maker-inline' option is not set
    @Mock
    DirectorRedis directorRedis
    @InjectMocks
    RightsRedisLogic rightsRedisLogic

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test get Driv Rights"() {
        given:
        when(directorRedis.get(anyString())).thenReturn(str)

        when:
        List<RightsModel> result = rightsRedisLogic.getDrivRights(0l, "monthIdx")

        then:
        result.size() == size
        where:
        str || size
        ""||0
        "[{\"id\":433,\"rightsConfigId\":1,\"rightsName\":\"福利金名称\",\"rightsDesc\":\"福利金简介\",\"cityId\":2,\"monthIdx\":\"2023-06\",\"drivId\":117,\"drivLevel\":0,\"rightsType\":1,\"useLimit\":99,\"useCount\":0,\"rightsStatus\":0,\"rightsStratTime\":\"2023-06-01 00:00:00\",\"rightsEndTime\":\"2023-06-30 23:59:59\",\"rightsIssueTime\":\"2023-06-13 11:44:20\",\"extend\":\"{\\\"welfareLimit\\\":50,\\\"welfareUse\\\":0}\"},{\"id\":434,\"rightsConfigId\":2,\"rightsName\":\"城市王者名称\",\"rightsDesc\":\"城市王者简介\",\"cityId\":2,\"monthIdx\":\"2023-06\",\"drivId\":117,\"drivLevel\":0,\"rightsType\":3,\"useLimit\":0,\"useCount\":0,\"rightsStatus\":0,\"rightsStratTime\":\"2023-06-01 00:00:00\",\"rightsEndTime\":\"2023-06-30 23:59:59\",\"rightsIssueTime\":\"2023-06-13 11:44:20\",\"extend\":\"\"},{\"id\":435,\"rightsConfigId\":4,\"rightsName\":\"改派权益\",\"rightsDesc\":\"改派权益简介\",\"cityId\":2,\"monthIdx\":\"2023-06\",\"drivId\":117,\"drivLevel\":0,\"rightsType\":2,\"useLimit\":6,\"useCount\":0,\"rightsStatus\":0,\"rightsStratTime\":\"2023-06-01 00:00:00\",\"rightsEndTime\":\"2023-06-30 23:59:59\",\"rightsIssueTime\":\"2023-06-13 11:44:20\",\"extend\":\"\"}]"||3

    }

    def "test save Driv Rights"() {
        given:
        when(directorRedis.set(anyString(), any(), anyLong())).thenReturn(Boolean.TRUE)

        when:
        def res = rightsRedisLogic.saveDrivRights([new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 0, 0, 0, 0, "rightsStratTime", "rightsEndTime", "rightsIssueTime", "extend")], 0l, "monthIdx")

        then:
        res == null
    }

    def "test del Driv Rights"() {
        given:
        when(directorRedis.remove(anyString())).thenReturn(Boolean.TRUE)

        when:
        def res = rightsRedisLogic.delDrivRights(0l, "monthIdx")

        then:
        res == null
    }

    def "test get Driv Level"() {
        given:
        when(directorRedis.get(anyString(), any())).thenReturn(new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0))

        when:
        LevelModel result = rightsRedisLogic.getDrivLevel(0l, "monthIdx")

        then:
        result != null
    }

    def "test save Driv Level"() {
        given:
        when(directorRedis.set(anyString(), any(), anyLong())).thenReturn(Boolean.TRUE)

        when:
        def res = rightsRedisLogic.saveDrivLevel(new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0), 0l, "monthIdx")

        then:
        res == null
    }

    def "test del Driv Level"() {
        given:
        when(directorRedis.remove(anyString())).thenReturn(Boolean.TRUE)

        when:
        def res = rightsRedisLogic.delDrivLevel(0l, "monthIdx")

        then:
        res == null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme