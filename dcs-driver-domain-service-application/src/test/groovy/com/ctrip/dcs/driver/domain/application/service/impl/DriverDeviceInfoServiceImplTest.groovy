package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverDeviceInfoDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverDeviceInfoPO
import com.ctrip.dcs.driver.domain.infrastructure.model.device.DriverDeviceInfoModel
import spock.lang.Specification

import java.time.LocalDateTime

class DriverDeviceInfoServiceImplTest extends Specification {
    def driverDeviceInfoDao = Mock(DriverDeviceInfoDao)

    def driverDeviceInfoService = new DriverDeviceInfoServiceImpl(
            driverDeviceInfoDao: driverDeviceInfoDao,
    )

    def "save_insert"() {
        DriverDeviceInfoModel deviceInfo = new DriverDeviceInfoModel(
                driverId: 2L,
                cid: "11",
                appId: "5206",
                appVer: "8400",
                rnVer: "**********",
                local: "zh-CN",
                os: "ios",
                osVer: "15.6",
                loginTime: LocalDateTime.now(),
                loginAccount: "***********",
                loginType: 1,
                activeTime: LocalDateTime.now().plusSeconds(-100)
        )
        given:
        driverDeviceInfoDao.findOne(_) >> null

        when:
        driverDeviceInfoService.save(deviceInfo)

        then:
        1 * driverDeviceInfoDao.insert(_)
    }

    def "save_update"() {
        DriverDeviceInfoModel deviceInfo = new DriverDeviceInfoModel(
                driverId: 2L,
                cid: "11",
                appId: "5206",
                appVer: "8400",
                rnVer: "**********",
                local: "zh-CN",
                os: "ios",
                osVer: "15.6",
                loginTime: LocalDateTime.now(),
                loginAccount: "***********",
                loginType: 1,
                activeTime: LocalDateTime.now()

        )
        given:
        driverDeviceInfoDao.findOne(_) >> new DriverDeviceInfoPO(id: 1001, activeTime: LocalDateTime.now().plusMinutes(-7))

        when:
        driverDeviceInfoService.save(deviceInfo)

        then:
        1 * driverDeviceInfoDao.update(_)
    }

    def "save_update_v2"() {
        DriverDeviceInfoModel deviceInfo = new DriverDeviceInfoModel(
                driverId: 2L,
                cid: "11",
                appId: "5206",
                appVer: "8400",
                rnVer: "**********",
                local: "zh-CN",
                os: "ios",
                osVer: "15.6",
                loginTime: LocalDateTime.now(),
                loginAccount: "***********",
                loginType: 1,
                activeTime: LocalDateTime.now()

        )
        given:
        driverDeviceInfoDao.findOne(_) >> new DriverDeviceInfoPO(id: 1001, activeTime: LocalDateTime.now().plusMinutes(-3))

        when:
        driverDeviceInfoService.save(deviceInfo)

        then:
        1 * driverDeviceInfoDao.update(_)
    }

    def "save_update_oldmq"() {
        DriverDeviceInfoModel deviceInfo = new DriverDeviceInfoModel(
                driverId: 2L,
                cid: "11",
                appId: "5206",
                appVer: "8400",
                rnVer: "**********",
                local: "zh-CN",
                os: "ios",
                osVer: "15.6"
        )
        given:
        driverDeviceInfoDao.findOne(_) >> new DriverDeviceInfoPO(id: 1001, activeTime: LocalDateTime.now().plusMinutes(-7))

        when:
        driverDeviceInfoService.save(deviceInfo)

        then:
        1 * driverDeviceInfoDao.update(_)
    }

    def "queryDeviceInfo_byDriverId"() {
        given:
        driverDeviceInfoDao.findOne(_) >> deviceProfilePO

        when:
        def deviceInfo = driverDeviceInfoService.queryDeviceInfo(driverId)

        then:
        appid == deviceInfo.appId
        appVer == deviceInfo.appVer
        where:
        appid  | appVer | driverId | deviceProfilePO
        "5206" | "8400" | 2L       | new DriverDeviceInfoPO(appId: "5206", appVersion: "8400")
    }


    def "queryDeviceInfo_byDriverId_null"() {
        given:
        driverDeviceInfoDao.findOne(_) >> null

        when:
        def deviceInfo = driverDeviceInfoService.queryDeviceInfo(2L)

        then:
        deviceInfo == null
    }


    def "queryDeviceInfo_byUid"() {
        given:
        driverDeviceInfoDao.findOne(_) >> deviceProfilePO

        when:
        def deviceInfo = driverDeviceInfoService.queryDeviceInfo("123")

        then:
        appid == deviceInfo.appId
        appVer == deviceInfo.appVer
        where:
        appid  | appVer | deviceProfilePO
        "5206" | "8400" | new DriverDeviceInfoPO(appId: "5206", appVersion: "8400")


    }

    def "queryDeviceInfo_byUid_null"() {
        given:
        driverDeviceInfoDao.findOne(_) >> null

        when:
        def deviceInfo = driverDeviceInfoService.queryDeviceInfo("123")

        then:
        deviceInfo == null

    }

}
