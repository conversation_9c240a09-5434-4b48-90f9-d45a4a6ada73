package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository
import com.ctrip.dcs.driver.value.account.AccountUidInfo
import com.ctrip.dcs.driver.account.application.service.impl.GenerateGlobalIdServiceImpl
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.GlobalIdRecordDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.GlobalIdRecordPO
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig

import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.google.common.collect.Lists
import spock.lang.Specification

class GernerateGlobalIdTest extends Specification {

    GenerateGlobalIdServiceImpl generateGlobalIdService

    UserCenterAccountGateway userCenterAccountGateway = Mock(UserCenterAccountGateway)
    AccountMapperDao accountMapperDao = Mock(AccountMapperDao)
    AccountBaseInfoDao accountBaseInfoDao = Mock(AccountBaseInfoDao)
    GlobalIdRecordDao globalIdRecordDao = Mock(GlobalIdRecordDao)
    DriverAccountConfig driverAccountConfig = Mock(DriverAccountConfig)
    ArchCoreInfoRepository archCoreInfoService = Mock(ArchCoreInfoRepository)

    def setup() {
        generateGlobalIdService = new GenerateGlobalIdServiceImpl()
        generateGlobalIdService.userCenterAccountGateway = userCenterAccountGateway;
        generateGlobalIdService.accountMapperDao = accountMapperDao;
        generateGlobalIdService.accountBaseInfoDao = accountBaseInfoDao;
        generateGlobalIdService.globalIdRecordDao = globalIdRecordDao;
        generateGlobalIdService.driverAccountConfig = driverAccountConfig;
        generateGlobalIdService.archCoreInfoService = archCoreInfoService;
    }

    def "getUIDByPhone"() {
        given:
        accountBaseInfoDao.queryByMobilePhone(_,_) >>> [new AccountBaseInfoPO(uid: "123"), null]
        userCenterAccountGateway.queryAccountUidByPhone(_,_) >>> [null, new AccountUidInfo("455", "udl")]

        when:
        def res1 = generateGlobalIdService.getUIDByPhone("12", "********")
        def res2 = generateGlobalIdService.getUIDByPhone("12", "********")
        def res3 = generateGlobalIdService.getUIDByPhone("12", "********")

        then:
        res1 == "123"
        res2 == ""
        res3 == "455"
    }

    def "findExistGlobalId"() {
        given:
        accountMapperDao.queryByUid(_) >>> [Lists.newArrayList(), Lists.newArrayList(new AccountMapperPO(source: "Driver", sourceId: "23"))]

        when:
        def res1 = generateGlobalIdService.findExistGlobalId("")
        def res2 = generateGlobalIdService.findExistGlobalId("111")
        def res3 = generateGlobalIdService.findExistGlobalId("11")

        then:
        res1 == null
        res2 == null
        res3 == 23
    }


    def "generateNewGlobalId"() {
        when:
        def res = generateGlobalIdService.generateNewGlobalId("Driver", "956", "9204")

        then:
        1 * globalIdRecordDao.insert(_ as GlobalIdRecordPO) >> 1

    }

}
