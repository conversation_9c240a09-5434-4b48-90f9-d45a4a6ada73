package com.ctrip.dcs.driver.domain.application.exector.rights

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig
import com.ctrip.dcs.driver.domain.rights.QueryRightsAndLevelRequestType
import com.ctrip.dcs.driver.domain.rights.QueryRightsAndLevelResponseType
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext
import spock.lang.Specification

class QueryRightsAndLevelEngineTest extends Specification {
    QueryRightsAndLevelEngine queryRightsAndLevelEngine
    DomainBeanFactory domainBeanFactory = Mock(DomainBeanFactory)
    RightsConfig rightsConfig = Mock(RightsConfig)
    LevelConfig levelConfig = Mock(LevelConfig)
    RightsRepoServiceImpl rightsRepoService = Mock(RightsRepoServiceImpl)


    def setup() {
        domainBeanFactory = new DomainBeanFactory(levelConfig: levelConfig, rightsConfig: rightsConfig, rightsRepoService: rightsRepoService)
        queryRightsAndLevelEngine = new QueryRightsAndLevelEngine(domainBeanFactory: domainBeanFactory)
    }

    def "test execute"() {
        def request = new QueryRightsAndLevelRequestType(driverId: 1, date: "2023-06", rightsTypes: Arrays.asList(1))

        ServiceExecuteContext.newContext().withRequest(request).withGlobalTraceId(UUID.randomUUID().toString())
                .withResponseClass(QueryRightsAndLevelResponseType.class).init()
        domainBeanFactory.rightsRepoService() >> rightsRepoService
        rightsRepoService.queryDriverRights(1L, Arrays.asList(1), "2023-06") >> [new RightsModel(1l, 1l, "rightsName", "rightsDesc", 1l, "monthIdx", 1l, 0, 0, 0, 0, 0, "2023-06-13 14:07:38", "2023-06-13 14:07:38", "2023-06-13 14:07:38", "extend")]
        rightsRepoService.queryDriverLevel(1, "2023-06") >> new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0)
        when:
        QueryRightsAndLevelResponseType result = queryRightsAndLevelEngine.execute(request)

        then:
        result != null
    }

}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme