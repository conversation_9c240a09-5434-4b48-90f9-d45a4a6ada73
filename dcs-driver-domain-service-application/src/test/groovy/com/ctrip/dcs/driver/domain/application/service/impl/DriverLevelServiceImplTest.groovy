package com.ctrip.dcs.driver.domain.application.service.impl

import com.ctrip.dcs.driver.domain.application.service.DriverBizGrayService
import com.ctrip.dcs.driver.domain.application.service.DriverLevelService
import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig
import com.ctrip.dcs.geo.domain.repository.CityRepository
import com.ctrip.dcs.geo.domain.value.City
import com.google.common.collect.Lists
import spock.lang.Specification

class DriverLevelServiceImplTest extends Specification {


    CityRepository cityRepository = Mock(CityRepository)
    SystemQConfig systemQConfig = Mock(SystemQConfig)
    LevelConfig levelConfig = Mock(LevelConfig)
    DriverBizGrayService driverBizGrayService = Mock(DriverBizGrayService)
    DriverLevelService driverLevelService

    def setup() {
        driverLevelService = new DriverLevelServiceImpl()
        driverLevelService.cityRepository = cityRepository
        driverLevelService.systemQConfig = systemQConfig
        driverLevelService.levelConfig = levelConfig
        driverLevelService.driverBizGrayService = driverBizGrayService
    }

    def "calcDriverLevel"() {
        def level0 = new FormLevelInfo(id: 1L, level: 0, levelName: "青铜", driverPointLow: BigDecimal.ZERO, activityLow: BigDecimal.ZERO, safePointLow: BigDecimal.ZERO)
        def level1 = new FormLevelInfo(id: 2L, level: 1, levelName: "白银", driverPointLow: BigDecimal.valueOf(10), activityLow: BigDecimal.valueOf(10), safePointLow: BigDecimal.ZERO)
        def level2 = new FormLevelInfo(id: 3L, level: 2, levelName: "黄金", driverPointLow: BigDecimal.valueOf(20), activityLow: BigDecimal.valueOf(20), safePointLow: BigDecimal.ZERO)
        def level3 = new FormLevelInfo(id: 4L, level: 3, levelName: "铂金", driverPointLow: BigDecimal.valueOf(30), activityLow: BigDecimal.valueOf(30), safePointLow: BigDecimal.valueOf(50))
        def level4 = new FormLevelInfo(id: 5L, level: 4, levelName: "钻石", driverPointLow: BigDecimal.valueOf(40), activityLow: BigDecimal.valueOf(40), safePointLow: BigDecimal.valueOf(50))

        given:
        levelConfig.getCityDriverLevel(_) >>> [Lists.newArrayList(), Lists.newArrayList(level0, level1, level2, level3, level4)]
        cityRepository.findOne(_) >>> [new City(chineseMainland: true)]
        systemQConfig.getString(_) >> ""
        driverBizGrayService.queryBizGraySwitch(_,_,_,_,_,_,_) >>> [false, true]

        when:
        def res0 = driverLevelService.calcDriverLevel(123L, 2L, BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(49))
        // 安全分不参与等级计算
        def res1 = driverLevelService.calcDriverLevel(123L, 2L, BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(49))
        // 安全分参与等级计算但未达标
        def res2 = driverLevelService.calcDriverLevel(123L, 2L, BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(49))
        // 安全分参与等级计算且达标
        def res3 = driverLevelService.calcDriverLevel(123L, 2L, BigDecimal.valueOf(35), BigDecimal.valueOf(35), BigDecimal.valueOf(50))

        then:
        res0.getLevel() == 0
        res1.getLevel() == 3
        res2.getLevel() == 2
        res3.getLevel() == 3

    }


    def "getDefaultForm1"() {

        when:
        def res2 = driverLevelService.getDefaultForm(1L, Lists.newArrayList(new FormLevelInfo(level: LevelEnum.LEVEL_TYPE_BRONZE.getCode())))
        def res3 = driverLevelService.getDefaultForm(1L, Lists.newArrayList(new FormLevelInfo(level: LevelEnum.LEVEL_TYPE_REGULAR_MEDAL.getCode())))

        then:
        res2.getLevel() == LevelEnum.LEVEL_TYPE_BRONZE.getCode()
        res3.getLevel() == LevelEnum.LEVEL_TYPE_REGULAR_MEDAL.getCode()
    }

    def "getDefaultForm2"() {
        given:
        cityRepository.findOne(_) >>> [new City(chineseMainland: true), new City(chineseMainland: false)]
        systemQConfig.getString(_) >> ""

        when:
        def res2 = driverLevelService.getDefaultForm(1L, Lists.newArrayList())
        def res3 = driverLevelService.getDefaultForm(1L, Lists.newArrayList())

        then:
        res2.getLevel() == 0
        res3.getLevel() == LevelEnum.LEVEL_TYPE_REGULAR_MEDAL.getCode()
    }
}
