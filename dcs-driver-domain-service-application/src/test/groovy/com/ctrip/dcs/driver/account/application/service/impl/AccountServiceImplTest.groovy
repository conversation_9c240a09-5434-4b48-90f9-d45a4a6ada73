package com.ctrip.dcs.driver.account.application.service.impl

import com.ctrip.arch.coreinfo.enums.KeyType
import com.ctrip.dcs.driver.account.application.cache.AccountInfoCache
import com.ctrip.dcs.driver.account.application.cache.AccountUDLCache
import com.ctrip.dcs.driver.account.application.dto.RegisterAccountResult
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig
import com.ctrip.dcs.driver.account.infrastructure.value.RegisterNewAccountParam
import com.ctrip.dcs.driver.account.infrastructure.value.RegisterNewAccountV1Param
import com.ctrip.dcs.driver.account.infrastructure.value.UpdateAccountParam
import com.ctrip.dcs.driver.convert.AccountInfoConvert
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService
import com.ctrip.dcs.driver.gateway.AccountRepository
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService
import com.ctrip.dcs.driver.value.account.AccountUidInfo
import com.ctrip.igt.framework.common.exception.BizException
import spock.lang.Specification
import spock.lang.Subject

/*
作者：pl.yang
创建时间：2025/4/3-下午3:24-2025
*/

class AccountServiceImplTest extends Specification {
    // 定义Mock对象
    def accountBaseInfoDao = Mock(AccountBaseInfoDao)
    def accountMapperDao = Mock(AccountMapperDao)
    def userCenterAccountGateway = Mock(UserCenterAccountGateway)
    def archCoreInfoService = Mock(ArchCoreInfoRepository)
    def driverAccountConfig = Mock(DriverAccountConfig)
    def lockService = Mock(LockService);
    def accountInfoCache = Mock(AccountInfoCache);
    def tripUniversityService = Mock(TripUniversityService)
    def accountChangeLogHelper = Mock(AccountChangeLogHelper)
    def accountUDLCache = Mock(AccountUDLCache)
    //accountInfoConvert
    def accountInfoConvert = Mock(AccountInfoConvert)
    //AccountRepository
    def accountRepository = Mock(AccountRepository)


    // 初始化被测类
    @Subject
    def service = new AccountServiceImpl(
            accountBaseInfoDao: accountBaseInfoDao,
            accountMapperDao: accountMapperDao,
            userCenterAccountGateway: userCenterAccountGateway,
            archCoreInfoService: archCoreInfoService,
            lockService: lockService,
            driverAccountConfig: driverAccountConfig,
            accountInfoCache: accountInfoCache,
            tripUniversityService: tripUniversityService,
            accountChangeLogHelper: accountChangeLogHelper,
            accountUDLCache: accountUDLCache,
            accountInfoConvert: accountInfoConvert
            ,accountRepository:accountRepository
    )

    // 测试updateAccount方法
    def "测试updateAccount-成功更新所有字段"() {
        given: "存在一个账户且参数包含所有更新字段"
        def param = createUpdateParam()
        def existingAccount = createExistingAccount()

        // 模拟加密服务
        archCoreInfoService.encryptByType(KeyType.Phone, "***********") >> "encrypted_***********"
        archCoreInfoService.encryptByType(KeyType.Mail, "<EMAIL>") >> "<EMAIL>"
        archCoreInfoService.encryptByType(KeyType.Identity_Card, "NewIDCard") >> "encrypted_NewIDCard"

        // 模拟数据库查询
        accountBaseInfoDao.queryByUID("uid123") >> existingAccount

        // 模拟锁服务直接执行
        lockService.executeInLock(_, _, _) >> { args -> args[2].call() }

        // 配置超时时间
        driverAccountConfig.getAccountLockWaitMills() >> 1000
        //
        accountMapperDao.queryByUid(_) >> []

        when: "调用updateAccount方法"
        UpdateAccountResponseType result = service.updateAccount(param)

        then: "验证流程"
        // 验证用户中心绑定操作
        1 * userCenterAccountGateway.bindEmail("uid123", "<EMAIL>", "system") >> true
        1 * userCenterAccountGateway.bindMobilePhone("uid123", "86", "encrypted_***********", "system") >> true
        1 * accountBaseInfoDao.update(_) >> { AccountBaseInfoPO updated ->
            assert updated.name == "NewName"
            assert updated.idCardNo == "encrypted_NewIDCard"  // 验证加密后的身份证号
            assert updated.email == "<EMAIL>"   // 验证加密后的邮箱
            assert updated.countryCode == "86"
            assert updated.phoneNumber == "encrypted_***********"  // 验证加密后的手机号
            assert updated.payoneerAccountId == "PAY123"
            return 1
        }
        1 * accountInfoCache.delAccount(_);
        1 * tripUniversityService.updateTripUniversityAccountAsync(_);

        // 验证返回结果
        result.getResponseResult().getReturnCode() == "200"
    }


    def "测试updateAccount-更新已存在的手机号应抛出异常"() {
        given: "存在一个账户且尝试绑定已存在的手机号"
        def param = createUpdateParam().tap {
            countryCode = "86"
            phoneNumber = "***********"
        }
        def existingAccount = createExistingAccount()


        // 模拟加密服务
        archCoreInfoService.encryptByType(KeyType.Phone, "***********") >> "encrypted_***********"
        archCoreInfoService.encryptByType(KeyType.Mail, "<EMAIL>") >> "<EMAIL>"
        archCoreInfoService.encryptByType(KeyType.Identity_Card, "NewIDCard") >> "encrypted_NewIDCard"

        // 模拟数据库查询

        // 模拟锁服务直接执行
        lockService.executeInLock(_, _, _) >> { args -> args[2].call() }

        when: "调用updateAccount方法"
        service.updateAccount(param)

        then: "应抛出手机号已被绑定的异常"
        1 * accountBaseInfoDao.queryByUID("uid123") >> existingAccount
        1 * userCenterAccountGateway.queryAccountUidByPhone("86", "encrypted_***********") >> new AccountUidInfo("otherUid", "udl")
        thrown(BizException)

    }

    // 测试doRegister方法
    def "测试doRegister-新用户注册成功"() {
        given: "新用户注册参数"
        def param = createRegisterParam()

        when: "调用doRegister方法"
        def result = service.doRegister(param)

        then: "验证注册流程"
        1 * accountBaseInfoDao.queryByMobilePhone("86", "***********") >> null
        1 * userCenterAccountGateway.queryAccountUidByPhone(_, _) >> null
        1 * userCenterAccountGateway.registerNewAccount(_) >> RegisterAccountResult.builder().uid("newUid").udl("newUdl").build();
        1 * accountBaseInfoDao.insert(_) >> { args ->
            def account = args[0] as AccountBaseInfoPO
            assert account.uid == "newUid"
            assert account.providerDataLocation == "newUdl"
            return 1
        }
        1 * accountMapperDao.insert(_) >> {
            return 1
        }
        result.uid == "newUid"
    }

    def "测试doRegister-重复注册应更新现有账户"() {
        given: "已存在的账户信息"
        def param = createRegisterParam_over()
        def existingAccount = new AccountBaseInfoPO(id: 1, uid: "existingUid", isOversea: 1)
        accountMapperDao.queryByUid(_) >> []
        when: "调用doRegister方法"
        def result = service.doRegister(param)

        then: "验证更新流程"
        1 * accountBaseInfoDao.queryByMobilePhone("86", "***********") >> existingAccount
        1 * accountBaseInfoDao.update(_) >> { args ->
            def updated = args[0] as AccountBaseInfoPO
            assert updated.name == "TestUser"
            return 1
        }
        result.uid == "existingUid"
    }
//
    // 测试registerV1和registerAccountV1方法
    def "测试registerV1-新用户注册成功生成新UID"() {
        given: "V1版本注册参数"
        def param = createRegisterV1Param()
        lockService.executeInLock(_, _, _) >> { arg -> arg[2].call() }

        when: "调用registerV1方法"
        def result = service.registerV1(param)

        then: "验证V1注册流程"
        1 * userCenterAccountGateway.queryAccountUid(_) >> null
        1 * userCenterAccountGateway.registerNewAccount(_) >> RegisterAccountResult.builder().uid("v1Uid").udl("v1Udl").build()

        1 * accountMapperDao.insert(_) >> { args ->
            def mapper = args[0] as AccountMapperPO
            assert mapper.uid == "v1Uid"
            return 1
        }
        result.uid == "v1Uid"
    }

    def "测试registerAccountV1-用户已存在直接返回UID"() {
        given: "已存在的用户信息"
        def param = createRegisterV1Param()

        when: "调用registerAccountV1方法"
        def result = service.registerAccountV1(param)

        then: "验证直接返回现有UID"
        1 * userCenterAccountGateway.queryAccountUid(_) >> new AccountUidInfo("existingUid", "existingUdl")
        0 * userCenterAccountGateway.registerNewAccount(_)
        result.uid == "existingUid"
    }

    // 辅助方法
    private createUpdateParam() {
        new UpdateAccountParam(
                uid: "uid123",
                name: "NewName",
                idCardNo: "NewIDCard",
                email: "<EMAIL>",
                countryCode: "86",
                phoneNumber: "***********",
                payoneerAccountId: "PAY123",
                source: "system"
        )
    }

    private createExistingAccount() {
        new AccountBaseInfoPO(
                uid: "uid123",
                name: "OldName",
                countryCode: "86",
                phoneNumber: "***********",
                email: "<EMAIL>"
        )
    }

    private createRegisterParam() {
        new RegisterNewAccountParam(
                countryCode: "86",
                phoneNumber: "***********",
                name: "TestUser",
                source: "driver",
                sourceId: "1001",
        )
    }

    private createRegisterParam_over() {
        new RegisterNewAccountParam(
                countryCode: "86",
                phoneNumber: "***********",
                name: "TestUser",
                source: "driver",
                sourceId: "1001",
                isOversea: 1
        )
    }

    private createRegisterV1Param() {
        new RegisterNewAccountV1Param(
                countryCode: "86",
                phoneNumber: "***********",
                source: "driver",
                sourceId: "1001"
        )
    }


    def "测试getAccountInfoByUID-缓存命中"() {
        given: "缓存中存在账户信息"
        def uid = "testUid"
        def cachedAccountInfo = new AccountInfoDTO(uid: uid)

        when: "调用getAccountInfoByUID方法"
        def result = service.getAccountInfoByUID(uid)

        then: "应从缓存中获取账户信息"
        1 * accountInfoCache.getAccount(uid) >> cachedAccountInfo
        result == cachedAccountInfo
    }

    def "测试getAccountInfoByUID-缓存未命中数据库存在"() {
        given: "缓存中不存在，但数据库中存在账户信息"
        def uid = "testUid"
        def accountBaseInfoPO = new AccountBaseInfoPO(uid: uid)
        def accountMapperPOList = [new AccountMapperPO(uid: uid)]

        when: "调用getAccountInfoByUID方法"
        def result = service.getAccountInfoByUID(uid)

        then: "应从数据库中获取账户信息并缓存"
        1 * accountInfoCache.getAccount(uid) >> null
        1 * accountBaseInfoDao.queryByUID(uid) >> accountBaseInfoPO
        1 * accountMapperDao.queryByUid(uid) >> accountMapperPOList
        1 * accountInfoCache.saveAccount(_, _)
    }

    def "测试getAccountInfoByUID-缓存和数据库均未命中"() {
        given: "缓存和数据库中均不存在账户信息"
        def uid = "testUid"

        when: "调用getAccountInfoByUID方法"
        def result = service.getAccountInfoByUID(uid)

        then: "应返回null"
        1 * accountInfoCache.getAccount(uid) >> null
        1 * accountBaseInfoDao.queryByUID(uid) >> null
        result == null
    }

    def "测试getAccountUDLByUid-缓存命中"() {
        given: "缓存中存在UDL信息"
        def uids = ["uid1", "uid2"]
        def cachedUDL = [AccountUDLDo.builder().udl("uid1").build(), AccountUDLDo.builder().udl("uid2").build()]

        when: "调用getAccountUDLByUid方法"
        def result = service.getAccountUDLByUid(uids)

        then: "应从缓存中获取UDL信息"
        1 * accountUDLCache.getAccountUDLByUid(uids) >> cachedUDL
        result == cachedUDL
    }

    def "测试getAccountUDLByUid-部分缓存未命中"() {
        given: "部分UID在缓存中不存在"
        def uids = ["uid1", "uid2"]
        def cachedUDL = [AccountUDLDo.builder().uid("uid1").build()]
        def dbUDL = [new AccountMapperPO(uid: "uid2")]

        when: "调用getAccountUDLByUid方法"
        def result = service.getAccountUDLByUid(uids)

        then: "应从数据库中获取未命中的UDL信息并缓存"
        1 * accountUDLCache.getAccountUDLByUid(uids) >> cachedUDL
        1 * accountRepository.batchQueryByUid(["uid2"]) >> dbUDL
        1 * accountUDLCache.saveAccountUDLByUid(_)
        result.size() == 2
    }

    def "测试getAccountUDLBySource-缓存命中"() {
        given: "缓存中存在UDL信息"
        def sourceIds = ["source1", "source2"]
        def cachedUDL = [AccountUDLDo.builder().sourceId("source1").build(), AccountUDLDo.builder().sourceId("source2").build()]

        when: "调用getAccountUDLBySource方法"
        def result = service.getAccountUDLBySource(sourceIds)

        then: "应从缓存中获取UDL信息"
        1 * accountUDLCache.getAccountUDLBySourceId(sourceIds) >> cachedUDL
        result == cachedUDL
    }

    def "测试getAccountUDLBySource-部分缓存未命中"() {
        given: "部分Source ID在缓存中不存在"
        def sourceIds = ["source1", "source2"]
        def cachedUDL = [AccountUDLDo.builder().sourceId("source1").build()]
        def dbUDL = [new AccountMapperPO(sourceId: "source2")]

        when: "调用getAccountUDLBySource方法"
        def result = service.getAccountUDLBySource(sourceIds)

        then: "应从数据库中获取未命中的UDL信息并缓存"
        1 * accountUDLCache.getAccountUDLBySourceId(sourceIds) >> cachedUDL
        1 * accountRepository.batchQueryBySourceId(["source2"]) >> dbUDL
        1 * accountUDLCache.saveAccountUDLBySourceId(_)
        result.size() == 2
    }
}
