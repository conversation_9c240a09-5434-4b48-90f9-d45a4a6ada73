package com.ctrip.dcs.driver.domain.application.mq

import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway
import com.ctrip.igt.framework.common.clogging.Logger
import org.junit.platform.commons.util.StringUtils
import qunar.tc.qmq.Message
import qunar.tc.qmq.base.BaseMessage
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class DriverInfoModifyListenerTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    UserCenterAccountGateway accounService
    @InjectMocks
    DriverInfoModifyListener driverInfoModifyListener
    @Mock
    AccountChangeLogHelper accountChangeLogHelper

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Message"() {
        Message message = new BaseMessage();
        message.setProperty("content", "{\"email\":\"TODG_1nnt0mql7e66\",\"countryCode\":\"TODG_1nnt0mql7e66\",\"phone\":\"TODG_1nnt0mql7e66\",\"uid\":\"TODG_1nnt0mql7e66\",\"modifyUser\":\"虞家荣\",\"drvId\":3455166,\"cityId\":359}");
        given:
        when(accounService.bindEmail(any(), anyString())).thenReturn(null)
        when(accounService.bindMobilePhone(any(), anyString())).thenReturn(null)
        when(accountChangeLogHelper.saveLog((AccountChangeLog)any())).thenReturn(true)

        when:
        driverInfoModifyListener.onMessage(message)

        then:
        StringUtils.isNotBlank(message.getStringProperty("content"))
    }

    def "test on Message1"() {
        Message message = new BaseMessage();
        message.setProperty("content", "");
        given:
        when(accounService.bindEmail(any(), anyString())).thenReturn(null)
        when(accounService.bindMobilePhone(any(), anyString())).thenReturn(null)
        when(accountChangeLogHelper.saveLog((AccountChangeLog)any())).thenReturn(true)

        when:
        driverInfoModifyListener.onMessage(message)

        then:
        StringUtils.isBlank(message.getStringProperty("content"))
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme
