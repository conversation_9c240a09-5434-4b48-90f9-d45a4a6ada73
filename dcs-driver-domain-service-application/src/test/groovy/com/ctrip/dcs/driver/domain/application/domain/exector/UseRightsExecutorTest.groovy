package com.ctrip.dcs.driver.domain.application.domain.exector

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService
import com.ctrip.dcs.driver.domain.application.service.impl.RightsDBDataServiceImpl
import com.ctrip.dcs.driver.domain.application.service.impl.RightsRepoServiceImpl
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import com.ctrip.dcs.driver.value.rights.DriverRightsObject
import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import spock.lang.Specification

import java.sql.Timestamp

class UseRightsExecutorTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    DriverRightsObject owner
    @Mock
    DrivRightsDao drivRightsDao =Mock(DrivRightsDao)
    @Mock
    DrivLevelDao drivLevelDao =Mock(DrivLevelDao)
    @Mock
    DrivRightsRecordDao drivRightsRecordDao =Mock(DrivRightsRecordDao)
    RightsRepoServiceImpl repoService = Mock(RightsRepoServiceImpl)
    @Mock
    DomainBeanFactory domainBeanFactory
    @Mock
    RightsDBDataService rightsDBDataService
    @InjectMocks
    UseRightsExecutor useRightsExecutor

    def setup() {
        rightsDBDataService = new RightsDBDataServiceImpl(drivLevelDao: drivLevelDao, drivRightsDao: drivRightsDao,drivRightsRecordDao: drivRightsRecordDao)
        domainBeanFactory = new DomainBeanFactory(rightsDBDataService: rightsDBDataService,rightsRepoService: repoService)
        useRightsExecutor = new UseRightsExecutor(owner,domainBeanFactory)
    }

    def "test do Work"() {
        given:
        repoService.queryDriverLevel(1l, LocalDateTimeUtils.monthIndexStr()) >>new LevelModel(1l, "levelName", 1l, "monthIdx", 1l, 0, 0 as BigDecimal, 0 as BigDecimal, 0)

        drivRightsDao.queryDriverRights(1l, LocalDateTimeUtils.monthIndexStr()) >>[new DrivRightsPO(id: 1l, rightsConfigId: 1l, rightsName: "rightsName", rightsDesc: "rightsDesc", cityId: 1l, monthIdx: "monthIdx", drivId: 1l, drivLevel: 0, rightsType: 0, useLimit: 0, useCount: 0, rightsStatus: 0, rightsStratTime: new Timestamp(1686636458573L), rightsEndTime: new Timestamp(1686636458573L), extend: "extend", datachangeCreatetime: new Timestamp(1686636458573L))]
        drivRightsRecordDao.queryDriverRecordsBySupplyOrderId(1l, 0,"supplyOrderId") >>[]
        when:
        Boolean result = useRightsExecutor.doWork(new UseRightsCondition(1l, 0,"", "userOrderId", "purchaseOrderId", "supplyOrderId", "punishOrderId", 0 as BigDecimal,""))

        then:
        result == Boolean.TRUE
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme