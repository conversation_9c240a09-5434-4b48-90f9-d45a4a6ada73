package com.ctrip.dcs.driver.trip.university.application.schedule;

import com.ctrip.dcs.driver.trip.university.infrastructure.utils.DateUtil;
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.driver.trip.university.infrastructure.gateway.TripUniversityGateway;
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityFailedLogDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityQueryCondition;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO;
import com.ctrip.dcs.go.domain.page.PageRequest;
import com.ctrip.dcs.go.log.Log;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants.*;

@Component
public class SyncDriverInfo2TripUniversityByFailedLogSchedule {
    Log log = Log.getInstance(SyncDriverInfo2TripUniversityByFailedLogSchedule.class);

    @Autowired
    private TripUniversityGateway tripUniversityGateway;

    @Autowired
    TripUniversityService tripUniversityService;

    @Autowired
    @Qualifier("TripUniversityThreadPool")
    ExecutorService executorService;

    @Autowired
    TripUniversityQconfig tripUniversityQconfig;

    @QSchedule("dcs.sync.driver.info.to.trip.university.by.failed.log.job")
    public void onExecute(Parameter parameter) {
        String type = DEFAULT_TYPE;
        String from = null;
        String to = null;
        String drvIds = null;
        if (parameter != null) {
            type = Optional.ofNullable(parameter.getString("type")).orElse(DEFAULT_TYPE);
            from = parameter.getString("from");
            to = parameter.getString("to");
            drvIds = parameter.getString("drvIds");
        }

        switch(type) {
            case DEFAULT_TYPE:
                syncFromFailedLog();
                break;
            case BY_DRV_IDS:
                syncByDrvIds(drvIds);
                break;
            case BY_DATE:
                syncByDate(from, to);
                break;
        }
    }

    private void syncByDate(String from, String to) {
        DriverSyncDrvInfoToTripUniversityQueryCondition condition = DriverSyncDrvInfoToTripUniversityQueryCondition.builder()
          .from(DateUtil.string2Timestamp(from, FORMAT))
          .to(DateUtil.string2Timestamp(to, FORMAT))
          .build();
        syncFromFailedLog(condition);
    }

    private void syncByDrvIds(String drvIds) {
        DriverSyncDrvInfoToTripUniversityQueryCondition condition = DriverSyncDrvInfoToTripUniversityQueryCondition.builder()
          .drvIdList(Arrays.stream(drvIds.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList()))
          .build();
        syncFromFailedLog(condition);
    }

    private void syncFromFailedLog() {
        DriverSyncDrvInfoToTripUniversityQueryCondition condition = DriverSyncDrvInfoToTripUniversityQueryCondition.builder()
          .build();
        syncFromFailedLog(condition);
    }

    private void syncFromFailedLog(DriverSyncDrvInfoToTripUniversityQueryCondition condition) {
        Logger logger = TaskHolder.getKeeper().getLogger();
        logger.info("dcs.sync.driver.info.to.trip.university.by.failed.log.job start drvId: {}", condition);

        int pageSize = tripUniversityQconfig.getSyncBatchSize();
        AtomicReference<Long> drvId = new AtomicReference<>(0L);
        List<DriverSyncDrvInfoToTripUniversityFailedLogDTO> resultList;
        try{
            do{
                condition.setSortId(drvId.get());
                condition.setPagination(PageRequest.builder().pageSize(pageSize).build());
                // 查询失败记录
                resultList =
                  tripUniversityGateway.querySyncFailedLog(condition);
                // 同步
                CompletableFuture.allOf(resultList.stream().map(
                    item -> CompletableFuture.supplyAsync(
                      () -> tripUniversityService.syncDriverInfoToTripUniversity(convert(item)), executorService))
                  .toArray(CompletableFuture[]::new)).get();

                // 获取最大ID
                resultList.forEach(item -> {
                    drvId.set(Math.max(drvId.get(), item.getId()));
                });
                logger.info("dcs.sync.driver.info.to.trip.university.by.failed.log.job synced drvId: {}", drvId);
            }while (CollectionUtils.isNotEmpty(resultList));
        }catch (Exception e) {
            logger.error("dcs.sync.driver.info.to.trip.university.by.failed.log.job error", e);
        }
    }

    private TripUniversityDriverDTO convert(DriverSyncDrvInfoToTripUniversityFailedLogDTO failedLogDTO) {
        TripUniversityDriverDTO tripUniversityDriverDTO = new TripUniversityDriverDTO();
        tripUniversityDriverDTO.setFailedLogId(failedLogDTO.getId());
        tripUniversityDriverDTO.setRetryCount(failedLogDTO.getRetryCount());
        tripUniversityDriverDTO.setUid(failedLogDTO.getUid());
        tripUniversityDriverDTO.setDrvId(failedLogDTO.getDrvId());
        return tripUniversityDriverDTO;
    }
}
