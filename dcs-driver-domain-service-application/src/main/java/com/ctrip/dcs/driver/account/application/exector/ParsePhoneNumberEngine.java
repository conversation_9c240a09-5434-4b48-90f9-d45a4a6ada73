    package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.common.AccountExceptionCode;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.ParsePhoneNumberRequestType;
import com.ctrip.dcs.driver.domain.account.ParsePhoneNumberResponseType;
import com.ctrip.dcs.driver.domain.account.TelphoneNumberType;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ParsePhoneNumberEngine extends ShoppingExecutor<ParsePhoneNumberRequestType, ParsePhoneNumberResponseType> implements Validator<ParsePhoneNumberRequestType> {

    @Autowired
    private AccountService accountService;
    @Autowired
    private ArchCoreInfoRepository archCoreInfoService;

    @Override
    public ParsePhoneNumberResponseType execute(ParsePhoneNumberRequestType request) {
        ParsePhoneNumberResponseType resp = new ParsePhoneNumberResponseType();

        NumberDTO numberDTO = accountService.splitPhoneNumber(request.getCountryCode(), request.getNumber());
        if (numberDTO == null) {
            resp.setValidNumber(false);
            Cat.logEvent(CatEventType.PARSE_PHONE_NUMBER, "failed");
            return ServiceResponseUtils.fail(resp, AccountExceptionCode.SPLIT_PHONE_NUMBER.getCode(), AccountExceptionCode.SPLIT_PHONE_NUMBER.getMsg());
        }
        resp.setCounryCode(numberDTO.getCountryCode());
        String fullPhoneNumber;
        if (archCoreInfoService.isEncrypt(KeyType.Phone, request.getNumber())) {
            // bodyNumber先解密再加上cityCode之后加密（原始手机号如果是密文，拆码平台会去除cityCode后单独加密，后续比较会失败）
            String decryptBodyNumber = archCoreInfoService.decryptByType(KeyType.Phone, numberDTO.getBodyNumber());
            fullPhoneNumber = numberDTO.getCityCode() + decryptBodyNumber;
            fullPhoneNumber = archCoreInfoService.encryptByType(KeyType.Phone, fullPhoneNumber);
        } else {
            // 原始手机号没加密过
            fullPhoneNumber = numberDTO.getCityCode() + numberDTO.getBodyNumber();
        }


        resp.setNumber(fullPhoneNumber);

        if (BooleanUtils.isFalse(numberDTO.isValid())) {
            // 号码不合法
            resp.setValidNumber(false);
            Cat.logEvent(CatEventType.PARSE_PHONE_NUMBER, "NumberInvalid");
        } else if (StringUtils.isNotBlank(request.getCountryCode()) && ObjectUtils.notEqual(request.getCountryCode(), numberDTO.getCountryCode())) {
            // 国家码不匹配，不合法（请求国家码为空不比较，因为没有国家码拆码服务也能自能解析出国家码）
            resp.setValidNumber(false);
            Cat.logEvent(CatEventType.PARSE_PHONE_NUMBER, "CountryCodeNotMatch");
        } else if (ObjectUtils.notEqual(request.getNumber(), fullPhoneNumber)) {
            // 拆码后号码不匹配，不合法
            resp.setValidNumber(false);
            Cat.logEvent(CatEventType.PARSE_PHONE_NUMBER, "PhoneNumberNotMatch");
        } else if (TelphoneNumberType.MOBILE.equals(request.getPhoneType()) && BooleanUtils.isNotTrue(numberDTO.isMobile())) {
            // 国家码和号码都匹配，但请求仅需要校验手机号
            resp.setValidNumber(false);
            Cat.logEvent(CatEventType.PARSE_PHONE_NUMBER, "TypeNotMatch");
        } else {
            // 号码合法，拆码后国家码和号码与请求匹配，号码类型和请求类型匹配
            Cat.logEvent(CatEventType.PARSE_PHONE_NUMBER, "Success");
            resp.setValidNumber(true);
        }
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public void validate(AbstractValidator<ParsePhoneNumberRequestType> validator) {
        validator.ruleFor("countryCode").notNull().notEmpty();
        validator.ruleFor("number").notNull().notEmpty();
        validator.ruleFor("phoneType").notNull();
    }
}
