package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.GuideApplyExamDBDataService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideApplyExamDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideApplyExamPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class GuideApplyExamDBDataServiceImpl implements GuideApplyExamDBDataService {
    @Autowired
    private GuideApplyExamDao guideApplyExamDao;

    @Override
    public void insertGuideApplyExam(GuideApplyExamPO guideApplyExamPO) {
        guideApplyExamDao.insert(guideApplyExamPO);
    }

    @Override
    public GuideApplyExamModel queryGuideApplyExamByAccountAndSubject(String examAccountId, String applySubject) {
        List<GuideApplyExamPO> guideApplyExamPOS = guideApplyExamDao.queryGuideApplyExam(examAccountId, applySubject);
        if (CollectionUtils.isEmpty(guideApplyExamPOS)) {
            return null;
        }
        return convert2GuideApplyExamModel(guideApplyExamPOS.get(0));
    }

    @Override
    public List<GuideApplyExamModel> queryGuideApplyExamByAccount(String examAccountId){
        List<GuideApplyExamPO> guideApplyExamPOS = guideApplyExamDao.queryInfoByExamAccountId(examAccountId);
        if (CollectionUtils.isEmpty(guideApplyExamPOS)) {
            return null;
        }
        return guideApplyExamPOS.stream().map(po -> convert2GuideApplyExamModel(po)).collect(Collectors.toList());
    }

    @Override
    public  List<GuideApplyExamModel> queryPassedGuideApplyExamByAccount(String examAccountId){
        List<GuideApplyExamPO> guideApplyExamPOS = guideApplyExamDao.queryPassedInfoByExamAccountId(examAccountId);
        if (CollectionUtils.isEmpty(guideApplyExamPOS)) {
            return null;
        }
        return guideApplyExamPOS.stream().map(po -> convert2GuideApplyExamModel(po)).collect(Collectors.toList());
    }


    @Override
    public Long countCallExamFailed(){
        return guideApplyExamDao.countCallExamFailed();
    }

    @Override
    public List<GuideApplyExamModel> queryCallExamFailedRecords(int startIndex, int count){
        List<GuideApplyExamPO> guideApplyExamPOS = guideApplyExamDao.queryCallExamFailedRecords(startIndex, count);
        if (CollectionUtils.isEmpty(guideApplyExamPOS)) {
            return null;
        }
        return guideApplyExamPOS.stream().map(po -> convert2GuideApplyExamModel(po)).collect(Collectors.toList());
    }

    @Override
    public List<GuideApplyExamModel> queryApplyFailedRecords(int startIndex, int count){
        List<GuideApplyExamPO> guideApplyExamPOS = guideApplyExamDao.queryApplyFailedRecords(startIndex, count);
        if (CollectionUtils.isEmpty(guideApplyExamPOS)) {
            return null;
        }
        return guideApplyExamPOS.stream().map(po -> convert2GuideApplyExamModel(po)).collect(Collectors.toList());
    }


    @Override
    public void updateGuideApplyExam(GuideApplyExamPO guideApplyExamPO){
        guideApplyExamDao.update(guideApplyExamPO);
    }

    @Override
    public Long queryMaxId(Timestamp time){
        GuideApplyExamPO guideApplyExamPO = guideApplyExamDao.queryMaxIdByChangeTime(time);
        if(guideApplyExamPO == null){
            return null;
        }
        return guideApplyExamPO.getId();
    }

    @Override
    public Long queryMinId(Timestamp time){
        GuideApplyExamPO guideApplyExamPO = guideApplyExamDao.queryMinIdByChangeTime(time);
        if(guideApplyExamPO == null){
            return null;
        }
        return guideApplyExamPO.getId();
    }

    @Override
    public List<GuideApplyExamModel> queryUpassedBetweenIds(Long startId,Long endId){
        List<GuideApplyExamPO> guideApplyExamPOS =
            guideApplyExamDao.queryUpassedBetweenIds(startId, endId);
        if (CollectionUtils.isEmpty(guideApplyExamPOS)) {
            return Collections.emptyList();
        }
        return guideApplyExamPOS.stream().map(po -> convert2GuideApplyExamModel(po)).collect(Collectors.toList());
    }

    private GuideApplyExamModel convert2GuideApplyExamModel(GuideApplyExamPO record) {
        GuideApplyExamModel model = new GuideApplyExamModel();
        model.setId(record.getId());
        model.setGuideId(record.getGuideId());
        model.setExamAccountId(record.getExamAccountId());
        model.setApplySubject(record.getApplySubject());
        model.setGuideName(record.getGuideName());
        model.setAccount(record.getAccount());
        model.setApplyTime(LocalDateTimeUtils.format(record.getApplyTime()));
        model.setTimeZone(record.getTimeZone());
        model.setSubjectName(record.getSubjectName());
        model.setApplyResult(record.getApplyResult());
        model.setExamIsPassed(record.getExamIsPassed());
        model.setCallExamSuccess(record.getCallExamSuccess());
        model.setDatachangeCreatetime(LocalDateTimeUtils.format(record.getDatachangeCreatetime()));

        return model;
    }
}
