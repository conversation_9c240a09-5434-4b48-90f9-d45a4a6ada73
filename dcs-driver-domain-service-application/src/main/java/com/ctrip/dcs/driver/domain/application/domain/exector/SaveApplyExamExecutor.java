package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.http.HttpClient;
import com.ctrip.dcs.driver.domain.application.http.request.ExamChangeUserRequest;
import com.ctrip.dcs.driver.domain.application.http.request.ExamChangeUserRequestData;
import com.ctrip.dcs.driver.domain.application.http.response.ExamAccessTokenResData;
import com.ctrip.dcs.driver.domain.application.http.response.ExamChangeUserResData;
import com.ctrip.dcs.driver.domain.application.http.response.ExamCommonResponse;
import com.ctrip.dcs.driver.domain.infrastructure.constant.CallExamSuccessEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamApplyResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamIsPassedEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideApplyExamPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.value.exam.ApplyExamObject;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class SaveApplyExamExecutor extends DomainExecutor<ApplyExamObject, Void, Boolean> {

  public static final Logger LOGGER = LoggerFactory.getLogger(SaveApplyExamExecutor.class);

  public SaveApplyExamExecutor(ApplyExamObject owner, DomainBeanFactory domainBeanFactory) {
    super(owner, domainBeanFactory);
  }

  @Override
  public Boolean doWork(Void aLong) {
    try {
      GuideApplyExamModel guideApplyExamModel = queryIfPassedExam();
      //该科目已通过
      if (Objects.nonNull(
          guideApplyExamModel) && guideApplyExamModel.getExamIsPassed() == ExamIsPassedEnum.PASSED.getCode()) {
        return true;
      }
      if (Objects.isNull(guideApplyExamModel)) {
        insertApplyExamRecord();
        this.domainBeanFactory.executorService().submit(() -> {
          callExamDataExchange();
        });
        return false;
      }
      saveApplyExamRecord(guideApplyExamModel);
    } catch (Exception e) {
      LOGGER.error(
          String.format("failed to SaveApplyExam guideId:%s,applySubject:%s", this.owner.guideId(),
              this.owner.applySubject()), e);
      throw new BizException(
          String.format("failed to SaveApplyExam guideId:%s,applySubject:%s", this.owner.guideId(),
              this.owner.applySubject()));
    }
    return false;
  }

  public void callExamDataExchange() {
    String apiDomainName = this.domainBeanFactory.examInterfaceConfig().getApiDomainName();
    String changeUserInfoUrl = this.domainBeanFactory.examInterfaceConfig().getChangeUserInfoUrl();
    String accessToken = this.domainBeanFactory.examRedisLogic().getAccessToken();
    if(StringUtils.isEmpty(accessToken)){
      accessToken = getAccessToken();
    }
    String id = this.owner.examAccountId();
    String name = this.owner.guideName();
    String deptId = this.owner.applySubject();

    ExamChangeUserRequest request = new ExamChangeUserRequest();
    request.setFields(new String[] {"id", "user_name", "dept_id"});
    ExamChangeUserRequestData data = new ExamChangeUserRequestData();
    data.setId(id);
    data.setDept_id(deptId);
    data.setUser_name(name);
    request.setData(data);
    String requestUrl = apiDomainName + String.format(changeUserInfoUrl, accessToken);
    String response = this.domainBeanFactory.httpClient()
         .post(requestUrl, JacksonUtil.serialize(request));
    ExamCommonResponse<ExamChangeUserResData> deserialize = JacksonUtil.deserialize(response,
        new TypeToken<ExamCommonResponse<ExamChangeUserResData>>() {
        }.getType());
    if (deserialize != null && deserialize.getData() != null && deserialize.getData().isSuccess()) {
      this.owner.setCallExamSuccess(CallExamSuccessEnum.CALL_SUCCESS);
      saveApplyExamRecord(null);
    }
  }

  private String getAccessToken() {
    HttpClient httpClient = this.domainBeanFactory.httpClient();
    String ssoDomainName = this.domainBeanFactory.examInterfaceConfig().getSsoDomainName();
    String openAuthInterfaceUrl = this.domainBeanFactory.examInterfaceConfig().getOpenAuthInterfaceUrl();
    String secret = this.domainBeanFactory.commonExamSecretUtils().secret();
    String tenantid = this.domainBeanFactory.examInterfaceConfig().getTenantid();
    String requestUrl = ssoDomainName + String.format(openAuthInterfaceUrl,tenantid,secret);
    String responseStr = httpClient.get(requestUrl);
    if(StringUtils.isEmpty(responseStr)){
      return Strings.EMPTY;
    }
    ExamCommonResponse<ExamAccessTokenResData> response = JacksonUtil.deserialize(responseStr,
        new TypeToken<ExamCommonResponse<ExamAccessTokenResData>>() {
        }.getType());
    if(response != null && response.getData() != null){
      String accessToken = response.getData().getAccess_token();
      long expiresIn = response.getData().getExpires_in();
      this.domainBeanFactory.examRedisLogic().saveAccessToken(accessToken,expiresIn);
      return accessToken;
    }
    return Strings.EMPTY;
  }

  protected GuideApplyExamModel queryIfPassedExam() {
    FromGuideExamInfo examInfoByDeptId =
        this.domainBeanFactory.guideExamConfig().getExamInfoByDeptId(this.owner.applySubject());
    if(examInfoByDeptId == null){
      LOGGER.error(String.format("guideId:%s,applySubject:%s illegal", this.owner.guideId(), this.owner.applySubject()));
      throw new BizException(String.format("guideId:%s,applySubject:%s illegal", this.owner.guideId(), this.owner.applySubject()));
    }
    if(!LocalDateTimeUtils.isNowInDate(examInfoByDeptId.getStartTime(),examInfoByDeptId.getEndTime())){
      LOGGER.error(String.format("guideId:%s,applySubject:%s Invalid subject", this.owner.guideId(), this.owner.applySubject()));
      throw new BizException(String.format("guideId:%s,applySubject:%s Invalid subject", this.owner.guideId(), this.owner.applySubject()));
    }
    List<GuideApplyExamModel> guideApplyExamModels =
        this.domainBeanFactory.guideApplyExamDBDataService()
            .queryGuideApplyExamByAccount(this.owner.examAccountId());
    if(CollectionUtils.isEmpty(guideApplyExamModels)){
      return null;
    }
    List<GuideApplyExamModel> historyApplyRecords = guideApplyExamModels.stream()
          .filter(o -> o.getApplySubject().contains(examInfoByDeptId.getParentDeptId()))
          .filter(o->o.getExamIsPassed().equals(ExamIsPassedEnum.PASSED.getCode()))
          .collect(Collectors.toList());
    if(historyApplyRecords.size()>0){
      return historyApplyRecords.get(0);
    }
    return guideApplyExamModels.stream()
        .filter(o->o.getApplySubject().equals(examInfoByDeptId.getDeptId())).findFirst().orElse(null);
  }

  protected void insertApplyExamRecord() {
    GuideApplyExamPO guideApplyExamPO = convert2GuideApplyExamPO();
    guideApplyExamPO.setDatachangeLasttime(
        Timestamp.valueOf(LocalDateTime.now(ZoneId.of("Asia/Shanghai"))));
    this.domainBeanFactory.guideApplyExamDBDataService().insertGuideApplyExam(guideApplyExamPO);
  }

  protected void saveApplyExamRecord(GuideApplyExamModel guideApplyExamModel) {
    if(guideApplyExamModel == null){
      guideApplyExamModel = this.domainBeanFactory.guideApplyExamDBDataService()
          .queryGuideApplyExamByAccountAndSubject(this.owner.examAccountId(),
              this.owner.applySubject());
    }
    boolean isUpdate = false;
    GuideApplyExamPO guideApplyExamPO;
    if (Objects.isNull(guideApplyExamModel)) {
      guideApplyExamPO = convert2GuideApplyExamPO();
    } else {
      guideApplyExamPO = convert2GuideApplyExamPO(guideApplyExamModel);
      isUpdate = true;
    }
    guideApplyExamPO.setDatachangeLasttime(
        Timestamp.valueOf(LocalDateTime.now(ZoneId.of("Asia/Shanghai"))));
    if (isUpdate) {
      this.domainBeanFactory.guideApplyExamDBDataService().updateGuideApplyExam(guideApplyExamPO);
    } else {
      this.domainBeanFactory.guideApplyExamDBDataService().insertGuideApplyExam(guideApplyExamPO);
    }
  }

  private GuideApplyExamPO convert2GuideApplyExamPO() {
    GuideApplyExamPO po = new GuideApplyExamPO();
    po.setGuideId(this.owner.guideId());
    po.setExamAccountId(this.owner.examAccountId());
    po.setGuideName(this.owner.guideName());
    po.setAccount(this.owner.account());
    po.setApplySubject(this.owner.applySubject());
    po.setSubjectName(this.owner.subjectName());
    po.setApplyTime(Timestamp.valueOf(LocalDateTime.now(ZoneId.of("Asia/Shanghai"))));
    po.setTimeZone(new BigDecimal("8"));
    return po;
  }

  private GuideApplyExamPO convert2GuideApplyExamPO(GuideApplyExamModel model) {
    GuideApplyExamPO po = new GuideApplyExamPO();
    po.setId(model.getId());
    //申请结果，调用结果，考试结果，通过了则不再修改
    po.setApplyResult(model.getApplyResult()==ExamApplyResultEnum.APPLY_SUCCESS.getCode() ? model.getApplyResult():this.owner.applyResult().getCode());
    po.setCallExamSuccess(model.getCallExamSuccess()==CallExamSuccessEnum.CALL_SUCCESS.getCode() ? model.getCallExamSuccess():this.owner.callExamSuccess().getCode());
    po.setExamIsPassed(model.getExamIsPassed()==ExamIsPassedEnum.PASSED.getCode() ? model.getExamIsPassed():this.owner.examIsPassed().getCode());
    return po;
  }

}
