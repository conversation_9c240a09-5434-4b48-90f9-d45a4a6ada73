package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel;

import java.util.List;

public interface RightsDBDataService {
    List<RightsModel> queryDriverRights(long driverId, String monthIdx);

    List<RightsRecordModel> queryDriverRecords(long driverId, String useStartDate, String useEndDate, List<Integer> rightsTypes);

    LevelModel queryDriverLevel(long driverId, String monthIdx);

    void saveRightsRecords(DrivRightsRecordPO recordPO);

    void updateDriverRights(DrivRightsPO rightsPO);

    List<RightsRecordModel> queryDriverRecordsById(long id);

    List<RightsRecordModel> queryDriverRecordsBySupplyOrderId(long driverId, int rightType, String supplyOrderId);

    List<RightsRecordModel> querDriverRecordsByDriver(Integer rightsType, Long id);
}
