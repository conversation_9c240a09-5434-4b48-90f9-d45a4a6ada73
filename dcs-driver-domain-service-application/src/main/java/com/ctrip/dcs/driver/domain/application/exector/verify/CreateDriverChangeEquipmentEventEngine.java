package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsDrvLoginInformationDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsVerifyEventDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.TmsDrvLoginInformationPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.TmsVerifyEventPO;
import com.ctrip.dcs.driver.domain.verify.CreateDriverChangeEquipmentEventRequestType;
import com.ctrip.dcs.driver.domain.verify.CreateDriverChangeEquipmentEventResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 换设备登录事件
 */
@Component
public class CreateDriverChangeEquipmentEventEngine extends ShoppingExecutor<CreateDriverChangeEquipmentEventRequestType, CreateDriverChangeEquipmentEventResponseType>
        implements Validator<CreateDriverChangeEquipmentEventRequestType> {

    @Autowired
    private TmsVerifyEventDao tmsVerifyEventDao;
    @Autowired
    private TmsDrvLoginInformationDao tmsDrvLoginInformationDao;

    private static final String CONSUMER_NAME = "100038374";

    @Override
    public void validate(AbstractValidator<CreateDriverChangeEquipmentEventRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final CreateDriverChangeEquipmentEventEngine owner;
        private final CreateDriverChangeEquipmentEventResponseType response;
        private final CreateDriverChangeEquipmentEventRequestType request;

        private Executor(CreateDriverChangeEquipmentEventRequestType request, CreateDriverChangeEquipmentEventResponseType response, CreateDriverChangeEquipmentEventEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;
        }

        @Override
        protected boolean validate() {
            return true;
        }

        @Override
        protected void buildResponse() {
            this.checkDriverVeriifyResult();
            if (ObjectUtils.defaultIfNull(request.isIsSaveLoginRecord(), true)) {
                //同步司机登录信息
                this.owner.tmsDrvLoginInformationDao.insert(buildLoginInfoPO());
            }
        }

        /**
         * 1、海外司机不新增事件
         * 2、tms_drv_login_information 无数据--->5
         * 3、tms_drv_login_information 数据和本次登录相同，返回--->5
         * 4、3不同，查询事件是否存在；不存在插入
         * 5、同步司机登录信息
         * */
        private void checkDriverVeriifyResult() {
            if(ObjectUtils.defaultIfNull(request.internalScope, 0) != 0) {
                return;
            }

            TmsDrvLoginInformationPO loginInfo = this.owner.tmsDrvLoginInformationDao.findOne(request.driverId);
            if(Objects.isNull(loginInfo)){
                return;
            }

            //和前一次设备一致
            if (StringUtils.isNotEmpty(loginInfo.getDrvImei()) && Objects.equals(request.driverImei, loginInfo.getDrvImei())) {
                return;
            }

            List<TmsVerifyEventPO> verifyEvents = this.owner.tmsVerifyEventDao.findNeedVerifyEvents(request.driverId);
            if(CollectionUtils.isEmpty(verifyEvents) || (Objects.nonNull(verifyEvents.get(0)) && ObjectUtils.defaultIfNull(verifyEvents.get(0).getVerifyFlag(), 0) != 2)) {
                //司机变更设置，或者事件状态不是未验证
                this.owner.tmsVerifyEventDao.insert(this.buildTmsVerifyEvent());
            }
        }

        /**
         * 验证事件
         * */
        private TmsVerifyEventPO buildTmsVerifyEvent(){
            TmsVerifyEventPO tmsVerifyEventPO = new TmsVerifyEventPO();
            tmsVerifyEventPO.setVerifySourceId(request.driverId);
            tmsVerifyEventPO.setVerifySourceType(1);
            tmsVerifyEventPO.setVerifyType(1);
            tmsVerifyEventPO.setVerifyFlag(2);
            tmsVerifyEventPO.setVerifyStatus(1);
            tmsVerifyEventPO.setVerifyResultCode(Strings.EMPTY);
            tmsVerifyEventPO.setDrvImei(request.driverImei);
            tmsVerifyEventPO.setVerifyReasonStatus(1);
            tmsVerifyEventPO.setVerifyStartTime(Timestamp.valueOf(LocalDateTime.now()));
            tmsVerifyEventPO.setVerifyEndTime(Timestamp.valueOf(LocalDateTime.now()));
            tmsVerifyEventPO.setVerifyNoticeTime(Timestamp.valueOf(LocalDateTime.now()));
            tmsVerifyEventPO.setCreateUser(CONSUMER_NAME);
            tmsVerifyEventPO.setModifyUser(CONSUMER_NAME);
            return tmsVerifyEventPO;
        }

        /**
         * 登录记录
         * */
        private TmsDrvLoginInformationPO buildLoginInfoPO(){
            TmsDrvLoginInformationPO informationPO = new TmsDrvLoginInformationPO();
            informationPO.setDrvId(request.driverId);
            informationPO.setDrvLoginTime(Timestamp.valueOf(LocalDateTime.now()));
            informationPO.setDrvLocLat(request.driverLocLat == null ? 0D : request.driverLocLat);
            informationPO.setDrvLocLong(request.driverLocLong == null ? 0D : request.driverLocLong);
            informationPO.setDrvLocCsys(request.driverLocCsys);
            informationPO.setCreateUser(CONSUMER_NAME);
            informationPO.setModifyUser(CONSUMER_NAME);
            informationPO.setDrvImei(request.driverImei);
            return informationPO;
        }
    }

    @Override
    public CreateDriverChangeEquipmentEventResponseType execute(CreateDriverChangeEquipmentEventRequestType request) {
        CreateDriverChangeEquipmentEventResponseType response = new CreateDriverChangeEquipmentEventResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public CreateDriverChangeEquipmentEventResponseType onException(CreateDriverChangeEquipmentEventRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("CreateDriverChangeEquipmentEvent error", ex);
        return super.onException(req, ex);
    }
}
