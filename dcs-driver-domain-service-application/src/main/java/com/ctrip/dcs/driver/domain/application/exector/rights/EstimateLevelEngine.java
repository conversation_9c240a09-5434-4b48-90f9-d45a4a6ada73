package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.rights.EstimateLevelRequestType;
import com.ctrip.dcs.driver.domain.rights.EstimateLevelResponseType;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.driver.value.rights.LevelObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class EstimateLevelEngine extends ShoppingExecutor<EstimateLevelRequestType, EstimateLevelResponseType>
        implements Validator<EstimateLevelRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public EstimateLevelResponseType execute(EstimateLevelRequestType request) {
        EstimateLevelResponseType response = new EstimateLevelResponseType();
        EstimateLevelEngine.Executor executor = new EstimateLevelEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<EstimateLevelRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("cityId").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final EstimateLevelEngine owner;
        private final EstimateLevelResponseType response;
        private final EstimateLevelRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(EstimateLevelRequestType request, EstimateLevelResponseType response, EstimateLevelEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;

            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory, request.driverId, request.cityId);
        }

        @Override
        protected void buildResponse() {
            LevelObject levelObject = driverRightsObject.estimateLevel();
            if(Objects.isNull(levelObject) || Objects.isNull(levelObject.drivLevel())){
                return;
            }
            response.id = levelObject.levelConfigId();
            response.driverLevel = levelObject.drivLevel().getCode();
            response.levelName = levelObject.levelName();
            response.driverPoint = levelObject.drivPoint();
            response.activityPoint = levelObject.drivActivity();
            response.safePoint = levelObject.driverSafePoint();
            response.rank = levelObject.drivRank();
            response.totalRank = levelObject.drivTotalRank();
            response.rankGap = levelObject.rankGap();
            response.driverPointLow = levelObject.drivPointLow();
            response.activityPointLow =levelObject.drivActivityLow();
            response.safePointLow =levelObject.driverSafePointLow();
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(driverRightsObject);
        }
    }
}
