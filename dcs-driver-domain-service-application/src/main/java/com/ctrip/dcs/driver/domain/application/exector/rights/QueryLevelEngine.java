package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.rights.DriverLevelDTO;
import com.ctrip.dcs.driver.domain.rights.QueryLevelRequestType;
import com.ctrip.dcs.driver.domain.rights.QueryLevelResponseType;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.driver.value.rights.LevelObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class QueryLevelEngine extends ShoppingExecutor<QueryLevelRequestType, QueryLevelResponseType>
        implements Validator<QueryLevelRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public QueryLevelResponseType execute(QueryLevelRequestType request) {
        QueryLevelResponseType response = new QueryLevelResponseType();
        QueryLevelEngine.Executor executor = new QueryLevelEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryLevelRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("date").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryLevelEngine owner;
        private final QueryLevelResponseType response;
        private final QueryLevelRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(QueryLevelRequestType request, QueryLevelResponseType response, QueryLevelEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;

            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory, request.driverId, request.date);
        }

        @Override
        protected void buildResponse() {
            LevelObject levelObject = driverRightsObject.queryDriverLevel();
            if(Objects.isNull(levelObject) || Objects.isNull(levelObject.drivLevel())){
                return;
            }
            response.driverLevel=convert2DriverLevelDTO(levelObject);
        }

        private DriverLevelDTO convert2DriverLevelDTO(LevelObject levelObject) {
            DriverLevelDTO driverLevelDTO = new DriverLevelDTO();
            driverLevelDTO.setLevelConfigId(levelObject.levelConfigId());
            driverLevelDTO.setDriverId(levelObject.drivId());
            driverLevelDTO.setDate(levelObject.monthIdx());
            driverLevelDTO.setCityId(levelObject.cityId());
            driverLevelDTO.setDriverLevel(levelObject.drivLevel().getCode());
            driverLevelDTO.setLevelName(levelObject.levelName());
            return driverLevelDTO;
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(driverRightsObject);
        }
    }
}
