package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.schedule.DriverLevelScheduleLogic;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.igt.framework.common.metrics.MetricsFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class DrivPointFinishListener implements MessageListener {

    public static final Logger logger = LoggerFactory.getLogger(DrivPointFinishListener.class);

    public static final String CONSUMER_GROUP = "100038374";
    public static final String DRIVER_LEVEL_JOB_FAIL = "driver.level.job.fail";

    @Autowired
    DriverLevelScheduleLogic driverLevelScheduleLogic;
    @Autowired
    private GeoGateway geoGateway;


    @Override
    @QmqConsumer(prefix = "dcs.driver.calculate.month.active.point.notification.qmq", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        // 每月1号分数快照
        try {
            // 所有支持司机等级的城市
            List<Long> allCityList = driverLevelScheduleLogic.getCity();

            String cityIdsStr = message.getStringProperty("cityIdList");
            List<Long> cityIdList = Lists.newArrayList();
            if (StringUtils.isNotBlank(cityIdsStr)) {
                // 指定了城市id（新流程），则筛选指定城市id
                for (Long cityId : Arrays.stream(cityIdsStr.split(",")).map(Long::valueOf).collect(Collectors.toList())) {
                    if (allCityList.contains(cityId)) {
                        cityIdList.add(cityId);// 在配置中
                    } else {
                        logger.warn("city id not support level " + cityId);// 不在配置中
                    }
                }
            } else {
                cityIdList = allCityList;
            }

            if (CollectionUtils.isEmpty(cityIdList)) {
                return;
            }

            // 计算分数、等级快照
            driverLevelScheduleLogic.doSnapShot(cityIdList);

            // 计算司机等级
            LocalDateTime localCurrentTime = geoGateway.getLocalCurrentTime(cityIdList.get(0));
            driverLevelScheduleLogic.doUpdateLevel(cityIdList, logger, LocalDateTimeUtils.firstDayOfMonthStr(localCurrentTime));
        } catch (Exception e) {
            logger.error("driver level job fail");
            MetricsFactory.getMetricRegistry().resetCounter(DRIVER_LEVEL_JOB_FAIL).inc();
        }
    }
}
