package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.exam.ExamInfoDTO;
import com.ctrip.dcs.driver.domain.exam.QueryValidityExamRequestType;
import com.ctrip.dcs.driver.domain.exam.QueryValidityExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class QueryValidityExamEngine
    extends ShoppingExecutor<QueryValidityExamRequestType, QueryValidityExamResponseType> {

  @Autowired
  private DomainBeanFactory domainBeanFactory;

  @Autowired
  CommonMultipleLanguages commonMultipleLanguages;

  @Override
  public QueryValidityExamResponseType execute(
      QueryValidityExamRequestType queryValidityExamRequestType) {
    QueryValidityExamResponseType response = new QueryValidityExamResponseType();
    List<FromGuideExamInfo> fromGuideExamInfos =
        this.domainBeanFactory.guideExamConfig().queryValidityExam();
    if (CollectionUtils.isNotEmpty(fromGuideExamInfos)) {
      response.examInfoList = fromGuideExamInfos.stream().map(fromGuideExamInfo -> {
        ExamInfoDTO examInfoDTO = new ExamInfoDTO();
        examInfoDTO.setDeptId(fromGuideExamInfo.getDeptId());
        examInfoDTO.setDeptName(commonMultipleLanguages.getContent(fromGuideExamInfo.getDeptSharkey()));
        return examInfoDTO;
      }).collect(Collectors.toList());
    }
    return ServiceResponseUtils.success(response);
  }
}
