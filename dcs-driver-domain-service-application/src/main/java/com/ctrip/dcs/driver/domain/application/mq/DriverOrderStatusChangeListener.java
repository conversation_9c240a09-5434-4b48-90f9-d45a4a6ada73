package com.ctrip.dcs.driver.domain.application.mq;

import cn.hutool.core.util.ObjectUtil;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfig;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO;
import com.ctrip.dcs.driver.message.dto.*;
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageProducer;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
public class DriverOrderStatusChangeListener {

    @Autowired
    DriverTaskAdapter driverTaskAdapter;

    @Autowired
    MessageProducer messageProducer;

    @Autowired
    BusinessConfig businessConfig;

    public static final Logger LOGGER = LoggerFactory.getLogger(DriverOrderStatusChangeListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    /**
     * 订单取消时，对应任务状态
     * 如果任务不是完成、过期、取消，则更新为 取消，
     * 发送模板4084
     * */
    @QmqConsumer(prefix = "dcs.self.driver.order.canceled", consumerGroup = CONSUMER_GROUP, idempotentChecker = "redisIdempotent")
    public void onDriverOrderCanceled(Message message) {
        DriverTaskRecordDO driverTaskRecord = getValidDriverTaskInfo(message);
        if(Objects.nonNull(driverTaskRecord)) {
            //出车检查任务取消
            driverTaskAdapter.updateTaskStatus(driverTaskRecord.getTaskId(), DriverTaskStatusEnum.CANCEL);
            //发送模板4084
            this.pushTaskStatusMessage("4084", driverTaskRecord);
        }
    }

    /**
     * 有创建的任务，如果任务状态不是完成、过期、取消，
     * 则更新任务完成时间，
     * 同时发送 延时（+2h）qmq消息：
     * dcs.driver.terminal.carinspection.notice
     * */
    @QmqConsumer(prefix = "dcs.self.driver.order.finished", consumerGroup = CONSUMER_GROUP, idempotentChecker = "redisIdempotent")
    public void onDriverOrderFinished(Message message) {
        DriverTaskRecordDO driverTaskRecord = getValidDriverTaskInfo(message);
        if(Objects.nonNull(driverTaskRecord)) {
            int expireMinutes = ObjectUtil.defaultIfNull(businessConfig.getOrderFinishValidMinutes(), 120);
            //更新任务完成时间
            driverTaskAdapter.updateTaskValidEndTime(driverTaskRecord.getTaskId(), expireMinutes);

            //发送 延时（+2h）qmq消息
            Message noticeMessage = messageProducer.generateMessage(Constants.DRIVER_CARINSPECTION_NOTICE);
            noticeMessage.setDelayTime(expireMinutes, TimeUnit.MINUTES);

            noticeMessage.setProperty("driverOrderId", driverTaskRecord.getDriverOrderId());
            noticeMessage.setProperty("customerOrderId", driverTaskRecord.getCustomerOrderId());
            noticeMessage.setProperty("driverId", String.valueOf(driverTaskRecord.getDriverId()));
            noticeMessage.setProperty("taskId", driverTaskRecord.getTaskId());
            LOGGER.info("DriverOrderStatusChange",
                    String.format("%s:{%s}", Constants.DRIVER_CARINSPECTION_NOTICE, noticeMessage));
            messageProducer.sendMessage(noticeMessage);
        }
    }

    /**
     * 如果任务状态不是完成、过期、取消，则更新为 过期，
     * 发送模板4085
     * */
    @QmqConsumer(prefix = Constants.DRIVER_CARINSPECTION_NOTICE, consumerGroup = CONSUMER_GROUP, idempotentChecker = "redisIdempotent")
    public void onCarInspectionNotice(Message message) {
        DriverTaskRecordDO driverTaskRecord = getValidDriverTaskInfo(message);
        if(Objects.nonNull(driverTaskRecord)) {
            //出车检查任务未完成(过期）
            driverTaskAdapter.updateTaskStatus(driverTaskRecord.getTaskId(), DriverTaskStatusEnum.EXPIRE);
            //发送模板4085
            this.pushTaskStatusMessage("4085", driverTaskRecord);
        }
    }

    /**
     * 查询有效的出车检查任务
     * */
    private DriverTaskRecordDO getValidDriverTaskInfo(Message message) {
        if(Objects.isNull(message)){
            return null;
        }

        long driverId = message.getLongProperty("driverId");
        String driverOrderId = message.getStringProperty("driverOrderId");
        if (driverId == 0 || Objects.isNull(driverOrderId)) {
            return null;
        }

        //查询任务
        DriverTaskRecordDO driverTaskRecord = driverTaskAdapter.queryDriverTaskByDriverOrderId(driverId, driverOrderId);
        if (Objects.nonNull(driverTaskRecord)) {
            if (driverTaskRecord.getTaskStatus().isActive()) {
                return driverTaskRecord;
            }
        }
        return null;
    }

    /**
     * 发送检查任务消息
     * */
    private void pushTaskStatusMessage(String templateId, DriverTaskRecordDO driverTaskRecord) {
        //发送模板消息
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setDriverIds(Lists.newArrayList(driverTaskRecord.getDriverId()));
        pushMessageDTO.setTemplateId(templateId);
        Map<String, String> sharkValues = Maps.newHashMap();
        pushMessageDTO.setSharkValues(sharkValues);
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("orderId", driverTaskRecord.getDriverOrderId());
        dataMap.put("customeroid", driverTaskRecord.getCustomerOrderId());
        pushMessageDTO.setData(dataMap);

        String data = JsonUtil.toString(pushMessageDTO);
        Map<String, Object> qmqContent = Maps.newHashMap();
        qmqContent.put("data", data);
        QmqProducerProvider.sendMessage(Constants.DRIVER_PUSH_MESSAGE_TOPIC, qmqContent);
        LOGGER.info("DriverOrderStatusChange", String.format("%s:{%s}", templateId, JsonUtil.toString(qmqContent)));
    }
}
