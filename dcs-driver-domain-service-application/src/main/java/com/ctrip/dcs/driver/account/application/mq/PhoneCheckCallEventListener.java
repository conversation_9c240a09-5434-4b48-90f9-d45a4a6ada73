package com.ctrip.dcs.driver.account.application.mq;

import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig;
import com.ctrip.dcs.driver.account.domain.constant.PhoneCheckState;
import com.ctrip.dcs.driver.account.domain.constant.PhoneCheckTaskState;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.dianping.cat.Cat;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
public class PhoneCheckCallEventListener {

    Log log = Log.getInstance(PhoneCheckCallEventListener.class);

    @Resource
    PhoneCheckConfig config;
    @Resource
    PhoneCheckTaskTable taskTable;
    @Resource
    PhoneCheckTable checkTable;

    @QmqConsumer(prefix = "bbz.implus.outbound.result.data.to.domesticcar", consumerGroup = "*********")
    public void onMessage(Message message) {
        Call call = JsonUtil.fromString(message.getStringProperty("OutCallResultData"), Call.class);
        if (Objects.isNull(call)) {
            return;
        }
        if (!StringUtils.equalsIgnoreCase(call.getOriginalID(), config.getSkillGroupId())
                && !StringUtils.equalsIgnoreCase(call.getOriginalID(), config.getVoiceSkillGroupId())) {
            return;
        }
        PhoneCheckTaskEntity task = taskTable.queryByPk(Long.valueOf(call.getBatchId()));
        if (Objects.isNull(task)) {
            return;
        }
        if (PhoneCheckTaskState.Started.equals(task.getTaskState()) == false) {
            return;
        }
        log.info("PhoneCheckCallEventListener consumeMsg", String.format("taskId=%s,callResult=%s,hangupCode=%s",
                call.getBatchId(), call.getCallResult(), call.getHangupCode()));
        // 更新电话状态
        task.setTaskResult(JsonUtil.toString(call));
        task.setCallAlertSeconds(call.getTimeToAlert());
        task.setCallDurationSeconds(call.getTimeToAnswer());
        task.setCallReturnCode(call.getReturnCode());
        task.setCallReturnReason(call.getReturnReason());
        task.setCallHangupCode(call.getHangupCode());
        task.setCallHangupReason(call.getHangupReason());
        List<Call.Result> list = JsonUtil.fromString(call.getResult(), new TypeToken<List<Call.Result>>() {}.getType());
        Call.Result menu = Optional.ofNullable(list).orElse(Collections.emptyList()).stream().findFirst().orElseGet(Call.Result::new);
        task.setCallMenuCode(menu.getMenu());
        task.setCallMenuResult(menu.getDtmf());
        // 呼叫成功直接结束
        if (StringUtils.equalsIgnoreCase(call.getCallResult(), "COMPLETED")) {
            // 更新任务表
            Cat.logEvent(CatEventType.IVR_CALL_LISTENER_RESULT, call.getHangupCode());
            task.setTaskState(PhoneCheckTaskState.Succeed);
            task.setFinishTime(LocalDateTime.now());
            taskTable.update(task);
            // 更新主状态表
            PhoneCheckEntity check = checkTable.queryOne(PhoneCheckEntity.builder().phoneNumber(task.getPhoneNumber()).build());
            if (Objects.nonNull(check)) {
                check.setDriverId(task.getDriverId());
                check.setPhonePrefix(task.getPhonePrefix());
                check.setPhoneNumber(task.getPhoneNumber());
                check.setCheckType(task.getTaskType());
                check.setCheckState(PhoneCheckState.Succeed);
                checkTable.update(check);
            } else {
                check = new PhoneCheckEntity();
                check.setDriverId(task.getDriverId());
                check.setPhonePrefix(task.getPhonePrefix());
                check.setPhoneNumber(task.getPhoneNumber());
                check.setCheckType(task.getTaskType());
                check.setCheckState(PhoneCheckState.Succeed);
                checkTable.insert(check);
            }
            return;
        }
        // 超过重试次数  结束本次任务
        if (call.getExecuteCount() >= call.getMaxRetryCount()) {
            Cat.logEvent(CatEventType.IVR_CALL_LISTENER_RESULT, call.getHangupCode(), "1", "");
            task.setTaskState(PhoneCheckTaskState.Failed);
            task.setFinishTime(LocalDateTime.now());
            taskTable.update(task);
        }
    }

    @Data
    protected static class Call {
        String batchId;
        String taskUniqueId;
        String originalID;
        String aNI;
        String dNIS;
        String callResult;
        String returnCode;
        String returnReason;
        String hangupCode;
        String hangupReason;
        Integer timeToAlert;
        Integer timeToAnswer;
        Integer executeCount;
        Integer maxRetryCount;
        String recordFileName;
        String result;

        @Data
        protected static class Result {
            String menu;
            String dtmf;
        }

    }

}