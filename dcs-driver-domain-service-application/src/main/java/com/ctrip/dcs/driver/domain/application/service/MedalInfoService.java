package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;

import java.util.List;

/**
 * 聚合勋章有关的查询、更新
 * */
public interface MedalInfoService {

    List<MedalBasicInfoModel> queryBasicMedalInfo();

    void updateMedalInfo();

    GoldMedalDriverModel getGoldMedalDriverInfo(long driverId);
}
