package com.ctrip.dcs.driver.domain.application.exector.account;

import com.ctrip.dcs.driver.value.account.AccountUidInfo;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.account.infrastructure.value.RegisterNewAccountV1Param;
import com.ctrip.dcs.driver.domain.account.RegisterAccountRequestType;
import com.ctrip.dcs.driver.domain.account.RegisterAccountResponseType;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Description
 */
@Component
public class RegisterAccountEngine extends ShoppingExecutor<RegisterAccountRequestType, RegisterAccountResponseType> implements Validator<RegisterAccountRequestType> {


    @Autowired
    CityRepository cityRepository;
    @Autowired
    AccountService accountService;

    @Override
    public RegisterAccountResponseType execute(RegisterAccountRequestType request) {
        RegisterAccountResponseType response = new RegisterAccountResponseType();

        RegisterNewAccountV1Param accountBaseDto = buildParam(request);
        AccountUidInfo accountUidInfo = accountService.registerV1(accountBaseDto);
        if(accountUidInfo!=null){
            response.setUid(accountUidInfo.getUid());
        }else {
            Cat.logEvent(CatEventType.UPDATE_RESULT, "RegisterAccount_None");
        }
        return ServiceResponseUtils.success(response);
    }



    private RegisterNewAccountV1Param buildParam(RegisterAccountRequestType request) {
        RegisterNewAccountV1Param accountBaseDto = new RegisterNewAccountV1Param();
        accountBaseDto.setSource(request.getSource());
        accountBaseDto.setSourceId(request.getSourceId());
        accountBaseDto.setAccountType(request.getAccountType());
        accountBaseDto.setBindAccountType(request.getBindAccountType());
        accountBaseDto.setCountryCode(request.getCountryCode());
        accountBaseDto.setPhoneNumber(request.getPhoneNumber());
        accountBaseDto.setEmail(request.getEmail());
        accountBaseDto.setOperator("SYSTEM");
        accountBaseDto.setCityId(request.getCityId());
        if (request.getCityId() != null) {
            City city = cityRepository.findOne(request.getCityId());
            accountBaseDto.setOversea(BooleanUtils.isFalse(city.isChineseMainland()));
        }
        return accountBaseDto;
    }

    @Override
    public void validate(AbstractValidator<RegisterAccountRequestType> validator) {
        validator.ruleFor("accountType").notNull().notEmpty();
        validator.ruleFor("source").notNull().notEmpty();
        validator.ruleFor("sourceId").notNull().notEmpty();
    }
}
