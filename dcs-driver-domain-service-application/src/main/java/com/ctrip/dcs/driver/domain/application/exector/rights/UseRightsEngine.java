package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.application.mq.WorkBenchLogProducer;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.domain.rights.UseRightsRequestType;
import com.ctrip.dcs.driver.domain.rights.UseRightsResponseType;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class UseRightsEngine extends ShoppingExecutor<UseRightsRequestType, UseRightsResponseType> implements Validator<UseRightsRequestType> {

    @Autowired
    DomainBeanFactory domainBeanFactory;
    @Autowired
    LockService lockService;

    @Override
    public UseRightsResponseType execute(UseRightsRequestType request) {
        UseRightsResponseType response = new UseRightsResponseType();
        UseRightsEngine.Executor executor = new UseRightsEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.fail(response, "510", "missing parameter");
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<UseRightsRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("rightsType").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final UseRightsEngine owner;
        private final UseRightsResponseType response;
        private final UseRightsRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(UseRightsRequestType request, UseRightsResponseType response, UseRightsEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;

            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory, request.driverId, "");
        }

        @Override
        protected void buildResponse() {
            UseRightsCondition useRightsCondition = buildUseRightsCondition(request);
            Boolean isSuccess = this.owner.lockService.executeInLock(String.format("dcs_driver_use_rights:%s_%s", useRightsCondition.getDriverId(), useRightsCondition.getRightsType()), 1000, () -> this.driverRightsObject.useRights(useRightsCondition));
            if(isSuccess){
                // 发消息推送给客服
                try {
                    WorkBenchLogProducer.sendMessage(WorkBenchLogProducer.buildMessase(useRightsCondition));
                } catch (Exception e) {
                    CommonLogger.INSTANCE.error(e);
                }
            }
        }

        private UseRightsCondition buildUseRightsCondition(UseRightsRequestType request) {
            UseRightsCondition useRightsCondition = UseRightsCondition.builder().driverId(request.getDriverId()).rightsType(request.getRightsType()).userOrderId(request.getUserOrderId()).purchaseOrderId(request.getPurchaseOrderId()).supplyOrderId(request.getSupplyOrderId()).punishOrderId(request.getPunishOrderId()).money(request.getMoney()).build();
            return useRightsCondition;
        }

        @Override
        protected boolean validate() {
            switch (RightsTypeEnum.getByCode(request.rightsType)){
                case RIGHTS_TYPE_CITYKING:
                case RIGHTS_TYPE_WITHDRAW:
                    return true;
                default:
                    return !Strings.isBlank(request.getUserOrderId()) && !Strings.isBlank(request.getSupplyOrderId());
            }
        }
    }

}
