package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByIdCardRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByIdCardResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@ServiceLogTag(tagKeys= {"idCard"})
public class QueryAccountByIdCardEngine extends ShoppingExecutor<QueryAccountByIdCardRequestType, QueryAccountByIdCardResponseType> implements Validator<QueryAccountByIdCardRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public QueryAccountByIdCardResponseType execute(QueryAccountByIdCardRequestType request) {


        QueryAccountByIdCardResponseType resp = new QueryAccountByIdCardResponseType();
        resp.setAccountDetailList(Lists.newArrayList());
        List<AccountInfoDTO> accountList = accountService.getAccountInfoByIdCard(request.getIdCard());
        if (CollectionUtils.isNotEmpty(accountList)) {
            resp.setAccountDetailList(accountList.stream().map(AccountDetailConvert::convert).collect(Collectors.toList()));
        }
        Cat.logEvent(CatEventType.QUERY_BY_ID_CARD_RESULT, accountList.size() + "");
        DriverMetricUtil.batchMetricUdlIsValid(resp.getAccountDetailList());
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public Boolean isEmptyResult(QueryAccountByIdCardResponseType resp) {
        return !(resp!=null&&CollectionUtils.isNotEmpty(resp.getAccountDetailList()));
    }
}
