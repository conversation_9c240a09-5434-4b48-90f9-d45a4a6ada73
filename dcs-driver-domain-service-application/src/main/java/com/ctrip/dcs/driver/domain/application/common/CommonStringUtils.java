package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.shopping.utils.ShoppingArrayUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;

import java.util.List;

public final class CommonStringUtils {

  public static String[] splite(String content, int size) {

    if (StringUtils.isEmpty(content)) {
      return ArrayUtils.EMPTY_STRING_ARRAY;
    }

    int beginIndex = 0;
    int endIndex = 0;

    List<String> s = Lists.newArrayList();

    while (beginIndex < content.length()) {
      endIndex += size;
      endIndex = Math.min(endIndex, content.length());
      s.add(content.substring(beginIndex, endIndex));
      beginIndex = endIndex;
    }

    return ShoppingArrayUtils.toArray(s, String.class);
  }

  /**
   * 2022-07-28  20220728
   * 2022-07     202207
   * */
  public static int convertTimeToInt(String timeValue) {
    if(StringUtils.isBlank(timeValue)) {
      return 0;
    }
    return Integer.parseInt(StringUtils.replace(timeValue,"-", Strings.EMPTY));
  }

  /**
   * 金牌司机
   * */
  public final static String GOLD_MEDAL = "goldMedal";
}
