package com.ctrip.dcs.driver.domain.application.mq;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.driver.account.application.schedule.PhoneCheckTaskGenerateScheduler;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverLostContactEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverLostContactTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.DriverOrderServiceProxy;
import com.ctrip.dcs.driver.domain.application.dto.SpecialQuestionDTO;
import com.ctrip.dcs.driver.domain.application.mq.dto.Struct;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.dcs.driver.trip.university.infrastructure.utils.JsonUtil;
import com.ctrip.dcs.self.order.query.api.QueryOrderListRequestType;
import com.ctrip.dcs.self.order.query.api.QueryOrderListResponseType;
import com.ctrip.dcs.self.order.query.dto.BaseDetail;
import com.ctrip.dcs.self.order.query.dto.DataSwitch;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.fasterxml.jackson.core.type.TypeReference;

import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

@Component
public class DriverNegativeQuestionnaireListener implements MessageListener {
    public static final Logger LOGGER = LoggerFactory.getLogger(DriverInfoModifyListener.class);

    public static final String CONSUMER_GROUP = "100038374";
    @Autowired
    private DriverOrderServiceProxy driverOrderServiceProxy;

    @Autowired
    private PhoneCheckTaskGenerateScheduler phoneCheckTaskGenerateScheduler;

    @Autowired
    private DriverLostContactTable driverLostContactTable;

    @Autowired
    SystemQConfig systemQConfig;


    @Override
    @QmqConsumer(prefix = "dcs.question.driver.not.contact", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        String ivrSwitch = systemQConfig.getString("ivr_switch");
        if (StringUtils.equals(ivrSwitch, "false")) {
            return;
        }
        String data = message.getStringProperty("data");
        SpecialQuestionDTO specialQuestionDTO = JsonUtil.fromJson(data, new TypeReference<SpecialQuestionDTO>() {});
        doProcessOrder(specialQuestionDTO.getOrderId());
    }

    public void doProcessOrder(String orderId) {
        QueryOrderListRequestType requestType = new QueryOrderListRequestType();
        requestType.setUserOrderId(orderId);
        DataSwitch dataSwitch = new DataSwitch();
        dataSwitch.setBaseDetailSwitch(true);
        requestType.setDataSwitch(dataSwitch);
        QueryOrderListResponseType response = driverOrderServiceProxy.queryOrderList(requestType);
        if (response == null || CollectionUtils.isEmpty(response.getOrderList())) {
            LOGGER.warn("order is empty", orderId);
            return;
        }
        BaseDetail baseDetail = response.getOrderList().get(0).getBaseDetail();
        if (Objects.isNull(baseDetail)) {
            LOGGER.warn("baseDetail is null", orderId);
            return;
        }
        Long drvId = baseDetail.getDrvId();
        if (Objects.isNull(drvId) || Objects.equals(drvId, 0L)) {
            LOGGER.warn("drvId is null", orderId);
            return;
        }
        Struct parameter = new Struct();
        parameter.setDriverIds(Collections.singletonList(drvId));
        parameter.setValidateRecentOrder(false);
        parameter.setLastDriverId(0L);
        parameter.setPageSize(1);
        parameter.setCountLimit(1);
        parameter.setPlanTime(LocalDateTime.now());
        LOGGER.info("apply_driver", " {}, {}", orderId, com.ctrip.dcs.go.util.JsonUtil.toString(parameter));
        phoneCheckTaskGenerateScheduler.apply(parameter);
        // 入库统计
        DriverLostContactEntity driverLostContactEntity = new DriverLostContactEntity();
        driverLostContactEntity.setDrvId(drvId);
        driverLostContactEntity.setOrderId(orderId);
        driverLostContactTable.insert(driverLostContactEntity);
    }
}
