package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.common.CommonStringUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.honour.DirectorObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.honour.DriverMedalObjectImpl;
import com.ctrip.dcs.driver.domain.honour.*;
import com.ctrip.dcs.driver.value.honour.DirectorObject;
import com.ctrip.dcs.driver.value.honour.DriverMedalObject;
import com.ctrip.dcs.driver.value.honour.MedalObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询司机勋章
 */
@Component
public class QueryDriverMedalListEngine extends ShoppingExecutor<QueryDriverMedalListRequestType, QueryDriverMedalListResponseType>
        implements Validator<QueryDriverMedalListRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<QueryDriverMedalListRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("type").notNull().notEmpty().greaterThan(0);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverMedalListEngine owner;
        private final QueryDriverMedalListResponseType response;
        private final QueryDriverMedalListRequestType request;
        private DirectorObject directorObject;

        private Executor(QueryDriverMedalListRequestType request, QueryDriverMedalListResponseType response, QueryDriverMedalListEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            // init response
            response.medalCount = 0;
            response.medalList = new ArrayList<>();
            response.driverHeadImg = Strings.EMPTY;
            response.driverName = Strings.EMPTY;
            directorObject = new DirectorObjectImpl.Builder(this.request.driverId, 1, this.owner.domainBeanFactory).build();
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(directorObject) && this.directorObject.isSupportHonourMedal();
        }

        @Override
        protected void buildResponse() {
            response.driverHeadImg = this.directorObject.driverHeadImg();
            response.driverName = this.directorObject.name();

            // 开关关闭，直接返回空列表
            if(!this.owner.domainBeanFactory.honourFormConfig().isShowMedal()) {
                return;
            }

            DriverMedalObject driverMedalObject = new DriverMedalObjectImpl(this.owner.domainBeanFactory, this.request.driverId, this.request.type);
            response.medalCount = driverMedalObject.totalCount();

            //勋章列表/新勋章
            MedalObject[] medalObjects = driverMedalObject.queryMedalList();
            this.convertMedalList(medalObjects);
            if(Objects.nonNull(driverMedalObject.batchDataTime())){
                response.updateTime = driverMedalObject.batchDataTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
        }

        private void convertMedalList(MedalObject[] medalObjects) {
            if(Objects.isNull(medalObjects) || medalObjects.length == 0) {
                return;
            }
            Map<String, List<MedalObject>> medalObjectsGroupBy = Arrays.stream(medalObjects)
                    .collect(Collectors.groupingBy(MedalObject::typeCode));
            List<HonourMedalInfoDTO> medalInfoDTOList = new ArrayList<>();
            medalObjectsGroupBy.forEach((medalType, medalList) ->{
                if(CollectionUtils.isNotEmpty(medalList)) {
                    MedalObject medalObject0 = medalList.get(0);
                    HonourMedalInfoDTO honourMedalInfo = new HonourMedalInfoDTO();
                    honourMedalInfo.sortIndex = medalObject0.sortIndex();
                    honourMedalInfo.code = medalObject0.type().getTypeCode();
                    honourMedalInfo.hasGrade = medalObject0.type().isHasLevel();
                    honourMedalInfo.remindCount = 0;
                    honourMedalInfo.detail = this.buildDetails(medalList, honourMedalInfo);
                    if (Boolean.FALSE.equals(honourMedalInfo.hasGrade)) {
                        honourMedalInfo.remindCount = medalObject0.grade();
                        honourMedalInfo.detail.sort(Comparator.comparing(HonourMedalDetailInfoDTO::getSortIndex));
                    }
                    medalInfoDTOList.add(honourMedalInfo);
                }
            });
            response.medalList = medalInfoDTOList;
            response.medalList.sort(Comparator.comparing(HonourMedalInfoDTO::getSortIndex));
        }

        private List<HonourMedalDetailInfoDTO> buildDetails(List<MedalObject> medalList, HonourMedalInfoDTO honourMedalInfo) {
            List<HonourMedalDetailInfoDTO> detailInfoDTOS = new ArrayList<>();
            boolean isFirstInProgressNotice = false;
            for(MedalObject m : medalList) {
                HonourMedalDetailInfoDTO medalDetail = new HonourMedalDetailInfoDTO();
                medalDetail.code = m.code();
                medalDetail.sortIndex = 0;
                if(Boolean.FALSE.equals(honourMedalInfo.hasGrade)) {
                    medalDetail.sortIndex = m.type().getIndex();
                }
                medalDetail.grade = m.grade();
                medalDetail.image = m.icon();
                medalDetail.notStartRemind = m.notStartRemind();
                medalDetail.inProgressRemind = 0;
                // 只提示下一个勋章
                if(Boolean.TRUE.equals(medalDetail.notStartRemind)) {
                    isFirstInProgressNotice = true;
                }
                if(!isFirstInProgressNotice && m.inProgressRemind() > 0) {
                    medalDetail.inProgressRemind = m.inProgressRemind();
                    isFirstInProgressNotice = true;
                }
                medalDetail.position = m.position();
                medalDetail.total = m.total();
                medalDetail.extendInfo = null;
                if(CommonStringUtils.GOLD_MEDAL.equalsIgnoreCase(m.code())){
                    medalDetail.extendInfo = new MedalExtendInfoDTO();
                    medalDetail.extendInfo.exist = true;
                    medalDetail.extendInfo.type = 1;
                    medalDetail.extendInfo.totalize = m.totalCount();
                    medalDetail.extendInfo.topPercent = 0;
                }
                detailInfoDTOS.add(medalDetail);
            }
            return detailInfoDTOS;
        }
    }

    @Override
    public QueryDriverMedalListResponseType execute(QueryDriverMedalListRequestType request) {
        QueryDriverMedalListResponseType response = new QueryDriverMedalListResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverMedalListResponseType onException(QueryDriverMedalListRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverMedalList error", ex);
        return super.onException(req, ex);
    }
}
