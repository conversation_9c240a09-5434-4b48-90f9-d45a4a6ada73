package com.ctrip.dcs.driver.trip.university.application.service;

import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenRequestDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverForDisCardDTO;
import com.ctrip.igt.framework.common.result.Result;

/**
 * <AUTHOR>
 */
public interface TripUniversityService {

  /**
   * 同步司机信息到携程大学
   * @param driverDTO
   * @return
   */
  boolean syncDriverInfoToTripUniversity(TripUniversityDriverDTO driverDTO);

  /**
   * 获取携程大学token
   * @param requestDTO
   * @return
   */
  TripUniversityAccessTokenDTO getAccessToken(TripUniversityAccessTokenRequestDTO requestDTO);

  /**
   * 异步更新更新携程大学账号信息
   *
   * @param driverDTO
   */
  void updateTripUniversityAccountAsync(TripUniversityDriverDTO driverDTO);

  /**
   * 根据DRV_CODE查询DRV_ID
   *
   * @param drvCode
   * @return
   */
  Result<String> queryDrvIdByCode(String drvCode);

  Result<String> updateDrvPhoneAndEmailForDuplicateProcess(TripUniversityDriverForDisCardDTO dto);
}
