package com.ctrip.dcs.driver.account.application.mq;

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverTable;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService;
import com.ctrip.dcs.go.log.Log;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/13 13:42
 * @Description: 供应链司机入住审核消息监听处理异步发起IVR外呼
 */
@Component
public class DriverApproveMessageListener {
    Log log = Log.getInstance(DriverApproveMessageListener.class);

    @Resource
    private PhoneCheckService phoneCheckService;
    @Resource
    private DriverTable driverTable;

    /**
     * 编辑后审批通过，临时派遣司机转正
     * @param message
     */
    @QmqConsumer(prefix = Constants.DRIVER_APPROVE_TO_OFFICIAL_TOPIC, consumerGroup = "*********")
    public void driverApproveListener(Message message){
        try{
            Long sourceId = message.getLongProperty("sourceId");
            // 1.司机,2.车辆
            Integer sourceType = message.getIntProperty("sourceType");
            if(sourceId <= 0 || sourceType != 1) {
                return;
            }
            log.info("DriverApproveMessageListener","sourceId:{},sourceType:{}", sourceId, sourceType);

            DriverEntity driver = driverTable.queryByPk(sourceId);
            if (Objects.isNull(driver)) {
                log.warn("driverApproveListener", "driverId forDriver notExists {}", sourceId);
                return;
            }
            phoneCheckService.generatePhoneCheckTaskByDriver(driver, null);
        } catch (NeedRetryException e){
            log.error("driverApproveListener:{}", e.getLocalizedMessage(), e);
        }
    }
}
