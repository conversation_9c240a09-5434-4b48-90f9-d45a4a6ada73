package com.ctrip.dcs.driver.account.application.cache;

import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class AppPushMessageCache {

    private static final String DRIVER_VERSION_REDIS_KEY = "version_new:%s";

    @Autowired
    DirectorRedis directorRedis;

    /**
     * 查询司机客户端版本号，数据由司机端*********写入
     */
    public BigDecimal getDriverVersion(long driverId) {
        String versionStr = this.directorRedis.get(String.format(DRIVER_VERSION_REDIS_KEY, driverId));
        if (StringUtils.isNotBlank(versionStr)) {
            String[] versionArr = versionStr.split("#");
            if (versionArr.length == 2) {
                return new BigDecimal(versionArr[1]);
            }
        }
        return BigDecimal.ZERO;
    }
}
