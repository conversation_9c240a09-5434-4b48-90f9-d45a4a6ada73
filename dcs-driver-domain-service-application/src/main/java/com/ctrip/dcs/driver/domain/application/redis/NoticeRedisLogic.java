package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.application.redis.obj.NoticeContentDTO;
import com.ctrip.dcs.driver.domain.application.redis.obj.NoticeIdDTO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverNoticeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeModel;
import com.ctrip.igt.framework.common.datetime.DateUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by <AUTHOR> on 2023/3/5 20:21
 */
@Component
public class NoticeRedisLogic {

  private static final String VALID_NOTICE_KEY = "notice:valid:ids";
  private static final String NOTICE_CONTENT_KEY = "notice:content:%s";
  private static final String NOTICE_TARGET_KEY = "notice:target:%s";
  private static final String NOTICE_MAIL_KEY = "notice:mail:%s_%s";

  @Autowired
  DirectorRedis directorRedis;

  public List<NoticeIdDTO> getValidNotice() {
    String noticeStr = directorRedis.get(VALID_NOTICE_KEY);
    if(StringUtils.isNotEmpty(noticeStr)){
      List<NoticeIdDTO> idDTOS = JacksonUtil.parseArray(noticeStr, NoticeIdDTO.class);
      return idDTOS;
    }
    return Collections.emptyList();
  }

  public boolean isMyNotice(long noticeId, String targetId){
    String result = directorRedis.hget(String.format(NOTICE_TARGET_KEY, noticeId), targetId);
    return "1".equals(result);
  }

  public NoticeContentDTO getNoticeContent(long noticeId){
    String noticeContentStr = directorRedis.get(String.format(NOTICE_CONTENT_KEY, noticeId));
    if(StringUtils.isNotEmpty(noticeContentStr)){
      return JacksonUtil.deserialize(noticeContentStr, NoticeContentDTO.class);
    }
    return null;
  }

  public boolean updateValidNotice(List<DriverNoticeRecordPO> noticeRecords) {
    if(CollectionUtils.isEmpty(noticeRecords)){
      return directorRedis.remove(VALID_NOTICE_KEY);
    }else{
      Optional<Timestamp> endDate = noticeRecords.stream().max(Comparator.comparing(
          DriverNoticeRecordPO::getEndDate)).map(DriverNoticeRecordPO::getEndDate);
      if (endDate.isPresent()) {
        return directorRedis.set(VALID_NOTICE_KEY, JacksonUtil.serialize(convertId(noticeRecords)),
            (endDate.get().getTime() - System.currentTimeMillis()) / 1000);
      }
      return false;
    }
  }

  public boolean updateNoticeContent(DriverNoticeRecordPO noticeRecordPO) {
    if (noticeRecordPO.getNoticeStatus() == 0) {
      //下线公告，删除缓存
      return directorRedis.remove(String.format(NOTICE_CONTENT_KEY, noticeRecordPO.getId()));
    } else {
      return directorRedis.set(String.format(NOTICE_CONTENT_KEY, noticeRecordPO.getId()),
          JacksonUtil.serialize(convertContent(noticeRecordPO)),
          (noticeRecordPO.getEndDate().getTime() - System.currentTimeMillis()) / 1000);
    }
  }

  public boolean cacheNoticeTarget(long noticeId, List<Long> targetIds, Timestamp endDate) {
    Map<String, String> fields = new HashMap<>();
    targetIds.forEach(e -> fields.put(String.valueOf(e), "1"));
    return directorRedis.hmset(String.format(NOTICE_TARGET_KEY, noticeId), fields,
        (endDate.getTime() - System.currentTimeMillis()) / 1000);
  }

  public boolean isNeedSendMail(long noticeId, String targetId){
    return StringUtils.isBlank(directorRedis.get(String.format(NOTICE_MAIL_KEY, noticeId, targetId)));
  }

  public boolean markMailSendFail(String noticeId, String targetId) {
    return directorRedis.remove(String.format(NOTICE_MAIL_KEY, noticeId, targetId));
  }

  private NoticeContentDTO convertContent(DriverNoticeRecordPO noticeRecordPO) {
    NoticeContentDTO contentDTO = new NoticeContentDTO();
    contentDTO.setNoticeId(noticeRecordPO.getId());
    contentDTO.setNoticeType(noticeRecordPO.getNoticeStatus());
    contentDTO.setTitle(noticeRecordPO.getNoticeTitle());
    contentDTO.setTitleEn(noticeRecordPO.getNoticeTitleEn());
    contentDTO.setContent(noticeRecordPO.getNoticeContent());
    contentDTO.setContentEn(noticeRecordPO.getNoticeContentEn());
    contentDTO.setStartDate(DateFormatUtils.format(noticeRecordPO.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
    contentDTO.setEndDate(DateFormatUtils.format(noticeRecordPO.getEndDate(), "yyyy-MM-dd HH:mm:ss"));
    contentDTO.setUri(noticeRecordPO.getNoticeUri());
    return contentDTO;
  }

  private List<NoticeIdDTO> convertId(List<DriverNoticeRecordPO> noticeRecords) {
    return noticeRecords.stream().map(e -> {
      NoticeIdDTO idDTO = new NoticeIdDTO();
      idDTO.setNoticeId(e.getId());
      idDTO.setIdType(e.getNoticeConditionType());
      idDTO.setProductType(e.getProductType());
      idDTO.setUserType(e.getUserType());
      idDTO.setDriverStatus(e.getDriverStatus());
      idDTO.setCityIds(e.getCityIds());
      idDTO.setCountryIds(e.getCountryIds());
      idDTO.setVehicleTypes(e.getVehicleTypes());
      idDTO.setStartDate(DateFormatUtils.format(e.getStartDate(), "yyyy-MM-dd HH:mm:ss"));
      idDTO.setEndDate(DateFormatUtils.format(e.getEndDate(), "yyyy-MM-dd HH:mm:ss"));
      return idDTO;
    }).collect(Collectors.toList());
  }


  public Boolean markMailSendSucess(NoticeModel noticeModel, String targetId) {
    return directorRedis.set(String.format(NOTICE_MAIL_KEY, noticeModel.getId(), targetId), "1",
            (DateUtils.parse(noticeModel.getEndDate(), "yyyy-MM-dd HH:mm:ss").getTime() - System.currentTimeMillis()) / 1000);
  }
}
