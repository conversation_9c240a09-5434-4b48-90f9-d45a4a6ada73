package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.redis.PointInfoRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverPointRestfulShowInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.setting.DriverPointRestfulPopInfoModel;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPointPopInfoRequestType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPointPopInfoResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 查询司机分弹窗信息
 */
@Component
public class QueryDriverPointPopInfoEngine extends ShoppingExecutor<QueryDriverPointPopInfoRequestType, QueryDriverPointPopInfoResponseType>
        implements Validator<QueryDriverPointPopInfoRequestType> {

    @Autowired
    private DriverPointRestfulShowInfoDao driverPointRestfulShowInfoDao;

    @Autowired
    PointInfoRedisLogic pointInfoRedisLogic;

    @Override
    public void validate(AbstractValidator<QueryDriverPointPopInfoRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverPointPopInfoEngine owner;
        private final QueryDriverPointPopInfoResponseType response;
        private final QueryDriverPointPopInfoRequestType request;

        private Executor(QueryDriverPointPopInfoRequestType request, QueryDriverPointPopInfoResponseType response, QueryDriverPointPopInfoEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;
            this.response.hasMainPageInfo = false;
            this.response.hasVoidanceInfo = false;
        }

        private void QueryDriverPointPopInfo() {
            //缓存
            DriverPointRestfulPopInfoModel driverPointRestfulPopInfo = this.owner.pointInfoRedisLogic.queryDriverPointInfo(this.request.driverId);
            if(Objects.nonNull(driverPointRestfulPopInfo)) {
                this.response.hasMainPageInfo = ObjectUtils.defaultIfNull(driverPointRestfulPopInfo.getNovicePointGuidePop(), 0).equals(1);
                this.response.hasVoidanceInfo = ObjectUtils.defaultIfNull(driverPointRestfulPopInfo.getNovicePointVoidancePop(), 0).equals(1);
                return;
            }

            // 查DB
            DriverPointRestfulShowInfoPO driverPointRestfulShowInfoPO = this.owner.driverPointRestfulShowInfoDao.findOne(request.driverId);
            if(Objects.nonNull(driverPointRestfulShowInfoPO)){
                this.response.hasMainPageInfo = ObjectUtils.defaultIfNull(driverPointRestfulShowInfoPO.getNovicePointGuidePop(), 0).equals(1);
                this.response.hasVoidanceInfo = ObjectUtils.defaultIfNull(driverPointRestfulShowInfoPO.getNovicePointVoidancePop(), 0).equals(1);
                this.owner.pointInfoRedisLogic.saveDriverPointInfo(this.request.driverId, driverPointRestfulShowInfoPO);
            }
        }

        @Override
        protected boolean validate() {
            return true;
        }

        @Override
        protected void buildResponse() {
            if(!this.validate()){
                return;
            }

            this.QueryDriverPointPopInfo();
        }
    }

    @Override
    public QueryDriverPointPopInfoResponseType execute(QueryDriverPointPopInfoRequestType request) {
        QueryDriverPointPopInfoResponseType response = new QueryDriverPointPopInfoResponseType();
        Executor executor = new Executor(request, response, this);
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverPointPopInfoResponseType onException(QueryDriverPointPopInfoRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverPointPopInfo error", ex);
        return super.onException(req, ex);
    }
}
