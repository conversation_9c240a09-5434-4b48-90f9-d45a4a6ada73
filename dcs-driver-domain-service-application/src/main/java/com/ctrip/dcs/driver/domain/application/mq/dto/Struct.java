package com.ctrip.dcs.driver.domain.application.mq.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
@Data
public class Struct {
    Integer recentOrderDaysThreshold;
    List<Long> supplierIds;
    List<Long> driverIds;
    List<Long> countryIds;
    List<Long> cityIds;
    List<Long> blackCityIds;
    Integer driverActive;
    Integer driverStatus;
    Long lastDriverId;
    Integer pageSize;
    Integer countLimit;
    Boolean validateRecentOrder;
    Boolean validateRepeatCheck;
    LocalDateTime planTime;
}
