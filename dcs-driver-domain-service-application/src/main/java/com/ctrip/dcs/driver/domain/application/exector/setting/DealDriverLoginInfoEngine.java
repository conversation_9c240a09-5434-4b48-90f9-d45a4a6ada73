package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivLoginInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivLoginInfoPO;
import com.ctrip.dcs.driver.domain.setting.DealDriverLoginInfoRequestType;
import com.ctrip.dcs.driver.domain.setting.DealDriverLoginInfoResponseType;
import com.ctrip.dcs.driver.domain.setting.DriverLoginInfoDTO;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * 司机登录信息
 */
@Component
public class DealDriverLoginInfoEngine extends ShoppingExecutor<DealDriverLoginInfoRequestType, DealDriverLoginInfoResponseType>
        implements Validator<DealDriverLoginInfoRequestType> {

    @Autowired
    DrivLoginInfoDao drivLoginInfoDao;

    @Override
    public void validate(AbstractValidator<DealDriverLoginInfoRequestType> validator) {
        validator.ruleFor("driverLoginInfo").notNull();
        validator.ruleFor("driverLoginInfo").setValidator(new DriverLoginInfoDTOValidator());
    }

    /**
     * locale
     */
    public static class DriverLoginInfoDTOValidator extends AbstractValidator<DriverLoginInfoDTO> {
        public DriverLoginInfoDTOValidator() {
            super(DriverLoginInfoDTO.class);
            ruleFor("driverId").notNull().notEmpty();
        }
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final DealDriverLoginInfoEngine owner;
        private final DealDriverLoginInfoResponseType response;
        private final DriverLoginInfoDTO requestDriverLoginInfo;

        private Executor(DealDriverLoginInfoRequestType request, DealDriverLoginInfoResponseType response, DealDriverLoginInfoEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.requestDriverLoginInfo = request.driverLoginInfo;
        }

        private void DealDriverLoginInfo() {
            DrivLoginInfoPO loginInfoPO = this.owner.drivLoginInfoDao.findOne(requestDriverLoginInfo.driverId);
            int dealType = ObjectUtils.defaultIfNull(requestDriverLoginInfo.type, 0);
            if(dealType == 0 && loginInfoPO != null){
                response.driverLoginInfo = new DriverLoginInfoDTO();
                response.driverLoginInfo.phoneSign = loginInfoPO.getPhoneSign();
            }
            if(dealType == 1){
                if (loginInfoPO == null) {
                    loginInfoPO = new DrivLoginInfoPO();
                    loginInfoPO.setPhoneSign(requestDriverLoginInfo.phoneSign);
                    loginInfoPO.setDrivId(requestDriverLoginInfo.driverId);
                    loginInfoPO.setTerminalInfo(requestDriverLoginInfo.terminalInfo);
                    loginInfoPO.setOsType(ObjectUtils.defaultIfNull(requestDriverLoginInfo.osType, 0));
                    loginInfoPO.setCreateTime(new Timestamp(System.currentTimeMillis()));
                    this.owner.drivLoginInfoDao.insert(loginInfoPO);
                } else {
                    boolean isUpdate = false;
                    if(StringUtils.isNotBlank(requestDriverLoginInfo.phoneSign)) {
                        loginInfoPO.setPhoneSign(requestDriverLoginInfo.phoneSign);
                        isUpdate = true;
                    }
                    if(StringUtils.isNotBlank(requestDriverLoginInfo.terminalInfo)) {
                        loginInfoPO.setTerminalInfo(requestDriverLoginInfo.terminalInfo);
                        isUpdate = true;
                    }
                    if(requestDriverLoginInfo.osType != null) {
                        loginInfoPO.setOsType(requestDriverLoginInfo.osType);
                        isUpdate = true;
                    }
                    if(isUpdate) {
                        this.owner.drivLoginInfoDao.update(loginInfoPO);
                    }
                }
            }
        }

        @Override
        protected boolean validate() {
            return true;
        }

        @Override
        protected void buildResponse() {
            if(!this.validate()){
                return;
            }

            this.DealDriverLoginInfo();
        }
    }

    @Override
    public DealDriverLoginInfoResponseType execute(DealDriverLoginInfoRequestType request) {
        DealDriverLoginInfoResponseType response = new DealDriverLoginInfoResponseType();
        Executor executor = new Executor(request, response, this);
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public DealDriverLoginInfoResponseType onException(DealDriverLoginInfoRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("DealDriverLoginInfoEngine error", ex);
        return super.onException(req, ex);
    }
}
