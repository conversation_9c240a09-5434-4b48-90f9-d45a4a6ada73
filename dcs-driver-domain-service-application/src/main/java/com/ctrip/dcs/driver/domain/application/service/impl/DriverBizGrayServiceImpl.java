package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.redis.DriverAppVerRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.DriverBizGrayService;
import com.ctrip.dcs.driver.domain.application.service.TmsTransportProxyService;
import com.ctrip.dcs.driver.domain.application.service.TourService;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ProductLineEnum;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.DriverGreyConfig;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA;
import com.ctrip.frt.product.soa.DriverBasicInfoType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DriverBizGrayServiceImpl implements DriverBizGrayService {
    private static final Logger logger = LoggerFactory.getLogger(DriverBizGrayServiceImpl.class);

    @Autowired
    private DriverGreyConfig driverGreyConfig;
    @Autowired
    private DriverAppVerRedisLogic driverAppVerRedisLogic;
    @Autowired
    private TourService tourService;
    @Autowired
    private TmsTransportProxyService tmsTransportProxyService;
    @Autowired
    private CityRepository cityRepository;

    @Override
    public boolean queryBizGraySwitch(String bizGrayKey, Long driverId, String productLine, Long cityId, String appVer, String rnVer,
                                      Boolean ignoreVersion) {
        // 参数校验
        if (Objects.isNull(driverId) || StringUtils.isBlank(bizGrayKey)) {
            return false;
        }
        if (Objects.isNull(driverGreyConfig.getConfig()) || !driverGreyConfig.getConfig().containsKey(bizGrayKey)) {
            return false;
        }
        DriverGreyConfig.BizGreyConfigEntity greyConfig = driverGreyConfig.getConfig().get(bizGrayKey);
        String logTitle = String.format("queryBizGraySwitch:%s:%s", driverId, bizGrayKey);
        // 1.先判断app版本，没有传则从缓存取。如果忽略版本校验则跳过
        if (StringUtils.isAnyBlank(appVer, rnVer) && BooleanUtils.isNotTrue(ignoreVersion)) {
            Pair<String, String> pair = driverAppVerRedisLogic.getAppRnVer(driverId);
            if (Objects.nonNull(pair)) {
                appVer = pair.getLeft();
                rnVer = pair.getRight();
            }
        }
        boolean isGrey = false;
        // APP版本判断。如果忽略版本校验则跳过
        if (StringUtils.isNotBlank(greyConfig.getAppVer()) && BooleanUtils.isNotTrue(ignoreVersion)) {
            BigDecimal formAppVersion = BigDecimal.valueOf(Long.parseLong(greyConfig.getAppVer()));
            BigDecimal appVersion = StringUtils.isBlank(appVer) ? BigDecimal.ZERO : BigDecimal.valueOf(Long.parseLong(appVer));
            isGrey = appVersion.compareTo(formAppVersion) >= 0;
            if (!isGrey) {
                Cat.logEvent(CatEventType.QUERY_BIZ_GRAY_SWITCH, "false");
                logger.info(logTitle, "driverAppVer not match");
                return false;
            }
        }
        // RN版本判断。如果忽略版本校验则跳过
        if (StringUtils.isNotBlank(greyConfig.getRnVer()) && BooleanUtils.isNotTrue(ignoreVersion)) {
            BigDecimal formRnVersion = BigDecimal.valueOf(Long.parseLong(greyConfig.getRnVer()));
            BigDecimal rnVersion = StringUtils.isBlank(rnVer) ? BigDecimal.ZERO : BigDecimal.valueOf(Long.parseLong(rnVer));
            isGrey = rnVersion.compareTo(formRnVersion) >= 0;
            if (!isGrey) {
                Cat.logEvent(CatEventType.QUERY_BIZ_GRAY_SWITCH, "false");
                logger.info(logTitle, "driverAppRnVer not match");
                return false;
            }
        }
        // 全量判断
        if (StringUtils.isBlank(greyConfig.getDriverIds())
                && StringUtils.isBlank(greyConfig.getCityIds())
                && StringUtils.isBlank(greyConfig.getCountryIds())) {
            Cat.logEvent(CatEventType.QUERY_BIZ_GRAY_SWITCH, "true");
            logger.info(logTitle, "gray config all");
            return true;
        }
        // 司机id灰度
        if (StringUtils.isNotBlank(greyConfig.getDriverIds())) {
            isGrey = Arrays.stream(greyConfig.getDriverIds().split(",")).map(Long::parseLong)
                    .collect(Collectors.toSet()).contains(driverId);
            if (isGrey) {
                Cat.logEvent(CatEventType.QUERY_BIZ_GRAY_SWITCH, "true");
                return true;
            }
            logger.info(logTitle, "driverId not match");
        }
        // 司机服务城市灰度
        if (StringUtils.isNotBlank(greyConfig.getCityIds())) {
            // 如果未传城市id，查询供应链拿城市id
            if (Objects.isNull(cityId)) {
                cityId = getDriverCityId(driverId, productLine);
            }
            isGrey = Arrays.stream(greyConfig.getCityIds().split(",")).map(Long::parseLong)
                    .collect(Collectors.toSet()).contains(cityId);
            if (isGrey) {
                Cat.logEvent(CatEventType.QUERY_BIZ_GRAY_SWITCH, "true");
                return true;
            }
            logger.info(logTitle, "cityId not match");
        }
        // 司机服务国家灰度
        if (StringUtils.isNotBlank(greyConfig.getCountryIds())) {
            Long countryId = 0L;
            // 如果未传城市id，查询供应链拿城市id
            if (Objects.isNull(cityId)) {
                cityId = getDriverCityId(driverId, productLine);
            }
            // 根据城市查询国家id
            City city = cityRepository.findOne(cityId);
            if (Objects.nonNull(city)) {
                countryId = city.getCountryId();
            }
            isGrey = Arrays.stream(greyConfig.getCountryIds().split(",")).map(Long::parseLong)
                    .collect(Collectors.toSet()).contains(countryId);
            if (isGrey) {
                Cat.logEvent(CatEventType.QUERY_BIZ_GRAY_SWITCH, "true");
                return true;
            }
            logger.info(logTitle, "countryId not match");
        }
        Cat.logEvent(CatEventType.QUERY_BIZ_GRAY_SWITCH, "false");
        return isGrey;
    }

    /**
     * 根据产线和司导id，查询司导城市id
     *
     * @param driverId
     * @param productLine
     * @return
     */
    private Long getDriverCityId(Long driverId, String productLine) {
        Long cityId = 0L;
        // 包车，考虑切司机灰度中，先查司导供应链
        if (ProductLineEnum.DAY.name().equals(productLine)) {
            DriverBasicInfoType driverInfo = tourService.getDriverInfo(driverId);
            if (Objects.nonNull(driverInfo)) {
                cityId = driverInfo.getServiceCityId();
            } else {
                // 司导供应链查不到，从司机供应链再查一次 todo 等灰度完，无需再从司机供应链查询
                QueryDrvDetailDTOSOA queryDrvDetailDTOSOA = tmsTransportProxyService.queryDriverDetail(driverId);
                if (Objects.nonNull(queryDrvDetailDTOSOA)) {
                    cityId = queryDrvDetailDTOSOA.getCityId();
                }
            }
        } else if (ProductLineEnum.JNT.name().equals(productLine) || ProductLineEnum.POINT.name().equals(productLine)) {
            QueryDrvDetailDTOSOA queryDrvDetailDTOSOA = tmsTransportProxyService.queryDriverDetail(driverId);
            if (Objects.nonNull(queryDrvDetailDTOSOA)) {
                cityId = queryDrvDetailDTOSOA.getCityId();
            }
        }
        return cityId;
    }
}
