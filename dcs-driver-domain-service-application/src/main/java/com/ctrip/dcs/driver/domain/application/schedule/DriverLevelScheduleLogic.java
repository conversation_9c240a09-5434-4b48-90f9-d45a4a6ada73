package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.application.exector.rights.UseRightsEngine;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.DriverLevelService;
import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDAlDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DriverPointTotalInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DriverPointTotalInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.RightsReissueDto;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.DriverPointModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverLevelGreyConfig;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.shopping.utils.ShoppingActionUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.google.common.collect.Lists;
import credis.java.client.util.JsonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class DriverLevelScheduleLogic {
    public static final Logger LOGGER = LoggerFactory.getLogger(DriverLevelScheduleLogic.class);

    public static final Integer PAGE_SIZE = 200;
    public static final String DEFAULT_LEVEVL_NAME_KEY = "default_levevl_name";
    public static final String DEFAULT_OVERSEA_LEVEVL_NAME_KEY = "default_ovearsea_levevl_name";
    public static final String DEFAULT_DO_SNAPSHOT_HOUR_KEY = "default_do_snapshot_hour";
    private static final int CITY_POINT_SNAPSHOT_CACHE_EXPIRE_SECONDS = 23 * 60 * 60;

    @Autowired
    DrivLevelDAlDao drivLevelDAlDao;

    @Autowired
    DrivRightsDao drivRightsDao;

    @Autowired
    DrivLevelDao drivLevelDao;

    @Autowired
    DriverPointTotalInfoDao driverPointTotalInfoDao;

    @Autowired
    private LevelConfig levelConfig;

    @Autowired
    private DriverLevelGreyConfig driverLevelGreyConfig;

    @Autowired
    private RightsConfig rightsConfig;

    @Autowired
    private RightsRedisLogic rightsRedisLogic;

    @Autowired
    SystemQConfig systemQConfig;

    @Autowired
    UseRightsEngine useRightsEngine;

    @Autowired
    DomainBeanFactory domainBeanFactory;

    @Autowired
    LockService lockService;
    @Autowired
    CityRepository cityRepository;
    @Autowired
    GeoGateway geoGateway;
    @Autowired
    DirectorRedis directorRedis;
    @Autowired
    DriverLevelService driverLevelService;


    /**
     * 司机等级计算，暂不执行
     */
    @QSchedule("dcs.driver.level.update")
    public void updateDriverLevel(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        Logger logger = monitor.getLogger();
        logger.info("dcs.driver.level.update start");

        //自定义司机分快照时间，默认每月1号
        String pointDate = StringUtils.defaultIfBlank(parameter.getString("date"), LocalDateTimeUtils.fitstDayOfMonthStr());

        String cityIdStr = parameter.getString("cityIdList");
        List<Long> cityIdList;
        if (StringUtils.isNotBlank(cityIdStr)) {
            cityIdList = Arrays.stream(cityIdStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
        } else {
            cityIdList = getCity();
        }

        this.doUpdateLevel(cityIdList, logger,pointDate);

        logger.info("dcs.driver.level.update end");
    }

    public void doUpdateLevel(List<Long> cityIdList, Logger logger, String pointDate){
        if (CollectionUtils.isEmpty(cityIdList)) {
            return;
        }
        LocalDateTime localCurrentTime = geoGateway.getLocalCurrentTime(cityIdList.get(0));
        String monthIdx = LocalDateTimeUtils.monthIndexStr(localCurrentTime);
        //获取灰度城市列表
        ShoppingActionUtils.tryActions(cityIdList, cityId -> {
            LOGGER.info(String.format("driver level do update, city: %s, local time : %s", cityId, localCurrentTime));
            logger.info(String.format("driver level do update, city: %s, local time : %s", cityId, localCurrentTime));
            Long count = driverPointTotalInfoDao.countByCityId(cityId);
            Long dbCount = drivLevelDao.countByCityIdAndMonthIdx(cityId, monthIdx);
            if (count > dbCount) {
                for (int i = 1; i <= (count / PAGE_SIZE) + 1; i++) {
                    //获取司机id
                    List<Long> drivIds = queryDrivIds(cityId, i, logger);
                    if (CollectionUtils.isEmpty(drivIds)) {
                        logger.warn(String.format("queryDriverId error,cityId:%s", cityId));
                        break;
                    }
                    //获取司机分
                    List<DriverPointModel> driverPointSnapshot = rightsRedisLogic.getDriverPointSnapshot(drivIds,pointDate);
                    if (CollectionUtils.isEmpty(driverPointSnapshot)) {
                        logger.warn(String.format("getDriverPointSnapshot error,cityId:%s", cityId));
                        continue;
                    }
                    //计算司机等级，落表
                    saveDB(cityId, monthIdx, driverPointSnapshot, logger);
                }
                Long successNum = drivLevelDao.countByCityIdAndMonthIdx(cityId, monthIdx);
                logger.info(String.format("updateDriverLevel success cityId:%s ,driver total count:%s,actual success driver count:%s", cityId, count, successNum));
                if (count > successNum.intValue()) {
                    logger.warn(String.format("updateDriverLevel failed cityId:%s,driver total count:%s,actual success driver count:%s", cityId, count, successNum));
                }
            } else {
                logger.info(String.format("updateDriverLevel  cityId:%s ,finished , skip", cityId));
            }
        });
    }

    public List<Long> queryDrivIds(Long cityId, int i, Logger logger) {
        List<Long> longlist = Lists.newArrayList();
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll().atPage(i, PAGE_SIZE).orderBy("id", true);
            builder.and().equal("city_id", cityId.toString(), Types.VARCHAR);
            List<DriverPointTotalInfoPO> poList = driverPointTotalInfoDao.query(builder);
            longlist = poList.stream().map(o -> Long.valueOf(o.getDriverId())).collect(Collectors.toList());
        } catch (Exception e) {
            logger.warn("failed to queryDrivIds ", e);
        }
        return longlist;
    }

    public void saveDB(Long cityId, String monthIdx, List<DriverPointModel> modelList, Logger logger) {
        List<DrivLevelPO> records = modelList.stream().map(model -> {
            DrivLevelPO po = new DrivLevelPO();
            po.setLevelConfigId(model.getLevelConfigId());
            po.setLevelName(model.getLevelName());
            po.setCityId(cityId);
            po.setMonthIdx(monthIdx);
            po.setDrivId(model.getDrivId());
            po.setDrivLevel(model.getDrivLevel());
            po.setDrivPoint(Optional.ofNullable(model.getDrivPoint()).orElse(BigDecimal.ZERO).toString());
            po.setDrivActivity(Optional.ofNullable(model.getDrivActivity()).orElse(BigDecimal.ZERO).toString());
            po.setDrivRank(Optional.ofNullable(model.getDrivRank()).orElse(0).toString());
            return po;
        }).collect(Collectors.toList());
        //批量插入忽略唯一索引报错，插入重复数据
        try {
            drivLevelDAlDao.batchInsertDuplicateUpdate(DalHints.createIfAbsent(null), records);
        } catch (Exception e) {
            logger.warn("failed to batchInsert dirv_level", e);
        }
    }

    public List<Long> getCity() {
        List<Long> list = new ArrayList<>();
        Map<Long, LocalDate> cityMap = driverLevelGreyConfig.cityMap;
        for (Long key : cityMap.keySet()) {
            if (cityMap.get(key).isBefore(LocalDate.now()) || cityMap.get(key).isEqual(LocalDate.now())) {
                list.add(key);
            }
        }
        return list;
    }


    /**
     * 司机权益补发
     *
     * @param parameter
     */
    @QSchedule("dcs.driver.rights.reissue")
    public void rightsReissue(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        Logger logger = monitor.getLogger();
        if (Objects.isNull(parameter)) {
            return;
        }

        String data = StringUtils.defaultIfBlank(parameter.getString("data"), Strings.EMPTY);
        List<RightsReissueDto> rightsReissueDtos = JacksonUtil.parseArray(data, RightsReissueDto.class);
        if (CollectionUtils.isEmpty(rightsReissueDtos)) {
            return;
        }
        drivRightsDao.batchInsert(buildRecords(rightsReissueDtos, logger));
        // 删除缓存
        rightsRedisLogic.delDrivRights(rightsReissueDtos.get(0).getDriverId(),LocalDateTimeUtils.monthIndexStr());
        logger.info("dcs.driver.rights.reissue end");
    }


    public List<DrivRightsPO> buildRecords(List<RightsReissueDto> rightsReissueDtos, Logger logger) {
        String monthIdx = LocalDateTimeUtils.monthIndexStr();

        List<DrivRightsPO> list = new ArrayList<>();
        rightsReissueDtos.stream().forEach(reissue -> {
            DrivLevelPO drivLevelPO = drivLevelDao.queryDriverLevel(reissue.getDriverId(), monthIdx);
            if (Objects.isNull(drivLevelPO)) {
                logger.warn(String.format("driver does not exist drivId:%s", reissue.getDriverId()));
            }else {
                //校验并获取权益配置信息
                FormRightsInfo formRightsInfo = rightsConfig.getRightsInfo(drivLevelPO.getCityId(), reissue.getLevel(), reissue.getRightsType());
                if (Objects.nonNull(formRightsInfo)) {
                    DrivRightsPO po = new DrivRightsPO();
                    po.setRightsConfigId(formRightsInfo.getId());
                    po.setRightsName(formRightsInfo.getRightsName());
                    po.setRightsDesc(formRightsInfo.getRightsDesc());
                    po.setCityId(drivLevelPO.getCityId());
                    po.setMonthIdx(monthIdx);
                    po.setDrivId(reissue.getDriverId());
                    po.setDrivLevel(drivLevelPO.getDrivLevel());
                    po.setRightsType(reissue.getRightsType());
                    po.setUseLimit(reissue.getLimit());
                    po.setUseCount(0);
                    po.setRightsStatus(RightsStatusEnum.RIGHTS_STATUS_ISSUED.getStatus());
                    po.setRightsStratTime(Timestamp.valueOf(LocalDateTimeUtils.firstDayOfMonth()));
                    po.setRightsEndTime(Timestamp.valueOf(LocalDateTimeUtils.lastDayOfMonth()));
                    po.setExtend(CommonRightsExtendUtils.buildExtend(formRightsInfo.getRightsType(), formRightsInfo.getExtend()));
                    list.add(po);
                }
            }
        });
        return list;
    }

    /**
     * 司机分快照
     */
    @QSchedule("dcs.driver.point.snapshot")
    public void pointSnapshot(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        Logger logger = monitor.getLogger();
        List<Long> cityList;
        if(StringUtils.isBlank(parameter.getString("citys"))){
            // 筛选需要计算的城市（当地时间凌晨1点）
            cityList = pickNeedDoSnapShotCityId(getCity(), logger);
        } else {
            cityList = Arrays.stream(parameter.getString("citys").split(",")).map(Long::valueOf).collect(Collectors.toList());
        }
        doSnapShot(cityList);
        logger.info("dcs.driver.point.snapshot end");
    }

    public void doSnapShot(List<Long> cityIdList) {
        if (CollectionUtils.isEmpty(cityIdList)) {
            return;
        }
        LocalDateTime localCurrentTime = geoGateway.getLocalCurrentTime(cityIdList.get(0));
        String todayStr = LocalDateTimeUtils.todayStr(localCurrentTime);
        ShoppingActionUtils.tryActions(cityIdList, city -> {
            LOGGER.info(String.format("driver point do snapshot, city: %s, local time : %s", city, localCurrentTime));
            Map<Integer, Integer> LevelRankMap = new HashMap<>();
            Map<Integer, FormLevelInfo> LevelInfoMap = new HashMap<>();

            levelConfig.getCityDriverLevel(city).stream().forEach(o -> {
                if (o.getHasRank() == 1) {
                    LevelRankMap.put(o.getLevel(), o.getRankLow());
                }
                LevelInfoMap.put(o.getLevel(), o);
            });
            //保存等级-1的model
            Map<Integer, List<DriverPointModel>> downGrade = new HashMap();
            int totalRank = 1;
            BigDecimal drivPointLow = BigDecimal.ZERO;
            BigDecimal drivActivityLow = BigDecimal.ZERO;
            BigDecimal safeLow = BigDecimal.ZERO;
            //从钻石开始排名
            int startLevel = LevelEnum.LEVEL_TYPE_DIAMOND.getCode();
            int endLevel = LevelEnum.LEVEL_TYPE_BRONZE.getCode();
            City cityInfo = cityRepository.findOne(city);
            if (cityInfo != null && !cityInfo.isChineseMainland()) {
                // 境外
                startLevel = LevelEnum.LEVEL_TYPE_GOLD_MEDAL.getCode();
                endLevel = LevelEnum.LEVEL_TYPE_REGULAR_MEDAL.getCode();

            }
            for (int i = startLevel; i >= endLevel; i--) {
                //有排名门槛
                if (LevelRankMap.containsKey(i)) {
                    List<DriverPointModel> pointList = driverPointTotalInfoDao.queryByCityIdAndLevel(city, i).stream().map(o -> convert2PointModel(o, cityInfo, LevelInfoMap)).collect(Collectors.toList());
                    //先查看下有没有上个等级降级，加入当前等级
                    if (downGrade.containsKey(i)) {
                        pointList.addAll(downGrade.get(i));
                    }
                    if (CollectionUtils.isEmpty(pointList)) {
                        continue;
                    }
                    pointList = pointList.stream().sorted(Comparator.comparing(DriverPointModel::getTotalPoint).reversed()).collect(Collectors.toList());
                    //按照总分从大到小排序，分组
                    Map<BigDecimal, List<DriverPointModel>> rankMap =  pointList.stream().collect(Collectors.groupingBy(DriverPointModel::getTotalPoint));
                    int rank = 1;
                    //排名门槛
                    int rankLimit = LevelRankMap.get(i);
                    //距离当前等级还差多少排名gap
                    int rankGap = 1;
                    //降级list
                    List<DriverPointModel> restList = new ArrayList<>();
                    FormLevelInfo formLevelInfo = LevelInfoMap.get(i - 1);
                    for (int k = 0; k < pointList.size();) {
                        //排名未达标 等级-1
                        if (rank > rankLimit) {
                            DriverPointModel model = pointList.get(k);
                            model.setDrivPointLow(drivPointLow);
                            model.setDrivActivityLow(drivActivityLow);
                            model.setDriverSafePointLow(safeLow);
                            model.setDrivLevel(model.getDrivLevel() - 1);
                            model.setRankGap(rankGap++);
                            if (Objects.isNull(formLevelInfo)) {
                                model.setLevelConfigId(0L);
                                model.setLevelName(systemQConfig.getString(DEFAULT_LEVEVL_NAME_KEY));
                            } else {
                                model.setLevelConfigId(formLevelInfo.getId());
                                model.setLevelName(formLevelInfo.getLevelName());
                            }
                            if(i == LevelEnum.LEVEL_TYPE_PLATINUM.getCode()){
                                model.setTotalRank(totalRank++);
                            }
                            restList.add(model);
                            k++;
                        } else {
                            List<DriverPointModel> driverPointModels = rankMap.get(pointList.get(k).getTotalPoint());
                            for (DriverPointModel model : driverPointModels) {
                                model.setDrivRank(rank);
                                model.setTotalRank(totalRank);
                                model.setDrivPointLow(drivPointLow);
                                model.setDrivActivityLow(drivActivityLow);
                                model.setDriverSafePointLow(safeLow);
                            }
                            totalRank += driverPointModels.size();
                            rank += driverPointModels.size();
                            k += driverPointModels.size();
                            if(rank > rankLimit){
                                drivPointLow = driverPointModels.get(0).getDrivPoint();
                                drivActivityLow = driverPointModels.get(0).getDrivActivity();
                                safeLow = driverPointModels.get(0).getDriverSafePointLow();
                            }
                        }
                    }

                    if (CollectionUtils.isNotEmpty(restList)) {
                        downGrade.put(i - 1, restList);
                    }
                    rightsRedisLogic.driverPointSnapshot(pointList.subList(0, rank - 1), todayStr);
                    drivPointLow = pointList.get(rank - 2).getDrivPoint();
                    drivActivityLow = pointList.get(rank - 2).getDrivActivity();
                    safeLow = pointList.get(rank - 2).getDriverSafePointLow();
                } else {
                    //无排名门槛
                    Long count = driverPointTotalInfoDao.countByCityIdAndLevel(city, i);
                    for (int j = 1; j <= (count / PAGE_SIZE) + 1; j++) {
                        SelectSqlBuilder builder = new SelectSqlBuilder();
                        builder.selectAll().atPage(j, PAGE_SIZE).orderBy("id", true);
                        builder.and().equal("city_id", city.toString(), Types.VARCHAR);
                        builder.and().equal("driv_level", i, Types.TINYINT);
                        List<DriverPointModel> driverPointModels = driverPointTotalInfoDao.query(builder).stream().map(o -> convert2PointModel(o, cityInfo, LevelInfoMap)).collect(Collectors.toList());
                        rightsRedisLogic.driverPointSnapshot(driverPointModels, todayStr);
                    }
                    //等级-1，且没有排名要求
                    if (downGrade.containsKey(i)) {
                        rightsRedisLogic.driverPointSnapshot(downGrade.get(i), todayStr);

                    }
                }
            }
            String redisKey = buildCityDoSnapshotPointKey(city, localCurrentTime);
            directorRedis.set(redisKey, "1", CITY_POINT_SNAPSHOT_CACHE_EXPIRE_SECONDS);
        });
    }

    private DriverPointModel convert2PointModel(DriverPointTotalInfoPO po, City cityInfo, Map<Integer, FormLevelInfo> levelInfoMap) {
        DriverPointModel driverPointModel = new DriverPointModel();
        FormLevelInfo formLevelInfo = levelInfoMap.get(po.getDrivLevel());
        if (Objects.isNull(formLevelInfo)) {
            driverPointModel.setLevelConfigId(0L);
            driverPointModel.setLevelName(systemQConfig.getString(DEFAULT_LEVEVL_NAME_KEY));
            if (cityInfo != null && !cityInfo.isChineseMainland()) {
                // 境外
                driverPointModel.setLevelName(systemQConfig.getString(DEFAULT_OVERSEA_LEVEVL_NAME_KEY));
            }
        } else {
            driverPointModel.setLevelConfigId(formLevelInfo.getId());
            driverPointModel.setLevelName(formLevelInfo.getLevelName());
        }
        driverPointModel.setDrivLevel(po.getDrivLevel());
        driverPointModel.setDrivPoint(po.getOrderInfoPoint());
        driverPointModel.setDrivId(Long.valueOf(po.getDriverId()));
        driverPointModel.setDrivActivity(po.getRewardInfoPoint());
        driverPointModel.setDriverSafePoint(po.getSafePoint());
        driverPointModel.setTotalPoint(po.getOrderInfoPoint().add(po.getRewardInfoPoint()));
        return driverPointModel;
    }

    /**
     * 初始化等级
     *
     * @param parameter
     */
    @QSchedule("dcs.driver.levl.init")
    public void initDriverLevel(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        Logger logger = monitor.getLogger();

        if (Objects.isNull(parameter)) {
            return;
        }

        String[] cityIds = StringUtils.defaultIfBlank(parameter.getString("citys"), Strings.EMPTY).split(",");
        ShoppingActionUtils.tryActions(cityIds,city->{
            long count = driverPointTotalInfoDao.countByCityId(Long.valueOf(city));
            for (int i = 1; i <= (count / PAGE_SIZE) + 1; i++) {
                SelectSqlBuilder builder = new SelectSqlBuilder();
                builder.selectAll().atPage(i, PAGE_SIZE).orderBy("id", true);
                builder.and().equal("city_id", city, Types.VARCHAR);
                List<DriverPointTotalInfoPO> driverPointTotalInfoPOS = driverPointTotalInfoDao.query(builder);

                driverPointTotalInfoDao.batchUpdate(driverPointTotalInfoPOS.stream().map(o -> {
                    DriverPointTotalInfoPO po = new DriverPointTotalInfoPO();
                    po.setId(o.getId());
                    int level = driverLevelService.calcDriverLevel(Long.valueOf(o.getDriverId()), Long.valueOf(o.getCityId()), o.getOrderInfoPoint(), o.getRewardInfoPoint(), o.getSafePoint()).getLevel();
                    po.setDrivLevel(level);
                    return po;
                }).collect(Collectors.toList()));
            }
        });
        logger.info("dcs.driver.levl.init end");
    }

    @QSchedule("dcs.driver.level.update.test")
    public void updateDriverLevelForTest(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        Logger logger = monitor.getLogger();
        logger.info("dcs.driver.level.update start");

        String pointDate = parameter.getString("date");

        Long cityId = Long.valueOf(parameter.getString("city"));

        //获取灰度城市列表
        List<Long> drivIds =Arrays.stream(StringUtils.defaultIfBlank(parameter.getString("drivers"), Strings.EMPTY).split(",")).map(o->Long.valueOf(o)).collect(Collectors.toList());

        //获取司机分
        List<DriverPointModel> driverPointSnapshot = rightsRedisLogic.getDriverPointSnapshot(drivIds,pointDate).stream().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(driverPointSnapshot)) {
            logger.warn(String.format("getDriverPointSnapshot error,cityId:%s", cityId));
        }
        //计算司机等级，落表
        LocalDateTime localCurrentTime = geoGateway.getLocalCurrentTime(cityId);
        String monthIdx = LocalDateTimeUtils.monthIndexStr(localCurrentTime);
        saveDB(cityId, monthIdx, driverPointSnapshot, logger);

        logger.info("dcs.driver.level.update end");
    }

    @QSchedule("dcs.driver.rights.use.cityking")
    public void useCityKingRights(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        Logger logger = monitor.getLogger();
        logger.info("dcs.driver.rights.use.cityking start");

        Long count = drivRightsDao.countByRightsAndMonth(RightsTypeEnum.RIGHTS_TYPE_CITYKING.getCode(), LocalDateTimeUtils.monthIndexStr());

        for (int i = 1; i <= (count / PAGE_SIZE) + 1; i++) {
            //获取司机id
            List<Long> drivIds = queryDrivIdsByRights( i, logger);
            if (CollectionUtils.isEmpty(drivIds)) {
                logger.warn(String.format("queryDrivIdsByRights error,i:%s", i));
                break;
            }
            drivIds.stream().forEach(id->{
                UseRightsCondition useRightsCondition = UseRightsCondition.builder().driverId(id).rightsType(RightsTypeEnum.RIGHTS_TYPE_CITYKING.getCode()).supplyOrderId(LocalDateTimeUtils.todayStr()).build();
                DriverRightsObject driverRightsObject = new DriverRightsObjectImpl(domainBeanFactory, id, "");
                driverRightsObject.useRights(useRightsCondition);
            });
        }

        logger.info("dcs.driver.rights.use.cityking end");
    }

    private List<Long> queryDrivIdsByRights(int i, Logger logger) {
        List<Long> longlist = Lists.newArrayList();
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll().atPage(i, PAGE_SIZE).orderBy("id", true);
            builder.and().equal("rights_type", RightsTypeEnum.RIGHTS_TYPE_CITYKING.getCode(), Types.TINYINT);
            builder.and().equal("month_idx", LocalDateTimeUtils.monthIndexStr(), Types.VARCHAR);
            builder.and().notEqual("rights_status", RightsStatusEnum.RIGHTS_STATUS_CANCELLED.getStatus(), Types.TINYINT);
            List<DrivRightsPO> poList = drivRightsDao.query(builder);
            longlist = poList.stream().map(o -> o.getDrivId()).collect(Collectors.toList());
        } catch (Exception e) {
            logger.warn("failed to queryDrivIdsByRights ", e);
        }
        return longlist;
    }

    @QSchedule("dcs.driver.rights.cancell")
    public void cancellRights(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        Logger logger = monitor.getLogger();
        logger.info("dcs.driver.rights.cancell start");

        String data = parameter.getString("data");
        if(Strings.isBlank(data)){
            return;
        }
        List<RightsModel> list= JacksonUtil.parseArray(data,RightsModel.class);
        ShoppingActionUtils.tryActions(list,rightsModel->{
            lockService.executeInLock(String.format("dcs_driver_use_rights:%s_%s", rightsModel.getDrivId(), rightsModel.getRightsType()), 1000, () -> drivRightsDao.updateRightsCancell(rightsModel.getDrivId(),rightsModel.getRightsType(),rightsModel.getMonthIdx()));

        });

        logger.info("dcs.driver.rights.cancell end");
    }

    /**
     * 筛选需要do snapshot的城市
     */
    private List<Long> pickNeedDoSnapShotCityId(List<Long> allCityIdList, Logger logger) {
        // 获取每个城市的当地时间
        Map<Long, LocalDateTime> localCurrentTimeMap = geoGateway.getLocalCurrentTime(allCityIdList);
        List<Long> calcCityIdList = Lists.newArrayList();
        for (Map.Entry<Long, LocalDateTime> entry : localCurrentTimeMap.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            // 每月1号不执行job，由消息触发
            if (entry.getValue().getDayOfMonth() == 1) {
                continue;
            }
            int hour = entry.getValue().getHour();
            int calcHour = Integer.parseInt(StringUtils.defaultIfBlank(systemQConfig.getString(DEFAULT_DO_SNAPSHOT_HOUR_KEY), "1"));
            if (hour != calcHour) {
                // 不是当地凌晨一点，不执行
                continue;
            }
            if (directorRedis.get(buildCityDoSnapshotPointKey(entry.getKey(), entry.getValue())) != null) {
                // 在对应时刻，但该城市当日已计算过，不执行
                continue;
            }
            calcCityIdList.add(entry.getKey());
        }
        // 当地时间在对应时间点，且尚未计算过的城市
        logger.info("pick do snapshot city id list: " + JsonUtil.toJson(calcCityIdList));
        return calcCityIdList;
    }

    private String buildCityDoSnapshotPointKey(Long cityId, LocalDateTime localDateTime) {
        return "dcs:driver:domain:point:snapshot:city:" + cityId + ":" + LocalDateTimeUtils.formatDate(localDateTime, LocalDateTimeUtils.DATE_FORMAT_DAY_KEY);
    }
}
