package com.ctrip.dcs.driver.account.application.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.basebiz.callcenter.splitservice.contract.EnumCallDirection;
import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType;
import com.ctrip.dcs.driver.account.application.cache.AccountInfoCache;
import com.ctrip.dcs.driver.account.application.cache.AccountUDLCache;
import com.ctrip.dcs.driver.account.application.cache.AppPushMessageCache;
import com.ctrip.dcs.driver.account.application.dto.RegisterAccountResult;
import com.ctrip.dcs.driver.account.domain.condition.PhoneInfoCondition;
import com.ctrip.dcs.driver.account.domain.condition.RegisterDriverUdlCondition;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.PhoneNumberServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.common.AccountExceptionCode;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountType;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.GlobalIdRecordDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.GlobalIdRecordPO;
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper;
import com.ctrip.dcs.driver.account.infrastructure.gateway.EmailServiceGateway;
import com.ctrip.dcs.driver.account.infrastructure.gateway.QunarAccountGateway;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.ChineseConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.EmailNotifyConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.*;
import com.ctrip.dcs.driver.account.infrastructure.value.emil.EmailConfigInfo;
import com.ctrip.dcs.driver.convert.AccountInfoConvert;
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType;
import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.TmsTransportProxyService;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.driver.domain.infrastructure.dto.account.AccountBaseDto;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.gateway.AccountRepository;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.driver.message.dto.AccountBaseInfoUpdateMessageDTO;
import com.ctrip.dcs.driver.message.dto.AccountIdentitySourceDTO;
import com.ctrip.dcs.driver.message.dto.PushMessageDTO;
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO;
import com.ctrip.dcs.driver.value.account.AccountUidInfo;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.producer.MessageProducerProvider;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class AccountServiceImpl implements AccountService {
    public static final Logger log = LoggerFactory.getLogger(AccountServiceImpl.class);

    @Autowired
    private UserCenterAccountGateway userCenterAccountGateway;
    @Autowired
    private AccountMapperDao accountMapperDao;
    @Autowired
    private AccountBaseInfoDao accountBaseInfoDao;
    @Autowired
    private GlobalIdRecordDao globalIdRecordDao;
    @Autowired
    private QunarAccountGateway qunarAccountGateway;
    @Autowired
    private AccountInfoCache accountInfoCache;
    @Autowired
    private DriverAccountConfig driverAccountConfig;
    @Autowired
    private ArchCoreInfoRepository archCoreInfoService;
    @Autowired
    private LockService lockService;
    @Autowired
    private TmsTransportProxyService tmsTransportProxyService;
    @Autowired
    private ChineseConfig chineseConfig;
    @Autowired
    private EmailNotifyConfig emailNotifyConfig;
    @Autowired
    private EmailServiceGateway emailServiceGateway;
    @Autowired
    private AccountChangeLogHelper accountChangeLogHelper;
    @Autowired
    private AppPushMessageCache appPushMessageCache;
    @Autowired
    private CommonMultipleLanguages commonMultipleLanguages;
    @Autowired
    private PushConfigRedisLogic pushConfigRedisLogic;

    @Autowired
    private TripUniversityService tripUniversityService;

    @Autowired
    private PhoneNumberServiceProxy phoneNumberService;

    @Autowired
    AccountUDLCache accountUDLCache;

    @Autowired
    AccountInfoConvert accountInfoConvert;
    @Autowired
    AccountRepository accountRepository;

    private MessageProducerProvider messageProducerProvider = new MessageProducerProvider();


    @Override
    public AccountBaseInfoPO register(RegisterNewAccountParam param) {
        // 参数预处理，加密
        preProcessParam(param);
        log.info("start register new account", JsonUtil.toString(param));
        // 手机号加锁
        return lockService.executeInLock(String.format(Constants.ACCOUNT_LOCK_KEY_PATTERN, param.getCountryCode(), param.getPhoneNumber()),
                driverAccountConfig.getAccountLockWaitMills(), () -> doRegister(param));
    }

    @Override
    public AccountUidInfo registerV1(RegisterNewAccountV1Param param) {
        //需要手机号加锁
        log.info("start registerV1 new account", JsonUtil.toString(param));
        String key = String.format(Constants.ACCOUNT_LOCK_KEY_PATTERN_V1, param.getCountryCode(), param.getPhoneNumber());
        return lockService.executeInLock(key, driverAccountConfig.getAccountLockWaitMills(), () -> registerAccountV1(param));
    }
    protected AccountUidInfo registerAccountV1(RegisterNewAccountV1Param param) {
        AccountBaseDto accountBaseDto=  convertAccountBaseDto(param);
        AccountUidInfo accountUidInfo = userCenterAccountGateway.queryAccountUid(accountBaseDto);
        String uid = Optional.ofNullable(accountUidInfo).map(AccountUidInfo::getUid).orElse(null);
        String udl = Optional.ofNullable(accountUidInfo).map(AccountUidInfo::getUdl).orElse(null);
        RegisterAccountResult registerResult = null;
        if (StringUtils.isBlank(uid)) {
            // 用户中心注册
            registerResult = userCenterAccountGateway.registerNewAccount(accountBaseDto);
            uid = registerResult.getUid();
            udl = registerResult.getUdl();
            accountUidInfo = new AccountUidInfo(uid, udl);
        }
        // 落关联关系表
        try {
            saveAccountV1(accountBaseDto, uid, udl);
        } catch (Exception e) {
            Throwable cause = e.getCause();
            if (cause instanceof SQLIntegrityConstraintViolationException) {
                CommonLogger.INSTANCE.warn("RegisterAccountEngine", "have been register");
            } else {
                throw new BizException("500", "failed to register account");
            }

        }
        return accountUidInfo;
    }

    @Transactional //加上事务
    private void saveAccountV1(AccountBaseDto accountBaseDto, String uid, String udl) {
        accountMapperDao.insert(buildRecord(accountBaseDto, uid, udl));
    }

    private AccountBaseDto convertAccountBaseDto(RegisterNewAccountV1Param request) {
        AccountBaseDto accountBaseDto = new AccountBaseDto();
        accountBaseDto.setSource(request.getSource());
        accountBaseDto.setSourceId(request.getSourceId());
        accountBaseDto.setAccountType(request.getAccountType());
        accountBaseDto.setBindAccountType(request.getBindAccountType());
        accountBaseDto.setCountryCode(request.getCountryCode());
        accountBaseDto.setPhoneNumber(request.getPhoneNumber());
        accountBaseDto.setEmail(request.getEmail());
        accountBaseDto.setOperator(request.getOperator());
        accountBaseDto.setCityId(request.getCityId());
        accountBaseDto.setOversea(request.getOversea());
        return accountBaseDto;
    }

    private AccountMapperPO buildRecord(AccountBaseDto request, String uid, String udl) {
        AccountMapperPO accountMapperPO = new AccountMapperPO();
        accountMapperPO.setUid(uid);
        accountMapperPO.setSource(request.getSource());
        accountMapperPO.setSourceId(request.getSourceId());
        accountMapperPO.setIsValid(1);
        accountMapperPO.setProviderDataLocation(udl);
        return accountMapperPO;
    }


    protected AccountBaseInfoPO doRegister(RegisterNewAccountParam param) {
        Cat.logEvent(CatEventType.REGISTER_SOURCE, param.getSource());
        Cat.logEvent(CatEventType.REGISTER_COUNTRY, param.getCountryCode());
        // 检查sourceId 与 手机号对应关系
        checkPhone(param);

        AccountBaseInfoPO accountBaseInfo;

        // 查询用户uid
        String uid;
        AccountUidInfo accountUidInfo;
        accountBaseInfo = accountBaseInfoDao.queryByMobilePhone(param.getCountryCode(), param.getPhoneNumber());
        if (accountBaseInfo != null) {
            log.info("register account get uid", "table");
            uid = accountBaseInfo.getUid();
            accountUidInfo = new AccountUidInfo(accountBaseInfo.getUid(), accountBaseInfo.getProviderDataLocation());
            if (Objects.equals(accountBaseInfo.getIsOversea(), 1) ^ BooleanUtils.isTrue(param.getIsOversea())) {
                // 境内外信息不一致
                log.warn("register account area conflict", String.format("table: %s, req: %s", accountBaseInfo.getIsOversea(), param.getIsOversea()));
                Cat.logEvent(CatEventType.REGISTER_AREA_CONFLICT, "1");
            }
            Cat.logEvent(CatEventType.REGISTER_UID_FROM, "Table");
        } else {
            // 本地账户不存在，查询用户中心
            log.info("register account get uid", "user center");
            accountUidInfo = userCenterAccountGateway.queryAccountUidByPhone(param.getCountryCode(), param.getPhoneNumber());
            uid = Optional.ofNullable(accountUidInfo).map(AccountUidInfo::getUid).orElse(null);
            Cat.logEvent(CatEventType.REGISTER_UID_FROM, StringUtils.isNotBlank(uid) ? "UserCenter" : "None");
        }
        checkPayoneerAccountId(uid, param.getPayoneerAccountId());

        List<AccountMapperPO> identityList = Lists.newArrayList();
        if (accountUidInfo != null) {
            identityList = accountMapperDao.queryByUid(uid);
            // 用户已注册过，检查sourceId 跟已经注册过的sourceId是否一致，不一致则报错，
            // 因为司机id,和司导id 必须一致，再注册前，会调用GenerateGlobalIdEngine，去生成司机id
            checkSourceId(identityList, param);
        }

        List<AccountChangeLog> logList = Lists.newArrayList();
        boolean accountUpdate = false;
        if (accountBaseInfo != null) {
            String beforeUpdate = JsonUtil.toString(accountBaseInfo);
            // 本地有账户信息，更新 //todo：这里更新 没有区分境外，境内系统呢？
            accountUpdate = repeatRegisterUpdateAccount(accountBaseInfo, param);
            String afterUpdate = JsonUtil.toString(accountBaseInfo);
            logList.add(new AccountChangeLog(AccountChangeLogType.REGISTER_UPDATE_ACCOUNT, param.getSource(), uid, beforeUpdate, afterUpdate));
        } else {
            // 本地没有账户信息，新建
            accountBaseInfo = createNewAccount(param, accountUidInfo);
            logList.add(new AccountChangeLog(AccountChangeLogType.CREATE_NEW_ACCOUNT, param.getSource(), accountBaseInfo.getUid(), "", JsonUtil.toString(accountBaseInfo)));
        }

        // 境内 司机或司导生成ppm
        if (Objects.equals(accountBaseInfo.getIsOversea(), 0) && StringUtils.isBlank(accountBaseInfo.getPpmAccountId()) && (
                AccountSouceEnum.isDriverGuide(param.getSource()) || AccountSouceEnum.isDriver(param.getSource()))) {
            accountBaseInfo.setPpmAccountId(qunarAccountGateway.getPPMAccountIDFromQunar(param.getSourceId()));
            log.info("register account create ppm account", accountBaseInfo.getPpmAccountId());
        }

        // 保存账户信息
        AccountMapperPO targetIdentity = identityList.stream().filter(o -> Objects.equals(o.getSource(), param.getSource())).findAny().orElse(null);
        if (targetIdentity == null) {
            // 身份不存在
            log.info("register account add new identity", "identity is null");
            targetIdentity = buildAccountMapper(accountBaseInfo.getUid(), accountBaseInfo.getProviderDataLocation(), param.getSource(), param.getSourceId(), param.getValid());
            identityList.add(targetIdentity);
            logList.add(new AccountChangeLog(AccountChangeLogType.REGISTER_ADD_IDENTITY, param.getSource(), accountBaseInfo.getUid(), "", JsonUtil.toString(targetIdentity)));
        } else {
            // 身份已存在
            log.info("register account repeat identity", JsonUtil.toString(targetIdentity));
            String before = JsonUtil.toString(targetIdentity);
            targetIdentity.setIsValid(BooleanUtils.isFalse(param.getValid()) ? 0 : 1);
            String update = JsonUtil.toString(targetIdentity);
            logList.add(new AccountChangeLog(AccountChangeLogType.REGISTER_UPDATE_IDENTITY, param.getSource(), accountBaseInfo.getUid(), before, update));
        }

        saveAccount(accountBaseInfo, targetIdentity);
        // 检查uid状态，是否需要解冻
        checkUidUserCenterState(uid);

        // 更新缓存
        accountInfoCache.delAccount(accountBaseInfo.getUid());

        // 存在多身份时，发送账户变更消息，通知其他供应链
        if (accountUpdate) {
            sendAccountBaseInfoChangeMessage(uid, param.getSource(), identityList);
        }

        accountChangeLogHelper.saveLog(logList);

        // 同步到携程大学
        tripUniversityService.updateTripUniversityAccountAsync(TripUniversityDriverDTO.builder().uid(accountBaseInfo.getUid()).build());
        return accountBaseInfo;
    }

    @Override
    public UpdateAccountResponseType updateAccount(UpdateAccountParam param) {
        Cat.logEvent(CatEventType.UPDATE_SOURCE, param.getSource());
        // 参数预处理，加密
        preProcessParam(param);
        log.info("update account start", JsonUtil.toString(param));

        AccountBaseInfoPO accountBaseInfo = accountBaseInfoDao.queryByUID(param.getUid());
        if (accountBaseInfo == null) {
            Cat.logEvent(CatEventType.UPDATE_RESULT, "NoAccount");
            log.warn("update account failed, account not exist");
            return ServiceResponseUtils.fail(new UpdateAccountResponseType(), AccountExceptionCode.ACCOUNT_NOT_EXIST.getCode(), AccountExceptionCode.ACCOUNT_NOT_EXIST.getMsg());
        }
        return lockService.executeInLock(String.format(Constants.ACCOUNT_UPDATE_KEY_PATTERN, param.getUid()),
                driverAccountConfig.getAccountLockWaitMills(), () -> doUpdateAccount(accountBaseInfo, param));
    }

    protected UpdateAccountResponseType doUpdateAccount(AccountBaseInfoPO accountBaseInfo, UpdateAccountParam param) {
        UpdateAccountResponseType response = new UpdateAccountResponseType();
        String uid = param.getUid();
        log.info("update account", String.format("before: %s, param: %s", JsonUtil.toString(accountBaseInfo), JsonUtil.toString(param)));

        checkPayoneerAccountId(accountBaseInfo.getUid(), param.getPayoneerAccountId());
        checkUpdatePhoneAndEmail(accountBaseInfo, param);

        String beforeUpdate = JsonUtil.toString(accountBaseInfo);

        boolean updateFlag = false;
        if (StringUtils.isNotBlank(param.getName()) && ObjectUtils.notEqual(accountBaseInfo.getName(), param.getName())) {
            accountBaseInfo.setName(param.getName());
            Cat.logEvent(CatEventType.UPDATE_FIELD, "name");
            updateFlag = true;
        }


        if (StringUtils.isNotBlank(param.getIdCardNo()) && ObjectUtils.notEqual(accountBaseInfo.getIdCardNo(), param.getIdCardNo())) {
            Cat.logEvent(CatEventType.UPDATE_FIELD, "idCardNo");
            accountBaseInfo.setIdCardNo(param.getIdCardNo());
            updateFlag = true;
        }

        if (StringUtils.isNotBlank(param.getEmail()) && ObjectUtils.notEqual(accountBaseInfo.getEmail(), param.getEmail())) {
            // 用户中心换绑
            Cat.logEvent(CatEventType.UPDATE_FIELD, "email");
            log.info("update account bind new email", param.getEmail());
            boolean result = userCenterAccountGateway.bindEmail(uid, param.getEmail(), param.getSource());
            if (!result) {
                log.info("update account bind new email failed");
                return ServiceResponseUtils.fail(response, AccountExceptionCode.INVOKE_USER_CENTER_BIND_ERROR.getCode(), "bind email error");
            }
            accountBaseInfo.setEmail(param.getEmail());
            updateFlag = true;
        }

        if (StringUtils.isNotBlank(param.getCountryCode()) || StringUtils.isNotBlank(param.getPhoneNumber())) {
            if (ObjectUtils.notEqual(accountBaseInfo.getCountryCode(), param.getCountryCode()) || ObjectUtils.notEqual(accountBaseInfo.getPhoneNumber(), param.getPhoneNumber())) {
                // 用户中心换绑
                Cat.logEvent(CatEventType.UPDATE_FIELD, "phone");
                log.info("update account bind new phone", param.getCountryCode() + " - " + param.getPhoneNumber());
                boolean result = userCenterAccountGateway.bindMobilePhone(uid, param.getCountryCode(), param.getPhoneNumber(), param.getSource());
                if (!result) {
                    log.info("update account bind new phone failed");
                    return ServiceResponseUtils.fail(response, AccountExceptionCode.INVOKE_USER_CENTER_BIND_ERROR.getCode(), "bind phone error");
                }
                accountBaseInfo.setCountryCode(param.getCountryCode());
                accountBaseInfo.setPhoneNumber(param.getPhoneNumber());
                updateFlag = true;
            }
        }

        if (ObjectUtils.notEqual(accountBaseInfo.getPayoneerAccountId(), param.getPayoneerAccountId())) {
            Cat.logEvent(CatEventType.UPDATE_FIELD, "payoneerAccount");
            accountBaseInfo.setPayoneerAccountId(param.getPayoneerAccountId());
            updateFlag = true;
        }

        if (StringUtils.isNotBlank(param.getPpmAccountId()) && ObjectUtils.notEqual(accountBaseInfo.getPpmAccountId(), param.getPpmAccountId())) {
            Cat.logEvent(CatEventType.UPDATE_FIELD, "ppmAccount");
            accountBaseInfo.setPpmAccountId(param.getPpmAccountId());
            updateFlag = true;
        }
        accountBaseInfo.setModifyUser(param.getModifyUser());
        if (updateFlag) {
            accountBaseInfoDao.update(accountBaseInfo);
            List<AccountMapperPO> identityList = accountMapperDao.queryByUid(uid);
            // 更新缓存
            accountInfoCache.delAccount(accountBaseInfo.getUid());
            // 发账户变更消息
            sendAccountBaseInfoChangeMessage(uid, param.getSource(), identityList);
            // 同步到携程大学
            tripUniversityService.updateTripUniversityAccountAsync(TripUniversityDriverDTO.builder().uid(uid).build());

            log.info("update account finish", "1");
            Cat.logEvent(CatEventType.UPDATE_RESULT, "success");
        } else {
            Cat.logEvent(CatEventType.UPDATE_RESULT, "NoUpdate");
        }
        String afterUpdate = JsonUtil.toString(accountBaseInfo);
        accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.UPDATE_ACCOUNT, param.getSource(), uid, beforeUpdate, afterUpdate));
        return ServiceResponseUtils.success(response);
    }

    @Override
    public AccountInfoDTO getAccountInfoByUID(String uid) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "UID");
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        AccountInfoDTO accountInfo = accountInfoCache.getAccount(uid);
        if (accountInfo == null) {
            AccountBaseInfoPO accountBaseInfoPO = accountBaseInfoDao.queryByUID(uid);
            if (accountBaseInfoPO != null) {
                Cat.logEvent(CatEventType.QUERY_DATA_SOURCE, "DB");
                List<AccountMapperPO> accountMapperPOList = accountMapperDao.queryByUid(uid);
                accountInfo = buildAccountInfoDTO(accountBaseInfoPO, accountMapperPOList);
                accountInfoCache.saveAccount(accountInfo, driverAccountConfig.getAccountInfoRedisCacheSeconds());
            } else {
                Cat.logEvent(CatEventType.QUERY_DATA_SOURCE, "None");
            }
        } else {
            Cat.logEvent(CatEventType.QUERY_DATA_SOURCE, "Cache");
        }
        return accountInfo;
    }

    //通过司机id查询udl
    @Override
    public List<AccountUDLDo> getAccountUDLBySource(List<String> sourceIds) {
        Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "Query");
        List<AccountUDLDo> accountUDL = accountUDLCache.getAccountUDLBySourceId(sourceIds);
        //找到缓存中没有的
        List<String> sourceIdsNoCache = sourceIds.stream().filter(sourceId -> !accountUDL.stream().map(AccountUDLDo::getSourceId).toList().contains(sourceId)).collect(Collectors.toList());
        if (!sourceIdsNoCache.isEmpty()) {
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "QueryFromDB");
            List<AccountUDLDo> accountUDLDoList = accountRepository.batchQueryBySourceId(sourceIdsNoCache);
            if (CollectionUtils.isNotEmpty(accountUDLDoList)) {
                accountUDLCache.saveAccountUDLBySourceId(accountUDLDoList);
                accountUDL.addAll(accountUDLDoList);
            }
        }
        return  accountUDL;
    }

    @Override
    public List<AccountUDLDo> getAccountUDLByUid(List<String> uids) {
        Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "Query");
        List<AccountUDLDo> accountUDL = accountUDLCache.getAccountUDLByUid(uids);
        //找到缓存中没有的
        List<String> uidsNoCache = uids.stream().filter(uid -> !accountUDL.stream().map(AccountUDLDo::getUid).toList().contains(uid)).collect(Collectors.toList());
        if (!uidsNoCache.isEmpty()) {
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "QueryFromDB");
            List<AccountUDLDo> accountUDLDoList = accountRepository.batchQueryByUid(uidsNoCache);
            if (CollectionUtils.isNotEmpty(accountUDLDoList)) {
                accountUDLCache.saveAccountUDLByUid(accountUDLDoList);
                accountUDL.addAll(accountUDLDoList);
            }
        }
        return  accountUDL;
    }

    private List<AccountUDLDo> convertAccountUDLDos(List<AccountMapperPO> driverUdlPOSDb) {
        if(CollUtil.isNotEmpty(driverUdlPOSDb)){
            return CollUtil.distinct(driverUdlPOSDb.stream().map(accountMapperPO -> {
                return AccountUDLDo
                        .builder()
                        .sourceId(accountMapperPO.getSourceId())
                        .uid(accountMapperPO.getUid())
                        .udl(accountMapperPO.getProviderDataLocation()).build();
            }).collect(Collectors.toList()), AccountUDLDo::getSourceId,true);
        }
        return Lists.newArrayList();

    }


    @Override
    public AccountInfoDTO getAccountInfoBySource(String source, String sourceId) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "Source");
        if (StringUtils.isAnyBlank(source, sourceId)) {
            return null;
        }
        String uid = accountInfoCache.getUIDBySource(source, sourceId);
        if (StringUtils.isBlank(uid)) {
            AccountMapperPO accountMapperPO = accountMapperDao.queryBySource(source, sourceId);
            if (accountMapperPO != null) {
                uid = accountMapperPO.getUid();
                accountInfoCache.saveSourceUIDMapping(source, accountMapperPO.getSourceId(), uid, driverAccountConfig.getAccountSourceUIDMappingRedisCacheSeconds());
            }
        }
        return getAccountInfoByUID(uid);
    }

    @Override
    public List<AccountInfoDTO> getAccountInfoByName(String name) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "Name");
        if (StringUtils.isBlank(name)) {
            return Lists.newArrayList();
        }
        List<AccountBaseInfoPO> accountBaseInfoList = accountBaseInfoDao.queryByName(name);
        if (CollectionUtils.isEmpty(accountBaseInfoList)) {
            return Lists.newArrayList();
        }
        return accountBaseInfoList.stream().map(o -> getAccountInfoByUID(o.getUid())).collect(Collectors.toList());
    }

    @Override
    public List<AccountInfoDTO> getAccountInfoByIdCard(String idCard) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "IdCard");
        if (StringUtils.isBlank(idCard)) {
            return Lists.newArrayList();
        }
        idCard = archCoreInfoService.encryptIdCard(idCard);
        List<AccountBaseInfoPO> accountBaseInfoList = accountBaseInfoDao.queryByIdCardNo(idCard);
        if (CollectionUtils.isEmpty(accountBaseInfoList)) {
            return Lists.newArrayList();
        }
        return accountBaseInfoList.stream().map(o -> getAccountInfoByUID(o.getUid())).collect(Collectors.toList());
    }

    @Override
    public AccountInfoDTO getAccountInfoByMobilePhone(String countryCode, String phoneNumber) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "Phone");
        if (StringUtils.isAnyBlank(countryCode, phoneNumber)) {
            return null;
        }
        phoneNumber = archCoreInfoService.encryptByType(KeyType.Phone, phoneNumber);
        // 去用户中心查询uid（不直接查表的原因：查询的手机号或者表里的手机号可能带前导0，加密后不能匹配）
        AccountUidInfo accountUidInfo = userCenterAccountGateway.queryAccountUidByPhone(countryCode, phoneNumber);
        if (accountUidInfo == null) {
            return null;
        }
        return getAccountInfoByUID(accountUidInfo.getUid());
    }

    @Override
    public List<AccountInfoDTO> batchGetAccountInfoByMobilePhone(List<PhoneInfoCondition> phoneInfoConditions) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "BatchPhone");
        if (CollectionUtils.isEmpty(phoneInfoConditions)) {
            return Lists.newArrayList();
        }
        //手机号 批量查询账号uid信息
        List<AccountUidInfo> accountUidInfos = getAccountUidInfos(phoneInfoConditions);

        if (CollectionUtils.isEmpty(accountUidInfos)) {
            return Lists.newArrayList();
        }
        List<String> uidList = accountUidInfos.stream().map(AccountUidInfo::getUid).toList();

        return batchGetAccountInfoByUID(uidList);
    }

    private List<AccountUidInfo> getAccountUidInfos(List<PhoneInfoCondition> phoneInfoConditions) {
        if (CollectionUtils.isEmpty(phoneInfoConditions)) {
            return Lists.newArrayList();
        }
        List<String> phoneNumberList = phoneInfoConditions.stream().map(PhoneInfoCondition::getPhoneNumber).collect(Collectors.toList());
        Map<String, String> phoneNumberEnMap = archCoreInfoService.batchEncryptByType(KeyType.Phone, phoneNumberList);
        phoneInfoConditions = phoneInfoConditions.stream().map(t -> PhoneInfoCondition.builder().phoneNumber(phoneNumberEnMap.get(t.getPhoneNumber())).countryCode(t.getCountryCode()).build()).toList();
        List<AccountUidInfo> result = new ArrayList<>();
        List<PhoneInfoCondition> listRpc = phoneInfoConditions.stream().filter(t -> StringUtils.isNotBlank(t.getCountryCode()) && StringUtils.isNotBlank(t.getPhoneNumber())).toList();
        List<AccountUidInfo> result1 = getAccountUidInfosByPhoneFromRpc(listRpc);

        List<PhoneInfoCondition> listDb = phoneInfoConditions.stream().filter(t -> StringUtils.isBlank(t.getCountryCode()) && StringUtils.isNotBlank(t.getPhoneNumber())).toList();
        List<AccountUidInfo> result2 = getAccountUidInfosByPhoneFromBb(listDb);

        if (CollUtil.isNotEmpty(result1)) {
            result.addAll(result1);
        }
        if (CollUtil.isNotEmpty(result2)) {
            result.addAll(result2);
        }
        result = CollUtil.distinct(result, AccountUidInfo::getUid, true);

        return result;
    }

    private List<AccountUidInfo> getAccountUidInfosByPhoneFromBb(List<PhoneInfoCondition> phoneInfoConditions) {
        List<String> phone = phoneInfoConditions.stream().map(PhoneInfoCondition::getPhoneNumber).toList();
        if (CollectionUtils.isEmpty(phone)) {
            return Lists.newArrayList();
        }
        List<AccountBaseInfoPO> accountBaseInfoPOS = accountBaseInfoDao.batchQueryByMobilePhone(phone);
        return convertAccountUidInfo(accountBaseInfoPOS);
    }

    private static @Nullable List<AccountUidInfo> convertAccountUidInfo(List<AccountBaseInfoPO> accountBaseInfoPOS) {
        if (accountBaseInfoPOS != null) {
            return accountBaseInfoPOS.stream().map(t -> new AccountUidInfo(t.getUid(), t.getProviderDataLocation())).toList();
        }
        return Lists.newArrayList();
    }

    private List<AccountUidInfo> getAccountUidInfosByPhoneFromRpc(List<PhoneInfoCondition> phoneInfoConditions) {
        return userCenterAccountGateway.batchQueryAccountUidByPhone(phoneInfoConditions);
    }


    @Override
    public AccountInfoDTO getAccountInfoByEmail(String email) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "Email");
        if (StringUtils.isBlank(email)) {
            return null;
        }
        email = archCoreInfoService.encryptByType(KeyType.Mail, email);
        AccountBaseInfoPO accountBaseInfoPO = accountBaseInfoDao.queryByEmail(email);
        if (accountBaseInfoPO == null) {
            return null;
        }
        return getAccountInfoByUID(accountBaseInfoPO.getUid());
    }

    @Override
    public AccountInfoDTO getAccountInfoByPayoneer(String payoneerAccountId) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "Payoneer");
        if (StringUtils.isBlank(payoneerAccountId)) {
            return null;
        }
        AccountBaseInfoPO accountBaseInfoPO = accountBaseInfoDao.queryByPayoneerAccountId(payoneerAccountId);
        if (accountBaseInfoPO == null) {
            return null;
        }
        return getAccountInfoByUID(accountBaseInfoPO.getUid());
    }


    @Override
    public List<AccountInfoDTO> batchGetAccountInfoByUID(List<String> uidList) {
        Cat.logEvent(CatEventType.QUERY_TYPE, "BatchUid");
        if (CollectionUtils.isEmpty(uidList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.size(uidList) > driverAccountConfig.getBatchQueryAccountCountLimit()) {
            throw new BizException(AccountExceptionCode.QUERY_SIZE_TOO_LONG.getCode(), "query size too long, max : " + driverAccountConfig.getBatchQueryAccountCountLimit());
        }
        List<AccountInfoDTO> accountInfoList = accountInfoCache.batchGetAccount(uidList);
        List<String> cachedUIDList = accountInfoList.stream().map(AccountInfoDTO::getUid).distinct().collect(Collectors.toList());
        List<String> missedUIDList = uidList.stream().filter(o -> !cachedUIDList.contains(o)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(missedUIDList)) {
            List<AccountBaseInfoPO> missedAccountBaseInfoList = accountBaseInfoDao.batchQueryByUID(missedUIDList);
            if (CollectionUtils.isNotEmpty(missedAccountBaseInfoList)) {
                List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryByUid(missedUIDList);
                Map<String, List<AccountMapperPO>> accountMapperMap = accountMapperPOList.stream().collect(Collectors.groupingBy(AccountMapperPO::getUid));
                List<AccountInfoDTO> missedAccountInfoDTOList = missedAccountBaseInfoList.stream().map(o -> buildAccountInfoDTO(o, accountMapperMap.getOrDefault(o.getUid(), Lists.newArrayList()))).collect(Collectors.toList());
                accountInfoCache.batchSaveAccount(missedAccountInfoDTOList, driverAccountConfig.getAccountInfoRedisCacheSeconds());
                accountInfoList.addAll(missedAccountInfoDTOList);
            }
        }
        return accountInfoList;
    }


    @Override
    public AccountInfoDTO getAccountByDriverId(Long driverId) {
        if (driverId == null || driverId <= 0) {
            return null;
        }
        List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryBySource(Lists.newArrayList(AccountSouceEnum.DRIVER_SOURCE.getName(), AccountSouceEnum.DRIVER_GUIDE.getName()), driverId.toString());
        if (CollectionUtils.isEmpty(accountMapperPOList)) {
            return null;
        }
        return getAccountInfoByUID(accountMapperPOList.get(0).getUid());
    }

    @Override
    public List<AccountInfoDTO> batchGetAccountByDriverId(List<Long> driverIdList) {
        if (CollectionUtils.isEmpty(driverIdList)) {
            return Lists.newArrayList();
        }
        List<String> sourceIdList = driverIdList.stream().filter(Objects::nonNull).distinct().map(Object::toString).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sourceIdList)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.size(sourceIdList) > driverAccountConfig.getBatchQueryAccountCountLimit()) {
            throw new BizException(AccountExceptionCode.QUERY_SIZE_TOO_LONG.getCode(), "query size too long, max : " + driverAccountConfig.getBatchQueryAccountCountLimit());
        }
        List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryBySource(Lists.newArrayList(AccountSouceEnum.DRIVER_SOURCE.getName(), AccountSouceEnum.DRIVER_GUIDE.getName()), sourceIdList);
        if (CollectionUtils.isEmpty(accountMapperPOList)) {
            return Lists.newArrayList();
        }
        List<String> uidList = accountMapperPOList.stream().map(AccountMapperPO::getUid).distinct().collect(Collectors.toList());
        return batchGetAccountInfoByUID(uidList);
    }

    @Override
    public List<AccountMapperPO> getIdentityByUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            return Lists.newArrayList();
        }
        return accountMapperDao.queryByUid(uid);
    }

    @Override
    public List<AccountMapperPO> getIdentityByDriverId(Long driverId) {
        if (driverId == null || driverId <= 0) {
            return Lists.newArrayList();
        }
        return accountMapperDao.batchQueryBySource(Lists.newArrayList(AccountSouceEnum.DRIVER_SOURCE.getName(), AccountSouceEnum.DRIVER_GUIDE.getName()), driverId.toString());
    }

    @Override
    public void updateAccountIdentityState(String uid, AccountSouceEnum sourceEnum, boolean valid) {
        log.info("update account identity start", uid + " : " + sourceEnum.getName() + " : " + valid);
        if (StringUtils.isBlank(uid)) {
            Cat.logEvent(CatEventType.UPDATE_IDENTITY_STATE_RESULT, "NoUid");
            log.warn("update account identity", "no uid");
            return;
        }
        Cat.logEvent(CatEventType.UPDATE_IDENTITY_STATE_SOURCE, sourceEnum.getName());
        List<AccountMapperPO> identityList = accountMapperDao.queryByUid(uid);
        AccountMapperPO identity = Optional.ofNullable(identityList).orElse(Lists.newArrayList()).stream()
                .filter(o -> Objects.equals(o.getSource(), sourceEnum.getName()))
                .findAny().orElse(null);
        if (identity == null) {
            Cat.logEvent(CatEventType.UPDATE_IDENTITY_STATE_RESULT, "NoIdentity");
            log.warn("update account identity", "no identity");
            return;
        }
        if (valid ^ Objects.equals(identity.getIsValid(), 1)) {
            String beforeUpdate = JsonUtil.toString(identity);
            identity.setIsValid(valid ? 1 : 0);
            accountMapperDao.update(identity);
            String afterUpdate = JsonUtil.toString(identity);
            accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.UPDATE_ACCOUNT_IDENTITY, sourceEnum.getName(), uid, beforeUpdate, afterUpdate));

            // 刷新缓存
            accountInfoCache.delAccount(uid);
            // 下线通知客户端（仅新版本）
            if (!valid) {
                log.info("send driver offline app push", JsonUtil.toString(identity));
                sendDriverOfflineAppPush(identity);
            }
            Cat.logEvent(CatEventType.UPDATE_IDENTITY_STATE_RESULT, "Success");
        } else {
            Cat.logEvent(CatEventType.UPDATE_IDENTITY_STATE_RESULT, "NoUpdate");
        }
    }


    @Override
    public void refreshAccountCache(String uid) {
        AccountBaseInfoPO accountBaseInfoPO = accountBaseInfoDao.queryByUID(uid);
        if (accountBaseInfoPO != null) {
            List<AccountMapperPO> accountMapperPOList = accountMapperDao.queryByUid(uid);
            AccountInfoDTO accountInfo = buildAccountInfoDTO(accountBaseInfoPO, accountMapperPOList);
            accountInfoCache.saveAccount(accountInfo, driverAccountConfig.getAccountInfoRedisCacheSeconds());
            log.info("refresh account cache", uid);
        } else {
            accountInfoCache.delAccount(uid);
            log.info("del account cache", uid);
        }
    }

    @Override
    public void reCalcDriverIdentityState(String uid) {
        List<AccountMapperPO> identityList = accountMapperDao.queryByUid(uid);
        if (CollectionUtils.isEmpty(identityList)) {
            log.info("re calc driver identity finish", "no identity");
            return;
        }
        // 没有司导身份，无需处理
        if (identityList.stream().noneMatch(o -> AccountSouceEnum.isDriverGuide(o.getSource()))) {
            log.info("re calc driver identity finish", "no driver guide identity");
            return;
        }
        AccountMapperPO driverIdentity = identityList.stream().filter(o -> AccountSouceEnum.isDriver(o.getSource())).findAny().orElse(null);
        // 司机状态有效，检查产线，是否需要无效
        if (driverIdentity != null && Objects.equals(driverIdentity.getIsValid(), 1)) {
            calcDriverState(driverIdentity);
            return;
        }
        log.info("re calc driver identity finish", "no driver identity or driver state is invalid");
    }

    @Override
    public NumberDTO splitPhoneNumber(String countryCode, String number) {
        SplitNumberRequestType request = new SplitNumberRequestType();
        request.setNumber("+" + countryCode + number);
        request.setDirection(EnumCallDirection.INBOUND);
        request.setRegionCode("CN");
        SplitNumberResponseType response = phoneNumberService.splitNumber(request);
        return Optional.ofNullable(response).map(o -> o.result).orElse(null);
    }

    @Override
    public AccountUDLDo registerDriverUdl(RegisterDriverUdlCondition registerDriverUdlCondition) {
        return lockService.executeInLock(String.format(Constants.ACCOUNT_OTA_CREATE_UDL_KEY_PATTERN, registerDriverUdlCondition.getDriverId()),
                driverAccountConfig.getAccountLockWaitMills(), () -> doRegisterOtaDriverUDl(registerDriverUdlCondition));
    }

    private AccountUDLDo doRegisterOtaDriverUDl(RegisterDriverUdlCondition registerDriverUdlCondition) {
        List<AccountUDLDo> accountUDL = accountUDLCache.getAccountUDLByUid(Lists.newArrayList(accountRepository.createOtaUid(registerDriverUdlCondition.getDriverId())));
        if (CollUtil.isNotEmpty(accountUDL)) {
            return accountUDL.getFirst();
        }
        AccountUDLDo accountUDLDo = accountRepository.saveOtaDriverUdl(registerDriverUdlCondition);
        if (accountUDLDo != null) {
            accountUDLCache.saveAccountUDLByUid(Lists.newArrayList(accountUDLDo));
        }
        return accountUDLDo;
    }


    private void calcDriverState(AccountMapperPO driverIdentity) {
        QueryDrvDetailDTOSOA driverDetail = tmsTransportProxyService.queryDriverDetail(Long.valueOf(driverIdentity.getSourceId()));
        if (driverDetail == null) {
            log.warn("re calc driver identity failed", "no driver detail");
            Cat.logEvent(CatEventType.RE_CALC_DRIVER_IDENTITY_STATE_RESULT, "NoDriver");
            return;
        }
        String beforeUpdate = JsonUtil.toString(driverIdentity);
        if (Constants.DRIVER_INVALID_STATUS.contains(driverDetail.getDrvStatus())) {
            // 司机状态已经是无效，则修改
            driverIdentity.setIsValid(0);
            accountMapperDao.update(driverIdentity);
            accountInfoCache.delAccount(driverIdentity.getUid());
            String afterUpdate = JsonUtil.toString(driverIdentity);
            accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.RE_CALC_DRIVER_IDENTITY, AccountSouceEnum.DRIVER_GUIDE.getName(), driverIdentity.getUid(), beforeUpdate, afterUpdate));
            log.info("re calc driver identity finish", "driver status invalid");
            Cat.logEvent(CatEventType.RE_CALC_DRIVER_IDENTITY_STATE_RESULT, "DriverStateInvalid");
            return;
        }
        // 司机状态仍然有效，检查产线，没有非包车产线，则无效
        boolean isValid = Optional.ofNullable(driverDetail.getProLineList()).orElse(Lists.newArrayList()).stream()
                .anyMatch(o -> driverAccountConfig.getDgTransferDriverProLine() != o);
        if (!isValid) {
            // 不存在除包车之外的其他产线，则改为无效
            driverIdentity.setIsValid(0);
            accountMapperDao.update(driverIdentity);
            accountInfoCache.delAccount(driverIdentity.getUid());
            Cat.logEvent(CatEventType.RE_CALC_DRIVER_IDENTITY_STATE_RESULT, "DriverProLineInvalid");
            log.info("re calc driver identity finish", "all driver pro line transfer");
            String afterUpdate = JsonUtil.toString(driverIdentity);
            accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.RE_CALC_DRIVER_IDENTITY, AccountSouceEnum.DRIVER_GUIDE.getName(), driverIdentity.getUid(), beforeUpdate, afterUpdate));
        }
    }

    /**
     * 新入驻&老账户同步更新账户信息，原则：缺少的字段补齐，冲突字段不更新，只告警
     */
    private boolean repeatRegisterUpdateAccount(AccountBaseInfoPO accountBaseInfo, RegisterNewAccountParam param) {
        boolean updateFlag = false;
        List<Triple<String, String, String>> conflictInfoList = Lists.newArrayList();
        if (StringUtils.isBlank(accountBaseInfo.getName()) && StringUtils.isNotBlank(param.getName())) {
            accountBaseInfo.setName(param.getName());
            updateFlag = true;
        } else if (ObjectUtils.notEqual(accountBaseInfo.getName(), param.getName())) {
            Cat.logEvent(CatEventType.REGISTER_CONFLICT_FIELD, "name");
            conflictInfoList.add(Triple.of("name", accountBaseInfo.getName(), param.getName()));
        }


        if (StringUtils.isBlank(accountBaseInfo.getIdCardNo()) && StringUtils.isNotBlank(param.getIdCardNo())) {
            accountBaseInfo.setIdCardNo(param.getIdCardNo());
            updateFlag = true;
        } else if (StringUtils.isNotBlank(accountBaseInfo.getIdCardNo()) && StringUtils.isNotBlank(param.getIdCardNo()) && ObjectUtils.notEqual(accountBaseInfo.getIdCardNo(), param.getIdCardNo())) {
            conflictInfoList.add(Triple.of("idCardNo", accountBaseInfo.getIdCardNo(), param.getIdCardNo()));
            Cat.logEvent(CatEventType.REGISTER_CONFLICT_FIELD, "idCardNo");
        }

        if (StringUtils.isBlank(accountBaseInfo.getEmail()) && StringUtils.isNotBlank(param.getEmail())) {
            accountBaseInfo.setEmail(param.getEmail());
            // 新增邮箱，去用户中心绑定
            userCenterAccountGateway.bindEmail(buildAccountRegisterParam(param), accountBaseInfo.getUid());
            updateFlag = true;

        } else if (StringUtils.isNotBlank(param.getEmail()) && ObjectUtils.notEqual(accountBaseInfo.getEmail(), param.getEmail())) {
            Cat.logEvent(CatEventType.REGISTER_CONFLICT_FIELD, "email");
            conflictInfoList.add(Triple.of("email", accountBaseInfo.getEmail(), param.getEmail()));
        }


        if (StringUtils.isBlank(accountBaseInfo.getPayoneerAccountId()) && StringUtils.isNotBlank(param.getPayoneerAccountId())) {
            accountBaseInfo.setPayoneerAccountId(param.getPayoneerAccountId());
            updateFlag = true;
        } else if (StringUtils.isNotBlank(accountBaseInfo.getPayoneerAccountId()) && StringUtils.isNotBlank(param.getPayoneerAccountId()) && ObjectUtils.notEqual(accountBaseInfo.getPayoneerAccountId(), param.getPayoneerAccountId())) {
            Cat.logEvent(CatEventType.REGISTER_CONFLICT_FIELD, "payoneerAccountId");
            conflictInfoList.add(Triple.of("payoneerAccountId", accountBaseInfo.getPayoneerAccountId(), param.getPayoneerAccountId()));
        }

        if (accountBaseInfo.getIsOversea() == null) {
            accountBaseInfo.setIsOversea(BooleanUtils.isTrue(param.getIsOversea()) ? 1 : 0);
            updateFlag = true;
        } else if (Objects.equals(accountBaseInfo.getIsOversea(), 1) ^ BooleanUtils.isTrue(param.getIsOversea())) {
            Cat.logEvent(CatEventType.REGISTER_CONFLICT_FIELD, "isOversea");
            conflictInfoList.add(Triple.of("isOversea", accountBaseInfo.getIsOversea().toString(), Optional.ofNullable(param.getIsOversea()).map(o -> o.toString()).orElse(null)));
        }
        //http://idev.ctripcorp.com?5399238 如果已经注册为境内，再来注册境外，则抛出异常，司机，司导只能同时存在 境外，或则 只能在境外 todo 跟(任文鹏) 进一步确定下
        boolean db_isOversea = accountBaseInfo.getIsOversea() == 1;
        if (ObjectUtils.notEqual(db_isOversea, param.getIsOversea())) {
            Cat.logEvent(CatEventType.REGISTER_CONFLICT_FIELD, "driver_register_area_error");
            log.warn("register account repeat", "driver_register_area_error:"+accountBaseInfo.getUid()+"db:"+accountBaseInfo.getIsOversea()+"param:"+param.getIsOversea());
            throw new BizException(AccountExceptionCode.DRIVER_REGISTER_AREA_ERROR.getCode(),AccountExceptionCode.DRIVER_REGISTER_AREA_ERROR.getMsg());
        }


        log.info("register account repeat", String.format("local : %s, param : %s", JsonUtil.toString(accountBaseInfo), JsonUtil.toString(param)));

        if (CollectionUtils.isNotEmpty(conflictInfoList)) {
            // 信息邮冲突，发邮件
            sendRegisterConflictEmail(accountBaseInfo.getUid(), conflictInfoList);
        }
        accountBaseInfo.setModifyUser(param.getModifyUser());
        return updateFlag;
    }

    private void sendRegisterConflictEmail(String uid, List<Triple<String, String, String>> conflictInfoList) {
        EmailConfigInfo emailConfig = emailNotifyConfig.getRegisterAccountInfoConflictConfig();
        if (CollectionUtils.isEmpty(conflictInfoList) || emailConfig == null) {
            return;
        }
        String conflictItem = conflictInfoList.stream().map(o -> String.format("%s（origin：%s，new：%s）", chineseConfig.get(o.getLeft()), o.getMiddle(), o.getRight()))
                .collect(Collectors.joining("、"));
        SendEmailCondition sendEmailCondition = new SendEmailCondition();
        sendEmailCondition.setSendCode(emailConfig.getSendCode());
        sendEmailCondition.setSender(emailConfig.getSenderEmail());
        sendEmailCondition.setReceiver(Lists.newArrayList(emailConfig.getReceiverEmails().split(",")));
        sendEmailCondition.setSubject(emailConfig.getSubjectTemplate());
        sendEmailCondition.setBodyContent(String.format(emailConfig.getContentTemplate(), uid, conflictItem));
        sendEmailCondition.setBodyHtml(true);
        emailServiceGateway.sendEmail(sendEmailCondition);
    }

    /**
     * 去创建新账户
     */
    private AccountBaseInfoPO createNewAccount(RegisterNewAccountParam param, AccountUidInfo accountUidInfo) {
        if (accountUidInfo == null) {
            // uid为空，先去用户中心注册
            RegisterAccountResult registerAccountResult = userCenterAccountGateway.registerNewAccount(buildAccountRegisterParam(param));
            accountUidInfo = new AccountUidInfo(registerAccountResult.getUid(), registerAccountResult.getUdl());
            String uid = registerAccountResult.getUid();
            log.info("register account from user center", JsonUtil.toString(registerAccountResult));
            accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.GENERATE_UID, param.getSource(), uid, "", uid));
        }

        AccountBaseInfoPO accountBaseInfo = new AccountBaseInfoPO();
        accountBaseInfo.setUid(accountUidInfo.getUid());
        accountBaseInfo.setProviderDataLocation(accountUidInfo.getUdl());
        accountBaseInfo.setName(param.getName());
        accountBaseInfo.setIdCardNo(param.getIdCardNo());
        accountBaseInfo.setCountryCode(param.getCountryCode());
        accountBaseInfo.setPhoneNumber(param.getPhoneNumber());
        accountBaseInfo.setEmail(param.getEmail());
        accountBaseInfo.setPayoneerAccountId(param.getPayoneerAccountId());
        if (param.getIsOversea() != null) {
            accountBaseInfo.setIsOversea(BooleanUtils.isTrue(param.getIsOversea()) ? 1 : 0);
        }
        // 仅司导刚上线时，司机存量账户同步时，司机需要传递已有ppm账户id，数据搬迁完，即可废弃
        if (StringUtils.isNotBlank(param.getPpmAccountId())) {
            accountBaseInfo.setPpmAccountId(param.getPpmAccountId());
        }
        accountBaseInfo.setRegisterSource(param.getSource());
        accountBaseInfo.setModifyUser(param.getModifyUser());
        return accountBaseInfo;
    }


    private void checkPayoneerAccountId(String uid, String newPayoneerAccountId) {
        if (StringUtils.isNotBlank(newPayoneerAccountId)) {
            AccountBaseInfoPO accountBaseInfoPO = accountBaseInfoDao.queryByPayoneerAccountId(newPayoneerAccountId);
            // uid不一致
            if (accountBaseInfoPO != null && ObjectUtils.notEqual(uid, accountBaseInfoPO.getUid())) {
                throw new BizException(AccountExceptionCode.PAYONEER_ACCOUNT_REPEAT.getCode(), AccountExceptionCode.PAYONEER_ACCOUNT_REPEAT.getMsg());
            }
        }
    }

    private boolean checkUpdatePhoneAndEmail(AccountBaseInfoPO accountBaseInfo, UpdateAccountParam param) {
        // 邮箱换绑，检查邮件是否已被绑定
        if (StringUtils.isNotBlank(param.getEmail()) && ObjectUtils.notEqual(accountBaseInfo.getEmail(), param.getEmail())) {
            AccountUidInfo uidByEmail = userCenterAccountGateway.queryAccountUidByEmail(param.getEmail());
            if (uidByEmail != null && StringUtils.isNotBlank(uidByEmail.getUid()) && ObjectUtils.notEqual(accountBaseInfo.getUid(), uidByEmail.getUid())) {
                // 新邮箱已关联uid且不是当前账号，会换绑失败，所以无法更新
                throw new BizException(AccountExceptionCode.EMAIL_BIND_ALREADY.getCode(), AccountExceptionCode.EMAIL_BIND_ALREADY.getMsg());
            }
        }

        // 手机号换绑，检查新手机号是否已存在
        if (StringUtils.isNotBlank(param.getCountryCode()) || StringUtils.isNotBlank(param.getPhoneNumber())) {
            if (ObjectUtils.notEqual(accountBaseInfo.getCountryCode(), param.getCountryCode()) || ObjectUtils.notEqual(accountBaseInfo.getPhoneNumber(), param.getPhoneNumber())) {
               // 国家码或手机号任一发生变化
                AccountUidInfo uidByPhone = userCenterAccountGateway.queryAccountUidByPhone(param.getCountryCode(), param.getPhoneNumber());
                if (uidByPhone != null && StringUtils.isNotBlank(uidByPhone.getUid()) && ObjectUtils.notEqual(accountBaseInfo.getUid(), uidByPhone.getUid())) {
                    // 新手机号已关联uid且不是当前账号，会换绑失败，所以无法更新
                    throw new BizException(AccountExceptionCode.PHONE_BIND_ALREADY.getCode(), AccountExceptionCode.PHONE_BIND_ALREADY.getMsg());
                }
            }
        }
        return true;
    }

    private boolean checkUidUserCenterState(String uid) {
        if (StringUtils.isBlank(uid)) {
            return true;
        }
        boolean result = userCenterAccountGateway.unfreezeAccount(uid);
        if (result) {
            log.info("register unfreeze account success", uid);
            return true;
        } else {
            log.info("register unfreeze account failed", uid);
            return false;
        }
    }

    /**
     * 如果sourceId已存在，检查其对应的账户手机号是否匹配，不匹配
     */
    private void checkPhone(RegisterNewAccountParam param) {
        // 司机司导入驻&检查检查，向导单独流程，无需处理
        if (!AccountSouceEnum.isDriver(param.getSource()) && !AccountSouceEnum.isDriverGuide(param.getSource())) {
            return;
        }
        List<AccountMapperPO> accountMapperBySourceId = accountMapperDao.batchQueryBySource(Lists.newArrayList(AccountSouceEnum.DRIVER_SOURCE.getName(), AccountSouceEnum.DRIVER_GUIDE.getName()), param.getSourceId());
        if (CollectionUtils.isEmpty(accountMapperBySourceId)) {
            return;
        }
        AccountBaseInfoPO accountBaseInfo = accountBaseInfoDao.queryByUID(accountMapperBySourceId.get(0).getUid());
        // 上线初期，可能存在只有身份但没有账户的情况
        if (accountBaseInfo != null) {
            // 注册的手机号和同一个sourceId下已有账户的手机号不一致
            if (ObjectUtils.notEqual(accountBaseInfo.getCountryCode(), param.getCountryCode()) || ObjectUtils.notEqual(accountBaseInfo.getPhoneNumber(), param.getPhoneNumber())) {
                log.warn("register account check phone failed", String.format("local : %s, param : %s", JsonUtil.toString(accountBaseInfo), JsonUtil.toString(param)));
                Cat.logEvent(CatEventType.REGISTER_RESULT, "PhoneConflict");
                throw new BizException(AccountExceptionCode.REGISTER_PHONE_ERROR.getCode(), AccountExceptionCode.REGISTER_PHONE_ERROR.getMsg());
            }
        } else {
            log.warn("register cant not find account");
        }
    }

    private void checkSourceId(List<AccountMapperPO> identityList, RegisterNewAccountParam param) {
        if (CollectionUtils.isEmpty(identityList)) {
            return;
        }
        // 司机司导入驻&检查检查，向导单独流程，无需处理
        if (!AccountSouceEnum.isDriver(param.getSource()) && !AccountSouceEnum.isDriverGuide(param.getSource())) {
            return;
        }
        // 司机司导入驻校验sourceId
        List<AccountMapperPO> accountMapperList = identityList.stream().filter(o ->
                        AccountSouceEnum.isDriver(o.getSource()) || AccountSouceEnum.isDriverGuide(o.getSource()))
                .collect(Collectors.toList());
        // 司机司导都检查sourceId是与否一致
        if (accountMapperList.stream().anyMatch(o -> ObjectUtils.notEqual(o.getSourceId(), param.getSourceId()))) {
            // sourceId错误，与已有司机&司导的不一致
            log.warn("register account check souce id failed", String.format("identityList : %s, param : %s", JsonUtil.toString(identityList), JsonUtil.toString(param)));
            Cat.logEvent(CatEventType.REGISTER_RESULT, "SourceIdConflict");
            throw new BizException(AccountExceptionCode.REGISTER_SOURCE_ID_ERROR.getCode(), AccountExceptionCode.REGISTER_SOURCE_ID_ERROR.getMsg());
        }
    }

    private AccountBaseDto buildAccountRegisterParam(RegisterNewAccountParam param) {
        AccountBaseDto accountBaseDto = new AccountBaseDto();
        accountBaseDto.setSource(param.getSource());
        accountBaseDto.setSourceId(param.getSourceId());
        if (AccountSouceEnum.isGuide(param.getSource())) {
            // 向导邮箱注册，绑定手机号（现有逻辑）
            accountBaseDto.setAccountType(AccountType.EMAIL.getCode());
            if (StringUtils.isNotBlank(param.getPhoneNumber())) {
                accountBaseDto.setBindAccountType(AccountType.PHONE.getCode());
            }
        } else {
            // 司机&司导手机号注册，绑定邮箱
            accountBaseDto.setAccountType(AccountType.PHONE.getCode());
            if (StringUtils.isNotBlank(param.getEmail())) {
                accountBaseDto.setBindAccountType(AccountType.EMAIL.getCode());
            }
        }
        accountBaseDto.setCountryCode(param.getCountryCode());
        accountBaseDto.setPhoneNumber(param.getPhoneNumber());
        accountBaseDto.setEmail(param.getEmail());
        accountBaseDto.setOperator("SYSTEM");
        accountBaseDto.setCityId(param.getCityId());
        accountBaseDto.setOversea(param.getIsOversea());
        return accountBaseDto;
    }

    private AccountMapperPO buildAccountMapper(String uid, String udl, String source, String sourceId, Boolean valid) {
        AccountMapperPO accountMapperPO = new AccountMapperPO();
        accountMapperPO.setUid(uid);
        accountMapperPO.setSource(source);
        accountMapperPO.setSourceId(sourceId);
        accountMapperPO.setProviderDataLocation(udl);
        accountMapperPO.setIsValid(BooleanUtils.isFalse(valid) ? 0 : 1);
        return accountMapperPO;
    }

    @Transactional
    private void saveAccount(AccountBaseInfoPO accountBaseInfo, AccountMapperPO accountMapper) {
        try {
            // 司机&司导注册删除id生成记录
            if (AccountSouceEnum.isDriver(accountMapper.getSource()) || AccountSouceEnum.isDriverGuide(accountMapper.getSource())) {
                GlobalIdRecordPO condition = new GlobalIdRecordPO();
                condition.setId(Long.parseLong(accountMapper.getSourceId()));
                globalIdRecordDao.delete(condition);
                log.info("register account delete global id", accountMapper.getSourceId());
            }

            // 本地账户不存在，生成本地账户；已存在则更新
            if (accountBaseInfo.getId() == null) {
                log.info("register account insert local account", JsonUtil.toString(accountBaseInfo));
                accountBaseInfoDao.insert(accountBaseInfo);
                Cat.logEvent(CatEventType.REGISTER_RESULT, "NewRegister");
                Cat.logEvent(CatEventType.REGISTER_FIRST_SOURCE, accountBaseInfo.getRegisterSource());
            } else {
                log.info("register account update local account", JsonUtil.toString(accountBaseInfo));
                accountBaseInfoDao.update(accountBaseInfo);
                Cat.logEvent(CatEventType.REGISTER_RESULT, "RepeatRegister");
            }

            try {
                // 更新mapper
                if (accountMapper.getId() == null) {
                    accountMapperDao.insert(accountMapper);
                    log.info("register account insert new identity", JsonUtil.toString(accountMapper));
                    Cat.logEvent(CatEventType.REGISTER_IDENTITY, "New");
                } else {
                    accountMapperDao.update(accountMapper);
                    log.info("register account update identity", JsonUtil.toString(accountMapper));
                    Cat.logEvent(CatEventType.REGISTER_IDENTITY, "Exist");
                }
            } catch (Exception e) {
                Throwable cause = e.getCause();
                if (cause instanceof SQLIntegrityConstraintViolationException) {
                    // 重复注册，不抛异常，不做处理
                    log.warn("register account identity repeat", JsonUtil.toString(accountMapper));
                    Cat.logEvent(CatEventType.REGISTER_IDENTITY, "Conflict");
                } else {
                    throw e;
                }
            }
        } catch (Exception e) {
            Cat.logEvent(CatEventType.REGISTER_RESULT, "SaveError");
            log.warn("register account save account unknown error", e);
            throw new BizException(AccountExceptionCode.REGISTER_ACCOUNT_ERROR.getCode(), AccountExceptionCode.REGISTER_ACCOUNT_ERROR.getMsg());
        }
    }


    private void sendAccountBaseInfoChangeMessage(String uid, String updateSource, List<AccountMapperPO> accountMapperPOList) {
        AccountBaseInfoUpdateMessageDTO message = new AccountBaseInfoUpdateMessageDTO();
        message.setUid(uid);
        message.setUpdateSource(updateSource);
        List<AccountIdentitySourceDTO> accountIdentitySourceDTOList = accountMapperPOList.stream()
                .map(o -> new AccountIdentitySourceDTO(o.getSource(), o.getSourceId())).collect(Collectors.toList());
        message.setAccountIdentitySourceList(accountIdentitySourceDTOList);

        String content = JsonUtil.toString(message);

        Message qmqMessage = messageProducerProvider.generateMessage(Constants.ACCOUNT_BASE_INFO_UPDATE_TOPIC);
        int delay = driverAccountConfig.getAccountUpdateQmqDelaySeconds();
        qmqMessage.setProperty("content", content);
        if (delay > 0) {
            qmqMessage.setDelayTime(delay, TimeUnit.SECONDS);
        }
        messageProducerProvider.sendMessage(qmqMessage);
        log.info("send account base info change qmq", uid);
    }

    private AccountInfoDTO buildAccountInfoDTO(AccountBaseInfoPO accountBaseInfoPO, List<AccountMapperPO> accountMapperPOList) {
        return accountInfoConvert.buildAccountInfoDTO(accountBaseInfoPO,accountMapperPOList);
    }

    private void preProcessParam(RegisterNewAccountParam param) {
        param.setPhoneNumber(archCoreInfoService.encryptByType(KeyType.Phone, param.getPhoneNumber()));
        if (StringUtils.isNotBlank(param.getEmail())) {
            param.setEmail(archCoreInfoService.encryptByType(KeyType.Mail, param.getEmail()));
        }
        if (StringUtils.isNotBlank(param.getIdCardNo())) {
            param.setIdCardNo(archCoreInfoService.encryptByType(KeyType.Identity_Card, param.getIdCardNo()));
        }
    }

    private void preProcessParam(UpdateAccountParam param) {
        param.setPhoneNumber(archCoreInfoService.encryptByType(KeyType.Phone, param.getPhoneNumber()));
        if (StringUtils.isNotBlank(param.getEmail())) {
            param.setEmail(archCoreInfoService.encryptByType(KeyType.Mail, param.getEmail()));
        }
        if (StringUtils.isNotBlank(param.getIdCardNo())) {
            param.setIdCardNo(archCoreInfoService.encryptByType(KeyType.Identity_Card, param.getIdCardNo()));
        }
    }


    private void sendDriverOfflineAppPush(AccountMapperPO identity) {
        if (ObjectUtils.notEqual(identity.getIsValid(), 0)) {
            return;
        }
        if (!AccountSouceEnum.isDriver(identity.getSource()) && !AccountSouceEnum.isDriverGuide(identity.getSource())) {
            return;
        }
        // 仅支持司导的客户端版本才发送通知
        long driverId = Long.parseLong(identity.getSourceId());
        BigDecimal driverVersion = appPushMessageCache.getDriverVersion(driverId);
        if (driverVersion.compareTo(BigDecimal.valueOf(driverAccountConfig.getSupportDriverGuideVersion())) < 0) {
            log.info("send driver offline app push failed, version not support", driverVersion.toString());
            return;
        }
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setDriverIds(Lists.newArrayList(driverId));
        pushMessageDTO.setTemplateId("4053");
        Map<String, String> sharkValues = Maps.newHashMap();
        String sharkKey = "show.tourdriver.pickupservice";// 接送机
        if (AccountSouceEnum.isDriverGuide(identity.getSource())) {
            sharkKey = "show.tourdriver.chartercar";// 包车
        }
        sharkValues.put("serviceline", commonMultipleLanguages.getContent(sharkKey, pushConfigRedisLogic.getDriverLanguage(driverId)));
        pushMessageDTO.setSharkValues(sharkValues);

        String data = JsonUtil.toString(pushMessageDTO);
        Map<String, Object> qmqContent = Maps.newHashMap();
        qmqContent.put("data", data);
        QmqProducerProvider.sendMessage(Constants.DRIVER_PUSH_MESSAGE_TOPIC, qmqContent);
        log.info("send driver offline app push", String.format("uid : %s, content : %s", identity.getUid(), data));

    }
}
