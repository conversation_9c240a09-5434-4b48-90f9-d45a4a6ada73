package com.ctrip.dcs.driver.domain.application.covert;

import com.ctrip.dcs.driver.account.domain.common.DriverDomainErrorCode;
import com.ctrip.dcs.driver.domain.application.condition.NotifyVerificationCodeByCallPhoneCondition;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.NotifyVerificationCodeByCallPhoneRequestType;
import com.ctrip.model.NotifyVerificationCodeByCallPhoneResponseType;
import org.springframework.stereotype.Component;

@Component
public class NotifyVerificationCodeByCallPhoneConvert {

    public NotifyVerificationCodeByCallPhoneCondition buildCondition(NotifyVerificationCodeByCallPhoneRequestType request) {
        NotifyVerificationCodeByCallPhoneCondition condition = new NotifyVerificationCodeByCallPhoneCondition();
        condition.setChannel(request.getChannel());
        condition.setLocale(request.getLocale());
        condition.setCountryCode(request.getCountryCode());
        condition.setPhoneNumber(request.getPhoneNumber());
        condition.setVerificationCode(request.getVerificationCode());
        return condition;
    }

    public NotifyVerificationCodeByCallPhoneResponseType buildResponse(boolean callResult) {
        NotifyVerificationCodeByCallPhoneResponseType response = new NotifyVerificationCodeByCallPhoneResponseType();
        if (callResult) {
            return ServiceResponseUtils.success(response);
        }
        return ServiceResponseUtils.fail(response, DriverDomainErrorCode.VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrCode(), DriverDomainErrorCode.VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrMsg());
    }
}
