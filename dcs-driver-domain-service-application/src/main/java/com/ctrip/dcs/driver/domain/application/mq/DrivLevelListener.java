package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.RightsConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DrivLevelListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DrivLevelListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    @Autowired
    private DrivRightsDao drivRightsDao;

    @Autowired
    private RightsConfig rightsConfig;

    @Autowired
    private RightsRedisLogic rightsRedisLogic;

    @Override
    @QmqConsumer(prefix = "dcs.canal.dcstransportdb.data.changed", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        String data = message.getStringProperty("dataChange");
        DataChange dataChange = JacksonUtil.deserialize(data, DataChange.class);
        if (Objects.isNull(dataChange)) {
            return;
        }
        try {
            switch (dataChange.getTableName()) {
                case "driv_level":
                    //driv_level 新增下发司机权益，更新&删除  删除缓存
                    if (dataChange.isInsert()) {
                        issueRights(dataChange);
                    } else {
                        rightsRedisLogic.delDrivLevel(Long.valueOf(dataChange.getBeforeColumnValue("driv_id")), dataChange.getBeforeColumnValue("month_idx"));
                    }
                    break;
                case "driv_rights":
                    //driv_rights 更新&删除  删除缓存
                    if (!dataChange.isInsert()) {
                        rightsRedisLogic.delDrivRights(Long.valueOf(dataChange.getBeforeColumnValue("driv_id")), dataChange.getBeforeColumnValue("month_idx"));
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            LOGGER.warn(e);
        }
    }

    private void issueRights(DataChange dataChange) {
        long driverId = 0;
        try {
            long cityId = Long.valueOf(dataChange.getAfterColumnValue("city_id"));
            driverId = Long.valueOf(dataChange.getAfterColumnValue("driv_id"));
            int level = Integer.valueOf(dataChange.getAfterColumnValue("driv_level"));
            List<FormRightsInfo> rightsInfo = rightsConfig.getRightsInfo(cityId, level);
            if (CollectionUtils.isEmpty(rightsInfo)) {
                return;
            }
            drivRightsDao.batchInsert(buildRecords(rightsInfo, cityId, driverId, level));
        } catch (Exception e) {
            LOGGER.warn("DrivLevelListener", String.format("failed to issue rights driverId:%s ", driverId), e);
        }
    }

    private List<DrivRightsPO> buildRecords(List<FormRightsInfo> rightsInfo, long cityId, long driverId, int level) {
        String monthIdx = LocalDateTimeUtils.monthIndexStr();
        return rightsInfo.stream().map(config -> {
            DrivRightsPO po = new DrivRightsPO();
            po.setRightsConfigId(config.getId());
            po.setRightsName(config.getRightsName());
            po.setRightsDesc(config.getRightsDesc());
            po.setCityId(cityId);
            po.setMonthIdx(monthIdx);
            po.setDrivId(driverId);
            po.setDrivLevel(level);
            po.setRightsType(config.getRightsType());
            po.setUseLimit(config.getUseLimit());
            po.setUseCount(0);
            po.setRightsStatus(RightsStatusEnum.RIGHTS_STATUS_ISSUED.getStatus());
            po.setRightsStratTime(Timestamp.valueOf(LocalDateTimeUtils.firstDayOfMonth()));
            po.setRightsEndTime(Timestamp.valueOf(LocalDateTimeUtils.lastDayOfMonth()));
            po.setExtend(CommonRightsExtendUtils.buildExtend(config.getRightsType(), config.getExtend()));

            return po;
        }).collect(Collectors.toList());
    }
}
