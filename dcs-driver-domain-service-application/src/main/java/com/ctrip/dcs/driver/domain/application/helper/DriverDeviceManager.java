package com.ctrip.dcs.driver.domain.application.helper;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.service.DriverDeviceInfoService;
import com.ctrip.dcs.driver.domain.infrastructure.model.device.DriverDeviceInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfiguration;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctriposs.baiji.rpc.server.HttpRequestContext;
import com.ctriposs.baiji.rpc.server.HttpRequestWrapper;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.Optional;

/**
 * @Date 2024/11/27
 * @Created laib
 */
@Component
public class DriverDeviceManager {
    private static final Logger logger = LoggerFactory.getLogger(DriverDeviceManager.class);

    @Autowired
    private DriverDeviceInfoService driverDeviceInfoService;

    @Autowired
    private BusinessConfiguration configuration;

    public boolean driverDeviceActiveCheckForVoip() {
        return configuration.getBizVoipGrayQConfig().getDriverDeviceActiveCheck();
    }

    public boolean fromUserRequest() {
        String appId = Optional.ofNullable(HttpRequestContext.getInstance())
                .map(HttpRequestContext::request)
                .map(HttpRequestWrapper::clientAppId)
                .orElse("");

        logger.info("DriverDeviceManager.fromUserRequest", "appid is:" + appId);
        return configuration.getBizVoipGrayQConfig().getAppIds().contains(appId);
    }

    public boolean activeForVoip(Long driverId) {
        Long configMinutes = configuration.getBizVoipGrayQConfig().getBizGrayDriverDeviceActiveIntervalMinutes();
        return active(driverId, configMinutes, ChronoUnit.MINUTES);
    }

    public boolean active(Long driverId, Long configMaxAllowActiveIntervalTime, ChronoUnit timeUnit) {
        DriverDeviceInfoModel deviceInfo = driverDeviceInfoService.queryDeviceInfo(driverId);
        if (Objects.isNull(deviceInfo)) {
            return false;
        }
        long driverLastedActiveIntervalTime = LocalDateTimeUtil.between(deviceInfo.getActiveTime(), LocalDateTime.now(), timeUnit);
        if (driverLastedActiveIntervalTime <= configMaxAllowActiveIntervalTime) {
            logger.info("DriverDeviceManager.active", "driverId is:" + driverId);
            return true;
        }
        Cat.logEvent(String.format(CatEventType.QUERY_BIZ_GRAY_SWITCH_ACTIVE_INTERVAL_TIMES, configMaxAllowActiveIntervalTime),
                String.valueOf(Math.round(driverLastedActiveIntervalTime / configMaxAllowActiveIntervalTime)));
        return false;
    }

}
