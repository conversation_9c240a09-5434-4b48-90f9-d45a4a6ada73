package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.domain.rights.mq.UseRightsMessage;
import com.ctrip.dcs.driver.message.api.PushMessageRequestType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DriverRightsVacationListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DriverRightsVacationListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    @Autowired
    DomainBeanFactory domainBeanFactory;
    @Autowired
    DrivRightsRecordDao drivRightsRecordDao;
    @Autowired
    DrivRightsDao drivRightsDao;
    @Autowired
    LockService lockService;
    @Autowired
    DriverMessageServiceProxy driverMessageServiceProxy;

    @Override
    @QmqConsumer(prefix = "dcs.driver.rights.vacation", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        String data = message.getStringProperty("content");
        if (Strings.isBlank(data)) {
            return;
        }
        List<UseRightsMessage> useRightsMessages = JacksonUtil.parseArray(data, UseRightsMessage.class);
        if (CollectionUtils.isEmpty(useRightsMessages)) {
            return;
        }
        List<UseRightsCondition> conditions = useRightsMessages.stream().map(o -> buildCondition(o)).collect(Collectors.toList());

        Boolean isSuccess = lockService.executeInLock(String.format("dcs_driver_use_rights:%s_%s", conditions.get(0).getDriverId(), conditions.get(0).getRightsType()), 1000, () -> handleVacation(conditions));
        if (isSuccess) {
            // 发消息推送给客服
            try {
                conditions.stream().forEach(condition -> WorkBenchLogProducer.sendMessage(WorkBenchLogProducer.buildVacationMessase(condition)));
                //push站内信
                PushMessageRequestType pushMessage = new PushMessageRequestType();
                pushMessage.setDriverIds(Arrays.asList(conditions.get(0).getDriverId()));
                pushMessage.setTemplateId("4042");
                Map<String, String> sharkMap = new HashMap<>();
                sharkMap.put("useCount", "1");
                sharkMap.put("orderCount", String.valueOf(conditions.size()));
                sharkMap.put("orders", String.join(",", conditions.stream().map(o -> o.getUserOrderId()).collect(Collectors.joining(","))));
                pushMessage.setSharkValues(sharkMap);
                Map<String, String> dataMap = new HashMap<>();
                dataMap.put("status","3");
                pushMessage.setData(dataMap);
                driverMessageServiceProxy.pushMessage(pushMessage);
            } catch (Exception e) {
                CommonLogger.INSTANCE.error(e);
            }
        }
    }

    private boolean handleVacation(List<UseRightsCondition> conditions) {
        long driverId = conditions.get(0).getDriverId();
        int rightsType = conditions.get(0).getRightsType();
        //check rights
        RightsModel rightsModel = this.domainBeanFactory.rightsDBDataService().queryDriverRights(driverId, LocalDateTimeUtils.monthIndexStr()).stream().filter(o -> o.getRightsStatus() != RightsStatusEnum.RIGHTS_STATUS_CANCELLED.getStatus()).filter(o -> o.getRightsType().equals(rightsType) && (o.getUseLimit() == 0 || (o.getUseLimit() - o.getUseCount()) > 0)).findFirst().orElse(null);

        if (Objects.isNull(rightsModel)) {
            LOGGER.error(String.format("driverId:%s,rightType:%s rights is unavailable,the driver does not have this rights or used", driverId, rightsType));
            return false;
        }
        LevelModel levelModel = this.domainBeanFactory.rightsRepoService().queryDriverLevel(driverId, LocalDateTimeUtils.monthIndexStr());
        if (Objects.isNull(levelModel)) {
            LOGGER.error(String.format("driverId:%s,rightType:%s driver is null", driverId, rightsType));
            return false;
        }
        //save
        try {
            saveAndUpdateRights(buildDrivRightsRecordPO(conditions, rightsModel, levelModel), buildDrivRightsPO(rightsModel, conditions.size()));
        } catch (Exception e) {
            LOGGER.error(String.format("failed to saveAndUpdateRights driverId:%s,rightType:%s", driverId, rightsType), e);
            return false;
        }
        return true;
    }

    private UseRightsCondition buildCondition(UseRightsMessage useRightsMessage) {
        UseRightsCondition useRightsCondition = new UseRightsCondition();
        useRightsCondition.setDriverId(useRightsMessage.getDriverId());
        useRightsCondition.setRightsType(useRightsMessage.getRightsType());
        useRightsCondition.setUserOrderId(useRightsMessage.getUserOrderId());
        useRightsCondition.setPurchaseOrderId(useRightsMessage.getPurchaseOrderId());
        useRightsCondition.setSupplyOrderId(useRightsMessage.getSupplyOrderId());
        useRightsCondition.setPunishOrderId(useRightsMessage.getPunishOrderId());
        useRightsCondition.setMoney(useRightsMessage.getMoney());
        useRightsCondition.setSysExpectBookTime(useRightsMessage.getSysExpectBookTime());
        return useRightsCondition;
    }


    private DrivRightsPO buildDrivRightsPO(RightsModel rightsModel, int size) {
        DrivRightsPO drivRightsPO = new DrivRightsPO();
        drivRightsPO.setId(rightsModel.getId());
        drivRightsPO.setUseCount(rightsModel.getUseCount() + 1);
        drivRightsPO.setRightsStatus(RightsStatusEnum.RIGHTS_STATUS_USED.getStatus());
        drivRightsPO.setExtend(CommonRightsExtendUtils.buildExtendForUpdate(rightsModel, BigDecimal.ZERO, size));
        return drivRightsPO;
    }

    private List<DrivRightsRecordPO> buildDrivRightsRecordPO(List<UseRightsCondition> conditions, RightsModel rightsModel, LevelModel levelModel) {
        return conditions.stream().map(condition -> {
            DrivRightsRecordPO drivRightsRecordPO = new DrivRightsRecordPO();
            drivRightsRecordPO.setRightsId(rightsModel.getId());
            drivRightsRecordPO.setDrivId(condition.getDriverId());
            drivRightsRecordPO.setRightsType(condition.getRightsType());
            drivRightsRecordPO.setUseLevel(levelModel.getDrivLevel());
            drivRightsRecordPO.setRightsName(rightsModel.getRightsName());
            drivRightsRecordPO.setLevelName(levelModel.getLevelName());
            drivRightsRecordPO.setUserOrderId(condition.getUserOrderId());
            drivRightsRecordPO.setSupplyOrderId(condition.getSupplyOrderId());
            drivRightsRecordPO.setExpectBookTime(condition.getSysExpectBookTime());
            return drivRightsRecordPO;
        }).collect(Collectors.toList());
    }

    @Transactional
    public void saveAndUpdateRights(List<DrivRightsRecordPO> recordPOs, DrivRightsPO rightsPO) {
        drivRightsRecordDao.batchInsert(recordPOs);
        drivRightsDao.update(rightsPO);
    }
}
