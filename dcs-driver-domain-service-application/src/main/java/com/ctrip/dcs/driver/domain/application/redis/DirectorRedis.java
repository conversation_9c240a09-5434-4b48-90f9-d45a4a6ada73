package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.gson.Gson;
import credis.java.client.CacheProvider;
import credis.java.client.util.CacheFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class DirectorRedis {

  public static final Logger logger = LoggerFactory.getLogger(DirectorRedis.class);

  private final Gson gson = new Gson();

  private final CacheProvider redisProvider = CacheFactory.getProvider("DCS_driver");

  DirectorRedis() {
  }

  private static boolean isNullOrEmpty(String s) {
    return null == s || s.isEmpty();
  }

  private String getKey(String key) {
    return String.format("%s_%s", "dcs_driver", key);
  }

  public CacheProvider getCacheProvider() {
    return this.redisProvider;
  }

  public String get(String key) {
    String keyParam = this.getKey(key);
    String value = this.redisProvider.get(keyParam);
    return value;
  }

  public Boolean setNx(String key, String value, long seconds) {
    String keyParam = this.getKey(key);
    if (Boolean.FALSE.equals(this.redisProvider.setnx(keyParam, value))) {
      return false;
    }
    if (0 < seconds) {
      return this.redisProvider.expire(keyParam, seconds);
    }
    return true;
  }

  public String hget(String key, String field) {
    String keyParam = this.getKey(key);
    return this.redisProvider.hget(keyParam, field);
  }

  public <T> T get(String key, Class<T> type) {
    String json = this.get(key);
    if (isNullOrEmpty(json)) {
      return null;
    }
    return gson.fromJson(json, type);
  }

  public Boolean set(String key, String value, long seconds) {
    String keyParam = this.getKey(key);
    if (Boolean.FALSE.equals(this.redisProvider.set(keyParam, value))) {
      return false;
    }
    if (0 < seconds) {
      return this.redisProvider.expire(keyParam, seconds);
    }
    return true;
  }

  public Boolean hset(String key, String field, String value, long seconds) {
    String keyParam = this.getKey(key);
    if (Boolean.FALSE.equals(this.redisProvider.hset(keyParam, field, value))) {
      return false;
    }
    if (0 < seconds) {
      return this.redisProvider.expire(keyParam, seconds);
    }
    return true;
  }

  public Boolean hmset(String key, Map<String, String> fields, long seconds) {
    String keyParam = this.getKey(key);
    if (Boolean.FALSE.equals(this.redisProvider.hmset(keyParam, fields))) {
      return false;
    }
    if (0 < seconds) {
      return this.redisProvider.expire(keyParam, seconds);
    }
    return true;
  }

  public <T> T hget(String key, String field, Class<T> type) {
    String json = this.hget(key, field);
    if (isNullOrEmpty(json)) {
      return null;
    }
    return gson.fromJson(json, type);
  }

  public Boolean remove(String key) {
    String keyParam = this.getKey(key);
    return this.redisProvider.del(keyParam);
  }

  /**
   * 默认1800s
   */
  public <T> Boolean set(String key, T object) {
    return this.set(key, object, 1800L);
  }

  public <T> Boolean set(String key, T object, long seconds) {
    String value = gson.toJson(object);
    return this.set(key, value, seconds);
  }
  public <T> Boolean hset(String key, String field, T object, long seconds) {
    String value = gson.toJson(object);
    return this.hset(key, field, value, seconds);
  }

  public Map<String, String> hgetAll(String key) {
    return this.redisProvider.hgetAll(this.getKey(key));
  }

  public Set<String> hkeys(String key) {
    return this.redisProvider.hkeys(this.getKey(key));
  }

  public Long incr(String key) {
    return this.redisProvider.incr(this.getKey(key));
  }

  public Long decr(String key) {
    return this.redisProvider.decr(this.getKey(key));
  }

  public Boolean expire(String key, long seconds) {
    if(Boolean.TRUE.equals(this.redisProvider.exists(this.getKey(key)))) {
      return this.redisProvider.expire(this.getKey(key), seconds);
    }
    return true;
  }

  /**
   * 剩余过期时间(单位秒)
   */
  public long ttl(String key) {
    return this.redisProvider.ttl(key);
  }

  /**
   * 剩余过期时间(单位秒)
   */
  public long getTtl(String key) {
    if(Boolean.TRUE.equals(this.redisProvider.exists(this.getKey(key)))) {
      return this.redisProvider.ttl(this.getKey(key));
    }
    return 0;
  }

  /**
   * 类型
   */
  public String type(String key) {
    return this.redisProvider.type(key);
  }
  public void mset(int seconds, String... keysvalues) {
    redisProvider.mset(seconds,keysvalues);
  }

  public List<String> mget(String... keys) {
    return redisProvider.mget(keys);
  }

    public <T> List<T> mget(Class<T> type, String... keys) {
      List<String> resultList = redisProvider.mget(keys);
      if (CollectionUtils.isEmpty(resultList)) {
        return Lists.newArrayList();
      }
      return resultList.stream().map(o -> gson.fromJson(o, type)).collect(Collectors.toList());
    }
}
