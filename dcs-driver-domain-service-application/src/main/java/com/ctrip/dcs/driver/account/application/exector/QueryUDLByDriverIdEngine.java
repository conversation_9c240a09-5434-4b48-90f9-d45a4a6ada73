package com.ctrip.dcs.driver.account.application.exector;
/*
作者：pl.yang
创建时间：2025/2/25-上午10:52-2025
*/


import com.ctrip.dcs.driver.account.application.convert.QueryUDLByDriverIdConvert;
import com.ctrip.dcs.driver.account.domain.condition.RegisterDriverUdlCondition;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.exception.ServiceValidationException;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: QueryUDLByDriverIdEngine
 * @Package com.ctrip.dcs.driver.account.application.exector
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/2/25 上午10:52
 */

@Component
@ServiceLogTag(tagKeys= {"driverId","uid","driverIds"})
public class QueryUDLByDriverIdEngine extends AbDriverDomainShoppingExecutor<QueryUDLByDriverIdRequestType, QueryUDLByDriverIdResponseType> implements Validator<QueryUDLByDriverIdRequestType> {
    @Autowired
    AccountService accountService;
    @Autowired
    QueryUDLByDriverIdConvert queryUDLByDriverIdConvert;

    @Override
    public void validate(AbstractValidator<QueryUDLByDriverIdRequestType> validator, QueryUDLByDriverIdRequestType req) {
        if(CollectionUtils.isNotEmpty(req.getDriverIds())&&req.getDriverIds().size()>100){
            throw new ServiceValidationException("driverIds.size()>100");
        }
        if(CollectionUtils.isNotEmpty(req.getUids())&&req.getUids().size()>100){
            throw new ServiceValidationException("uids.size()>100");
        }
    }

    @Override
    public QueryUDLByDriverIdResponseType execute(QueryUDLByDriverIdRequestType queryUDLByDriverIdRequestType) {
        List<String> sourceIds = queryUDLByDriverIdConvert.convertSourceId(queryUDLByDriverIdRequestType);
        List<String> uids = queryUDLByDriverIdConvert.convertUid(queryUDLByDriverIdRequestType);
        List<AccountUDLDo> accountUDLResult= Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(sourceIds)) {
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "DriverId");
            List<AccountUDLDo> accountUDLBySource = accountService.getAccountUDLBySource(sourceIds);
            if(CollectionUtils.isNotEmpty(accountUDLBySource)){
                accountUDLResult.addAll(accountUDLBySource);
            }
        }
        if(CollectionUtils.isNotEmpty(uids)){
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "UID");
            List<AccountUDLDo> accountUDLByUid = accountService.getAccountUDLByUid(uids);
            if(CollectionUtils.isNotEmpty(accountUDLByUid)){
                accountUDLResult.addAll(accountUDLByUid);
            }
        }
        if (queryUDLByDriverIdRequestType.getRegisterDriverUdl() != null) {
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "RegisterDriverUdl");
            RegisterDriverUdlCondition registerDriverUdlCondition = queryUDLByDriverIdConvert.convertRegisterDriverUdlCondition(queryUDLByDriverIdRequestType.getRegisterDriverUdl());
            AccountUDLDo accountUDLDo = accountService.registerDriverUdl(registerDriverUdlCondition);
            if (accountUDLDo != null) {
                accountUDLResult.add(accountUDLDo);
            }
        }
        return buildResponse(accountUDLResult.stream().filter(Objects::nonNull).toList());
    }

    private QueryUDLByDriverIdResponseType buildResponse(List<AccountUDLDo> accountUDLResult) {
        QueryUDLByDriverIdResponseType responseType = queryUDLByDriverIdConvert.convertAccountUDL(accountUDLResult);
        if (CollectionUtils.isNotEmpty(responseType.getDriverUdL())) {
            return ServiceResponseUtils.success(responseType);
        } else {
            //没有查询到，可以监控埋点
            return ServiceResponseUtils.success(responseType,"204");
        }
    }

    @Override
    public Boolean isEmptyResult(QueryUDLByDriverIdResponseType resp) {
        return !(resp!=null&&CollectionUtils.isNotEmpty(resp.getDriverUdL()));

    }
}
