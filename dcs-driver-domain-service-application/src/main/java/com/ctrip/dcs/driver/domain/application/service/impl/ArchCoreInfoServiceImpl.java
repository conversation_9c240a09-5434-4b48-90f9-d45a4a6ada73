package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.arch.coreinfo.CoreInfoClient;
import com.ctrip.arch.coreinfo.entity.InfoData;
import com.ctrip.arch.coreinfo.entity.InfoKey;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.microsoft.sqlserver.jdbc.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class ArchCoreInfoServiceImpl implements ArchCoreInfoRepository {

	private static final Logger LOGGER = LoggerFactory.getLogger(ArchCoreInfoRepository.class);

	@Override
	public String decryptIdCard(String decryptIdCard){
		InfoKey infoKey = new InfoKey(KeyType.Identity_Card, decryptIdCard);
		String decryptedIdCard = decryptIdCard;
		try {
			InfoData infoData = CoreInfoClient.getInstance().decrypt(infoKey);
			decryptedIdCard = StringUtils.isEmpty(infoData.getResult()) ? decryptIdCard : infoData.getResult();
		} catch (Exception ex) {
			LOGGER.error("decryptIdCard error", ex);
		}
		return decryptedIdCard;
	}

	@Override
	public String encryptIdCard(String idCard){
		InfoKey infoKey = new InfoKey(KeyType.Identity_Card, idCard);
		String encryptedIdCard = idCard;
		try {
			InfoData infoData = CoreInfoClient.getInstance().encrypt(infoKey);
			encryptedIdCard = StringUtils.isEmpty(infoData.getResult()) ? idCard : infoData.getResult();
		} catch (Exception ex) {
			LOGGER.error("encryptIdCard error", ex);
		}
		return encryptedIdCard;
	}

	@Override
	public String encryptByType(KeyType keyType, String value){
		InfoKey infoKey = new InfoKey(keyType, value);
		String encryptedValue = value;
		try {
			InfoData infoData = CoreInfoClient.getInstance().encrypt(infoKey);
			encryptedValue = org.apache.commons.lang3.StringUtils.isEmpty(infoData.getResult()) ? value : infoData.getResult();
			if (KeyType.Phone.equals(keyType) && org.apache.commons.lang3.StringUtils.isNotBlank(value) && value.startsWith("0")) {
				Cat.logEvent(CatEventType.PHONE_ENCRYPT_START_WITH_0, "0");
			}
		} catch (Exception ex) {
			LOGGER.error("encrypt by type error", ex);
		}
		return encryptedValue;
	}

	@Override
	public Map<String/*加密前的信息*/,String/*加密后的信息*/> batchEncryptByType(KeyType keyType, List<String> values){
		if(CollectionUtils.isEmpty(values)){
			return Maps.newHashMap();
		}
		List<InfoKey> infoKeys = Lists.newArrayList();
		for (String value : values) {
			InfoKey infoKey = new InfoKey(keyType, value);
			infoKeys.add(infoKey);
		}
		Map<String,String> encryptedValue = Maps.newHashMap();
		try {
			List<InfoData> dataList = CoreInfoClient.getInstance().encrypt(infoKeys);
			if(CollectionUtils.isEmpty(dataList)){
				return Maps.newHashMap();
			}
			for (InfoData infoData : dataList) {
				String result = infoData.getResult(); //获取解密后结果
				if (KeyType.Phone.equals(keyType) && org.apache.commons.lang3.StringUtils.isNotBlank(result) && result.startsWith("0")) {
					Cat.logEvent(CatEventType.PHONE_ENCRYPT_START_WITH_0, "0");
				}
				encryptedValue.put(infoData.getInfoKey().getKey(), result);
			}

		} catch (Exception ex) {
			LOGGER.error("encrypt by type error", ex);
		}
		return encryptedValue;
	}


	/**
	 * 目前使用：
	 *     Phone(1),
	 *     Mail(2),
	 *     Identity_Card(3),
	 *     Driver_License(14),
	 *     Address(22),
	 * */
	@Override
	public String decryptByType(KeyType keyType, String decryptValue){
		InfoKey infoKey = new InfoKey(keyType, decryptValue);
		String decryptedValue = decryptValue;
		try {
			InfoData infoData = CoreInfoClient.getInstance().decrypt(infoKey);
			decryptedValue = org.apache.commons.lang3.StringUtils.isEmpty(infoData.getResult()) ? decryptValue : infoData.getResult();
		} catch (Exception ex) {
			LOGGER.error("decryptByType error", ex);
		}
		return decryptedValue;
	}

	@Override
	public boolean isEncrypt(KeyType keyType, String value) {
		// 如果加密前后一致，说明是加密过的
		return Objects.equals(value, encryptByType(keyType, value));
	}
}
