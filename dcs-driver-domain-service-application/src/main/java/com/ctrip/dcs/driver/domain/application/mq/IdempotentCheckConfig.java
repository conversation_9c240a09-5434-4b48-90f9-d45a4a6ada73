package com.ctrip.dcs.driver.domain.application.mq;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import qunar.tc.qmq.consumer.idempotent.CRedisIdempotentChecker;

/**
 * Created by <AUTHOR> on 2022/5/20 17:00
 */
@Configuration
public class IdempotentCheckConfig {

  @Bean(name = "redisIdempotent")
  public CRedisIdempotentChecker redisIdempotent() {
    return new CRedisIdempotentChecker("DCS_driver", new IdempotentCheckFunction());
  }
}
