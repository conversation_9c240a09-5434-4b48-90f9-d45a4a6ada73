package com.ctrip.dcs.driver.domain.application.exector.phone;

import com.ctrip.dcs.driver.domain.application.condition.QueryCallPhoneForVerifyResultCondition;
import com.ctrip.dcs.driver.domain.application.covert.QueryCallPhoneForVerifyResultConvert;
import com.ctrip.dcs.driver.domain.application.dto.PhoneResultDTO;
import com.ctrip.dcs.driver.domain.application.service.QueryCallPhoneForVerifyResultService;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


@Component
public class QueryCallPhoneForVerifyResultEngine extends ShoppingExecutor<QueryCallPhoneForVerifyResultRequestType, QueryCallPhoneForVerifyResultResponseType>
        implements Validator<QueryCallPhoneForVerifyResultRequestType> {

    @Resource
    private QueryCallPhoneForVerifyResultConvert convert;
    @Resource
    private QueryCallPhoneForVerifyResultService service;

    /**
     * CallPhoneForVerify接口发起手机号校验后，需要检查结果
     *
     * @param requestType
     * @return
     */
    @Override
    public QueryCallPhoneForVerifyResultResponseType execute(QueryCallPhoneForVerifyResultRequestType requestType) {

        QueryCallPhoneForVerifyResultCondition condition = convert.buildCondition(requestType);
        Pair<String, List<PhoneResultDTO>> result = service.query(condition);
        return ServiceResponseUtils.success(convert.buildResponse(result));
    }

    @Override
    public void validate(AbstractValidator<QueryCallPhoneForVerifyResultRequestType> validator) {
        validator.ruleFor("callTaskId").notNull();
    }
}
