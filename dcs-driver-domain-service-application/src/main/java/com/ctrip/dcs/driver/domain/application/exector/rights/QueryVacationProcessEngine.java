package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.dcs.driver.domain.rights.QueryVacationProcessRequestType;
import com.ctrip.dcs.driver.domain.rights.QueryVacationProcessResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryVacationProcessEngine extends ShoppingExecutor<QueryVacationProcessRequestType, QueryVacationProcessResponseType>
        implements Validator<QueryVacationProcessRequestType> {

    @Autowired
    private SystemQConfig systemQConfig;

    @Override
    public QueryVacationProcessResponseType execute(QueryVacationProcessRequestType request) {
        QueryVacationProcessResponseType response = new QueryVacationProcessResponseType();
        String[] driverIds = systemQConfig.getString("vacation_driv_ids").split(",");
        String driverId = Arrays.stream(driverIds).filter(o -> o.equals(request.driverId.toString())).findFirst().orElse(Strings.EMPTY);
        if(StringUtils.isNotBlank(driverId)){
            response.setIsNewVcation(true);
            return ServiceResponseUtils.success(response);
        }

        String closeRights = systemQConfig.getString("close_rights");
        if (!qunar.agile.Strings.isBlank(closeRights) && closeRights.contains(String.valueOf(RightsTypeEnum.RIGHTS_TYPE_VACATION.getCode()))) {
            response.setIsNewVcation(false);
        }else {
            String[] cityIds = systemQConfig.getString("newVacationCityIds").split(",");
            String city = Arrays.stream(cityIds).filter(o -> o.equals(request.cityId.toString())).findFirst().orElse(Strings.EMPTY);
            response.setIsNewVcation(StringUtils.isNotBlank(city));
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryVacationProcessRequestType> validator) {
        validator.ruleFor("cityId").notNull().notEmpty();
        validator.ruleFor("driverId").notNull().notEmpty();
    }
}
