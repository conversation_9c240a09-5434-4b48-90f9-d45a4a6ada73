package com.ctrip.dcs.driver.account.application.service.impl;

import com.ctrip.dcs.driver.account.infrastructure.adapter.DcsScmMerchantServiceProxy;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidRequestType;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DcsScmMerchantServiceProxyImpl {

  @Autowired
  DcsScmMerchantServiceProxy dcsScmMerchantServiceProxy;

  private static final Logger LOGGER = LoggerFactory.getLogger(DcsScmMerchantServiceProxyImpl.class);


}
