package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.driver.domain.infrastructure.qconfig.KmsTokenConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.ExamAESUtils;
import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CommonExamSSOUtils {
  @Autowired
  private CommonLogger logger;

  @Autowired
  private KmsTokenConfig kmsTokenConfig;

  private String key;

  private String iv;

  public String key() {
    return this.key;
  }

  public String iv() {
    return this.iv;
  }

  @Autowired
  public void initialize() throws KMSUtils.KmsException {

    String keyToken = kmsTokenConfig.getExexmKeyToken();
    String ivToken = kmsTokenConfig.getExexmIvToken();
    if (StringUtils.isBlank(keyToken) || StringUtils.isBlank(ivToken)) {
      throw new KMSUtils.KmsException("CommonExamSSOUtils keyToken or ivToken empty");
    }
    this.key = KMSUtils.key(keyToken);
    this.iv = KMSUtils.key(ivToken);
    if (StringUtils.isBlank(this.key) || StringUtils.isBlank(this.iv)) {
      throw new KMSUtils.KmsException("CommonExamSSOUtils initialize error");
    }
    this.logger.info("KMS", "CommonExamSSOUtils key: [" + this.key + "]" + "iv: [" + this.iv + "]");
  }

  public String encryptByAES(String examAccountId) {
    return encryptByAES(examAccountId, System.currentTimeMillis() / 1000L);
  }

  public String encryptByAES(String examAccountId, long timestamp) {
    return ExamAESUtils.encrypt(examAccountId + "," + timestamp, this.key, this.iv);
  }

  public String encode(String code) {
    return ExamAESUtils.encode(code);
  }
}
