package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLogType;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;
import java.util.UUID;

@Component
public class AccountUnbindPhoneEmailSchedule {
    public Logger log = LoggerFactory.getLogger(AccountUnbindPhoneEmailSchedule.class);
    @Autowired
    private UserCenterAccountGateway userCenterAccountGateway;
    @Autowired
    private AccountChangeLogHelper accountChangeLogHelper;

    @QSchedule("dcs.driver.account.unbind.phone.email.job")
    public void onExecute(Parameter parameter) {
        //    Log log = Log.getInstance(GuideBindMobilePhoneSchedule.class);
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        try{
            // 指定uid
            Cat.getOrCreateTraceContext();
            String uids = parameter.getString("uidList");
            String type = parameter.getString("type");

            if (StringUtils.isBlank(uids) || StringUtils.isBlank(type)) {
                return;
            }
            log.info("unbind account start", "uidList : " + uids + " type : " + type);
            if (!Lists.newArrayList("phone", "email").contains(type)) {
                return;
            }
            List<String> uidList = Lists.newArrayList(uids.split(","));
            if (CollectionUtils.isEmpty(uidList)) {
                log.warn("uid list is blank");
                return;
            }
            for (String uid : uidList) {
                try {
                    boolean result = false;
                    AccountChangeLogType changeLogType = AccountChangeLogType.UNBIND_PHONE;
                    if ("phone".equals(type)) {
                        result = userCenterAccountGateway.unBindPhone(uid, "System");
                    } else if ("email".equals(type)) {
                        result = userCenterAccountGateway.unBindEmail(uid, "System");
                        changeLogType = AccountChangeLogType.UNBIND_EMAIL;
                    }
                    if (result) {
                        log.info("unbind +" + type + " success", uid);
                        accountChangeLogHelper.saveLog(new AccountChangeLog(changeLogType, "System", uid, "", ""));
                    }
                } catch (Exception e) {
                    log.warn("unbind account phone email error",  uid, e, Maps.newHashMap());
                }
            }
        } finally {
            LoggerContext.destroy();
            Cat.destroy();
        }
    }
}
