package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.honour.DirectorObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.honour.DriverMedalObjectImpl;
import com.ctrip.dcs.driver.domain.honour.CustomerHonourMedalInfoDTO;
import com.ctrip.dcs.driver.domain.honour.QueryCustomerMedalListRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryCustomerMedalListResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.HonourEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.CustomerMedalSimpleInfo;
import com.ctrip.dcs.driver.value.honour.DirectorObject;
import com.ctrip.dcs.driver.value.honour.DriverMedalObject;
import com.ctrip.dcs.driver.value.honour.MedalObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询用户侧司机勋章列表
 */
@Component
public class QueryCustomerMedalListEngine extends ShoppingExecutor<QueryCustomerMedalListRequestType, QueryCustomerMedalListResponseType>
        implements Validator<QueryCustomerMedalListRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Autowired
    private CommonMultipleLanguages commonMultipleLanguages;

    @Override
    public void validate(AbstractValidator<QueryCustomerMedalListRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryCustomerMedalListEngine owner;
        private final QueryCustomerMedalListResponseType response;
        private final QueryCustomerMedalListRequestType request;
        private DirectorObject directorObject;
        private boolean useCustomerInfoCache;

        private Executor(QueryCustomerMedalListRequestType request, QueryCustomerMedalListResponseType response, QueryCustomerMedalListEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;
            this.useCustomerInfoCache = this.owner.domainBeanFactory.honourImgConfig().isCustomerMedalCache();
            this.invokeService();
        }

        private void invokeService() {
            // init response
            response.medalList = new ArrayList<>();
            directorObject = new DirectorObjectImpl.Builder(this.request.driverId, 1, this.owner.domainBeanFactory).build();
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(directorObject) && this.directorObject.isSupportHonourMedal();
        }

        @Override
        protected void buildResponse() {
            //缓存数据
            if(this.useCustomerInfoCache) {
                Pair<Boolean, List<CustomerHonourMedalInfoDTO>> cacehMedalInfo =
                        this.owner.domainBeanFactory.honourRedisLogic().getDriverCustomerMedalInfo(this.request.driverId);
                if (Objects.nonNull(cacehMedalInfo) && BooleanUtils.isTrue(cacehMedalInfo.getLeft())) {
                    response.medalList = cacehMedalInfo.getRight();
                    return;
                }
            }
            DriverMedalObject driverMedalObject = new DriverMedalObjectImpl(this.owner.domainBeanFactory, this.request.driverId, 1);

            //勋章列表
            MedalObject[] medalObjects = driverMedalObject.queryMedalList();
            this.convertMedalList(medalObjects);
        }

        private void convertMedalList(MedalObject[] medalObjects) {
            if(Objects.isNull(medalObjects) || medalObjects.length == 0) {
                return;
            }
            response.medalList = new ArrayList<>();
            Map<String, List<MedalObject>> medalObjectsGroupBy = Arrays.stream(medalObjects)
                    .collect(Collectors.groupingBy(MedalObject::typeCode));
            medalObjectsGroupBy.forEach((medalType, medalList) ->{
                if(CollectionUtils.isNotEmpty(medalList)) {
                    if(medalList.get(0).type().isHasLevel()) {
                        this.dealCustomerLevelMedalInfo(medalList);
                    } else {
                        for(MedalObject m : medalList) {
                            if(BooleanUtils.isFalse(m.isLight())){
                                continue;
                            }
                            CustomerMedalSimpleInfo customerMedalSimpleInfo = this.owner.domainBeanFactory.honourFormConfig().getCustomerMedalInfo(m.code());
                            if(Objects.nonNull(customerMedalSimpleInfo)) {
                                CustomerHonourMedalInfoDTO honourMedalInfo = new CustomerHonourMedalInfoDTO();
                                honourMedalInfo.code = m.code();
                                if(m.type() == HonourEnum.MedalTypeEnum.HONOURWALL) {
                                    honourMedalInfo.image = this.owner.domainBeanFactory.honourFormConfig().getMedalNewImg(honourMedalInfo.code);
                                } else {
                                    honourMedalInfo.image = this.owner.domainBeanFactory.honourImgConfig().getMedalImg(honourMedalInfo.code, true, true);
                                }
                                honourMedalInfo.name = this.owner.commonMultipleLanguages.getContent(customerMedalSimpleInfo.getName());
                                honourMedalInfo.customerDesc = this.owner.commonMultipleLanguages.getContent(customerMedalSimpleInfo.getToCustomerDesc());
                                honourMedalInfo.sortIndex = customerMedalSimpleInfo.getCustomerShowIndex();
                                response.medalList.add(honourMedalInfo);
                            }
                        }
                    }
                }
            });
            //排序
            if(CollectionUtils.isNotEmpty(response.medalList)) {
                response.medalList.sort(Comparator.comparing(CustomerHonourMedalInfoDTO::getSortIndex));
            }
            if(this.useCustomerInfoCache) {
                this.owner.domainBeanFactory.honourRedisLogic().saveDriverCustomerMedalInfo(this.request.driverId, response.medalList);
            }
        }

        /**
         * 服务好评(1)：取50赞以上（包括50赞）的，赞数最多的勋章 例：司机有 “1赞、50赞、100赞” 三枚勋章，则取 “100赞” 勋章===返回 >= 50 && 点亮的勋章
         * 行为勋章(全部)：服务好、车内整洁、活地图、帮拿行李
         * 荣誉勋章(全部)：城市王者、公益大使、见义勇为、拾金不昧、惊喜服务、退役军人、抗疫先锋、好人好事
         * */
        private void dealCustomerLevelMedalInfo(List<MedalObject> medalList) {
            CustomerMedalSimpleInfo customerMedalSimpleInfo = this.owner.domainBeanFactory.honourFormConfig().getCustomerMedalInfo(medalList.get(0).typeCode());
            if(Objects.nonNull(customerMedalSimpleInfo)) {
                MedalObject medalObject = medalList.stream().filter(m -> m.grade() >= 50 && m.isLight()).max(Comparator.comparing(
                        MedalObject::grade)).orElse(null);
                if(Objects.nonNull(medalObject)){
                    CustomerHonourMedalInfoDTO honourMedalInfo = new CustomerHonourMedalInfoDTO();
                    honourMedalInfo.code = customerMedalSimpleInfo.getCode();
                    honourMedalInfo.image = this.owner.domainBeanFactory.honourImgConfig().getMedalImg(medalObject.code(), true, true);
                    honourMedalInfo.name = this.owner.commonMultipleLanguages.getContent(customerMedalSimpleInfo.getName());
                    honourMedalInfo.customerDesc = this.owner.commonMultipleLanguages.getContent(customerMedalSimpleInfo.getToCustomerDesc());
                    honourMedalInfo.sortIndex = customerMedalSimpleInfo.getCustomerShowIndex();
                    response.medalList.add(honourMedalInfo);
                }
            }
        }
    }

    @Override
    public QueryCustomerMedalListResponseType execute(QueryCustomerMedalListRequestType request) {
        QueryCustomerMedalListResponseType response = new QueryCustomerMedalListResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryCustomerMedalListResponseType onException(QueryCustomerMedalListRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryCustomerMedalList error", ex);
        return super.onException(req, ex);
    }
}
