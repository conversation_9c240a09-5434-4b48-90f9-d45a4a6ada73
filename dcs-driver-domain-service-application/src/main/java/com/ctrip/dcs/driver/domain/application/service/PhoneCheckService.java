package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.domain.login.DriverLoginSuccessMessage;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/11 10:47
 * @Description: 手机号校验服务实现
 */
public interface PhoneCheckService {

    /**
     * 发起IVR外呼
     * @param task
     */
    boolean ivrCall(PhoneCheckTaskEntity task);

    /**
     * 更新司机手机号检查流水表任务
     * @param loginMsg
     */
    void updateDriverPhoneCheckRes(DriverLoginSuccessMessage loginMsg);

    /**
     *
     * @param taskTableList
     * @param check
     */
    void doUpdatePhoneCheck(List<PhoneCheckTaskEntity> taskTableList, PhoneCheckEntity check);

    /**
     * 按照司机生成对应的IVR外呼任务流水表
     *
     * @param driver
     * @param planTime
     */
    void generatePhoneCheckTaskByDriver(DriverEntity driver, LocalDateTime planTime);
}
