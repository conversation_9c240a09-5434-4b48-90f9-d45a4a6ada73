package com.ctrip.dcs.driver.account.application.mq;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

@Component
public class DriverGuideStateChangeListener {
    Log log = Log.getInstance(DriverGuideStateChangeListener.class);

    @Autowired
    private AccountService accountService;

    @QmqConsumer(prefix = "tour.driver.platform.driver.status.changed", consumerGroup = "*********")
    public void onMessage(Message message) {
        String uid = message.getStringProperty("ctripUID");
        // 上线
        if (message.getTags().contains("tag_driver_online") || message.getTags().contains("tag_driver_freeze")) {
            log.info("driver guide state change valid", String.format("uid : %s, tags : %s", uid, JsonUtil.toString(message.getTags())));
            accountService.updateAccountIdentityState(uid, AccountSouceEnum.DRIVER_GUIDE, true);
        } else if (message.getTags().contains("tag_driver_offline")) {
            // 下线
            log.info("driver guide state change invalid", String.format("uid : %s, tags : %s", uid, JsonUtil.toString(message.getTags())));
            accountService.updateAccountIdentityState(uid, AccountSouceEnum.DRIVER_GUIDE, false);
        }
    }
}
