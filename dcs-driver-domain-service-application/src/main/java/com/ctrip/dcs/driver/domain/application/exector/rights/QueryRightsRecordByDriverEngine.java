package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordByDriverRequestType;
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordByDriverResponseType;
import com.ctrip.dcs.driver.domain.rights.RightsRecordDTO;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.driver.value.rights.RightsRecordObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryRightsRecordByDriverEngine extends ShoppingExecutor<QueryRightsRecordByDriverRequestType, QueryRightsRecordByDriverResponseType>  implements Validator<QueryRightsRecordByDriverRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public QueryRightsRecordByDriverResponseType execute(QueryRightsRecordByDriverRequestType request) {
        QueryRightsRecordByDriverResponseType response = new QueryRightsRecordByDriverResponseType();
        QueryRightsRecordByDriverEngine.Executor executor = new QueryRightsRecordByDriverEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.fail(response, "510", "missing parameter");
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryRightsRecordByDriverRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("rightsType").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryRightsRecordByDriverEngine owner;
        private final QueryRightsRecordByDriverResponseType response;
        private final QueryRightsRecordByDriverRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(QueryRightsRecordByDriverRequestType request, QueryRightsRecordByDriverResponseType response, QueryRightsRecordByDriverEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;

            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory, Optional.ofNullable(request.driverId).orElse(0L), Strings.EMPTY);
        }

        @Override
        protected void buildResponse() {
            List<RightsRecordObject> rightsRecordObjects = driverRightsObject.queryDriverRightsRecordByDriver(request.rightsType,request.driverId);
            if (CollectionUtils.isEmpty(rightsRecordObjects)) {
                return;
            }
            response.rightsRecords = rightsRecordObjects.stream().map(recordObject -> {
                RightsRecordDTO record = new RightsRecordDTO();
                record.setDriverId(recordObject.drivId());
                record.setRightsType(recordObject.rightsType().getCode());
                record.setRightsName(recordObject.rightsName());
                record.setUseLevel(recordObject.useLevel().getCode());
                record.setLevelName(recordObject.levelName());
                record.setUseDate(LocalDateTimeUtils.format(recordObject.rightsUsedTime()));
                record.setUserOrderId(recordObject.userOrderId());
                record.setPurchaseOrderId(recordObject.purchaseOrderId());
                record.setSupplyOrderId(recordObject.supplyOrderId());
                record.setPunishOrderId(recordObject.punishOrderId());
                record.setMoney(recordObject.money());

                return record;
            }).collect(Collectors.toList());
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(driverRightsObject);
        }
    }
}
