package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.driver.domain.infrastructure.utils.DESUtils;
import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import com.ctrip.framework.foundation.Foundation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public final class CommonCryptographicUtils {

  @Autowired
  private CommonLogger logger;
  private String encryptKey;

  public String encryptKey() {
    return this.encryptKey;
  }

  @Autowired
  public void initialize() throws KMSUtils.KmsException {

    String token = "p125c5d6e5c2c442609a3defd9300f22bd";
    if (Foundation.server().getEnv().isFAT() || Foundation.server().getEnv().isFWS()) {
      token = "f18b2a1a7c86a64e11b376ace6e578b063";
    }
    this.encryptKey = KMSUtils.key(token);
    if (StringUtils.isBlank(this.encryptKey)) {
      throw new KMSUtils.KmsException("DriverCommonCryptographicUtils initialize error");
    }
    this.logger.info("KMS", "DriverCommonCryptographicUtils key: [" + this.encryptKey + "]");
  }

  public String decryptByDES(String decryptString) {
    return decryptByDES(decryptString, encryptKey);
  }

  public String decryptByDES(String decryptString, String decryptKey) {
    return DESUtils.decryptDES(decryptString, decryptKey);
  }

  public String encryptByDES(String encryptString) {
    return encryptByDES(encryptString, encryptKey);
  }

  public String encryptByDES(String encryptString, String encryptKey) {
    return DESUtils.encryptDES(encryptString, encryptKey);
  }
}
