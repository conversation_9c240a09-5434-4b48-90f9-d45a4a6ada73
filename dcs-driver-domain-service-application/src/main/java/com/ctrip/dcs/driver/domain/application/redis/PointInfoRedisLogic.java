package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.setting.DriverPointRestfulPopInfoModel;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class PointInfoRedisLogic {

  private static final String DRIVER_POINT_POP_INFO_REDIS_KEY = "driver:pointpop:%s";
  private static final Long DRIVER_POINT_INFO_REDIS_EXPIRE_TIME = 7200L;

  @Autowired
  DirectorRedis directorRedis;

  /**
   * 保存司机分弹窗信息
   * */
  public void saveDriverPointInfo(long driverId, DriverPointRestfulShowInfoPO driverPointRestfulShowInfoPO) {
    if(Objects.nonNull(driverPointRestfulShowInfoPO)){
      DriverPointRestfulPopInfoModel driverPointRestfulPopInfo = new DriverPointRestfulPopInfoModel();
      driverPointRestfulPopInfo.setNovicePointGuidePop((int)ObjectUtils.defaultIfNull(driverPointRestfulShowInfoPO.getNovicePointGuidePop(), 0));
      driverPointRestfulPopInfo.setNovicePointVoidancePop((int)ObjectUtils.defaultIfNull(driverPointRestfulShowInfoPO.getNovicePointVoidancePop(), 0));
      directorRedis.set(this.getPointInfoKey(driverId),
              JacksonUtil.serialize(driverPointRestfulPopInfo),
              DRIVER_POINT_INFO_REDIS_EXPIRE_TIME);
    }
  }

  /**
   * 查询司机分弹窗信息
   * */
  public DriverPointRestfulPopInfoModel queryDriverPointInfo(long driverId) {
    String redisInfo = directorRedis.get(this.getPointInfoKey(driverId));
    if(StringUtils.isNotBlank(redisInfo)){
      try {
        return JacksonUtil.deserialize(redisInfo, DriverPointRestfulPopInfoModel.class);
      }catch (Exception e){
        return null;
      }
    }
    return null;
  }

  /**
   * 清除司机分弹窗信息
   * */
  public void clearDriverPointInfo(long driverId) {
    directorRedis.remove(this.getPointInfoKey(driverId));
  }

  /**
   * 司机分KEY
   * */
  private String getPointInfoKey(long driverId) {
   return String.format(DRIVER_POINT_POP_INFO_REDIS_KEY, driverId);
  }
}
