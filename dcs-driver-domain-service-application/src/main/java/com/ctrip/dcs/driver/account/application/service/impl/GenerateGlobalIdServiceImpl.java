package com.ctrip.dcs.driver.account.application.service.impl;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.driver.value.account.AccountUidInfo;
import com.ctrip.dcs.driver.account.application.service.GenerateGlobalIdService;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.GlobalIdRecordDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.GlobalIdRecordPO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.go.log.Log;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class GenerateGlobalIdServiceImpl implements GenerateGlobalIdService {
    Log log = Log.getInstance(GenerateGlobalIdServiceImpl.class);

    @Resource
    private AccountBaseInfoDao accountBaseInfoDao;
    @Resource
    private UserCenterAccountGateway userCenterAccountGateway;
    @Resource
    private AccountMapperDao accountMapperDao;
    @Resource
    private GlobalIdRecordDao globalIdRecordDao;
    @Resource
    private ArchCoreInfoRepository archCoreInfoService;
    @Resource
    private LockService lockService;
    @Resource
    private DriverAccountConfig driverAccountConfig;

    @Override
    public Long generateGlobalId(String source, String countryCode, String phoneNumber) {
        Cat.logEvent(CatEventType.GENERATE_GLOBAL_ID_SOURCE, source);
        String encryptedPhoneNumber = archCoreInfoService.encryptByType(KeyType.Phone, phoneNumber);
        // 手机号加锁
        return lockService.executeInLock(String.format(Constants.ACCOUNT_LOCK_KEY_PATTERN, countryCode, encryptedPhoneNumber),
                driverAccountConfig.getAccountLockWaitMills(), () -> doGenerateGlobalId(source, countryCode, encryptedPhoneNumber));
    }

    private Long doGenerateGlobalId(String source, String countryCode, String phoneNumber) {
        log.info("generate global id start", String.format("%s, %s, %s", source, countryCode, phoneNumber));
        // 根据手机号获取uid
        String uid = getUIDByPhone(countryCode, phoneNumber);

        // 根据uid查询是否已有司机&司导身份（不论是否有效），有则返回其id
        Long existGlobalId = findExistGlobalId(uid);
        if (existGlobalId != null) {
            log.info("find exist global id", existGlobalId.toString());
            Cat.logEvent(CatEventType.GENERATE_GLOBAL_ID_RESULT, "Exist");
            return existGlobalId;
        }

        // 没有uid或者有uid但没有司机/司导身份，则根据手机号查询是否已生成过id，已生成过则直接返回
        GlobalIdRecordPO globalIdRecord = globalIdRecordDao.queryByPhone(countryCode, phoneNumber);
        if (globalIdRecord != null) {
            log.info("find global id from record", globalIdRecord.getId().toString());
            Cat.logEvent(CatEventType.GENERATE_GLOBAL_ID_RESULT, "Record");
            return globalIdRecord.getId();
        }

        // 未生成过，则直接生成新的
        Cat.logEvent(CatEventType.GENERATE_GLOBAL_ID_RESULT, "New");
        Long id = generateNewGlobalId(source, countryCode, phoneNumber);
        log.info("generate new global id", String.format("%s, %s, %s, %s", source, countryCode, phoneNumber, id));
        return id;
    }

    private String getUIDByPhone(String countryCode, String phoneNumber) {
        AccountBaseInfoPO accountBaseInfo = accountBaseInfoDao.queryByMobilePhone(countryCode, phoneNumber);
        // 查询本地账户
        if (accountBaseInfo != null) {
            Cat.logEvent(CatEventType.GENERATE_GLOBAL_ID_UID_FROM, "Table");
            return accountBaseInfo.getUid();
        } else {
            // 本地可能没有该账户（上线初期），去用户中心查询
            AccountUidInfo accountUidInfo = userCenterAccountGateway.queryAccountUidByPhone(countryCode, phoneNumber);
            if (accountUidInfo != null) {
                Cat.logEvent(CatEventType.GENERATE_GLOBAL_ID_UID_FROM, "UserCenter");
                return accountUidInfo.getUid();
            } else {
                Cat.logEvent(CatEventType.GENERATE_GLOBAL_ID_UID_FROM, "None");
                return "";
            }
        }
    }

    /**
     * 根据uid查询是否已有司机&司导身份（不论是否有效），有则返回其id
     */
    private Long findExistGlobalId(String uid) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        List<AccountMapperPO> accountMapper = accountMapperDao.queryByUid(uid);
        if (CollectionUtils.isEmpty(accountMapper)) {
            return null;
        }
        for (AccountMapperPO mapper : accountMapper) {
            if (AccountSouceEnum.isDriverGuide(mapper.getSource()) || AccountSouceEnum.isDriver(mapper.getSource())) {
                return Long.parseLong(mapper.getSourceId());
            }
        }
        return null;
    }

    private Long generateNewGlobalId(String source, String countryCode, String phoneNumber) {
        GlobalIdRecordPO globalIdRecordPO = new GlobalIdRecordPO();
        globalIdRecordPO.setSource(source);
        globalIdRecordPO.setCountryCode(countryCode);
        globalIdRecordPO.setPhoneNumber(phoneNumber);
        globalIdRecordDao.insert(globalIdRecordPO);
        return globalIdRecordPO.getId();
    }

}
