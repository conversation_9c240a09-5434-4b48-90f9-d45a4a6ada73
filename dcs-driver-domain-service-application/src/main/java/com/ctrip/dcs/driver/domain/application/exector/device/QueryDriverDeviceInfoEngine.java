package com.ctrip.dcs.driver.domain.application.exector.device;


import com.ctrip.dcs.driver.domain.application.service.DriverDeviceInfoService;
import com.ctrip.dcs.driver.domain.device.QueryDriverDeviceInfoRequestType;
import com.ctrip.dcs.driver.domain.device.QueryDriverDeviceInfoResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.model.device.DriverDeviceInfoModel;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class QueryDriverDeviceInfoEngine extends ShoppingExecutor<QueryDriverDeviceInfoRequestType, QueryDriverDeviceInfoResponseType> implements Validator<QueryDriverDeviceInfoRequestType> {

    @Autowired
    DriverDeviceInfoService deviceInfoService;

    @Override
    public QueryDriverDeviceInfoResponseType execute(QueryDriverDeviceInfoRequestType request) {
        DriverDeviceInfoModel deviceInfoModel = null;
        if (Objects.nonNull(request.getDriverId())) {
            deviceInfoModel = deviceInfoService.queryDeviceInfo(request.getDriverId());
        } else if (StringUtils.isNotBlank(request.getUid())) {
            deviceInfoModel = deviceInfoService.queryDeviceInfo(request.getUid());
        }
        return ServiceResponseUtils.success(convert(deviceInfoModel));
    }

    private QueryDriverDeviceInfoResponseType convert(DriverDeviceInfoModel deviceInfoModel) {
        QueryDriverDeviceInfoResponseType resp = new QueryDriverDeviceInfoResponseType();
        if (deviceInfoModel == null) {
            return resp;
        }
        resp.setUid(deviceInfoModel.getUid());
        resp.setAppId(deviceInfoModel.getAppId());
        resp.setAppVer(deviceInfoModel.getAppVer());
        resp.setRnVer(deviceInfoModel.getRnVer());
        resp.setCid(deviceInfoModel.getCid());
        resp.setOs(deviceInfoModel.getOs());
        resp.setOsVer(deviceInfoModel.getOsVer());
        resp.setLocal(deviceInfoModel.getLocal());
        return resp;
    }

}
