package com.ctrip.dcs.driver.domain.application.http;

import com.dianping.cat.Cat;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;
import java.io.IOException;

@Component
public class HttpClient {
    private static QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    /**
     * get请求
     * 会出现如下的异常，增加重试
     * unexpected end of stream on https://miniprogram-kyc.tencentcloudapi.com/..."
     * cause = {EOFException@16474} "java.io.EOFException: \n not found: limit=0 content=…"
     *
     * @param url
     * @return
     * @throws IOException
     */
    public String get(String url) {
        try{
            ListenableFuture<Response> responseFuture = qunarAsyncClient.get(url);
            Response response = responseFuture.get();
            return response.getResponseBody();
        }catch (Exception e){
            Cat.logEvent("request_by_proxygate2_ex",url+";"+e);
        }
        return Strings.EMPTY;
    }

    /**
     * post请求
     *
     * @param url
     * @param json
     * @return
     * @throws IOException
     */
    public String post(String url, String json){
        try{
            QHttpOption option = new QHttpOption();
            option.addHeader("Content-Type", "application/json; charset=utf-8");
            option.setPostBodyData(json);
            ListenableFuture<Response> responseFuture = qunarAsyncClient.post(url, option);
            Response response = responseFuture.get();
            return response.getResponseBody();
        }catch (Exception e){
            Cat.logEvent("request_by_proxygate2_ex",url+";"+e);
        }
        return Strings.EMPTY;
    }
}
