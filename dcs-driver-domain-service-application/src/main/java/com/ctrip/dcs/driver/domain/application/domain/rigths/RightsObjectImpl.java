package com.ctrip.dcs.driver.domain.application.domain.rigths;

import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.VacationExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.WelfareExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsIntroduceInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.value.rights.RightsObject;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.gson.annotations.Expose;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 权益域
 */
public class RightsObjectImpl extends RightsObject {

    private final DomainBeanFactory domainBeanFactory;

    private final int REASSIGN_WEEK_COUNT = 4;

    public RightsObjectImpl(DomainBeanFactory domainBeanFactory, RightsModel rightsModel) {
        this.domainBeanFactory = domainBeanFactory;
        this.id = rightsModel.getId();
        this.rigthsConfigId = rightsModel.getRightsConfigId();
        this.rigthsType = RightsTypeEnum.getByCode(rightsModel.getRightsType());
        this.rightsName = rightsModel.getRightsName();
        this.rightsIntroduceInfos = domainBeanFactory.rightsConfig().getRightsIntroduces(rightsModel.getCityId(), rightsModel.getRightsType());
        this.rightsDesc = rightsModel.getRightsDesc();
        this.useLimit = rightsModel.getUseLimit();
        this.usedCount = rightsModel.getUseCount();
        this.rightsStatus = RightsStatusEnum.getByStatus(rightsModel.getRightsStatus());
        this.monthIdx = rightsModel.getMonthIdx();
        this.rightsStratTime = rightsModel.getRightsStratTime();
        this.rightsEndTime = rightsModel.getRightsEndTime();
        this.rightsIssueTime = rightsModel.getRightsIssueTime();
        switch (rigthsType) {
            case RIGHTS_TYPE_WELFARE:
                WelfareExtendDto welfareExtendDto = JacksonUtil.deserialize(rightsModel.getExtend(), WelfareExtendDto.class);
                if (Objects.nonNull(welfareExtendDto)) {
                    this.welfareLimit = Optional.ofNullable(welfareExtendDto.getWelfareLimit()).orElse(BigDecimal.ZERO);
                    this.welfareUsed = Optional.ofNullable(welfareExtendDto.getWelfareUse()).orElse(BigDecimal.ZERO);
                    break;
                }
            case RIGHTS_TYPE_REASSIGN:
                this.reassignUsedByWeek = REASSIGN_WEEK_COUNT-CommonRightsExtendUtils.getReassignUseCount(rightsModel.getExtend());
                break;
            case RIGHTS_TYPE_VACATION:
                VacationExtendDto vacationExtendDto = JacksonUtil.deserialize(rightsModel.getExtend(), VacationExtendDto.class);
                if(Objects.nonNull(vacationExtendDto)){
                    this.vacationLimit = vacationExtendDto.getVacationLimit();
                    this.vacationUsed = vacationExtendDto.getVacationUse();
                    break;
                }
        }
    }

    @Expose
    private long id;
    @Expose
    private long rigthsConfigId;
    @Expose
    private RightsTypeEnum rigthsType;
    @Expose
    private String rightsName;
    @Expose
    private List<FormRightsIntroduceInfo> rightsIntroduceInfos;
    @Expose
    private String rightsDesc;
    @Expose
    private int useLimit;
    @Expose
    private int usedCount;
    @Expose
    private int vacationLimit;
    @Expose
    private int vacationUsed;
    @Expose
    private RightsStatusEnum rightsStatus;
    @Expose
    private String monthIdx;
    @Expose
    private String rightsStratTime;
    @Expose
    private String rightsEndTime;
    @Expose
    private String rightsIssueTime;
    @Expose
    private BigDecimal welfareLimit;
    @Expose
    private BigDecimal welfareUsed;
    @Expose
    private int reassignUsedByWeek;

    @Override
    public long id() {
        return this.id;
    }

    @Override
    public long rigthsConfigId() {
        return this.rigthsConfigId;
    }

    @Override
    public RightsTypeEnum rigthsType() {
        return this.rigthsType;
    }

    @Override
    public String rightsName() {
        return this.rightsName;
    }

    @Override
    public String rightsDesc() {
        return this.rightsDesc;
    }

    @Override
    public List<FormRightsIntroduceInfo> rightsIntroduceInfos() {
        return this.rightsIntroduceInfos;
    }

    @Override
    public int useLimit() {
        return this.useLimit;
    }

    @Override
    public int usedCount() {
        return this.usedCount;
    }

    @Override
    public RightsStatusEnum rightsStatus() {
        return this.rightsStatus;
    }

    @Override
    public String monthIdx() {
        return this.monthIdx;
    }

    @Override
    public String rightsStratTime() {
        return this.rightsStratTime;
    }

    @Override
    public String rightsEndTime() {
        return this.rightsEndTime;
    }

    @Override
    public String rightsIssueTime() {
        return this.rightsIssueTime;
    }

    @Override
    public BigDecimal welfareLimit() {
        return this.welfareLimit;
    }

    @Override
    public BigDecimal welfareUsed() {
        return this.welfareUsed;
    }

    @Override
    public int reassignUsedByWeek() {
        return this.reassignUsedByWeek;
    }

    @Override
    public int vacationLimit() {
        return this.vacationLimit;
    }

    @Override
    public int vacationUsed() {
        return this.vacationUsed;
    }
}
