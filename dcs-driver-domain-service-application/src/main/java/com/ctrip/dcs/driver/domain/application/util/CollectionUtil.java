package com.ctrip.dcs.driver.domain.application.util;

import java.util.ArrayList;
import java.util.List;

public class CollectionUtil {

    public static <T> List<List<T>> slidingWindow(List<T> list, int windowSize) {
        List<List<T>> result = new ArrayList<>();
        int size = list.size();
        for (int i = 0; i < size; i += windowSize) {
            int end = Math.min(i + windowSize, size);
            List<T> window = list.subList(i, end);
            result.add(new ArrayList<>(window));
        }
        return result;
    }
}
