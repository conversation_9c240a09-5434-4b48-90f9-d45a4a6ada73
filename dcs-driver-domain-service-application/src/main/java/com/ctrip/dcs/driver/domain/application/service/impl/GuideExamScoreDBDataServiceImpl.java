package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.GuideExamScoreDBDataService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideExamScoreDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideExamScorePO;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideExamScoreModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GuideExamScoreDBDataServiceImpl implements GuideExamScoreDBDataService {
    @Autowired
    private GuideExamScoreDao guideExamScoreDao;

    @Override
    public  Map<String, List<GuideExamScoreModel>> queryPassedGuideExamScoreInfoMapByAccount(String examAccountId){
        List<GuideExamScorePO> guideExamScorePOS =
            guideExamScoreDao.queryPassedGuideExamScore(examAccountId);
        if (CollectionUtils.isEmpty(guideExamScorePOS)) {
            return Collections.emptyMap();
        }
        return guideExamScorePOS.stream()
            .sorted(Comparator.comparing(GuideExamScorePO::getExamScore).reversed())
            .map(po -> convert2GuideExamScoreModel(po))
            .collect(Collectors.groupingBy(GuideExamScoreModel::getApplySubject));
    }


    @Override
    public Long countByExamAccountIdApplySubject(String examAccountId, String applySubject){
        return guideExamScoreDao.countByExamAccountIdApplySubject(examAccountId, applySubject);
    }


    private GuideExamScoreModel convert2GuideExamScoreModel(GuideExamScorePO record) {
        GuideExamScoreModel model = new GuideExamScoreModel();
        model.setId(record.getId());
        model.setExamAccountId(record.getExamAccountId());
        model.setApplySubject(record.getApplySubject());
        model.setExamScore(record.getExamScore());
        model.setExamIsPassed(record.getExamIsPassed());
        model.setCompleteTime(LocalDateTimeUtils.format(record.getCompleteTime()));
        model.setTimeZone(record.getTimeZone());
        model.setDatachangeCreatetime(LocalDateTimeUtils.format(record.getDatachangeCreatetime()));
        return model;
    }
}
