package com.ctrip.dcs.driver.domain.application.exector.phone;

import com.ctrip.dcs.driver.domain.application.condition.NotifyVerificationCodeByCallPhoneCondition;
import com.ctrip.dcs.driver.domain.application.covert.NotifyVerificationCodeByCallPhoneConvert;
import com.ctrip.dcs.driver.domain.application.service.NotifyVerificationCodeByCallPhoneService;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.NotifyVerificationCodeByCallPhoneRequestType;
import com.ctrip.model.NotifyVerificationCodeByCallPhoneResponseType;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class NotifyVerificationCodeByCallPhoneEngine extends ShoppingExecutor<NotifyVerificationCodeByCallPhoneRequestType, NotifyVerificationCodeByCallPhoneResponseType>
        implements Validator<NotifyVerificationCodeByCallPhoneRequestType> {
    @Resource
    private NotifyVerificationCodeByCallPhoneConvert convert;
    @Resource
    private NotifyVerificationCodeByCallPhoneService service;

    /**
     * 拨通用户手机，告知其验证码
     *
     * @param request
     * @return
     */
    @Override
    public NotifyVerificationCodeByCallPhoneResponseType execute(NotifyVerificationCodeByCallPhoneRequestType request) {
        NotifyVerificationCodeByCallPhoneCondition condition = convert.buildCondition(request);
        boolean callResult = service.call(condition);
        return ServiceResponseUtils.success(convert.buildResponse(callResult));
    }

    @Override
    public void validate(AbstractValidator<NotifyVerificationCodeByCallPhoneRequestType> validator) {
        validator.ruleFor("locale").notNull().notEmpty();
        validator.ruleFor("countryCode").notNull().notEmpty();
        validator.ruleFor("phoneNumber").notNull().notEmpty();
        validator.ruleFor("verificationCode").notNull().notEmpty();
        validator.ruleFor("channel").notNull().notEmpty();
    }
}
