package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo;
import com.ctrip.dcs.driver.domain.rights.*;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryFormRightsAndLevelEngine extends ShoppingExecutor<QueryFormRightsAndLevelRequestType, QueryFormRightsAndLevelResponseType>
        implements Validator<QueryFormRightsAndLevelRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public QueryFormRightsAndLevelResponseType execute(QueryFormRightsAndLevelRequestType request) {
        QueryFormRightsAndLevelResponseType response = new QueryFormRightsAndLevelResponseType();

        List<FormLevelInfo> formLevelInfos = this.domainBeanFactory.levelConfig().getCityDriverLevel(request.cityId);
        if (CollectionUtils.isNotEmpty(formLevelInfos)) {
            response.levels = formLevelInfos.stream().map(formLevelInfo -> {
                FormLevelDTO formLevelDTO = new FormLevelDTO();
                formLevelDTO.setId(formLevelInfo.getId());
                formLevelDTO.setDriverLevel(formLevelInfo.getLevel());
                formLevelDTO.setLevelName(this.domainBeanFactory.driverLevelHelper().getDriverLevelName(formLevelInfo.getLevel(), formLevelInfo.getLevelName()));
                formLevelDTO.setCityIdsStr(formLevelInfo.getCityIdsStr());
                formLevelDTO.setRightsIds(this.domainBeanFactory.rightsConfig().levelRightsMap.get(formLevelInfo.getLevel()));
                formLevelDTO.setDriverPointLow(formLevelInfo.getDriverPointLow());
                formLevelDTO.setActivityLow(formLevelInfo.getActivityLow());
                formLevelDTO.setSafePointLow(formLevelInfo.getSafePointLow());
                formLevelDTO.setRankLow(formLevelInfo.getHasRank() == 0 ? 0 : formLevelInfo.getRankLow());
                return formLevelDTO;
            }).collect(Collectors.toList());
        }

        List<FormRightsInfo> formRightsInfos = this.domainBeanFactory.rightsConfig().cityRightsMap.get(request.cityId);
        if (CollectionUtils.isNotEmpty(formRightsInfos)) {
            response.rights = formRightsInfos.stream().map(formRightsInfo -> {
                FormRightsDTO formRightsDTO = new FormRightsDTO();
                formRightsDTO.setId(formRightsInfo.getId());
                formRightsDTO.setRightsType(formRightsInfo.getRightsType());
                formRightsDTO.setRightsName(formRightsInfo.getRightsName());
                formRightsDTO.setRightsDesc(formRightsInfo.getRightsDesc());
                formRightsDTO.setCityIdsStr(formRightsInfo.getCityIdsStr());
                formRightsDTO.setIntroductions(formRightsInfo.getRightsIntroduceList().stream().map(o -> {
                    FormRightsIntroducteDTO introducteDTO = new FormRightsIntroducteDTO();
                    introducteDTO.setTitle(o.getTitle());
                    introducteDTO.setIntroduce(o.getIntroduce());
                    return introducteDTO;
                }).collect(Collectors.toList()));
                formRightsDTO.setLevels(formRightsInfo.getLevelList().stream().map(o -> {
                    FormRightsChoiceLevelDTO choiceLevelDTO = new FormRightsChoiceLevelDTO();
                    choiceLevelDTO.setDriverLevel(o.getLevel());
                    choiceLevelDTO.setExtend(o.getExtend());
                    choiceLevelDTO.setUseLimit(o.getIsLimit() == 0 ? 0 : o.getUseLimit());
                    return choiceLevelDTO;
                }).collect(Collectors.toList()));
                return formRightsDTO;
            }).collect(Collectors.toList());
        }

        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryFormRightsAndLevelRequestType> validator) {
        validator.ruleFor("cityId").notNull().notEmpty();
    }
}
