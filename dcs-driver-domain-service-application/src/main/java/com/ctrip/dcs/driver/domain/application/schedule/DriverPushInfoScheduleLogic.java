package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.application.util.PushInfoUtil;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import com.ctrip.dcs.shopping.json.ShoppingJsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;

@Component
public class DriverPushInfoScheduleLogic {
    private final static int PUSH_INFO_REDIS_EXPIRE_TIME = 864000;  // 10 days
    private final static int PAGE_SIZE = 1000;                      // 每批次执行数量

    @Autowired
    PushConfigRedisLogic pushConfigRedisLogic;

    @Autowired
    private DriverOrderPushConfigDao driverOrderPushConfigDao;

    @Autowired
    DirectorRedis directorRedis;

    @QSchedule("dcs.driver.push.config.refresh.job")
    public void syncPushInfoToRedis(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        final TaskMonitor monitor = TaskHolder.getKeeper();
        //记录任务日志
        Logger logger = monitor.getLogger();
        logger.info("[dcs.driver.push.config.refresh.job] start");

        int size = driverOrderPushConfigDao.count();
        if(size == 0) {
            return;
        }

        AtomicInteger index = new AtomicInteger();  // 游标
        while(Math.multiplyExact(index.get(), PAGE_SIZE) <= size){
            List<DriverOrderPushConfigPO> driverOrderPushConfigList =
                    driverOrderPushConfigDao.findByLimit(Math.multiplyExact(index.get(), PAGE_SIZE), PAGE_SIZE);
            if(CollectionUtils.isNotEmpty(driverOrderPushConfigList)) {
                System.out.println(String.format("start : %s", LocalDateTime.now().toString()));
                this.directorRedis.mset(PUSH_INFO_REDIS_EXPIRE_TIME,
                        driverOrderPushConfigList.stream().flatMap(config ->
                        Stream.of(PushInfoUtil.getPushConfigKey(config.getDrvId()),
                        ShoppingJsonUtils.serializeToJson(PushInfoUtil.convertToPushInfo(config)))
                ).toArray(String[]::new));

                this.directorRedis.mset(-1,
                        driverOrderPushConfigList.stream().flatMap(config ->
                                Stream.of(String.format(PushInfoUtil.DRIVER_LANGUAGE_REDIS_KEY, config.getDrvId()), config.getDrvLanguage())
                        ).toArray(String[]::new));
            }
            index.incrementAndGet();
        }
        logger.info("[dcs.driver.push.config.refresh.job] end");
    }
}
