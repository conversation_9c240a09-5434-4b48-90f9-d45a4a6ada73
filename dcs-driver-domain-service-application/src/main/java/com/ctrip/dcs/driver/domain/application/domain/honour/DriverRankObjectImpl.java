package com.ctrip.dcs.driver.domain.application.domain.honour;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankCityDataModel;
import com.ctrip.dcs.driver.value.honour.DriverRankObject;
import com.ctrip.dcs.driver.value.honour.RankObject;
import com.google.gson.annotations.Expose;

public class DriverRankObjectImpl extends DriverRankObject {

    DriverRankObjectImpl(DomainBeanFactory domainBeanFactory, RankCityDataModel rankCityDataModel, RankObject owner) {
        this.driverId = rankCityDataModel.getDrvId();
        this.orderCount = rankCityDataModel.getOrderCnt();
        this.rank = rankCityDataModel.getRanking();
        this.likeCount = domainBeanFactory.honourRedisLogic().getCurrentRankLikeCount(
                this.driverId, owner.cityId(), owner.subDataTime());
        this.maskName = rankCityDataModel.getDrvName();
    }

    @Expose
    private long driverId;
    @Expose
    private int orderCount;
    @Expose
    private int likeCount;
    @Expose
    private int rank;
    @Expose
    private String maskName;

    @Override
    public long driverId() {
        return this.driverId;
    }

    @Override
    public int orderCount() {
        return this.orderCount;
    }

    @Override
    public int likeCount() {
        return this.likeCount;
    }

    @Override
    public int rank() {
        return this.rank;
    }

    @Override
    public String maskName() {
        return this.maskName;
    }
}
