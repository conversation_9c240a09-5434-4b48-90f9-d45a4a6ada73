package com.ctrip.dcs.driver.domain.application.http.request;

public class ExamGetDeptInfoRequest {
  private String[] codes;

  private String selectDate;

  private String endDate;

  private int currentPage;

  private String[] exFields;

  private int pageSize = 100;

  public int getPageSize() {
    return pageSize;
  }

  public void setPageSize(int pageSize) {
    this.pageSize = pageSize;
  }

  public String[] getExFields() {
    return exFields;
  }

  public void setExFields(String[] exFields) {
    this.exFields = exFields;
  }

  public int getCurrentPage() {
    return currentPage;
  }

  public void setCurrentPage(int currentPage) {
    this.currentPage = currentPage;
  }

  public String getSelectDate() {
    return selectDate;
  }

  public void setSelectDate(String selectDate) {
    this.selectDate = selectDate;
  }

  public String getEndDate() {
    return endDate;
  }

  public void setEndDate(String endDate) {
    this.endDate = endDate;
  }

  public String[] getCodes() {
    return codes;
  }

  public void setCodes(String[] codes) {
    this.codes = codes;
  }
}
