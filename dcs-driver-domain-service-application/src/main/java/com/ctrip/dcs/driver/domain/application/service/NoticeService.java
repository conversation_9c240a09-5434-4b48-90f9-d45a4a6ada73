package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeUserModel;

/**
 * Created by <AUTHOR> on 2023/3/5 20:10
 */
public interface NoticeService {

  /**
   * 查询公告
   */
  NoticeModel queryNotice(NoticeUserModel user);

  /**
   * vbk更新公告信息
   */
  Void noticeUpdate(long noticeId);
}
