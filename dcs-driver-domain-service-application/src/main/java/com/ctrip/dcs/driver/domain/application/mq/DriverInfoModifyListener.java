package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLogType;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.driver.domain.infrastructure.dto.account.AccountBaseDto;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.tms.transport.mq.model.DriverMQDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

@Component
public class DriverInfoModifyListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DriverInfoModifyListener.class);

    public static final String CONSUMER_GROUP = "*********";

    @Autowired
    UserCenterAccountGateway userCenterAccountGateway;
    @Autowired
    AccountChangeLogHelper accountChangeLogHelper;


    @Override
    @QmqConsumer(prefix = "dcs.tms.transport.phone.email.modify.qmq", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        String data = message.getStringProperty("content");
        DriverMQDTO driverDto = JacksonUtil.deserialize(data, DriverMQDTO.class);
        if (Objects.isNull(driverDto)) {
            return;
        }

        //修改邮箱
        if(StringUtils.isNotBlank(driverDto.getEmail())){
            LOGGER.info("tms update account email", JsonUtil.toString(driverDto));
            accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.BIND_EMAIL, AccountSouceEnum.DRIVER_SOURCE.getName(), driverDto.getUid(), "", driverDto.getEmail()));
            userCenterAccountGateway.bindEmail(buildDto(driverDto),driverDto.getUid());
        }

        //修改手机
        if (StringUtils.isNotBlank(driverDto.getPhone()) && StringUtils.isNotBlank(driverDto.getCountryCode())) {
            LOGGER.info("tms update account phone", JsonUtil.toString(driverDto));
            accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.BIND_PHONE, AccountSouceEnum.DRIVER_SOURCE.getName(), driverDto.getUid(), "", driverDto.getCountryCode() + "-" + driverDto.getPhone()));
            userCenterAccountGateway.bindMobilePhone(buildDto(driverDto),driverDto.getUid());
        }

    }

    private AccountBaseDto buildDto(DriverMQDTO driverDto) {
        AccountBaseDto accountBaseDto = new AccountBaseDto();
        accountBaseDto.setCountryCode(driverDto.getCountryCode());
        accountBaseDto.setPhoneNumber(driverDto.getPhone());
        accountBaseDto.setEmail(driverDto.getEmail());
        accountBaseDto.setOperator(driverDto.getModifyUser());
        return accountBaseDto;
    }

}
