package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.value.UpdateAccountParam;
import com.ctrip.dcs.driver.domain.account.UpdateAccountRequestType;
import com.ctrip.dcs.driver.domain.account.UpdateAccountResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UpdateAccountEngine extends ShoppingExecutor<UpdateAccountRequestType, UpdateAccountResponseType> implements Validator<UpdateAccountRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public void validate(AbstractValidator<UpdateAccountRequestType> validator) {
        validator.ruleFor("source").notNull().notEmpty();
        validator.ruleFor("uid").notNull().notEmpty();
    }

    @Override
    public UpdateAccountResponseType execute(UpdateAccountRequestType request) {

        UpdateAccountParam param = new UpdateAccountParam();
        param.setSource(request.getSource());
        param.setUid(request.getUid());
        param.setCountryCode(request.getCountryCode());
        param.setPhoneNumber(request.getPhoneNumber());
        param.setEmail(request.getEmail());
        param.setName(request.getName());
        param.setPayoneerAccountId(request.getPayoneerAccountId());
        param.setModifyUser(request.getModifyUser());
        return accountService.updateAccount(param);
    }
}
