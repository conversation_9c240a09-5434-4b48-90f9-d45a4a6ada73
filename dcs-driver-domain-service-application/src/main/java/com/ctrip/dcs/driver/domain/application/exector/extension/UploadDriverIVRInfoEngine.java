package com.ctrip.dcs.driver.domain.application.exector.extension;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.extension.DriverObjectImpl;
import com.ctrip.dcs.driver.domain.application.service.ExtensionService;
import com.ctrip.dcs.driver.domain.extension.UploadDriverIVRInfoRequestType;
import com.ctrip.dcs.driver.domain.extension.UploadDriverIVRInfoResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.model.extension.IVRExtDataInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.extension.IVRInfoModel;
import com.ctrip.dcs.driver.value.extension.DriverObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 上传IRV信息
 */
@Component
public class UploadDriverIVRInfoEngine extends ShoppingExecutor<UploadDriverIVRInfoRequestType, UploadDriverIVRInfoResponseType>
        implements Validator<UploadDriverIVRInfoRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Autowired
    private ExtensionService extensionService;

    @Override
    public void validate(AbstractValidator<UploadDriverIVRInfoRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("customerOrderId").notNull().notEmpty();
        validator.ruleFor("driverOrderId").notNull().notEmpty();
        validator.ruleFor("pageId").notNull().notEmpty();
        validator.ruleFor("servicePhone").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final UploadDriverIVRInfoEngine owner;
        private final UploadDriverIVRInfoResponseType response;
        private final UploadDriverIVRInfoRequestType request;
        private DriverObject driverObject;

        private Executor(UploadDriverIVRInfoRequestType request, UploadDriverIVRInfoResponseType response, UploadDriverIVRInfoEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.uuid = Strings.EMPTY;
            this.request = request;
            this.invokeService();
        }

        private void invokeService() {
            this.driverObject = new DriverObjectImpl(this.request.driverId, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            if(this.driverObject == null || this.driverObject.driverId() == 0){
                return false;
            }
            return true;
        }

        @Override
        protected void buildResponse() {
            this.response.uuid = this.owner.extensionService.sendAppUserInfo(this.bulidIVRInfoModel());
        }

        /**
         * 生成IVR上传数据模型
         * */
        private IVRInfoModel bulidIVRInfoModel() {
            IVRInfoModel ivrInfoModel = new IVRInfoModel();
            ivrInfoModel.setDriverId(this.request.driverId);
            ivrInfoModel.setPageId(this.request.pageId);
            IVRExtDataInfoModel ivrExtDataInfoModel = new IVRExtDataInfoModel();
            ivrExtDataInfoModel.setDriverId(String.valueOf(this.driverObject.driverId()));
            ivrExtDataInfoModel.setOrderId(this.request.customerOrderId);
            ivrExtDataInfoModel.setProdectId(this.request.driverOrderId);
            ivrExtDataInfoModel.setDrivermobile(this.driverObject.driverPhone());
            ivrExtDataInfoModel.setCityId(String.valueOf(this.driverObject.cityId()));
            ivrExtDataInfoModel.setServicePhone(this.request.servicePhone);
            ivrInfoModel.setIvrExtDataInfoModel(ivrExtDataInfoModel);
            return ivrInfoModel;
        }
    }

    @Override
    public UploadDriverIVRInfoResponseType execute(UploadDriverIVRInfoRequestType request) {
        UploadDriverIVRInfoResponseType response = new UploadDriverIVRInfoResponseType();
        Executor executor = new Executor(request, response, this);
        if(executor.validate()){
            executor.buildResponse();
        }
        if(StringUtils.isBlank(response.uuid)) {
            return ServiceResponseUtils.fail(response, "501", "UploadDriverIVRInfo failed");
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public UploadDriverIVRInfoResponseType onException(UploadDriverIVRInfoRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("UploadDriverIVRInfo error", ex);
        return super.onException(req, ex);
    }
}
