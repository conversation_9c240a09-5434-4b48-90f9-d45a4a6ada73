package com.ctrip.dcs.driver.account.application.convert;
/*
作者：pl.yang
创建时间：2025/5/7-上午10:25-2025
*/


import com.ctrip.dcs.driver.account.domain.condition.UnbindBankCardCondition;
import com.ctrip.dcs.driver.domain.account.UnbindAccountBankCardRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: UnbindAccountBankCardConverrt
 * @Package com.ctrip.dcs.driver.account.application.convert
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/7 上午10:25
 */

@Component
public class UnbindAccountBankCardConvert {

    public UnbindBankCardCondition convertCondition(UnbindAccountBankCardRequestType request) {
        return UnbindBankCardCondition.builder()
                .uid(request.getUid())
                .bankCardId(request.getBankCardId())
                .build();
    }
}
