package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.common.CommonPaginationUtils;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.dcs.driver.domain.rights.QueryDriverIdByRightsRequestType;
import com.ctrip.dcs.driver.domain.rights.QueryDriverIdByRightsResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;

import java.sql.Types;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryDriverIdByRightsEngine extends ShoppingExecutor<QueryDriverIdByRightsRequestType, QueryDriverIdByRightsResponseType>
        implements Validator<QueryDriverIdByRightsRequestType> {

    @Autowired
    private DrivRightsDao dao;
    @Autowired
    SystemQConfig systemQConfig;

    @Override
    public QueryDriverIdByRightsResponseType execute(QueryDriverIdByRightsRequestType request) {
        QueryDriverIdByRightsResponseType response = new QueryDriverIdByRightsResponseType();
        String closeRights = systemQConfig.getString("close_rights");
        if (!Strings.isBlank(closeRights) && closeRights.contains(request.getRightsType().toString())) {
            response.setPagination(CommonPaginationUtils.buildPaginationDTO(request.paginator.pageNo,request.paginator.pageSize,0));
            return response;
        }

        Long total = dao.countByRightsAndMonth(request.rightsType, request.date);
        PaginationDTO paginationDTO = CommonPaginationUtils.buildPaginationDTO(request.paginator.pageNo,request.paginator.pageSize,total.intValue());
        response.setDriverIds(queryDrivIdsByRights(request));
        response.setPagination(paginationDTO);
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryDriverIdByRightsRequestType> validator) {
        validator.ruleFor("rightsType").notNull().notEmpty();
        validator.ruleFor("date").notNull().notEmpty();
        validator.ruleFor("paginator").notNull().notEmpty();
        validator.ruleFor("paginator").setValidator(new PaginatorDTOValidator());
    }

    private static class PaginatorDTOValidator extends AbstractValidator<PaginatorDTO> {
        public PaginatorDTOValidator() {
            super(PaginatorDTO.class);
            ruleFor("pageNo").notNull().notEmpty().greaterThanOrEqualTo(1);
        }
    }

    private List<Long> queryDrivIdsByRights(QueryDriverIdByRightsRequestType request) {
        List<Long> longlist = Lists.newArrayList();
        try {
            SelectSqlBuilder builder = new SelectSqlBuilder();
            builder.selectAll().atPage(request.paginator.pageNo, request.paginator.pageSize).orderBy("id", true);
            builder.and().equal("rights_type", request.rightsType, Types.TINYINT);
            builder.and().equal("month_idx", request.date, Types.VARCHAR);
            builder.and().notEqual("rights_status", RightsStatusEnum.RIGHTS_STATUS_CANCELLED.getStatus(), Types.TINYINT);
            List<DrivRightsPO> poList = dao.query(builder);
            longlist = poList.stream().map(o -> o.getDrivId()).collect(Collectors.toList());
        } catch (Exception e) {
            CommonLogger.INSTANCE.warn("failed to queryDrivIdsByRights ", e);
        }
        return longlist;
    }
}
