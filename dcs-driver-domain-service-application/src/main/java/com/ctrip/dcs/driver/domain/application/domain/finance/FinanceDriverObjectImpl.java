package com.ctrip.dcs.driver.domain.application.domain.finance;

import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exector.CheckVerificationExecutor;
import com.ctrip.dcs.driver.domain.application.domain.exector.SendVerificationExecutor;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.PhoneAndVerifyCodeModel;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.regulation.OldDriverInfo;
import com.ctrip.frt.product.soa.DriverBasicInfoType;
import com.google.gson.annotations.Expose;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;

/**
 * <AUTHOR>
 * 司机钱包
 * */
public class FinanceDriverObjectImpl extends FinanceDriverObject {
  private final DomainBeanFactory domainBeanFactory;
  private final CheckVerificationExecutor checkVerificationExecutor;
  private final SendVerificationExecutor sendVerificationExecutor;

  /**
   * 新司机
   * */
  public FinanceDriverObjectImpl(long driverId, String sourceType, DomainBeanFactory domainBeanFactory){
    this.domainBeanFactory = domainBeanFactory;
    this.validResult = FinanceResultEnum.DRIVER_NOT_EXIST.getCode();
    this.isRegulation = true;

    checkVerificationExecutor = new CheckVerificationExecutor(this, this.domainBeanFactory);
    sendVerificationExecutor = new SendVerificationExecutor(this, this.domainBeanFactory);

    if(StringUtils.isNotBlank(sourceType) && AccountSouceEnum.isDriverGuide(sourceType)) {
      //获取司导信息
      DriverBasicInfoType driverInfo = domainBeanFactory.tourService().getDriverInfo(driverId);
      if (driverInfo == null) {
        return;
      }
      this.driverId = ObjectUtils.defaultIfNull(driverInfo.getDriverId(), 0L);
      this.driverPhone = ObjectUtils.defaultIfNull(driverInfo.getDrvPhone(), Strings.EMPTY);
      this.driverName = ObjectUtils.defaultIfNull(driverInfo.getDrvName(), Strings.EMPTY);
      this.identityCardId = Strings.EMPTY;
      this.validResult = this.getStatusValidResult(ObjectUtils.defaultIfNull(driverInfo.getDrvStatus(), 0));
      return;
    }

    DriverInfo driverInfo = domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverId);
    if(driverInfo == null){
      return;
    }
    this.driverId = ObjectUtils.defaultIfNull(driverInfo.driverId, 0L);
    this.driverPhone = ObjectUtils.defaultIfNull(driverInfo.driverPhone, Strings.EMPTY);
    this.driverName = ObjectUtils.defaultIfNull(driverInfo.driverName, Strings.EMPTY);
    this.identityCardId = ObjectUtils.defaultIfNull(driverInfo.drvIdcard, Strings.EMPTY);
    this.validResult = this.getStatusValidResult(ObjectUtils.defaultIfNull(driverInfo.status, 0));
  }

  /**
   * 老司机提现流程
   * */
  public FinanceDriverObjectImpl(String driverPhone, DomainBeanFactory domainBeanFactory){
    this.domainBeanFactory = domainBeanFactory;
    this.validResult = FinanceResultEnum.DRIVER_NOT_EXIST.getCode();
    this.isRegulation = true;

    checkVerificationExecutor = new CheckVerificationExecutor(this, this.domainBeanFactory);
    sendVerificationExecutor = new SendVerificationExecutor(this, this.domainBeanFactory);

    OldDriverInfo driverInfo = domainBeanFactory.tmsTransportServiceProxy().queryOldDriver(driverPhone);
    if(driverInfo == null){
      return;
    }
    this.driverId = ObjectUtils.defaultIfNull(driverInfo.drvId, 0L);
    this.driverPhone = ObjectUtils.defaultIfNull(driverInfo.drvPhone, Strings.EMPTY);
    this.driverName = ObjectUtils.defaultIfNull(driverInfo.drvName, Strings.EMPTY);
    this.identityCardId = ObjectUtils.defaultIfNull(driverInfo.drvIdcard, Strings.EMPTY);
    this.isRegulation = ObjectUtils.defaultIfNull(driverInfo.regulationType, 0) == 1;
    this.validResult = FinanceResultEnum.OK.getCode();
  }

  /**
   * 司机司导状态转换
   * */
  private String getStatusValidResult(int driverStatus) {
    String validResult = Strings.EMPTY;
    switch (driverStatus) {
      case 0:     //未激活
        validResult = FinanceResultEnum.DRIVER_STATUS_UNACTIVE.getCode();
        break;
      case 3:     //已下线
        validResult = FinanceResultEnum.DRIVER_STATUS_CHECKFAIL.getCode();
        break;
      default:
        validResult = FinanceResultEnum.OK.getCode();
        break;
    }
    return validResult;
  }

  @Expose
  private String validResult;
  @Expose
  private long driverId;
  @Expose
  private String driverPhone;
  @Expose
  private String driverName;
  @Expose
  private String identityCardId;
  @Expose
  private boolean isRegulation;

  @Override
  public String validResult() {
    return this.validResult;
  }

  /***/
  @Override
  public long driverId() {
    return this.driverId;
  }

  /***/
  @Override
  public String driverPhone(){
    return this.driverPhone;
  }

  /***/
  @Override
  public String driverName(){
    return this.driverName;
  }

  /***/
  @Override
  public String identityCardId(){
    return this.identityCardId;
  }

  @Override
  public boolean isRegulation(){
    return this.isRegulation;
  }

  @Override
  public String sendFinanceVerification() {
    return sendVerificationExecutor.doWork(this.driverPhone);
  }

  @Override
  public String checkFinanceVerification(String code) {
    PhoneAndVerifyCodeModel phoneAndVerifyCodeModel = new PhoneAndVerifyCodeModel();
    phoneAndVerifyCodeModel.setCode(code);
    phoneAndVerifyCodeModel.setDriverPhone(this.driverPhone);
    return checkVerificationExecutor.doWork(phoneAndVerifyCodeModel);
  }
}
