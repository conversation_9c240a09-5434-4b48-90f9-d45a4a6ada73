package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.condition.UnbindBankCardCondition;
import com.ctrip.dcs.driver.account.application.convert.UnbindAccountBankCardConvert;
import com.ctrip.dcs.driver.account.application.service.AccountBankCardService;
import com.ctrip.dcs.driver.domain.account.UnbindAccountBankCardRequestType;
import com.ctrip.dcs.driver.domain.account.UnbindAccountBankCardResponseType;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class UnbindAccountBankCardEngine extends ShoppingExecutor<UnbindAccountBankCardRequestType, UnbindAccountBankCardResponseType> implements Validator<UnbindAccountBankCardRequestType> {

    @Autowired
    AccountBankCardService accountBankCardService;
    @Autowired
    UnbindAccountBankCardConvert accountBankCardConvert;

    @Override
    public UnbindAccountBankCardResponseType execute(UnbindAccountBankCardRequestType request) {
        UnbindAccountBankCardResponseType resp = new UnbindAccountBankCardResponseType();
        UnbindBankCardCondition bankCardCondition=accountBankCardConvert.convertCondition(request);
        BaseResult<Void> bindBankCardResult = accountBankCardService.unbindBankCard(bankCardCondition);
        if(!bindBankCardResult.isSuccess()){
            return ServiceResponseUtils.fail(resp,bindBankCardResult.getCode(),bindBankCardResult.getMessage());
        }else {
            return ServiceResponseUtils.success(resp);
        }
    }

    @Override
    public void validate(AbstractValidator<UnbindAccountBankCardRequestType> validator) {
        validator.ruleFor("uid").notNull().notEmpty();
        validator.ruleFor("bankCardId").notNull().notEmpty();
    }
}
