package com.ctrip.dcs.driver.account.application.cache;
/*
作者：pl.yang
创建时间：2025/2/25-上午11:13-2025
*/


import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LRUCache;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountUDLCache
 * @Package com.ctrip.dcs.driver.account.application.cache
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/2/25 上午11:13
 */

@Component
public class AccountUDLCache {

    LRUCache<String, AccountUDLDo> sourceId_cache;
    LRUCache<String, AccountUDLDo> uid_cache;
    @Autowired
    DriverAccountConfig driverAccountConfig;

    @PostConstruct
    public void initCache() {
        //todo 需要评估下 司机的量是多少呢，并且占用的内存是多少
        //todo 评估下 是否需要做二级缓存 加一个redis ，避免数据雪蹦了 机器太多 打过去的qps太大
        sourceId_cache = CacheUtil.newLRUCache(driverAccountConfig.getUdLCacheMaxCount(),driverAccountConfig.getUdLCacheTimeOut()*1000/*自动过期时间 一天*/);
        uid_cache = CacheUtil.newLRUCache(driverAccountConfig.getUdLCacheMaxCount(), driverAccountConfig.getUdLCacheTimeOut()*1000/*自动过期时间 一天*/);

    }


    public synchronized void saveAccountUDLBySourceId(List<AccountUDLDo> accountInfoDTOS) {
        if (CollectionUtils.isEmpty(accountInfoDTOS)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(accountInfoDTOS)) {
            for (AccountUDLDo accountInfoDTO : accountInfoDTOS) {
                sourceId_cache.put(accountInfoDTO.getSourceId(), accountInfoDTO);
            }
        }
    }

    public synchronized List<AccountUDLDo> getAccountUDLBySourceId(List<String> sourceIdList) {
        if (CollectionUtils.isEmpty(sourceIdList)) {
            return Lists.newArrayList();
        }
        return sourceIdList.stream().map(o -> sourceId_cache.get(o,false/*不自动续期*/)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<AccountUDLDo> getAccountUDLByUid(List<String> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Lists.newArrayList();
        }
        return uids.stream().map(o -> uid_cache.get(o,false/*不自动续期*/)).filter(Objects::nonNull).collect(Collectors.toList());
    }
    public synchronized void saveAccountUDLByUid(List<AccountUDLDo> accountInfoDTOS) {
        if (CollectionUtils.isEmpty(accountInfoDTOS)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(accountInfoDTOS)) {
            for (AccountUDLDo accountInfoDTO : accountInfoDTOS) {
                uid_cache.put(accountInfoDTO.getUid(), accountInfoDTO);
            }
        }
    }
}
