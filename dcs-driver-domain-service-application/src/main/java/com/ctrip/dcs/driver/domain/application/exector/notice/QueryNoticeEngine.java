package com.ctrip.dcs.driver.domain.application.exector.notice;

import com.ctrip.dcs.driver.domain.application.service.NoticeService;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeUserModel;
import com.ctrip.dcs.driver.domain.notice.NoticeRecordDTO;
import com.ctrip.dcs.driver.domain.notice.NoticeUserDTO;
import com.ctrip.dcs.driver.domain.notice.QueryNoticeRequestType;
import com.ctrip.dcs.driver.domain.notice.QueryNoticeResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by <AUTHOR> on 2023/3/5 20:01
 */
@Component
public class QueryNoticeEngine extends ShoppingExecutor<QueryNoticeRequestType, QueryNoticeResponseType>
    implements Validator<QueryNoticeRequestType> {

  @Autowired
  private NoticeService noticeService;

  @Override
  public QueryNoticeResponseType execute(QueryNoticeRequestType requestType) {
    QueryNoticeResponseType responseType = new QueryNoticeResponseType();
    NoticeModel noticeModel = noticeService.queryNotice(convertUser(requestType.getUser()));
    if (noticeModel != null) {
      NoticeRecordDTO recordDTO = new NoticeRecordDTO();
      if (noticeModel.getExist()) {
        recordDTO.exist = true;
        recordDTO.id = noticeModel.getId();
        recordDTO.title = noticeModel.getTitle();
        recordDTO.content = noticeModel.getContent();
        recordDTO.status = noticeModel.getStatus();
        recordDTO.uri = noticeModel.getUri();
        recordDTO.startDate = noticeModel.getStartDate();
        recordDTO.endDate = noticeModel.getEndDate();
      } else {
        recordDTO.exist = false;
      }
      responseType.setNotice(recordDTO);
      return ServiceResponseUtils.success(responseType);
    } else {
      return ServiceResponseUtils.fail(responseType);
    }
  }

  @Override
  public void validate(AbstractValidator<QueryNoticeRequestType> validator) {
    validator.ruleFor("user").notNull();
  }

  private NoticeUserModel convertUser(NoticeUserDTO userDTO) {
    return NoticeUserModel.newBuilder()
        .withUserType(userDTO.getUserType())
        .withUserId(userDTO.getUserId())
        .withCityId(userDTO.getCityId())
        .withProductType(userDTO.getProductType())
        .withVehicleType(userDTO.getVehicleType())
        .withStatus(userDTO.getStatus()).build();
  }
}
