package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.util.List;

@Component
public class AccountBaseInfoCheckSchedule {
    Logger log = LoggerFactory.getLogger(AccountBaseInfoCheckSchedule.class);

    @Autowired
    private AccountBaseInfoDao accountBaseInfoDao;
    @Autowired
    private UserCenterAccountGateway userCenterAccountGateway;
    @Autowired
    private DirectorRedis directorRedis;
    @Autowired
    private DriverAccountConfig driverAccountConfig;

    private static final String PROGRESS_KEY = "dcs.driver.account.base.info.check.job.progress";

    private static final long PROCESS_KEY_EXPIRE_SECONDS = 30 * 24 * 3600;

    @QSchedule("dcs.driver.account.base.info.check.job")
    public void onExecute(Parameter parameter) {

        // 指定uid
        String uids = parameter.getString("uidList");
        if (StringUtils.isNotBlank(uids)) {
            log.info("check account base info start", "uids : " + uids);
            List<String> uidList = Lists.newArrayList(uids.split(","));
            if (CollectionUtils.isNotEmpty(uidList)) {
                List<AccountBaseInfoPO> accountBaseInfoPOList = accountBaseInfoDao.batchQueryByUID(uidList);
                batchCheck(accountBaseInfoPOList);
            }
            return;
        }

        Long startId = getStartId(parameter);
        TaskMonitor taskMonitor = TaskHolder.getKeeper();

        log.info("check account base info start", "start id : " + startId);
        while (true) {
            if (taskMonitor.isStopped()) {
                log.error("check account base info job stop", "current id : " + startId);
                break;
            }
            log.info("check account base info", "current id : " + startId);
            List<AccountBaseInfoPO> accountBaseInfoPOList = accountBaseInfoDao.batchQueryByPage(startId, 100);
            if (CollectionUtils.isEmpty(accountBaseInfoPOList)) {
                break;
            }
            batchCheck(accountBaseInfoPOList);
            startId = accountBaseInfoPOList.get(accountBaseInfoPOList.size() - 1).getId();
            directorRedis.set(PROGRESS_KEY, startId, PROCESS_KEY_EXPIRE_SECONDS);
        }
        log.info("check account base info finish", "last id : " + startId);
    }

    private Long getStartId(Parameter parameter) {
        String startId = parameter.getString("startId");
        if (StringUtils.isNotBlank(startId)) {
            log.info("check account base info get start id from param", startId);
            return Long.valueOf(startId);
        }
        String redisStartId = directorRedis.get(PROGRESS_KEY);
        if (StringUtils.isNotBlank(redisStartId)) {
            log.info("check account base info get start id from redis", redisStartId);
            return Long.valueOf(redisStartId);
        }
        log.info("check account base info get start id from 0", "0");
        return 0L;
    }

    protected void batchCheck(List<AccountBaseInfoPO> accountBaseInfoPOList) {
        for (AccountBaseInfoPO accountBaseInfoPO : accountBaseInfoPOList) {
            AccountInfoResponseType account = userCenterAccountGateway.getAccountByUid(accountBaseInfoPO.getUid());
            if (account == null) {
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "NoAccount");
                log.warn("check account base info no account", accountBaseInfoPO.getUid());
                continue;
            }
            if (ObjectUtils.notEqual(accountBaseInfoPO.getCountryCode(), account.getCountryCode())) {
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "CountryCodeConflict");
                log.warn("check account base info country code conflict", String.format("uid : %s, table : %s, account : %s",
                        accountBaseInfoPO.getUid(), accountBaseInfoPO.getCountryCode(), account.getCountryCode()));
                // continue;
            }
            if (ObjectUtils.notEqual(accountBaseInfoPO.getPhoneNumber(), account.getPhoneNumber())) {
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "PhoneCodeConflict");
                log.warn("check account base info country phone conflict", String.format("uid : %s, table : %s, account : %s",
                        accountBaseInfoPO.getUid(), accountBaseInfoPO.getPhoneNumber(), account.getPhoneNumber()));
                // continue;
            }
            if (StringUtils.isNotBlank(accountBaseInfoPO.getEmail()) && ObjectUtils.notEqual(accountBaseInfoPO.getEmail(), account.getEmail())) {
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "EmailConflict");
                log.warn("check account base info country email conflict", String.format("uid : %s, table : %s, account : %s",
                        accountBaseInfoPO.getUid(), accountBaseInfoPO.getEmail(), account.getEmail()));
                // continue;
            }
            Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "Finish");
            if (driverAccountConfig.getJobSleepMills() > 0) {
                try {
                    Thread.sleep(driverAccountConfig.getJobSleepMills());
                } catch (Exception e) {
                    log.warn("sleep exception", e);
                }
            }

        }
    }

}
