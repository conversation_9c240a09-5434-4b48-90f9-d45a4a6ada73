package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.common.CommonStringUtils;
import com.ctrip.dcs.driver.domain.application.redis.HonourRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.HonourDBDataService;
import com.ctrip.dcs.driver.domain.application.service.MedalInfoService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.tourai.TourAIOneServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.constant.HonourEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.DaasGateway;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.impl.DaasGatewayImpl;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聚合勋章有关的查询、更新
 * */
@Component
public class MedalInfoServiceImpl implements MedalInfoService {

    @Autowired
    private HonourRedisLogic honourRedisLogic;

    @Autowired
    private HonourDBDataService honourDBDataService;

    @Autowired
    TourAIOneServiceProxy tourAIOneServiceProxy;

    @Autowired
    private DaasGateway daasGateway;

    @Override
    public List<MedalBasicInfoModel> queryBasicMedalInfo() {
        List<MedalBasicInfoModel> medalBasicInfoModels = new ArrayList<>();
        List<DimPrdTrhDriverMedalInfoPO> medalInfoList = honourDBDataService.queryBasicMedalInfo();
        if(CollectionUtils.isNotEmpty(medalInfoList)) {
            List<DimPrdTrhDriverMedalInfoPO> sortedMedalInfoList = medalInfoList.stream()
             .filter(m -> StringUtils.isNotBlank(m.getMedalType()) && m.getMedalGrade() > 0)
             .sorted(Comparator.comparing(DimPrdTrhDriverMedalInfoPO::getMedalType)
                .thenComparing(DimPrdTrhDriverMedalInfoPO::getMedalGrade))
             .collect(Collectors.toList());

            if(CollectionUtils.isNotEmpty(sortedMedalInfoList)) {
                sortedMedalInfoList.forEach(m -> {
                    HonourEnum.MedalTypeEnum medalTypeEnum = HonourEnum.mathMedalType(m.getMedalType());
                    if (medalTypeEnum != null) {
                        MedalBasicInfoModel basicInfoModel = new MedalBasicInfoModel();
                        basicInfoModel.setType(medalTypeEnum);
                        basicInfoModel.setMedalCode(HonourEnum.bulidMedalCode(medalTypeEnum, m.getMedalGrade()));
                        basicInfoModel.setBimedalType(m.getMedalType());
                        basicInfoModel.setBimedalGrade(m.getMedalGrade());
                        basicInfoModel.setMedalGradeMin(m.getMedalGradeMin());
                        basicInfoModel.setMedalGradeMax(m.getMedalGradeMax());
                        medalBasicInfoModels.add(basicInfoModel);
                    }
                });
            }
        }

        return medalBasicInfoModels;
    }

    @Override
    public void updateMedalInfo() {
        List<MedalBasicInfoModel> medalBasicInfoModels = this.queryBasicMedalInfo();
        honourRedisLogic.saveCurrentMedalBasicInfo(medalBasicInfoModels);
    }

    @Override
    public GoldMedalDriverModel getGoldMedalDriverInfo(long driverId) {
        GoldMedalDriverModel goldMedalDriverModel = new GoldMedalDriverModel();
        goldMedalDriverModel.setType(CommonStringUtils.GOLD_MEDAL);
        goldMedalDriverModel.setLight(false);
        goldMedalDriverModel.setTotalCount(0);
        goldMedalDriverModel.setMonthTime(Strings.EMPTY);

        try{
            DaasGatewayImpl.GoldMedalDriverEntity goldMedalDriverEntity = daasGateway.queryGoldMedalDriverInfo(driverId + "");
            if (Objects.nonNull(goldMedalDriverEntity) && String.valueOf(driverId).equals(goldMedalDriverEntity.getDriverId())) {
                goldMedalDriverModel.setLight(Optional.of(goldMedalDriverEntity.getGoodDriver()).orElse(0) == 1);
                goldMedalDriverModel.setTotalCount(Optional.of(goldMedalDriverEntity.getTotalGoodDriverCnt()).orElse(0));
            }
        } catch (Exception e){
            return goldMedalDriverModel;
        }
        return goldMedalDriverModel;
    }

    private final static String GOLD_MEDAL_API_NAME = "getAdmSevTrhHighDrvSelectTIsGood";
    private final static String GOLD_MEDAL_TOKEN = "TIdugGjC-659-1703231473449";
    private final static String GOLD_MEDAL_DRIVER_ID_LIST = "driver_id_last";

    private static class AdmSeviceDriverInfo implements Serializable {
        private long driver_id_last;
        private String use_month_bj;
        private int is_good_driver;
        private int total_good_driver_cnt;

        public long getDriver_id_last() {
            return driver_id_last;
        }

        public void setDriver_id_last(long driver_id_last) {
            this.driver_id_last = driver_id_last;
        }

        public String getUse_month_bj() {
            return use_month_bj;
        }

        public void setUse_month_bj(String use_month_bj) {
            this.use_month_bj = use_month_bj;
        }

        public int getIs_good_driver() {
            return is_good_driver;
        }

        public void setIs_good_driver(int is_good_driver) {
            this.is_good_driver = is_good_driver;
        }

        public int getTotal_good_driver_cnt() {
            return total_good_driver_cnt;
        }

        public void setTotal_good_driver_cnt(int total_good_driver_cnt) {
            this.total_good_driver_cnt = total_good_driver_cnt;
        }
    }
}
