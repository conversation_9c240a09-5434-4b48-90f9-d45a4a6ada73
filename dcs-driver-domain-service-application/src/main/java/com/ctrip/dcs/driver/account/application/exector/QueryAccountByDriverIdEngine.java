package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByDriverIdResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTag(tagKeys= {"driverId"})
public class QueryAccountByDriverIdEngine extends ShoppingExecutor<QueryAccountByDriverIdRequestType, QueryAccountByDriverIdResponseType> implements Validator<QueryAccountByDriverIdRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
        public QueryAccountByDriverIdResponseType execute(QueryAccountByDriverIdRequestType request) {


        QueryAccountByDriverIdResponseType resp = new QueryAccountByDriverIdResponseType();

        AccountInfoDTO account = accountService.getAccountByDriverId(request.getDriverId());
        if (account != null) {
            resp.setAccountDetail(AccountDetailConvert.convert(account));
        }
        Cat.logEvent(CatEventType.QUERY_BY_DRIVER_ID_RESULT, account != null ? "1" : "0");
        DriverMetricUtil.metricUdlIsValid(resp.getAccountDetail());
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public Boolean isEmptyResult(QueryAccountByDriverIdResponseType resp) {
        return !(resp!=null&&resp.getAccountDetail()!=null&&resp.getAccountDetail().getUid()!=null);
    }
}
