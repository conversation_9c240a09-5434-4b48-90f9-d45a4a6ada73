package com.ctrip.dcs.driver.domain.application.exector.gray;

import com.ctrip.dcs.driver.domain.application.helper.DriverDeviceManager;
import com.ctrip.dcs.driver.domain.application.service.DriverBizGrayService;
import com.ctrip.dcs.driver.domain.gray.QueryDriverBizGraySwitchRequestType;
import com.ctrip.dcs.driver.domain.gray.QueryDriverBizGraySwitchResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class QueryDriverBizGraySwitchEngine extends ShoppingExecutor<QueryDriverBizGraySwitchRequestType, QueryDriverBizGraySwitchResponseType> implements Validator<QueryDriverBizGraySwitchRequestType> {

    private static final String BIZ_KEY_VOIP = "voip";
    @Autowired
    private DriverBizGrayService driverBizGrayService;

    @Autowired
    private DriverDeviceManager driverDeviceManager;

    @Override
    public void validate(AbstractValidator<QueryDriverBizGraySwitchRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("bizKey").notNull().notEmpty();
    }

    @Override
    public QueryDriverBizGraySwitchResponseType execute(QueryDriverBizGraySwitchRequestType request) {
        QueryDriverBizGraySwitchResponseType resp = new QueryDriverBizGraySwitchResponseType();

        boolean enableBizGraySwitch ;
        if (BIZ_KEY_VOIP.equals(request.getBizKey())
                && driverDeviceManager.fromUserRequest()
                && driverDeviceManager.driverDeviceActiveCheckForVoip()) {
            if (driverDeviceManager.activeForVoip(request.getDriverId())) {
                enableBizGraySwitch = driverBizGrayService.queryBizGraySwitch(request.getBizKey(),
                        request.getDriverId(),
                        request.getProductLine(),
                        request.getCityId(),
                        request.getAppVer(),
                        request.getRnVer(),
                        request.isIgnoreVersion());
                resp.setResult(enableBizGraySwitch);
                return ServiceResponseUtils.success(resp);
            }
            resp.setResult(Boolean.FALSE);
            return ServiceResponseUtils.success(resp);
        }

        enableBizGraySwitch = driverBizGrayService.queryBizGraySwitch(request.getBizKey(),
                request.getDriverId(),
                request.getProductLine(),
                request.getCityId(),
                request.getAppVer(),
                request.getRnVer(),
                request.isIgnoreVersion());
        resp.setResult(enableBizGraySwitch);
        return ServiceResponseUtils.success(resp);
    }

}
