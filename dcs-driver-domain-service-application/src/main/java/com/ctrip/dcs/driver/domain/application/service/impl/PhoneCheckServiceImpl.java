package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.basebiz.callcenter.splitservice.contract.EnumCallDirection;
import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.DialRecordTask;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskRequestType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskResponseType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitTaskStatus;
import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig;
import com.ctrip.dcs.driver.account.domain.constant.PhoneCheckState;
import com.ctrip.dcs.driver.account.domain.constant.PhoneCheckTaskState;
import com.ctrip.dcs.driver.account.domain.enums.LoginTypeEnum;
import com.ctrip.dcs.driver.account.domain.enums.PhoneCheckTaskType;
import com.ctrip.dcs.driver.account.domain.value.PhoneCheckVoiceCallTask;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.PhoneNumberServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.VoiceCallServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory;
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService;
import com.ctrip.dcs.driver.domain.login.DriverLoginSuccessMessage;
import com.ctrip.dcs.driver.trip.university.infrastructure.utils.CoreInfoUtil;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.platform.dal.dao.annotation.DalTransactional;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Types;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/11 10:47
 * @Description:
 */
@Service
public class PhoneCheckServiceImpl implements PhoneCheckService, ApplicationContextAware {
    Log log = Log.getInstance(PhoneCheckServiceImpl.class);

    private static final String IVR_FLOW_KEY = "IvrFlow";
    private static final String IVR_TTS_KEY = "TTS1";
    private static final String TTS_TEXT_SHARK_KEY = "driver.voiceCode.tts.textContent";
    static final String SUCCESS_CODE = "0";

    static final String CN = "CN";

    @Resource
    private PhoneCheckTaskTable phoneCheckTaskTable;
    @Resource
    private VoiceCallServiceProxy voiceCallServiceProxy;
    @Resource
    private PhoneCheckConfig phoneCheckConfig;
    @Resource
    PhoneCheckTable checkTable;
    @Resource
    private PhoneCheckTaskFactory phoneCheckTaskFactory;
    @Resource
    private CityRepository cityRepository;
    @Resource
    private PhoneCheckTable phoneCheckTable;
    @Resource
    private PhoneCheckConfig config;
    @Resource
    private PhoneNumberServiceProxy phoneNumberService;
    @Resource
    private DriverTable driverTable;
    @Resource
    private CommonMultipleLanguages commonMultipleLanguages;

    @Resource
    ArchCoreInfoServiceImpl archCoreInfoService;

    private ApplicationContext context;

    @Override
    public boolean ivrCall(PhoneCheckTaskEntity task) {
        PhoneCheckVoiceCallTask content = JsonUtil.fromString(task.getTaskContent(), PhoneCheckVoiceCallTask.class);
        SubmitOutboundTaskRequestType request = new SubmitOutboundTaskRequestType();
        request.setReqUuid(UUID.randomUUID().toString());
        request.setBatchID(String.valueOf(task.getId()));
        request.setMaxRetryCount(phoneCheckConfig.getVoiceMaxRetryCount());
        request.setRetryInterval(phoneCheckConfig.getVoiceRetryIntervalSeconds());
        request.setMaxAlertTime(phoneCheckConfig.getVoiceMaxAlertSeconds());
        request.setMaxConnectTime(phoneCheckConfig.getVoiceMaxDurationSeconds());
        DialRecordTask call = new DialRecordTask();
        call.setRecordGUID(UUID.randomUUID().toString());
        call.setDnis("00" + task.getPhonePrefix() + archCoreInfoService.decryptByType(KeyType.Phone, task.getPhoneNumber()));
        call.setSkillGroup(phoneCheckConfig.getVoiceSkillGroupId());

        String language = content.getDriverLanguages().get(0);
        String flow = phoneCheckConfig.getVoiceSkillFlowId(language);
        String text = getTtsSharkValue(language);

        if (StringUtils.isBlank(flow) || StringUtils.isBlank(text)) {
            task.setExecuteTime(LocalDateTime.now());
            task.setFinishTime(LocalDateTime.now());
            task.setTaskState(PhoneCheckTaskState.Failed);
            task.setTaskResult("Not Support Language");
            Cat.logEvent(CatEventType.VOICE_CODE_CALL_NOT_MATCH_LOCAL, language, "1", StringUtils.EMPTY);
            phoneCheckTaskTable.update(task);
            return false;
        }
        call.setContent(JsonUtil.toString(ImmutableMap.of(IVR_FLOW_KEY, flow, IVR_TTS_KEY, generateSpeechText(text, content.getVerificationCode()))));
        request.setTaskList(Lists.newArrayList(call));
        SubmitOutboundTaskResponseType response = voiceCallServiceProxy.submitOutboundTask(request);
        String code = Optional.ofNullable(response).map(SubmitOutboundTaskResponseType::getTaskListStatus).orElse(Collections.emptyList())
                .stream()
                .findFirst()
                .map(SubmitTaskStatus::getResultCode)
                .orElse(null);
        task.setTaskResult(JsonUtil.toString(response));
        task.setExecuteTime(LocalDateTime.now());

        Cat.logEvent(CatEventType.IVR_VOICE_CODE_CALL_RES, code);
        if (StringUtils.equalsIgnoreCase(code, SUCCESS_CODE) ) {
            task.setTaskState(PhoneCheckTaskState.Started);
        } else {
            task.setFinishTime(LocalDateTime.now());
            task.setTaskState(PhoneCheckTaskState.Failed);
        }
        phoneCheckTaskTable.update(task);
        return StringUtils.equalsIgnoreCase(code, SUCCESS_CODE);
    }

    public String getTtsSharkValue(String language) {
        String value = commonMultipleLanguages.getContent(TTS_TEXT_SHARK_KEY, language);
        if (StringUtils.isNotBlank(value)) {
            return value;
        }
        return phoneCheckConfig.getVoiceSpeechTemplate(language);
    }

    // 生产TTS文本
    protected String generateSpeechText(String template, String verificationCode) {
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < verificationCode.length(); i++) {
            builder.append(verificationCode.charAt(i));
            if (i < verificationCode.length() - 1) {
                builder.append(",");
            }
        }
        return StringUtils.replace(template, "{verificationCode}", builder.toString());
    }

    @Override
    public void generatePhoneCheckTaskByDriver(DriverEntity driver, LocalDateTime planTime) {
        //extra from PhoneCheckTaskGenerateScheduler
        log.info("StartGenerateTask", "DriverId={}", driver.getDriverId());
        try {
            if (cityRepository.findOne(driver.getCityId()).isChineseMainland()) {
                Cat.logEvent("generatePhoneCheckTaskByDriver.Validate", "ChineseMainland");
                log.warn("ValidateChineseMainland", "DriverId={}", driver.getDriverId());
                return;
            }
            if (validateRepeatCheck(driver.getDriverId(), driver.getPhonePrefix(), driver.getPhoneNumber())) {
                Cat.logEvent("generatePhoneCheckTaskByDriver.Validate", "RepeatCheck");
                log.warn("ValidateRepeatCheck", "DriverId={}", driver.getDriverId());
                return;
            }
            if (validateRepeatTask(driver.getDriverId(), driver.getPhonePrefix(), driver.getPhoneNumber())) {
                Cat.logEvent("generatePhoneCheckTaskByDriver.Validate", "RepeatTask");
                log.warn("ValidateRepeatTask", "DriverId={}", driver.getDriverId());
                return;
            }
            if (!validatePhoneNumber("+" + driver.getPhonePrefix() + CoreInfoUtil.decrypt(driver.getPhoneNumber(), KeyType.Phone))) {
                Cat.logEvent("generatePhoneCheckTaskByDriver.Validate", "PhoneNumber");
                log.warn("ValidatePhoneNumber", "DriverId={}", driver.getDriverId());
                return;
            }

            PhoneCheckTaskEntity task = phoneCheckTaskFactory.apply(driver, PhoneCheckTaskType.IVR_CALL,planTime);
            phoneCheckTaskTable.insert(task);
            log.info("DoGenerateTask", JsonUtil.toString(task));
        } finally {
            log.info("EndGenerateTask", "DriverId={}", driver.getDriverId());
        }
    }

    protected boolean validateRepeatCheck(Long driverId, String phonePrefix, String phoneNumber) {
        PhoneCheckEntity condition = PhoneCheckEntity.builder()
                .driverId(driverId)
                .phonePrefix(phonePrefix)
                .phoneNumber(phoneNumber)
                .checkState(PhoneCheckState.Succeed)
                .build();
        return phoneCheckTable.count(condition) > 0;
    }

    @SneakyThrows
    protected boolean validateRepeatTask(Long driverId, String phonePrefix, String phoneNumber) {
        SelectSqlBuilder sql = new SelectSqlBuilder().selectCount();
        sql.and().equal("driver_id", driverId, Types.BIGINT);
        sql.and().equal("phone_prefix", phonePrefix, Types.VARCHAR);
        sql.and().equal("phone_number", phoneNumber, Types.VARCHAR);
        sql.and().equal("task_type", 3, Types.INTEGER);
        sql.and().greaterThan("submit_time", LocalDateTime.now().minusDays(config.getRepeatTaskThresholdDays()), Types.TIMESTAMP);
        return phoneCheckTaskTable.count(sql) > 0;
    }
    protected boolean validatePhoneNumber(String phoneNumber) {
        SplitNumberRequestType request = new SplitNumberRequestType();
        request.setNumber(phoneNumber);
        request.setDirection(EnumCallDirection.INBOUND);
        request.setRegionCode("CN");
        SplitNumberResponseType response = phoneNumberService.splitNumber(request);
        return Optional.ofNullable(response)
                .map(SplitNumberResponseType::getResult)
                .map(NumberDTO::isValid)
                .orElse(false);
    }

    @Override
    public void updateDriverPhoneCheckRes(DriverLoginSuccessMessage loginMsg) {
        LoginTypeEnum loginType = LoginTypeEnum.findByCode(loginMsg.getLoginType());
        if (Objects.isNull(loginType)) {
            return;
        }
        DriverEntity driver = driverTable.queryByPk(loginMsg.getDriverId());
        if (Objects.isNull(driver)) {
            log.warn("updateDriverPhoneCheckRes", "DriverNotExist={}", loginMsg.getDriverId());
            return;
        }
        if (cityRepository.findOne(driver.getCityId()).isChineseMainland()) {
            log.warn("updateDriverPhoneCheckRes", "DriverId={}", driver.getDriverId());
            return;
        }
        PhoneCheckEntity check = checkTable.queryOne(PhoneCheckEntity.builder().driverId(loginMsg.getDriverId()).build());
        PhoneCheckTaskEntity sample = PhoneCheckTaskEntity.builder().driverId(loginMsg.getDriverId())
                .phoneNumber(loginMsg.getLoginAccount())
                .phonePrefix(loginMsg.getCountryCode())
                .taskType(PhoneCheckTaskType.IVR_CALL.getCode())
                .taskState(PhoneCheckTaskState.Created)
                .build();
        List<PhoneCheckTaskEntity> taskTableList = phoneCheckTaskTable.queryMany(sample);
        if (CollectionUtils.isNotEmpty(taskTableList)) {
            taskTableList.forEach(t -> {
                t.setTaskState(PhoneCheckTaskState.Canceled);
                t.setFinishTime(LocalDateTime.now());
                t.setTaskResult("finish by login use phoneCode");
            });
        }

        PhoneCheckTaskType checkTaskType = fetchPhoneCheckType(loginType);
        if (Objects.nonNull(check)) {
            check.setCheckType(checkTaskType.getCode());
            check.setCheckState(PhoneCheckState.Succeed);
        } else {
            check = new PhoneCheckEntity();
            check.setDriverId(loginMsg.getDriverId());
            check.setPhonePrefix(loginMsg.getCountryCode());
            check.setPhoneNumber(loginMsg.getLoginAccount());
            check.setCheckType(checkTaskType.getCode());
            check.setCheckState(PhoneCheckState.Succeed);
        }
        context.getBean(PhoneCheckService.class).doUpdatePhoneCheck(taskTableList, check);
    }

    @Override
    @DalTransactional(logicDbName = Constants.TMS_TRANSPORT_DBNAME)
    public void doUpdatePhoneCheck(List<PhoneCheckTaskEntity> taskTableList, PhoneCheckEntity check) {
        if (Objects.isNull(check.getId())) {
            checkTable.insert(check);
        } else {
            checkTable.update(check);
        }
        if (CollectionUtils.isNotEmpty(taskTableList)) {
            phoneCheckTaskTable.updateMany(taskTableList);
        }
    }

    private PhoneCheckTaskType fetchPhoneCheckType(LoginTypeEnum loginType) {
        switch (loginType) {
            case PHONE_VOICE:
                return PhoneCheckTaskType.VOICE;
            case PHONE_SMS:
                return PhoneCheckTaskType.SMS;
        }
        throw new BizException("loginType illegal");
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
}
