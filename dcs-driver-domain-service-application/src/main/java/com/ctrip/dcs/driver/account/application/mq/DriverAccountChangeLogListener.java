package com.ctrip.dcs.driver.account.application.mq;

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountChangeLogDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountChangeLogPO;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Optional;

@Component
public class DriverAccountChangeLogListener {
    Log log = Log.getInstance(DriverAccountChangeLogListener.class);

    @Autowired
    private AccountChangeLogDao accountChangeLogDao;
    @Autowired
    private AccountBaseInfoDao accountBaseInfoDao;
    @Autowired
    private UserCenterAccountGateway userCenterAccountGateway;

    @QmqConsumer(prefix = Constants.ACCOUNT_CHANGE_LOG_TOPIC, consumerGroup = "*********")
    public void onMessage(Message message) {
        String content = message.getStringProperty("content");
        if (StringUtils.isBlank(content)) {
            return;
        }
        AccountChangeLog accountChangeLog = JsonUtil.fromString(content, AccountChangeLog.class);
        AccountChangeLogPO accountChangeLogPO = new AccountChangeLogPO();
        accountChangeLogPO.setUid(accountChangeLog.getUid());
        accountChangeLogPO.setType(accountChangeLog.getType().name());
        accountChangeLogPO.setChangeBefore(accountChangeLog.getBefore());
        accountChangeLogPO.setChangeAfter(accountChangeLog.getAfter());
        accountChangeLogPO.setOperator(accountChangeLog.getOperator());
        accountChangeLogPO.setProviderDataLocation(getUdlByUid(accountChangeLogPO.getUid()));
        accountChangeLogDao.insert(accountChangeLogPO);
    }

    private String getUdlByUid(String uid) {
        try {
            AccountInfoResponseType accountInfo = userCenterAccountGateway.getAccountByUid(uid);
            if (accountInfo != null) {
                return accountInfo.getUdl();
            }
        } catch (Exception e) {
            log.warn("getUdlByUid from user center error", e);
        }
        return Optional.ofNullable(accountBaseInfoDao.queryByUID(uid)).map(AccountBaseInfoPO::getProviderDataLocation).orElse(null);
    }
}
