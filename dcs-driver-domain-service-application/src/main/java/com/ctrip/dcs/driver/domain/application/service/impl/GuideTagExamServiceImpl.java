package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exam.GuideExamObjectImpl;
import com.ctrip.dcs.driver.domain.application.service.GuideTagExamService;
import com.ctrip.dcs.driver.domain.exam.GuideExamScoreDTO;
import com.ctrip.dcs.driver.domain.exam.GuideTagExamDTO;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideExamScoreModel;
import com.ctrip.dcs.driver.value.exam.ApplyExamObject;
import com.ctrip.dcs.driver.value.exam.GuideExamObject;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class GuideTagExamServiceImpl implements GuideTagExamService {
  @Autowired
  DomainBeanFactory domainBeanFactory;

  @Override
  public GuideTagExamDTO queryAndSaveGuideTagExamDTO(long guideId){
    //获取到锁后再次查询缓存，如已构建完毕，则直接返回
    GuideTagExamDTO guideTagExamCache =
        domainBeanFactory.examRedisLogic().getGuideTagExam(guideId);
    if(guideTagExamCache != null){
      return guideTagExamCache;
    }
    return queryDBAndSaveCacheGuideTagExam(guideId);
  }

  @Override
  public GuideTagExamDTO queryDBAndSaveCacheGuideTagExam(long guideId) {
    String examAccountId = "guide" + String.format("%07d", guideId);
    GuideExamObject guideExamObject = new GuideExamObjectImpl(domainBeanFactory, examAccountId);
    //开始构建缓存
    List<ApplyExamObject> applyExamObjects = guideExamObject.queryPassedApplyExamRecords();
    if (CollectionUtils.isEmpty(applyExamObjects)) {
      GuideTagExamDTO guideTagExamDTO = buildUnpassedGuideTagExamDTO();
      saveGuideTagExamToCache(guideId,guideTagExamDTO);
      return guideTagExamDTO;
    }
    //查询构建已通过信息
    GuideTagExamDTO guideTagExamDTO  = null;
    List<GuideExamScoreDTO> passedExamDTOList = new ArrayList<>();
    Map<String, List<GuideExamScoreModel>> applySubjectGuideExamScoreMap = domainBeanFactory.guideExamScoreDBDataService()
        .queryPassedGuideExamScoreInfoMapByAccount(examAccountId);
    for (ApplyExamObject applyExamObject:applyExamObjects){
      List<GuideExamScoreModel> guideExamScoreModels =
          applySubjectGuideExamScoreMap.get(applyExamObject.applySubject());
      if(CollectionUtils.isEmpty(guideExamScoreModels)){
        CommonLogger.INSTANCE.warn("QueryGuideTagExamEngine",
            String.format("guide exam score failed, examAccountId:[%s], applySubject:[%s]", applyExamObject.examAccountId(), applyExamObject.applySubject()));
        guideTagExamDTO = buildUnpassedGuideTagExamDTO();
        saveGuideTagExamToCache(guideId,guideTagExamDTO);
        return guideTagExamDTO;
      }
      addGuideExamScoreDTO(applyExamObject, guideExamScoreModels, passedExamDTOList);
    }
    guideTagExamDTO = buildPassedGuideTagExamDTO(passedExamDTOList);
    saveGuideTagExamToCache(guideId,guideTagExamDTO);
    return guideTagExamDTO;
  }

  private void addGuideExamScoreDTO(ApplyExamObject applyExamObject,
      List<GuideExamScoreModel> guideExamScoreModels, List<GuideExamScoreDTO> passedExamDTOList) {
    GuideExamScoreDTO guideExamScoreDTO = new GuideExamScoreDTO();
    guideExamScoreDTO.setExamType(applyExamObject.parentApplySubject());
    guideExamScoreDTO.setScore(guideExamScoreModels.get(0).getExamScore());
    guideExamScoreDTO.setCompleteTime(guideExamScoreModels.get(0).getCompleteTime());
    passedExamDTOList.add(guideExamScoreDTO);
  }

  private GuideTagExamDTO buildPassedGuideTagExamDTO(List<GuideExamScoreDTO> passedExamDTOList) {
    GuideTagExamDTO guideTagExamDTO = new GuideTagExamDTO();
    guideTagExamDTO.setExamIsPass(true);
    guideTagExamDTO.setPassedExamList(passedExamDTOList);
    return guideTagExamDTO;
  }

  private GuideTagExamDTO buildUnpassedGuideTagExamDTO() {
    GuideTagExamDTO guideTagExamDTO = new GuideTagExamDTO();
    guideTagExamDTO.setExamIsPass(false);
    return guideTagExamDTO;
  }

  private void saveGuideTagExamToCache(long guideId,GuideTagExamDTO guideTagExamDTO){
    domainBeanFactory.examRedisLogic().saveGuidePassedExamInfo(guideId, JacksonUtil.serialize(guideTagExamDTO));
  }
}
