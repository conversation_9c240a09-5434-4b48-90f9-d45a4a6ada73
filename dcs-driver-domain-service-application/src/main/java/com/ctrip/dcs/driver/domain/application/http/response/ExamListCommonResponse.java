package com.ctrip.dcs.driver.domain.application.http.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ExamListCommonResponse<T> {
    private int code;

    private String msg;

    private List<T> data;

    private boolean success;
}
