package com.ctrip.dcs.driver.domain.application.domain.exam;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import com.ctrip.dcs.driver.value.exam.ApplyExamObject;
import com.ctrip.dcs.driver.value.exam.GuideExamObject;
import com.google.gson.annotations.Expose;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class GuideExamObjectImpl extends GuideExamObject {
  private final DomainBeanFactory domainBeanFactory;

  public GuideExamObjectImpl(DomainBeanFactory domainBeanFactory, String examAccountId) {
    this.domainBeanFactory = domainBeanFactory;
    this.examAccountId = examAccountId;
  }

  @Expose
  private String examAccountId;


  @Override
  public String examAccountId() {
    return this.examAccountId;
  }

  @Override
  public List<ApplyExamObject> queryApplyExamRecords() {
    List<GuideApplyExamModel> guideApplyExamModels =
        this.domainBeanFactory.guideApplyExamDBDataService()
            .queryGuideApplyExamByAccount(this.examAccountId);
    if (CollectionUtils.isEmpty(guideApplyExamModels)) {
      return Collections.emptyList();
    }
    return guideApplyExamModels.stream()
        .map(model -> new ApplyExamObjectImpl(this.domainBeanFactory, model))
        .collect(Collectors.toList());
  }

  @Override
  public List<ApplyExamObject> queryPassedApplyExamRecords(){
    List<GuideApplyExamModel> guideApplyExamModels =
        this.domainBeanFactory.guideApplyExamDBDataService()
            .queryPassedGuideApplyExamByAccount(this.examAccountId);
    if (CollectionUtils.isEmpty(guideApplyExamModels)) {
      return Collections.emptyList();
    }
    return guideApplyExamModels.stream()
        .map(model -> new ApplyExamObjectImpl(this.domainBeanFactory, model))
        .collect(Collectors.toList());
  }
}
