package com.ctrip.dcs.driver.domain.application.domain.honour;

import cn.hutool.core.util.IdcardUtil;
import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.value.honour.CommemorationObject;
import com.ctrip.dcs.driver.value.honour.DirectorObject;
import com.google.gson.annotations.Expose;
import org.owasp.csrfguard.util.Strings;

import java.time.LocalDate;
import java.time.Period;
import java.util.Objects;

/**
 * 纪念日
 * */
public final class CommemorationObjectImpl extends CommemorationObject {

  @Expose
  private boolean isBirthday;
  @Expose
  private boolean isActiveDay;
  @Expose
  private int activeDayCount;
  @Expose
  private int activeDayType;
  @Expose
  private String showName;
  @Expose
  private String showImg;

  public static final class Builder {
    private final DomainBeanFactory domainBeanFactory;
    private final long directorId;
    private final int directorType;
    private DirectorObject directorObject;

    public Builder(long directorId, int directorType, DomainBeanFactory domainBeanFactory) {
      this.domainBeanFactory = domainBeanFactory;
      this.directorId = directorId;
      this.directorType = directorType;
    }

    public CommemorationObject build() {
      CommemorationObjectImpl commemoration = new CommemorationObjectImpl();
      // form开关关闭
      if(!this.domainBeanFactory.honourFormConfig().isShowCommemoration()){
        return commemoration;
      }

      this.directorObject = new DirectorObjectImpl.Builder(directorId, directorType, domainBeanFactory).build();
      if(Objects.isNull(this.directorObject)){
        return commemoration;
      }
      //海外不提醒
      if(!this.directorObject.isSupportCommemoration()){
        return commemoration;
      }

      commemoration.isBirthday = this.isBirthday(this.directorObject.identityCardId());
      commemoration.showName = this.directorObject.name();
      commemoration.showImg = this.domainBeanFactory.honourImgConfig().getBirthdayImg();

      if(Objects.nonNull(this.directorObject.onlineDate()) && !LocalDate.MIN.equals(this.directorObject.onlineDate())) {
        // 30天、180天、1周年、2周年、3周年.........20周年
        Period period = Period.between(this.directorObject.onlineDate(), LocalDate.now());
        if(period.getYears() == 0) {
          long dayDiff = LocalDate.now().toEpochDay() - this.directorObject.onlineDate().toEpochDay();
          if(dayDiff == 30 || dayDiff == 180) {
            commemoration.isActiveDay = true;
            commemoration.showImg = this.domainBeanFactory.honourImgConfig().getActiveDayImg((int) dayDiff);
            commemoration.activeDayCount = (int) dayDiff;
            commemoration.activeDayType = 1;
          }
        } else if(period.getYears() > 0 && period.getMonths() == 0 && period.getDays() == 0){
          commemoration.isActiveDay = true;
          commemoration.showImg = this.domainBeanFactory.honourImgConfig().getActiveDayImg(period.getYears());
          commemoration.activeDayCount = period.getYears();
          commemoration.activeDayType = 2;
        }
      }

      return commemoration;
    }

    /**
     * 解密身份证&校验取得生日
     * 判断是否当前日同一天
     * */
    private boolean isBirthday(String strDecryptedIdCard) {
      try {
        String strIdCard = domainBeanFactory.archCoreInfoService().decryptIdCard(strDecryptedIdCard);
        if (!IdcardUtil.isValidCard(strIdCard)) {
          return false;
        }
        LocalDate birthday = IdcardUtil.getBirthDate(strIdCard).toLocalDateTime().toLocalDate();

        Period period = Period.between(birthday, LocalDate.now());
        return period.getYears() > 0 && period.getMonths() == 0 && period.getDays() == 0;
      }catch (Exception e) {
        CommonLogger.INSTANCE.warn("CommemorationObject",
                String.format("check is birthday failed, driverId:[%s], identityCardId:[%s]",
                this.directorObject.id(), this.directorObject.identityCardId()));
        return false;
      }
    }
  }

  public CommemorationObjectImpl(){
    this.isBirthday = false;
    this.isActiveDay = false;
    this.showImg = Strings.EMPTY;
    this.showName = Strings.EMPTY;
    this.activeDayCount = 0;
    this.activeDayType = 0;
  }

  @Override
  public boolean isBirthday(){
    return this.isBirthday;
  }

  @Override
  public boolean isActiveDay() {
    return this.isActiveDay;
  }

  @Override
  public int activeDayCount() {
    return this.activeDayCount;
  }

  @Override
  public int activeDayType() {
    return this.activeDayType;
  }

  @Override
  public String showName() {
    return this.showName;
  }

  @Override
  public String showImg() {
    return this.showImg;
  }
 }
