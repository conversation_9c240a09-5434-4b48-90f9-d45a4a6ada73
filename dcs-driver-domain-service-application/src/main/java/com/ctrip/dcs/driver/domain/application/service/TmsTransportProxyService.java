package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvDetailDTOSOA;
import com.ctrip.dcs.tms.transport.api.regulation.OldDriverInfo;

import java.util.List;

public interface TmsTransportProxyService {

  DriverInfo queryDriver(long id);

  /**
   * 根据城市查询司机
   * */
  List<Long> queryDriverList(long cityId);

  /**
   * 查询文艺复兴前的司机信息
   * */
  OldDriverInfo queryOldDriver(String driverPhone);

  QueryDrvDetailDTOSOA queryDriverDetail(Long driverId);
}
