package com.ctrip.dcs.driver.domain.application.covert;

import com.ctrip.dcs.driver.domain.application.condition.CallPhoneForVerifyCondition;
import com.ctrip.model.CallPhoneForVerifyRequestType;
import com.ctrip.model.CallPhoneForVerifyResponseType;
import org.springframework.stereotype.Component;

@Component
public class CallPhoneForVerifyConvert {

    public CallPhoneForVerifyCondition buildCondition(CallPhoneForVerifyRequestType request) {
        CallPhoneForVerifyCondition condition = new CallPhoneForVerifyCondition();
        condition.setChannel(request.getChannel());
        condition.setLocale(request.getLocale());
        condition.setCountryCode(request.getCountryCode());
        condition.setPhoneNumber(request.getPhoneNumber());
        return condition;

    }

    public CallPhoneForVerifyResponseType buildResponse(Long callTaskId) {
        CallPhoneForVerifyResponseType response = new CallPhoneForVerifyResponseType();
        response.setCallTaskId(callTaskId);
        return response;
    }
}
