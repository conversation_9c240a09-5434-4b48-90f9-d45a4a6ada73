package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.account.domain.common.DriverDomainErrorCode.VoiceCodeError;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory;
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.SubmitVoiceCodeCallRequestType;
import com.ctrip.model.SubmitVoiceCodeCallResponseType;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/10 23:32
 * @Description: 提交验证码外呼服务实现
 */
@Component
public class SubmitVoiceCodeCallEngine extends ShoppingExecutor<SubmitVoiceCodeCallRequestType, SubmitVoiceCodeCallResponseType>
        implements Validator<SubmitVoiceCodeCallRequestType> {

    static final String VOICE_CODE_CALL_KEY = "voice_code_call_key:%s_%s_%s";
    static final Long LOCK_MILLS = 10 * 1000L;

    @Resource
    private PhoneCheckService phoneCheckService;
    @Resource
    private PhoneCheckTaskFactory phoneCheckTaskFactory;
    @Resource
    private PhoneCheckTaskTable phoneCheckTaskTable;
    @Resource
    private DriverTable driverTable;
    @Resource
    private LockService lockService;

    @Override
    public SubmitVoiceCodeCallResponseType execute(SubmitVoiceCodeCallRequestType request) {
        try {
            String lockKey = String.format(VOICE_CODE_CALL_KEY, request.getCountryCode(), request.getPhoneNo(), request.getVerificationCode());
            return lockService.executeInLock(lockKey, LOCK_MILLS, () -> doExecute(request));
        } catch (Exception e) {
            return ServiceResponseUtils.fail(new SubmitVoiceCodeCallResponseType(),
                    VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrCode(), VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrMsg());
        }
    }

    protected SubmitVoiceCodeCallResponseType doExecute(SubmitVoiceCodeCallRequestType request) {
        SubmitVoiceCodeCallResponseType responseType = new SubmitVoiceCodeCallResponseType();
//        PhoneCheckTaskEntity sample = PhoneCheckTaskEntity.builder()
//                .driverId(request.getDriverId())
//                .phoneNumber(request.getPhoneNo()).taskType(PhoneCheckTaskType.VOICE.getCode())
//                .phonePrefix(request.getCountryCode())
//                .build();
//        List<PhoneCheckTaskEntity> taskTableList = phoneCheckTaskTable.queryMany(sample);
//        if (CollectionUtils.isNotEmpty(taskTableList) && taskTableList.stream()
//                .anyMatch(t -> StringUtils.contains(t.getTaskContent(), request.getVerificationCode()))) {
//            return ServiceResponseUtils.success(responseType);
//        }
        DriverEntity driver = driverTable.queryByPk(request.getDriverId());
        if (Objects.isNull(driver)) {
            return ServiceResponseUtils.fail(responseType, VoiceCodeError.DRIVER_ACCOUNT_NOT_EXIST.getErrCode(), VoiceCodeError.DRIVER_ACCOUNT_NOT_EXIST.getErrMsg());
        }
        driver.setPhoneNumber(request.getPhoneNo());
        driver.setPhonePrefix(request.getCountryCode());
        PhoneCheckTaskEntity task = phoneCheckTaskFactory.applyVoiceTask(driver, request.getVerificationCode(), request.getLocale());
        phoneCheckTaskTable.insert(task);

        //发起IVR外呼
        boolean success = phoneCheckService.ivrCall(task);
        if (!success) {
            return ServiceResponseUtils.fail(responseType, VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrCode(), VoiceCodeError.IVR_SUBMIT_OUT_CALL_ERROR.getErrMsg());
        }
        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void onExecuted(SubmitVoiceCodeCallRequestType req, SubmitVoiceCodeCallResponseType resp) {
        super.onExecuted(req, resp);
        Cat.logEvent(CatEventType.SUBMIT_VOICE_CODE_CALL, resp.getResponseResult().getReturnCode());
    }

    @Override
    public void validate(AbstractValidator<SubmitVoiceCodeCallRequestType> validator) {
        validator.ruleFor("driverId").notNull().greaterThan(0L);
        validator.ruleFor("phoneNo").notNull().notEmpty();
        validator.ruleFor("verificationCode").notNull().notEmpty();
        validator.ruleFor("locale").notNull().notEmpty();
    }

}
