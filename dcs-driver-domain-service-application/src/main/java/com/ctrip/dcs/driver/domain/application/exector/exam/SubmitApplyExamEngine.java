package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exam.ApplyExamObjectImpl;
import com.ctrip.dcs.driver.domain.exam.SubmitApplyExamRequestType;
import com.ctrip.dcs.driver.domain.exam.SubmitApplyExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import com.ctrip.dcs.driver.value.exam.ApplyExamObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTag(tagKeys = {"guideId"})
public class SubmitApplyExamEngine
    extends ShoppingExecutor<SubmitApplyExamRequestType, SubmitApplyExamResponseType>
    implements Validator<SubmitApplyExamRequestType> {

  @Autowired
  private DomainBeanFactory domainBeanFactory;

  @Autowired
  LockService lockService;


  @Override
  public SubmitApplyExamResponseType execute(SubmitApplyExamRequestType request) {
    SubmitApplyExamResponseType response = new SubmitApplyExamResponseType();
    Executor executor = new Executor(request, response, this);
    executor.buildResponse();
    return ServiceResponseUtils.success(response);
  }

  @Override
  public void validate(AbstractValidator<SubmitApplyExamRequestType> validator) {
    validator.ruleFor("guideId").notNull().notEmpty().greaterThan(0L);
    validator.ruleFor("examAccountId").notNull().notEmpty();
    validator.ruleFor("applySubject").notNull().notEmpty();
  }

  private static final class Executor extends ShoppingBaseExecutor {
    private final SubmitApplyExamEngine owner;
    private final SubmitApplyExamResponseType response;
    private final SubmitApplyExamRequestType request;

    private ApplyExamObject applyExamObject;

    private Executor(SubmitApplyExamRequestType request, SubmitApplyExamResponseType response,
        SubmitApplyExamEngine owner) {
      super(request);
      this.owner = owner;
      this.response = response;
      this.request = request;
      applyExamObject =
          new ApplyExamObjectImpl(this.owner.domainBeanFactory, buildGuideApplyExamModel(request));
    }

    private GuideApplyExamModel buildGuideApplyExamModel(SubmitApplyExamRequestType request) {
      GuideApplyExamModel model = new GuideApplyExamModel();
      model.setGuideId(request.guideId);
      model.setExamAccountId(request.examAccountId);
      model.setGuideName(request.guideName);
      model.setAccount(request.account);
      model.setApplySubject(request.applySubject);
      model.setSubjectName(
          this.owner.domainBeanFactory.guideExamConfig().getExamNameByDeptId(request.applySubject));
      return model;
    }

    @Override
    protected void buildResponse() {
      Boolean ifPassedExam = this.owner.lockService.executeInLock(
          String.format("dcs_guide_apply_exam:%s_%s", request.getGuideId(),
              request.getApplySubject()), 1000, () -> this.applyExamObject.saveApplyExam());
      this.response.ifPassedExam = ifPassedExam;
    }

    @Override
    protected boolean validate() {
      return true;
    }
  }

}
