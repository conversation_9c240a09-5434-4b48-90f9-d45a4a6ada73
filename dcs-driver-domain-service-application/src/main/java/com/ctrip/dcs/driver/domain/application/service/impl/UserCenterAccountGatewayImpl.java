package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.basebiz.account.api.soa.common.types.ClientInfo;
import com.ctrip.basebiz.account.service.soa.*;
import com.ctrip.dcs.driver.account.domain.condition.PhoneInfoCondition;
import com.ctrip.dcs.driver.value.account.AccountUidInfo;
import com.ctrip.dcs.driver.account.application.dto.RegisterAccountResult;
import com.ctrip.dcs.driver.account.infrastructure.common.AccountExceptionCode;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountType;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.CommonResult;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.AccountLoginApiInternalServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.AccountManagerApiServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.AccountServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.dto.account.AccountBaseDto;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.soa.platform.accountapiserverinternal.v1.RegisterByEmailRequestType;
import com.ctrip.soa.platform.accountapiserverinternal.v1.RegisterByEmailResponseType;
import com.ctrip.soa.platform.accountapiserverinternal.v1.RegisterByMobilePhoneRequestType;
import com.ctrip.soa.platform.accountapiserverinternal.v1.RegisterByMobilePhoneResponseType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.BindEmailRequestType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.BindEmailResponseType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.BindMobilePhoneRequestType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.BindMobilePhoneResponseType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.UnbindEmailRequestType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.UnbindEmailResponseType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.UnbindMobilePhoneRequestType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.UnbindMobilePhoneResponseType;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.*;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @Description
 */
@Slf4j
@Component
public class UserCenterAccountGatewayImpl implements UserCenterAccountGateway {

    @Autowired
    private AccountServiceProxy accountServiceProxy;
    @Autowired
    private AccountManagerApiServiceProxy accountManagerApiServiceProxy;
    @Autowired
    private AccountLoginApiInternalServiceProxy accountLoginApiInternalServiceProxy;
    @Autowired
    private CommonLogger logger;
    @Autowired
    private DriverAccountConfig driverAccountConfig;

    private static final Integer MAINLAND_SUBSYSTEM_ID = 90023;
    private static final Integer OVERSEA_SUBSYSTEM_ID = 90064;
    private static final Integer SUCCESS_CODE = 0;
    private static final Integer ACCOUNT_ALREADY_UNFROZEN_CODE = 30038;
    private static final String MAINLAND_ACCESS_CODE = "XDSOAAUTEHNTICATE";
    private static final String OVERSEA_ACCESS_CODE = "XDOVERSEASOAAUTHENTICATE";
    private static final String OPERATOR = "SYSTEM";
    private static final String REASON = "SYSTEM OPERATE";

    private static final Map<Integer, String> ACCESS_CODE_MAP = ImmutableMap.of(
            MAINLAND_SUBSYSTEM_ID, MAINLAND_ACCESS_CODE, //90023
            OVERSEA_SUBSYSTEM_ID, OVERSEA_ACCESS_CODE);  //90064

    @Override
    public AccountUidInfo queryAccountUidByPhone(String countryCode, String phoneNumber) {
        AccountBaseDto accountBaseDto = new AccountBaseDto();
        accountBaseDto.setAccountType(AccountType.PHONE.getCode());
        accountBaseDto.setCountryCode(countryCode);
        accountBaseDto.setPhoneNumber(phoneNumber);
        return queryAccountUid(accountBaseDto);
    }

    //批量查询   queryAccountUidByPhone
    @Override
    public List<AccountUidInfo> batchQueryAccountUidByPhone(List<PhoneInfoCondition> phoneInfoConditionList) {
        if (CollectionUtils.isEmpty(phoneInfoConditionList)) {
            return Lists.newArrayList();
        }
        List<AccountBaseDto> list = phoneInfoConditionList.stream().map(t -> {
            AccountBaseDto accountBaseDto = new AccountBaseDto();
            accountBaseDto.setAccountType(AccountType.PHONE.getCode());
            accountBaseDto.setCountryCode(t.getCountryCode());
            accountBaseDto.setPhoneNumber(t.getPhoneNumber());
            return accountBaseDto;
        }).toList();

        return batchGetAccountByMobile(list);
    }

    private List<AccountUidInfo> batchGetAccountByMobile(List<AccountBaseDto> accountBaseDto) {
        // 先查询境外子系统，如果没查到账户，则再查询境内子系统
        if (driverAccountConfig.isEnableQueryDiffSubSystem()) {
            //境外的系统
            Map<String, AccountUidInfo> result = batchGetAccountByMobile(accountBaseDto, OVERSEA_SUBSYSTEM_ID);
            //查询子系统不存在的账号,然后再从MAINLAND_SUBSYSTEM_ID 中再查询一遍
            List<AccountBaseDto> accountBaseDtoNoResult = accountBaseDto.stream().filter(t -> result.get(t.getPhoneNumber()) == null).toList();
            if (CollectionUtils.isNotEmpty(accountBaseDtoNoResult)) {
                Map<String, AccountUidInfo> result2 = batchGetAccountByMobile(accountBaseDtoNoResult, MAINLAND_SUBSYSTEM_ID);
                result.putAll(result2);
            }
            return result.values().stream().filter(t->{return StringUtils.isNotBlank(t.getUid());}).toList();
        } else {
            //境内的系统
            Map<String, AccountUidInfo> stringAccountUidInfoMap = batchGetAccountByMobile(accountBaseDto, MAINLAND_SUBSYSTEM_ID);
            //转成list
            return stringAccountUidInfoMap.values().stream().toList();
        }
    }

    private Map<String /*手机号 todo 手机不加上国家码 是否全球唯一呢*/, AccountUidInfo /*账号信息*/> batchGetAccountByMobile(List<AccountBaseDto> accountBaseDto, Integer subsystemId) {

        GetAccountsByMobilePhoneListRequestType requestType = new GetAccountsByMobilePhoneListRequestType();
        requestType.setSubSystemId(subsystemId);
        requestType.setMobilePhoneList(accountBaseDto.stream().map(t -> {
            MobilePhoneInfo mobilePhoneInfo = new MobilePhoneInfo();
            mobilePhoneInfo.setCountryCode(t.getCountryCode());
            mobilePhoneInfo.setPhoneNumber(t.getPhoneNumber());
            return mobilePhoneInfo;
        }).toList());
        requestType.setExpect(Lists.newArrayList(ExpectedInfo.ACCOUNT_META_INFO,ExpectedInfo.MOBILE_INFO));
        try {
            AccountInfoListResponseType responseType = accountServiceProxy.getAccountsByMobilePhoneList(requestType);
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getAccounts())) {
                Map<String, AccountUidInfo> result = Maps.newHashMap();
                for (AccountInfoResponseType accountInfoType : responseType.getAccounts()) {
                    AccountUidInfo accountUidInfo = new AccountUidInfo(accountInfoType.getUid(), accountInfoType.getUdl());
                    result.put(accountInfoType.getPhoneNumber(), accountUidInfo);
                }
                return result;
            }

        } catch (Exception ex) {
            logger.error(ex);
        }
        return Maps.newHashMap();
    }


    @Override
    public AccountUidInfo queryAccountUidByEmail(String email) {
        AccountBaseDto accountBaseDto = new AccountBaseDto();
        accountBaseDto.setAccountType(AccountType.EMAIL.getCode());
        accountBaseDto.setEmail(email);
        return queryAccountUid(accountBaseDto);
    }

    @Override
    public AccountUidInfo queryAccountUid(AccountBaseDto accountBaseDto) {
        AccountUidInfo accountUidInfo;
        switch (accountBaseDto.accountType) {
            case 1:
                accountUidInfo = getAccountByMobile(accountBaseDto);
                break;
            case 2:
                accountUidInfo = getAccountByEmail(accountBaseDto);
                break;
            default:
                accountUidInfo = null;
        }
        return accountUidInfo;
    }


    @Override
    public AccountInfoResponseType getAccountByUid(String uid) {
        GetAccountByUidRequestType requestType = new GetAccountByUidRequestType();
        requestType.setUid(uid);
        requestType.setExpect(Lists.newArrayList(ExpectedInfo.ACCOUNT_META_INFO, ExpectedInfo.EMAIL_INFO, ExpectedInfo.MOBILE_INFO));
        try {
            AccountInfoResponseType resp = accountServiceProxy.getAccountByUid(requestType);
            if (resp != null && SUCCESS_CODE.equals(resp.getReturnCode())) {
                return resp;
            }
        } catch (Exception ex) {
            logger.warn("get account from user center error", ex);
        }
        return null;
    }

    @Override
    public RegisterAccountResult registerNewAccount(AccountBaseDto accountBaseDto) {
        RegisterAccountResult registerResult;
        if (AccountType.isPhone(accountBaseDto.getAccountType())) {
            registerResult = registerAccountByMobilePhone(accountBaseDto);
        } else {
            registerResult = registerAccountByEmail(accountBaseDto);
        }
        if (!registerResult.isSuccess() || StringUtils.isBlank(registerResult.getUid())) {
            throw new BizException(AccountExceptionCode.INVOKE_USER_CENTER_REGISTER_ERROR.getCode(), AccountExceptionCode.INVOKE_USER_CENTER_REGISTER_ERROR.getMsg());
        }

        // 绑定副信息
        if (accountBaseDto.getBindAccountType() != null) {
            String uid = registerResult.getUid();
            CommonResult bindResult;
            if (AccountType.isPhone(accountBaseDto.getBindAccountType())) {
                bindResult = bindMobilePhone(accountBaseDto, uid);
            } else {
                bindResult = bindEmail(accountBaseDto, uid);
            }
            if (!bindResult.isSuccess()) {
                throw new BizException(AccountExceptionCode.INVOKE_USER_CENTER_BIND_ERROR.getCode(), bindResult.getMsg());
            }
        }
        return registerResult;
    }

    @Override
    public CommonResult bindEmail(AccountBaseDto accountBaseDto, String uid) {
        BindEmailRequestType request = new BindEmailRequestType();
        request.setAccessCode(queryAccessCodeByUid(uid));
        request.setUid(uid);
        request.setEmail(accountBaseDto.email);
        request.setOperator(accountBaseDto.getOperator());
        request.setReason(REASON);

        try {
            BindEmailResponseType responseType = accountManagerApiServiceProxy.bindEmail(request);
            if (Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode())) {
                return CommonResult.newBuilder().withSuccess(true).withCode("200").build();
            } else {
                return CommonResult.newBuilder().withSuccess(false).withCode(String.valueOf(responseType.getReturnCode())).withMsg(responseType.getMessage()).build();
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
        return CommonResult.newBuilder().withSuccess(false).withCode("500").build();
    }

    @Override
    public boolean bindEmail(String uid, String newEmail, String operator) {
        BindEmailRequestType request = new BindEmailRequestType();
        request.setAccessCode(queryAccessCodeByUid(uid));
        request.setUid(uid);
        request.setEmail(newEmail);
        request.setOperator(operator);
        request.setReason(REASON);

        try {
            BindEmailResponseType responseType = accountManagerApiServiceProxy.bindEmail(request);
            return Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode());
        } catch (Exception ex) {
            logger.warn("bind email from user center error", ex);
            throw new BizException(AccountExceptionCode.INVOKE_USER_CENTER_BIND_ERROR.getCode(), "bind email error");
        }
    }

    @Override
    public boolean unBindEmail(String uid, String operator) {
        UnbindEmailRequestType request = new UnbindEmailRequestType();
        request.setAccessCode(queryAccessCodeByUid(uid));
        request.setUid(uid);
        request.setOperator(operator);
        request.setReason(REASON);

        try {
            UnbindEmailResponseType responseType = accountManagerApiServiceProxy.unbindEmail(request);
            return Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode());
        } catch (Exception ex) {
            logger.warn("unbind email from user center error", ex);
            throw new BizException(AccountExceptionCode.INVOKE_USER_CENTER_UNBIND_ERROR.getCode(), "unbind email error");
        }
    }

    @Override
    public boolean unBindPhone(String uid, String operator) {
        UnbindMobilePhoneRequestType request = new UnbindMobilePhoneRequestType();
        request.setAccessCode(queryAccessCodeByUid(uid));
        request.setUid(uid);
        request.setOperator(operator);
        request.setReason(REASON);

        try {
            UnbindMobilePhoneResponseType responseType = accountManagerApiServiceProxy.unbindMobilePhone(request);
            return Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode());
        } catch (Exception ex) {
            logger.warn("unbind phone from user center error " + uid, ex);
            throw new BizException(AccountExceptionCode.INVOKE_USER_CENTER_UNBIND_ERROR.getCode(), "unbind phone error");
        }
    }

    @Override
    public CommonResult bindMobilePhone(AccountBaseDto accountBaseDto, String uid) {
        BindMobilePhoneRequestType request = new BindMobilePhoneRequestType();
        request.setAccessCode(queryAccessCodeByUid(uid));
        request.setUid(uid);
        request.setCountryCode(accountBaseDto.countryCode);
        request.setPhoneNumber(accountBaseDto.phoneNumber);
        request.setOperator(accountBaseDto.operator);
        request.setReason(REASON);

        try {
            BindMobilePhoneResponseType responseType = accountManagerApiServiceProxy.bindMobilePhone(request);
            if (Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode())) {
                return CommonResult.newBuilder().withSuccess(true).withCode("200").build();
            } else {
                return CommonResult.newBuilder().withSuccess(false).withCode(String.valueOf(responseType.getReturnCode())).withMsg(responseType.getMessage()).build();
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
        return CommonResult.newBuilder().withSuccess(false).withCode("500").build();
    }

    @Override
    public boolean bindMobilePhone(String uid, String countryCode, String phoneNumber, String operator) {
        BindMobilePhoneRequestType request = new BindMobilePhoneRequestType();
        request.setAccessCode(queryAccessCodeByUid(uid));
        request.setUid(uid);
        request.setCountryCode(countryCode);
        request.setPhoneNumber(phoneNumber);
        request.setOperator(operator);
        request.setReason(REASON);

        try {
            BindMobilePhoneResponseType responseType = accountManagerApiServiceProxy.bindMobilePhone(request);
            if (Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode())) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            logger.warn("bind phone from user center error", ex);
            throw new BizException(AccountExceptionCode.INVOKE_USER_CENTER_BIND_ERROR.getCode(), "bind phone error");
        }
    }

    @Override
    public boolean unfreezeAccount(String uid) {
        UnfreezeAccountByUidRequestType request = new UnfreezeAccountByUidRequestType();
        request.setAccessCode(queryAccessCodeByUid(uid));
        request.setOperator(OPERATOR);
        request.setUid(uid);
        request.setReason(REASON);
        try {
            UnfreezeAccountByUidResponseType responseType = accountManagerApiServiceProxy.unfreezeAccountByUid(request);
            return Objects.nonNull(responseType) &&
                    (SUCCESS_CODE.equals(responseType.getReturnCode()) || ACCOUNT_ALREADY_UNFROZEN_CODE.equals(responseType.getReturnCode()));
        } catch (Exception ex) {
            logger.warn("unfreeze account from user center error", ex);
        }
        return false;
    }

    private AccountUidInfo getAccountByMobile(AccountBaseDto accountBaseDto) {
        // 先查询境外子系统，如果没查到账户，则再查询境内子系统
        if (driverAccountConfig.isEnableQueryDiffSubSystem()) {
            //境外的系统
            AccountUidInfo result = getAccountByMobile(accountBaseDto, OVERSEA_SUBSYSTEM_ID);
            if (result != null) {
                return result;
            }
        }
        //境内的系统
        return getAccountByMobile(accountBaseDto, MAINLAND_SUBSYSTEM_ID);
    }

    private AccountUidInfo getAccountByMobile(AccountBaseDto accountBaseDto, Integer subsystemId) {

        GetAccountByMobilePhoneRequestType requestType = new GetAccountByMobilePhoneRequestType();
        requestType.setSubSystemId(subsystemId);
        requestType.setCountryCode(accountBaseDto.getCountryCode());
        requestType.setPhoneNumber(accountBaseDto.getPhoneNumber());
        requestType.setExpect(Lists.newArrayList(ExpectedInfo.ACCOUNT_META_INFO));
        try {
            AccountInfoResponseType responseType = accountServiceProxy.getAccountByMobilePhone(requestType);
            if (responseType != null && StringUtils.isNotBlank(responseType.getUid())) {
                return new AccountUidInfo(responseType.getUid(), responseType.getUdl());
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
        return null;
    }

    private AccountUidInfo getAccountByEmail(AccountBaseDto accountBaseDto) {
        // 先查询境外子系统，如果没查到账户，则再查询境内子系统
        if (driverAccountConfig.isEnableQueryDiffSubSystem()) {
            AccountUidInfo result = getAccountByEmail(accountBaseDto, OVERSEA_SUBSYSTEM_ID);
            if (result != null) {
                return result;
            }
        }
        return getAccountByEmail(accountBaseDto, MAINLAND_SUBSYSTEM_ID);
    }

    public AccountUidInfo getAccountByEmail(AccountBaseDto accountBaseDto, Integer subsystemId) {
        GetAccountByEmailRequestType requestType = new GetAccountByEmailRequestType();
        requestType.setSubSystemId(subsystemId);
        requestType.setEmail(accountBaseDto.getEmail());
        requestType.setExpect(Lists.newArrayList(ExpectedInfo.ACCOUNT_META_INFO));
        try {
            AccountInfoResponseType responseType = accountServiceProxy.getAccountByEmail(requestType);
            if (responseType != null && StringUtils.isNotBlank(responseType.getUid())) {
                return new AccountUidInfo(responseType.getUid(), responseType.getUdl());
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
        return null;
    }


    // public CommonResult registerByMobilePhone(AccountBaseDto accountBaseDto) {
    //     RegisterByMobilePhoneRequestType requestType = new RegisterByMobilePhoneRequestType();
    //     requestType.setAccessCode(MAINLAND_ACCESS_CODE);
    //     requestType.setCountryCode(accountBaseDto.countryCode);
    //     requestType.setMobilePhone(accountBaseDto.phoneNumber);
    //     requestType.setOperator(OPERATOR);
    //     ClientInfo clientInfo = new ClientInfo();
    //     clientInfo.setLocale(getLocaleByCity(accountBaseDto.getCityId()));
    //     requestType.setClientInfo(clientInfo);
    //
    //     try {
    //         RegisterByMobilePhoneResponseType responseType = accountLoginApiInternalServiceProxy.registerByMobilePhone(requestType);
    //         if (Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode())) {
    //             return CommonResult.newBuilder().withSuccess(true).withUid(responseType.getUid()).withCode("200").build();
    //         } else {
    //             return CommonResult.newBuilder().withSuccess(false).withCode(String.valueOf(responseType.getReturnCode())).withMsg(responseType.getMessage()).build();
    //         }
    //     } catch (Exception ex) {
    //         logger.error(ex);
    //     }
    //     return CommonResult.newBuilder().withSuccess(false).withCode("500").build();
    // }

    // public CommonResult registerByEmail(AccountBaseDto accountBaseDto) {
    //     RegisterByEmailRequestType requestType = new RegisterByEmailRequestType();
    //     requestType.setAccessCode(MAINLAND_ACCESS_CODE);
    //     requestType.setEmail(accountBaseDto.email);
    //     // 邮箱状态，1：未验证，0：已验证
    //     requestType.setEmailStatus(1);
    //     requestType.setOperator(OPERATOR);
    //
    //     ClientInfo clientInfo = new ClientInfo();
    //     clientInfo.setLocale(getLocaleByCity(accountBaseDto.getCityId()));
    //     requestType.setClientInfo(clientInfo);
    //     try {
    //         RegisterByEmailResponseType responseType = accountLoginApiInternalServiceProxy.registerByEmail(requestType);
    //         if (Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode())) {
    //             return CommonResult.newBuilder().withSuccess(true).withUid(responseType.getUid()).withCode("200").build();
    //         } else {
    //             return CommonResult.newBuilder().withSuccess(false).withCode(String.valueOf(responseType.getReturnCode())).withMsg(responseType.getMessage()).build();
    //         }
    //     } catch (Exception ex) {
    //         logger.error(ex);
    //     }
    //     return CommonResult.newBuilder().withSuccess(false).withCode("500").build();
    // }

    /**
     * 根据手机号注册（新）
     */
    public RegisterAccountResult registerAccountByMobilePhone(AccountBaseDto accountBaseDto) {
//        TODO 境外注册一个系统，境内一个系统呢 是通过  accessCode 来区分的
        String accessCode = getRegisterAccessCode(accountBaseDto.getOversea(), accountBaseDto.getCityId());
        RegisterByMobilePhoneRequestType requestType = new RegisterByMobilePhoneRequestType();
        requestType.setAccessCode(accessCode);
        requestType.setCountryCode(accountBaseDto.countryCode);
        requestType.setMobilePhone(accountBaseDto.phoneNumber);
        requestType.setOperator(OPERATOR);
        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setLocale(getLocale(accessCode, accountBaseDto.getOversea()));
        requestType.setClientInfo(clientInfo);

        try {
            RegisterByMobilePhoneResponseType responseType = accountLoginApiInternalServiceProxy.registerByMobilePhone(requestType);
            if (Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode())) {
                return RegisterAccountResult.builder().success(true).uid(responseType.getUid()).udl(responseType.getUdl()).code("200").accessCode(accessCode).build();
            } else {
                return RegisterAccountResult.builder().success(false).code(String.valueOf(responseType.getReturnCode())).message(responseType.getMessage()).build();
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
        return RegisterAccountResult.builder().success(false).code("500").build();
    }

    /**
     * 根据邮箱注册（新）
     */
    public RegisterAccountResult registerAccountByEmail(AccountBaseDto accountBaseDto) {
        RegisterByEmailRequestType requestType = new RegisterByEmailRequestType();
        String accessCode = getRegisterAccessCode(accountBaseDto.getOversea(), accountBaseDto.getCityId());
        requestType.setAccessCode(accessCode);
        requestType.setEmail(accountBaseDto.email);
        // 邮箱状态，1：未验证，0：已验证
        requestType.setEmailStatus(1);
        requestType.setOperator(OPERATOR);

        ClientInfo clientInfo = new ClientInfo();
        clientInfo.setLocale(getLocale(accessCode, accountBaseDto.getOversea()));
        requestType.setClientInfo(clientInfo);
        try {
            RegisterByEmailResponseType responseType = accountLoginApiInternalServiceProxy.registerByEmail(requestType);
            if (Objects.nonNull(responseType) && SUCCESS_CODE.equals(responseType.getReturnCode())) {
                return RegisterAccountResult.builder().success(true).uid(responseType.getUid()).udl(responseType.getUdl()).code("200").accessCode(accessCode).build();
            } else {
                return RegisterAccountResult.builder().success(false).code(String.valueOf(responseType.getReturnCode())).message(responseType.getMessage()).build();
            }
        } catch (Exception ex) {
            logger.error(ex);
        }
        return RegisterAccountResult.builder().success(false).code("500").build();
    }


    private String getLocale(String accessCode, Boolean oversea) {
        // 境外账号统一使用默认的locale。境内账号和oversea字段为空不用生成，向导上线前可能为空，可以不传，但不能传一个错的
        // 仅90064才传，90023限制了注册时locale只能是zh-CN或者空，否则会报错
        if (OVERSEA_ACCESS_CODE.equals(accessCode) && BooleanUtils.isTrue(oversea)) {
            return "en-US";
        }
        return "";
    }

    /**
     * 获取注册账号accessCode
     */
    private String getRegisterAccessCode(Boolean oversea, Long cityId) {
        // 境内外注册到不同的子系统
        String registerToDiffSubSystemCityIds = driverAccountConfig.getRegisterToDiffSubSystemCityIds();
        boolean enableRegisterToDiffSubSystem = false;
        if ("-1".equals(registerToDiffSubSystemCityIds)) {
            // 全量
            enableRegisterToDiffSubSystem = true;
        } else {
            List<String> configCityIds = Optional.ofNullable(registerToDiffSubSystemCityIds).map(o -> Arrays.asList(o.split(","))).orElse(Lists.newArrayList());
            // cityId不为空且配置包含，则允许注册到不同的子系统
            if (cityId != null && configCityIds.contains(cityId.toString())) {
                enableRegisterToDiffSubSystem = true;
            }
        }
        String accessCode = enableRegisterToDiffSubSystem && BooleanUtils.isTrue(oversea) ? OVERSEA_ACCESS_CODE/*海外注册码*/ : MAINLAND_ACCESS_CODE/*国内注册码*/;
        //埋点观察下
        Cat.logEvent(CatEventType.REGISTER_ACCESS_CODE, accessCode);
        return accessCode;
    }

    /**
     * 根据uid获取accessCode（通过uid反查所属subSystemId，根据systemId获取accessCode。
     */
    private String queryAccessCodeByUid(String uid) {
        AccountInfoResponseType accountInfo = getAccountByUid(uid);
        if (accountInfo == null) {
            return null;
        }
        Integer subSystemId = accountInfo.getAccountMetaData().getSubSystemId();
        String code = ACCESS_CODE_MAP.get(subSystemId);
        //添加一个日志，观察下情况，不然容易出现问题。
        logger.info("queryAccessCodeByUid", "uid : " + uid + " subSystemId : " + subSystemId + " accessCode : " + code);
        Cat.logEvent(CatEventType.QUERY_ACCESS_CODE_BY_UID, code);
        return code;
    }
}
