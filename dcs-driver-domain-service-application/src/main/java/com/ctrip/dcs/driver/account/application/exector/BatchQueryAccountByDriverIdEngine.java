package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountIdentityDTO;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.BatchQueryAccountByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.BatchQueryAccountByDriverIdResponseType;
import com.ctrip.dcs.driver.domain.account.DriverAccountDetailDTO;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class BatchQueryAccountByDriverIdEngine extends ShoppingExecutor<BatchQueryAccountByDriverIdRequestType, BatchQueryAccountByDriverIdResponseType> implements Validator<BatchQueryAccountByDriverIdRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public BatchQueryAccountByDriverIdResponseType execute(BatchQueryAccountByDriverIdRequestType request) {


        BatchQueryAccountByDriverIdResponseType resp = new BatchQueryAccountByDriverIdResponseType();

        resp.setDriverAccountDetailList(Lists.newArrayList());
        List<AccountInfoDTO> accountList = accountService.batchGetAccountByDriverId(request.getDriverIdList());
        if (CollectionUtils.isEmpty(accountList)) {
            Cat.logEvent(CatEventType.BATCH_QUERY_BY_DRIVER_ID_RESULT, "0");
            return ServiceResponseUtils.success(resp);
        }
        List<DriverAccountDetailDTO> driverAccountDetailDTOList = Lists.newArrayList();
        accountList.stream().filter(accountInfoDTO -> CollectionUtils.isNotEmpty(accountInfoDTO.getIdentityDTOList())).forEach(accountInfoDTO -> {
            List<AccountIdentityDTO> identityDTOList = accountInfoDTO.getIdentityDTOList().stream().filter(o -> AccountSouceEnum.isDriver(o.getSource()) || AccountSouceEnum.isDriverGuide(o.getSource())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(identityDTOList)) {
                driverAccountDetailDTOList.add(new DriverAccountDetailDTO(Long.valueOf(identityDTOList.get(0).getSourceId()), AccountDetailConvert.convert(accountInfoDTO)));
            }
        });

        resp.setDriverAccountDetailList(driverAccountDetailDTOList);
        Cat.logEvent(CatEventType.BATCH_QUERY_BY_DRIVER_ID_RESULT, CollectionUtils.isNotEmpty(driverAccountDetailDTOList) ? "1" : "0");
        DriverMetricUtil.batchMetricUdlIsValid2(accountList);
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public Boolean isEmptyResult(BatchQueryAccountByDriverIdResponseType resp) {
        return !(resp!=null&&CollectionUtils.isNotEmpty(resp.getDriverAccountDetailList()));

    }
}
