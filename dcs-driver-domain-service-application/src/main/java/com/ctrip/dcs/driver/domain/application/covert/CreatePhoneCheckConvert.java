package com.ctrip.dcs.driver.domain.application.covert;

import com.ctrip.dcs.driver.domain.application.condition.CreatePhoneCheckCondition;
import com.ctrip.model.CreatePhoneCheckRequestType;
import com.ctrip.model.CreatePhoneCheckResponseType;
import org.springframework.stereotype.Component;

@Component
public class CreatePhoneCheckConvert {

    public CreatePhoneCheckCondition buildCondition(CreatePhoneCheckRequestType requestType) {
        CreatePhoneCheckCondition condition = new CreatePhoneCheckCondition();
        condition.setCountryCode(requestType.getCountryCode());
        condition.setPhoneNumber(requestType.getPhoneNumber());
        condition.setCheckType(requestType.getCheckType());
        condition.setCheckState(requestType.getCheckState());
        return condition;
    }

    public CreatePhoneCheckResponseType buildResponse(boolean result) {
        CreatePhoneCheckResponseType  response = new CreatePhoneCheckResponseType();
        response.setResult(result);
        return response;
    }
}
