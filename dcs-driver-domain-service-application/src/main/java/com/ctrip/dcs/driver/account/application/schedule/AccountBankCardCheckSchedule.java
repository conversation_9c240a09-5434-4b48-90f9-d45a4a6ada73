package com.ctrip.dcs.driver.account.application.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.ctrip.dcs.driver.account.application.service.impl.AccountBankCardServiceImpl;
import com.ctrip.dcs.driver.account.domain.condition.*;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.gateway.AccountBankCardRecordRepository;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.dcs.driver.gateway.DriverAppPushMessageRepository;
import com.ctrip.dcs.driver.gateway.YeePayRepository;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.driver.value.PushMessageDo;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Component
public class AccountBankCardCheckSchedule {
    Log log = Log.getInstance(AccountBankCardCheckSchedule.class);




    @Autowired
    AccountBankCardRecordRepository accountBankCardRecordRepository;
    @Autowired
    YeePayRepository yeePayRepository;
    @Autowired
    DriverAccountConfig driverAccountConfig;
    @Autowired
    DriverAppPushMessageRepository driverAppPushMessageRepository;
    @Autowired
    AccountBankCardServiceImpl accountBankCardService;
    @Autowired
    CardInfoManagementRepository cardInfoManagementRepository;

    //账户的状态检查，最终一致性
    @QSchedule("dcs.driver.account.bankCard.check.job")
    public void onExecute(Parameter parameter) {
        AccountBankCardCheckCondition accountBankCardCheckCondition = convertAccountBankCardCheckCondition(parameter);
        if (CollUtil.isNotEmpty(accountBankCardCheckCondition.getUidList())) {
            checkStatusByUid(accountBankCardCheckCondition.getUidList());
        }
    }

    //删除报备人银行卡信息
    @QSchedule("dcs.driver.account.yeepay.bankCard.delete.job")
    public void deleteBankCardInfo(Parameter parameter) {
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString()).init();
        List<String> cardIdList = new ArrayList<>();

        if (StringUtils.isNotBlank(parameter.getString("requestParam"))) {
            List<DeleteYeePayCardByRequestIdCondition> request=convertDeleteYeePayCardByRequestIdCondition(parameter);
            doDeleteBankCardInfoByRequest(request);
            return;
        }
        if (StringUtils.isNotBlank(parameter.getString("bankCardNoList"))) {
            List<String> bankCardNoList = Lists.newArrayList(parameter.getString("bankCardNoList").split(","));
            cardIdList.addAll(convertCardId(bankCardNoList));
        }
        if (StringUtils.isNotBlank(parameter.getString("cardIdList"))) {
            cardIdList.addAll(Lists.newArrayList(parameter.getString("cardIdList").split(",")));
        }
        doDeleteBankCardInfo(cardIdList);
    }

    private void doDeleteBankCardInfoByRequest(List<DeleteYeePayCardByRequestIdCondition> request) {
        for (DeleteYeePayCardByRequestIdCondition requestCondition : request) {
            AccountBankCardDo accountBankCardDo = AccountBankCardDo.builder().requestId(requestCondition.getRequestId())
                    .isOverseaBankCard(requestCondition.isOversea()).build();
            BaseResult<Boolean> booleanBaseResult = yeePayRepository.deleteYeepayRecipientReport(DeleteYeepayRecipientReportCondition.builder().accountBankCardDo(accountBankCardDo).build());
            if (booleanBaseResult.isSuccess()) {
                log.info("deleteYeepayRecipientReport success " + requestCondition.getRequestId());
            } else {
                log.info("deleteYeepayRecipientReport failed " + requestCondition.getRequestId());
            }
        }
    }

    private List<DeleteYeePayCardByRequestIdCondition> convertDeleteYeePayCardByRequestIdCondition(Parameter parameter) {
        String string = parameter.getString("requestParam");
        return JacksonUtil.deserialize(string, new TypeReference<List<DeleteYeePayCardByRequestIdCondition>>() {
        });
    }

    private List<String> convertCardId(List<String> bankCardNoList) {
        Map<String, String> stringStringMap = cardInfoManagementRepository.queryCardInfoByCardNo(bankCardNoList);
        List<String> cardIdList = new ArrayList<>();
        for (Map.Entry<String, String> entry : stringStringMap.entrySet()) {
            cardIdList.add(entry.getValue());
        }
        return cardIdList;
    }

    private void doDeleteBankCardInfo(List<String> cardIdList) {
        for (String cardId : cardIdList) {
            sleep(driverAccountConfig.getCheckStatusByUidThreadSleepMills());
            log.info("deleteYeepayRecipientReport start " + cardId);
            BaseResult<AccountBankCardDo> accountBankCardDoBaseResult = accountBankCardRecordRepository.queryAccountBankCard(cardId);
            if (accountBankCardDoBaseResult.isSuccess()) {
                if (accountBankCardDoBaseResult.getData() != null) {
                    AccountBankCardDo data = accountBankCardDoBaseResult.getData();
                    BaseResult<Boolean> booleanBaseResult = yeePayRepository.deleteYeepayRecipientReport(DeleteYeepayRecipientReportCondition.builder().accountBankCardDo(data).build());
                    if (booleanBaseResult.isSuccess()) {
                        log.info("deleteYeepayRecipientReport success " + cardId);
                    } else {
                        log.info("deleteYeepayRecipientReport failed " + cardId);
                    }
                }
            }
        }
    }

    //扫描待审核，审核时间已经过了24的的银行卡 如果易宝是绑定的银行卡的状态是绑定的，则更新自己状态，并且发送消息
    @QSchedule("dcs.driver.account.bankCard.processing.check.job")
    public void processingCheck(Parameter parameter) {
        AccountBankCardProcessingCondition accountBankCardProcessingCondition = convertAccountBankCardProcessingCondition(parameter);
        List<AccountBankCardDo> accountBankCardDos = accountBankCardRecordRepository.scanDataBankCardProcessing(accountBankCardProcessingCondition);
        while (true) {
            if (CollUtil.isEmpty(accountBankCardDos)) {
                log.info("There is no data to be processed");
                break;
            }
            log.info("There is data to be processed,size:" + accountBankCardDos.size());
            long maxId = doProcessingCheck(accountBankCardDos);
            log.info("The processing is complete,maxId"+maxId);
            accountBankCardProcessingCondition.setId(maxId);
            accountBankCardDos = accountBankCardRecordRepository.scanDataBankCardProcessing(accountBankCardProcessingCondition);
        }
    }

    private long doProcessingCheck(List<AccountBankCardDo> accountBankCardDos) {
        for (AccountBankCardDo accountBankCardDo : accountBankCardDos) {
            if (accountBankCardDo.getCardStatus().isAuditing()) {
                BankCardStatusDo bankCardStatusDo = yeePayRepository.queryBankCardStatus(QueryBankCardStatusCondition.builder().requestId(accountBankCardDo.getRequestId())
//                        .accountNumber(accountBankCardDo.getCardNo())
                        .type(accountBankCardDo.getType())
                        .build());
                if (bankCardStatusDo.isBind()) {
                    log.info("The status of Yeepay is consistent and it is necessary to bring up Trading Treasure again");
                    Cat.logEvent(CatEventType.RE_CALC_DRIVER_BANK_CARD_STATUS_RESULT, "YeePayStatusConsistent");
                    //重新设置成绑定
                    accountBankCardDo.getCardStatus().bindBankSuccess();
                    accountBankCardRecordRepository.updateBankCardRecordStatus(accountBankCardDo);
                    //发送消息
                    PushMessageDo pushMessageDo = accountBankCardService.buildSuccessPushMessage(accountBankCardDo.getDriverId());
                    driverAppPushMessageRepository.sendPushMessage(pushMessageDo);
                }
            }
        }
        return accountBankCardDos.stream().map(AccountBankCardDo::getId).max(Long::compareTo).get();
    }

    private AccountBankCardProcessingCondition convertAccountBankCardProcessingCondition(Parameter parameter) {
        AccountBankCardProcessingCondition accountBankCardProcessingCondition=new AccountBankCardProcessingCondition();
        LocalDateTime now = LocalDateTimeUtil.now();
        accountBankCardProcessingCondition.setEndDate(LocalDateTimeUtil.format(now, "yyyy-MM-dd HH:mm:ss"));
        String offestMinutes = parameter.getString("minusMinutes");
        if (StringUtils.isNotBlank(offestMinutes)) {
            accountBankCardProcessingCondition.setStartDate(LocalDateTimeUtil.format(now.minusMinutes(Long.parseLong(offestMinutes)), "yyyy-MM-dd HH:mm:ss"));
        }
        accountBankCardProcessingCondition.setId(1L);
        return accountBankCardProcessingCondition;
    }


    private void checkStatusByUid(List<String> uidList) {
        for (String uid : uidList) {
            sleep(driverAccountConfig.getCheckStatusByUidThreadSleepMills());
            BaseResult<AccountBankCardDo> accountBankCardDoBaseResult = accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(uid);
            if (accountBankCardDoBaseResult.isSuccess()) {
                if (accountBankCardDoBaseResult.getData() != null) {
                    AccountBankCardDo data = accountBankCardDoBaseResult.getData();
                    if (data.getCardStatus().isBind()) {
                        //看下易宝那边是否是绑定的状态
                        BankCardStatusDo bankCardStatusDo = yeePayRepository.queryBankCardStatus(QueryBankCardStatusCondition.builder()
                                .requestId(data.getRequestId())
                                .type(data.getType())
                                .accountNumber(data.getCardNo())
                                .build());
                        //如果不是则要重新提交给易宝
                        if (!bankCardStatusDo.isBind()) {
                            log.info("The status of Yeepay is inconsistent and it is necessary to bring up Trading Treasure again");
                            //重新提交给易宝
                            SubmitYeepayRecipientReportCondition condition = SubmitYeepayRecipientReportCondition.builder().accountBankCardDo(data).build();
                            BaseResult<Boolean> submitYeepayRecipientReport = yeePayRepository.submitYeepayRecipientReport(condition);
                            if (submitYeepayRecipientReport.isSuccess()) {
                                //重新设置成审核中
                                log.info("submitYeepayRecipientReport success "+uid);
                            }
                        }


                    }

                }
            }
        }
    }

    private void sleep(long checkStatusByUidThreadSleepMills) {
        try {
            Thread.sleep(checkStatusByUidThreadSleepMills);
        } catch (Exception e) {
            log.warn("sleep exception", e);
            throw new RuntimeException(e);
        }
    }

    private AccountBankCardCheckCondition convertAccountBankCardCheckCondition(Parameter parameter) {
        AccountBankCardCheckCondition accountBankCardCheckCondition = new AccountBankCardCheckCondition();
        if (StringUtils.isNotBlank(parameter.getString("uidList"))) {
            accountBankCardCheckCondition.setUidList(Lists.newArrayList(parameter.getString("uidList").split(",")));
        }
        return accountBankCardCheckCondition;
    }

}
