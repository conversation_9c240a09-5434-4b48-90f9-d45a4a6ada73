package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.service.FinanceConvertService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.supply.SupplyAccountTransferServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.FinanceConfig;
import com.ctrip.dcs.supply.driver.dto.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
public class FinanceConvertServiceImpl implements FinanceConvertService {

	@Autowired
	SupplyAccountTransferServiceProxy supplyAccountTransferServiceProxy;

	@Autowired
	CommonMultipleLanguages commonMultipleLanguages;

	@Autowired
	FinanceConfig financeQconfig;

	@Override
	public Pair<String, Balance> queryDriverWithdrawBalance(long driverId, boolean isOldProcess){
		QueryDriverWithdrawBalanceResponseType responseType = null;
		try {
			QueryDriverWithdrawBalanceRequestType requestType = new QueryDriverWithdrawBalanceRequestType();
			requestType.driverId = driverId;
			requestType.sourceType = isOldProcess ? 2 : 1;
			responseType = supplyAccountTransferServiceProxy.queryDriverWithdrawBalance(requestType);
		} catch (Exception e){
			return Pair.of(FinanceResultEnum.ERROR.getCode(), null);
		}
		if(Objects.isNull(responseType) || Objects.isNull(responseType.responseResult)){
			return Pair.of(FinanceResultEnum.ERROR.getCode(), null);
		}
		return Pair.of(this.convertDriverFinanceResult(responseType.responseResult.returnCode), responseType.data);
	}

	@Override
	public List<CardInfo> queryDriverCardList(long driverId){
		QueryDriverCardListResponseType responseType = null;
		try {
			QueryDriverCardListRequestType requestType = new QueryDriverCardListRequestType();
			requestType.driverId = driverId;
			responseType = supplyAccountTransferServiceProxy.queryDriverCardList(requestType);
		} catch (Exception e){
			return Collections.emptyList();
		}
		if(Objects.isNull(responseType) || CollectionUtils.isEmpty(responseType.data)){
			return Collections.emptyList();
		}
		return responseType.data;
	}

	@Override
	public String driverWithdraw(WithdrawModel withdrawModel){
		DriverWithdrawResponseType responseType = null;
		try {
			DriverWithdrawRequestType requestType = new DriverWithdrawRequestType();
			requestType.driverId = withdrawModel.getDriverId();
			requestType.amount = withdrawModel.getAmount();
			requestType.bankCard = withdrawModel.getBankCard();
			requestType.bankCardInfo = withdrawModel.getBankCardInfo();
			requestType.driverName = withdrawModel.getDriverName();
			requestType.password = withdrawModel.getPassword();
			requestType.sourceType = withdrawModel.isOldProcess() ? 2 : 1;
			requestType.bankId = withdrawModel.getBankCardId();
			responseType = supplyAccountTransferServiceProxy.driverWithdraw(requestType);
		} catch (Exception e){
			return FinanceResultEnum.ERROR.getCode();
		}
		if(Objects.isNull(responseType) || Objects.isNull(responseType.responseResult)){
			return FinanceResultEnum.ERROR.getCode();
		}
		return this.convertDriverFinanceResult(responseType.responseResult.returnCode);
	}

	@Override
	public String driverResetPsw(WithdrawModel withdrawModel){
		DriverResetPswResponseType responseType = null;
		try {
			DriverResetPswRequestType requestType = new DriverResetPswRequestType();
			requestType.driverId = withdrawModel.getDriverId();
			requestType.identitycode = withdrawModel.getIdentitycode();
			requestType.password = withdrawModel.getPassword();
			requestType.sourceType = withdrawModel.isOldProcess() ? 2 : 1;
			responseType = supplyAccountTransferServiceProxy.driverResetPsw(requestType);
		} catch (Exception e){
			return FinanceResultEnum.ERROR.getCode();
		}
		if(Objects.isNull(responseType) || Objects.isNull(responseType.responseResult)){
			return FinanceResultEnum.ERROR.getCode();
		}
		return this.convertDriverFinanceResult(responseType.responseResult.returnCode);
	}

	@Override
	public String convertDriverFinanceResult(String serviceResultCode) {
		if(StringUtils.isBlank(serviceResultCode)) {
			return FinanceResultEnum.ERROR.getCode();
		}

		switch (serviceResultCode){
			case "200":
				return FinanceResultEnum.OK.getCode();
			case "500":
				return FinanceResultEnum.ERROR.getCode();
			default:
				String convertCode = financeQconfig.getConvertCode(serviceResultCode);
				return StringUtils.isNotBlank(convertCode) ?
						convertCode : FinanceResultEnum.DATA_FAIL.getCode();
		}
	}

	/**
	 * 返回CODE对应的描述
	 * */
	@Override
	public String getFinanceResult(String code) {
		String convertShark = financeQconfig.getConvertShark(code);
		return StringUtils.isNotBlank(convertShark) ?
				commonMultipleLanguages.getContent(convertShark) : Strings.EMPTY;
	}

	@Override
	public List<BalanceRecordDTO> queryDriverBalanceRecord(long driverId, String beginDate, String endDate){
		QueryDriverBalanceRecordResponseType responseType = null;
		try {
			QueryDriverBalanceRecordRequestType requestType = new QueryDriverBalanceRecordRequestType();
			requestType.driverId = driverId;
			requestType.beginDate = beginDate;
			requestType.endDate = endDate;
			responseType = supplyAccountTransferServiceProxy.queryDriverBalanceRecord(requestType);
		} catch (Exception e){
			return Collections.emptyList();
		}
		if(Objects.isNull(responseType) || CollectionUtils.isEmpty(responseType.data)){
			return Collections.emptyList();
		}
		return responseType.data;
	}
}
