package com.ctrip.dcs.driver.account.application.service;
/*
作者：pl.yang
创建时间：2025/4/28-上午11:09-2025
*/


import com.ctrip.dcs.driver.account.domain.condition.BindOverseaBankCardCondition;
import com.ctrip.dcs.driver.account.domain.condition.DriverBankCardBindResultCondition;
import com.ctrip.dcs.driver.account.domain.condition.DriverWithDrawCondition;
import com.ctrip.dcs.driver.account.domain.condition.UnbindBankCardCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.value.BaseResult;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountBankCardService
 * @Package com.ctrip.dcs.driver.account.application.service
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 上午11:09
 */


public interface AccountBankCardService {
    //绑定境外银行卡
    BaseResult<Void> bindOverseaBankCard(BindOverseaBankCardCondition bankCardCondition);

    BaseResult<Void> unbindBankCard(UnbindBankCardCondition condition);

    /*通过司机id查询银行信息*/
    BaseResult<AccountBankCardDo>  queryAccountBankCard(/*todo 封装成condition*/Long driverId,String bankCardNO);

    /**
     * 司机银行卡绑定结果处理
     * @param condition
     * @return
     */
    void  driverBankCardBindResultHandle(DriverBankCardBindResultCondition condition);

    //提现结果通知
    void driverWithDrawNotify(DriverWithDrawCondition condition);
}
