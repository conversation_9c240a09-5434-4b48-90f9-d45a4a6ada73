package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.go.log.Log;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;

@Component
public class RefreshAccountStateSchedule {
    Log log = Log.getInstance(RefreshAccountStateSchedule.class);
    @Autowired
    private AccountService accountService;

    @QSchedule("dcs.driver.account.refresh.account.state.job")
    public void onExecute(Parameter parameter) {

        // 指定uid
        String uids = parameter.getString("uidList");
        String valid = parameter.getString("valid");
        String source = parameter.getString("source");
        if (StringUtils.isBlank(uids)) {
            return;
        }
        List<String> uidList = Lists.newArrayList(uids.split(","));
        AccountSouceEnum accountSource = AccountSouceEnum.instanceOf(source);
        if (CollectionUtils.isEmpty(uidList) || accountSource == null) {
            return;
        }
        for (String uid : uidList) {
            accountService.updateAccountIdentityState(uid, accountSource, "1".equals(valid));
        }
    }
}
