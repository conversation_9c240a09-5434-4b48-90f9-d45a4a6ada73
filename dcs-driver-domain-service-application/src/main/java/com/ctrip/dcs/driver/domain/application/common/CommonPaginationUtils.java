package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.igt.PaginationDTO;

public final class CommonPaginationUtils {
  public static PaginationDTO buildPaginationDTO(Integer pageNo, Integer pageSize, Integer totalSize) {
    PaginationDTO pagination = new PaginationDTO();
    pagination.setPageNo(pageNo);
    pagination.setPageSize(pageSize);
    pagination.setTotalPages(totalSize / pageSize + (totalSize % pageSize == 0 ? 0 : 1));
    pagination.setTotalSize(totalSize);
    return pagination;
  }
}
