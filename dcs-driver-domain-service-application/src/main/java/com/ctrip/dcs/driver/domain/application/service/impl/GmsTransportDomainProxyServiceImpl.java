package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.GmsTransportDomainProxyService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.transport.GmsTransportDomainServiceProxy;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideAllInfoRequestType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideAllInfoResponseType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideBaseInfoByIdRequestType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideBaseInfoByIdResponseType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideAllInfoDTO;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideBaseInfoDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class GmsTransportDomainProxyServiceImpl implements GmsTransportDomainProxyService {
  private static final Logger LOGGER = LoggerFactory.getLogger(GmsTransportDomainProxyServiceImpl.class);

  @Autowired
  private GmsTransportDomainServiceProxy gmsTransportDomainServiceProxy;

  @Override
  public GuideBaseInfoDTO queryGuideBaseInfoById(long id){
    QueryGuideBaseInfoByIdRequestType requestType = new QueryGuideBaseInfoByIdRequestType();
    requestType.setGuideId(id);
    QueryGuideBaseInfoByIdResponseType responseType;
    try {
      responseType = gmsTransportDomainServiceProxy.queryGuideBaseInfoById(requestType);
    }catch (Exception e) {
      LOGGER.warn("query guide base info error", e);
      return null;
    }
    if(Objects.nonNull(responseType)){
      return responseType.baseInfo;
    }
    return null;
  }

  @Override
  public GuideAllInfoDTO queryGuideAllInfo(Long guideId) {
    QueryGuideAllInfoRequestType requestType = new QueryGuideAllInfoRequestType();
    requestType.setGuideId(guideId);
    QueryGuideAllInfoResponseType responseType;
    try {
      responseType = gmsTransportDomainServiceProxy.queryGuideAllInfo(requestType);
    } catch (Exception e) {
      LOGGER.warn("query guide all info error", e);
      return null;
    }
    if (Objects.nonNull(responseType) && responseType.getAllInfo() != null) {
      return responseType.getAllInfo();
    }
    return null;
  }
}
