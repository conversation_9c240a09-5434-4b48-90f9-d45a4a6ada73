package com.ctrip.dcs.driver.account.application.cache;

import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.go.util.JsonUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class AccountInfoCache {
    private static final String ACCOUNT_INFO_CACHE_KEY = "account_info_%s";

    private static final String ACCOUNT_SOURCE_UID_CACHE_KEY = "account_source_%s_%s";

    @Autowired
    DirectorRedis directorRedis;

    public void saveAccount(AccountInfoDTO accountInfo, int seconds) {
        directorRedis.set(String.format(ACCOUNT_INFO_CACHE_KEY, accountInfo.getUid()), accountInfo, seconds);
    }

    public void batchSaveAccount(List<AccountInfoDTO> accountInfoList, int seconds) {
        directorRedis.mset(seconds, accountInfoList.stream().flatMap(accountInfo ->
                Stream.of(String.format("dcs_driver_" + ACCOUNT_INFO_CACHE_KEY, accountInfo.getUid()), JsonUtil.toString(accountInfo))
        ).toArray(String[]::new));
    }

    public void delAccount(String uid) {
        directorRedis.remove(String.format(ACCOUNT_INFO_CACHE_KEY, uid));
    }


    public AccountInfoDTO getAccount(String uid) {
        return directorRedis.get(String.format(ACCOUNT_INFO_CACHE_KEY, uid), AccountInfoDTO.class);
    }

    public List<AccountInfoDTO> batchGetAccount(List<String> uidList) {
        String[] keys = uidList.stream().distinct().map(o -> String.format("dcs_driver_" + ACCOUNT_INFO_CACHE_KEY, o)).toArray(String[]::new);
        return Optional.ofNullable(directorRedis.mget(AccountInfoDTO.class, keys)).orElse(Lists.newArrayList())
                .stream().filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public void saveSourceUIDMapping(String source, String sourceId, String uid, long seconds) {
        directorRedis.set(String.format(ACCOUNT_SOURCE_UID_CACHE_KEY, source, sourceId), uid, seconds);
    }

    public String getUIDBySource(String source, String sourceId) {
        return directorRedis.get(String.format(ACCOUNT_SOURCE_UID_CACHE_KEY, source, sourceId), String.class);
    }
}
