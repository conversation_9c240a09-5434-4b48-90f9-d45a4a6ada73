package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.rights.DriverLevelDTO;
import com.ctrip.dcs.driver.domain.rights.DriverRightsDTO;
import com.ctrip.dcs.driver.domain.rights.QueryRightsAndLevelRequestType;
import com.ctrip.dcs.driver.domain.rights.QueryRightsAndLevelResponseType;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.driver.value.rights.LevelObject;
import com.ctrip.dcs.driver.value.rights.RightsObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class QueryRightsAndLevelEngine extends ShoppingExecutor<QueryRightsAndLevelRequestType, QueryRightsAndLevelResponseType>
        implements Validator<QueryRightsAndLevelRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public QueryRightsAndLevelResponseType execute(QueryRightsAndLevelRequestType request) {
        QueryRightsAndLevelResponseType response = new QueryRightsAndLevelResponseType();
        QueryRightsAndLevelEngine.Executor executor = new QueryRightsAndLevelEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryRightsAndLevelRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("date").notNull().notEmpty();
        validator.ruleFor("rightsTypes").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryRightsAndLevelEngine owner;
        private final QueryRightsAndLevelResponseType response;
        private final QueryRightsAndLevelRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(QueryRightsAndLevelRequestType request, QueryRightsAndLevelResponseType response, QueryRightsAndLevelEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;

            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory, request.driverId, request.date);
        }

        @Override
        protected void buildResponse() {
            List<RightsObject> rightsObjects = driverRightsObject.queryDriverRights(request.getRightsTypes());
            response.rights=rightsObjects.stream().map(rightsObject -> {
                return convert2DriverRightsDTO(rightsObject);
            }).collect(Collectors.toList());

            LevelObject levelObject = driverRightsObject.queryDriverLevel();
            if(Objects.isNull(levelObject) || Objects.isNull(levelObject.drivLevel())){
                return;
            }
            response.driverLevel=convert2DriverLevelDTO(levelObject);
        }

        private DriverLevelDTO convert2DriverLevelDTO(LevelObject levelObject) {
            DriverLevelDTO driverLevelDTO = new DriverLevelDTO();
            driverLevelDTO.setLevelConfigId(levelObject.levelConfigId());
            driverLevelDTO.setDriverId(levelObject.drivId());
            driverLevelDTO.setDate(levelObject.monthIdx());
            driverLevelDTO.setCityId(levelObject.cityId());
            driverLevelDTO.setDriverLevel(levelObject.drivLevel().getCode());
            driverLevelDTO.setLevelName(levelObject.levelName());
            return driverLevelDTO;
        }

        private DriverRightsDTO convert2DriverRightsDTO(RightsObject rightsObject) {
            DriverRightsDTO driverRightsDTO = new DriverRightsDTO();
            driverRightsDTO.setId(rightsObject.id());
            driverRightsDTO.setRightsConfigid(rightsObject.rigthsConfigId());
            driverRightsDTO.setRightsType(rightsObject.rigthsType().getCode());
            driverRightsDTO.setRightsStatus(rightsObject.rightsStatus().getStatus());
            driverRightsDTO.setRightsName(rightsObject.rightsName());
            driverRightsDTO.setRightsDesc(rightsObject.rightsDesc());
            driverRightsDTO.setDate(rightsObject.monthIdx());
            driverRightsDTO.setRightsStartTime(rightsObject.rightsStratTime());
            driverRightsDTO.setRightsEndTime(rightsObject.rightsEndTime());
            driverRightsDTO.setDeliveryTime(rightsObject.rightsIssueTime());
            driverRightsDTO.setUseLimit(rightsObject.useLimit());
            driverRightsDTO.setUseCount(rightsObject.usedCount());
            driverRightsDTO.setWelfareLimit(rightsObject.welfareLimit());
            driverRightsDTO.setWelfareUsed(rightsObject.welfareUsed());
            driverRightsDTO.setReassignUsedByWeek(rightsObject.reassignUsedByWeek());
            driverRightsDTO.setVacationLimit(rightsObject.vacationLimit());
            driverRightsDTO.setVacationUsed(rightsObject.vacationUsed());
            return driverRightsDTO;
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(driverRightsObject);
        }
    }
}
