package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.redis.NoticeRedisLogic;
import com.ctrip.dcs.driver.domain.application.redis.obj.NoticeContentDTO;
import com.ctrip.dcs.driver.domain.application.redis.obj.NoticeIdDTO;
import com.ctrip.dcs.driver.domain.application.service.NoticeService;
import com.ctrip.dcs.driver.domain.application.util.LanguageUtil;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverNoticeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverNoticeRecordTargetDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverNoticeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.notice.NoticeUserModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.GuideUtils;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.message.api.PushMessageRequestType;
import com.ctrip.dcs.driver.message.api.PushMessageResponseType;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.language.LanguageContext;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * Created by <AUTHOR> on 2023/3/5 20:20
 */
@Component
public class NoticeServiceImpl implements NoticeService {

  private static final Logger LOGGER = LoggerFactory.getLogger(NoticeServiceImpl.class);

  @Autowired
  DriverNoticeRecordDao driverNoticeRecordDao;

  @Autowired
  DriverNoticeRecordTargetDao driverNoticeRecordTargetDao;

  @Autowired
  NoticeRedisLogic noticeRedisLogic;

  @Autowired
  private DriverMessageServiceProxy driverMessageServiceProxy;

  @Autowired
  @Qualifier("CommonThreadPool")
  private ExecutorService executorService;

  @Autowired
  CityRepository cityRepository;

  /**
   * 指定具体司导
   */
  private static final int TARGET_ID_ASSIGN = 1;

  public static final int MSG_SENDING = 2;
  public static final int MSG_SENT = 3;
  public static final int MSG_SEND_FAIL = 4;

  @Override
  public NoticeModel queryNotice(NoticeUserModel user) {
    List<NoticeIdDTO> idDTOS = noticeRedisLogic.getValidNotice();
    if (CollectionUtils.isNotEmpty(idDTOS)) {
      long matchNoticeId = 0L;
      String targetId = targetId(user.getUserType(), user.getUserId());
      for (NoticeIdDTO idDTO : idDTOS) {
        if (LocalDateTimeUtils.isNowInDate(idDTO.getStartDate(), idDTO.getEndDate())) {
          if (idDTO.getIdType() == TARGET_ID_ASSIGN) {
            if (noticeRedisLogic.isMyNotice(idDTO.getNoticeId(), targetId)) {
              matchNoticeId = idDTO.getNoticeId();
              break;
            }
          } else {
            if (noticeConditionMatch(idDTO, user)) {
              matchNoticeId = idDTO.getNoticeId();
              break;
            }
          }
        }
      }
      if(matchNoticeId > 0L){
        NoticeContentDTO contentDTO = noticeRedisLogic.getNoticeContent(matchNoticeId);
        if (contentDTO != null) {
          NoticeModel noticeModel = convert(contentDTO);
          if (noticeRedisLogic.isNeedSendMail(matchNoticeId, targetId)) {
            asyncPushMail(noticeModel, targetId);
          }
          return noticeModel;
        } else {
          //不应该为null
          LOGGER.error("queryNotice", String.format("notice content not found:%s", matchNoticeId));
        }
      }
    }
    return NoticeModel.newBuilder().withExist(false).build();
  }

  @Override
  public Void noticeUpdate(long noticeId) {
    try {
      DriverNoticeRecordPO noticeRecordPO = driverNoticeRecordDao.findNoticeById(noticeId);
      if (noticeRecordPO != null) {
        int updateResult = driverNoticeRecordDao.updateSendStatus(MSG_SENDING, noticeId);
        boolean success;
        if (updateResult > 0) {
          success = noticeRedisLogic.updateNoticeContent(noticeRecordPO);
          List<DriverNoticeRecordPO> noticeRecords = driverNoticeRecordDao.queryActiveNotices(new Date());
          success = success && noticeRedisLogic.updateValidNotice(noticeRecords);
          //非下线状态，且是指定id公告，缓存公告目标id
          if (noticeRecordPO.getNoticeStatus() != 0 && noticeRecordPO.getNoticeConditionType() == TARGET_ID_ASSIGN) {
            List<Long> targetIds = driverNoticeRecordTargetDao.getNoticeDriverIds(noticeId);
            success = success && noticeRedisLogic.cacheNoticeTarget(noticeId, targetIds, noticeRecordPO.getEndDate());
          }
          if (success) {
            driverNoticeRecordDao.updateSendStatus(MSG_SENT, noticeId);
          } else {
            driverNoticeRecordDao.updateSendStatus(MSG_SEND_FAIL, noticeId);
          }
        }
      }
    } catch (Exception e) {
      driverNoticeRecordDao.updateSendStatus(MSG_SEND_FAIL, noticeId);
      LOGGER.error(e);
    }
    return null;
  }

  private void asyncPushMail(NoticeModel noticeModel, String targetId) {
    executorService.submit(() -> {
      PushMessageRequestType requestType = new PushMessageRequestType();
      Map<String, String> data = new HashMap<>();
      data.put("recordId", noticeModel.getId());
      data.put("title", noticeModel.getTitle());
      data.put("content", noticeModel.getContent());
      data.put("mailUri", noticeModel.getUri());
      data.put("pushType", "0");
      requestType.setTemplateId("4998");
      requestType.setData(data);
      if(GuideUtils.isGuide(targetId)){
        requestType.setGuideIds(Lists.newArrayList(GuideUtils.guideId(targetId)));
      }else{
        requestType.setDriverIds(Lists.newArrayList(Long.parseLong(targetId)));
      }
      PushMessageResponseType responseType = driverMessageServiceProxy.pushMessage(requestType);
      if (Objects.nonNull(responseType) && responseType.getResponseResult().isSuccess()) {
        //成功推送站内信，记录redis
        noticeRedisLogic.markMailSendSucess(noticeModel,targetId);
      }
    });
  }

  /**
   * 推送条件检查
   * 1	司机
   * 2	向导
   * 31	司导，当前司机		司机产线 + 司导
   * 32	司导，支持包车		包车产线 + 司导
   * 33	司导，不支持包车	              司导
   * */
  private boolean noticeConditionMatch(NoticeIdDTO idDTO, NoticeUserModel userModel) {
    if("0".equals(StringUtils.defaultIfBlank(idDTO.getUserType(), "0"))){
      if (StringUtils.isNotBlank(idDTO.getProductType()) && Arrays.stream(idDTO.getProductType().split(",")).anyMatch(userModel.getProductType()::contains)) {
        return noticeConditionMatchDetail(idDTO, userModel);
      }
    } else {
      //工作台新配置(1:司机2:向导3:司导）
      switch (idDTO.getUserType()){
        case "1":
          if(userModel.getUserType() == 1 || userModel.getUserType() == 31) {
            return noticeConditionMatchDetail(idDTO, userModel);
          }
          break;
        case "2":
          if(userModel.getUserType() == 2) {
            return noticeConditionMatchDetail(idDTO, userModel);
          }
          break;
        case "3":
          if(userModel.getUserType() > 30) {
            return noticeConditionMatchDetail(idDTO, userModel);
          }
          break;
        default:
          break;
      }
    }
    return false;
  }

  /**
   * 条件检查：司机状态、车型、国家、城市
   * */
  private boolean noticeConditionMatchDetail(NoticeIdDTO idDTO, NoticeUserModel userModel) {
      int status = (int) Math.pow(2, userModel.getStatus());
      if ((idDTO.getDriverStatus() & status) == status) {
        if (isVehicleMatch(idDTO.getVehicleTypes(), userModel)) {
          City city = cityRepository.findOne(userModel.getCityId());
          if (idDTO.getCountryIds().contains(String.format(",%s,", city.getCountryId()))) {
            if (StringUtils.isEmpty(idDTO.getCityIds())
                || idDTO.getCityIds().contains(String.format(",%s,", userModel.getCityId()))) {
              return true;
            }
          }
        }
      }
    return false;
  }

  private boolean isVehicleMatch(String noticeVehicleTypes, NoticeUserModel userModel) {
    if (userModel.getUserType() == GuideUtils.GUIDE_TYPE) {
      //向导不关联车，忽略车型条件参数
      return true;
    } else {
      return StringUtils.isEmpty(noticeVehicleTypes)
          || noticeVehicleTypes.contains(String.valueOf(userModel.getVehicleType()));
    }
  }

  private NoticeModel convert(NoticeContentDTO contentDTO) {
    boolean isMultiLanguage = StringUtils.isNotEmpty(contentDTO.getContentEn()) &&
            LanguageUtil.isMultiLanguage(LanguageContext.getLanguage().getLocaleCode());
    return NoticeModel.newBuilder().withExist(true).withId(String.valueOf(contentDTO.getNoticeId()))
        .withTitle(isMultiLanguage ? contentDTO.getTitleEn() : contentDTO.getTitle())
        .withContent(isMultiLanguage ? contentDTO.getContentEn() : contentDTO.getContent())
        .withUri(StringUtils.isNotEmpty(contentDTO.getUri()) ? ("page_" + contentDTO.getUri()) : "")
        .withStatus(contentDTO.getNoticeType())
        .withStartDate(contentDTO.getStartDate())
        .withEndDate(contentDTO.getEndDate()).build();
  }

  private String targetId(int userType, long userId) {
    return GuideUtils.isGuide(userId, userType) ? GuideUtils.guideId(userId) : String.valueOf(userId);
  }
}
