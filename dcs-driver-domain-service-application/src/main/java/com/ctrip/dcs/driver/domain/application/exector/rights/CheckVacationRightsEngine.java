package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.rights.CheckVacationRightsRequestType;
import com.ctrip.dcs.driver.domain.rights.CheckVacationRightsResponseType;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class CheckVacationRightsEngine extends ShoppingExecutor<CheckVacationRightsRequestType, CheckVacationRightsResponseType>
        implements Validator<CheckVacationRightsRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;


    @Override
    public CheckVacationRightsResponseType execute(CheckVacationRightsRequestType request) {
        CheckVacationRightsResponseType response = new CheckVacationRightsResponseType();
        CheckVacationRightsEngine.Executor executor = new CheckVacationRightsEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<CheckVacationRightsRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("cityId").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final CheckVacationRightsEngine owner;
        private final CheckVacationRightsResponseType response;
        private final CheckVacationRightsRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(CheckVacationRightsRequestType request, CheckVacationRightsResponseType response, CheckVacationRightsEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;

            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory, request.driverId, "");
        }

        @Override
        protected void buildResponse() {
            response.canUse = this.owner.domainBeanFactory.driverLevelGreyConfig().canUseVacationRights(request.cityId);
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(driverRightsObject);
        }
    }

}
