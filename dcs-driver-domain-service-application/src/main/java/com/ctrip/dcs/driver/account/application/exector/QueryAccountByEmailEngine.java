package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByEmailRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByEmailResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTag(tagKeys= {"email"})
public class QueryAccountByEmailEngine extends ShoppingExecutor<QueryAccountByEmailRequestType, QueryAccountByEmailResponseType> implements Validator<QueryAccountByEmailRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public QueryAccountByEmailResponseType execute(QueryAccountByEmailRequestType request) {


        QueryAccountByEmailResponseType resp = new QueryAccountByEmailResponseType();

        AccountInfoDTO account = accountService.getAccountInfoByEmail(request.getEmail());
        if (account != null) {
            resp.setAccountDetail(AccountDetailConvert.convert(account));
        }
        Cat.logEvent(CatEventType.QUERY_BY_EMAIL_RESULT, account != null ? "1" : "0");
        DriverMetricUtil.metricUdlIsValid(resp.getAccountDetail());
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public Boolean isEmptyResult(QueryAccountByEmailResponseType resp) {
        return !(resp!=null&&resp.getAccountDetail()!=null&&resp.getAccountDetail().getUid()!=null);
    }
}
