package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.common.DriverLevelHelper;
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService;
import com.ctrip.dcs.driver.domain.application.service.RightsRepoService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class RightsRepoServiceImpl implements RightsRepoService {

    @Autowired
    RightsDBDataService rightsDBDataService;
    @Autowired
    RightsRedisLogic rightsRedisLogic;
    @Autowired
    DriverLevelHelper driverLevelHelper;

    @Override
    public List<RightsModel> queryDriverRights(long driverId, List<Integer> rightsTypes, String monthIdx) {
        List<RightsModel> drivRightsCache = rightsRedisLogic.getDrivRights(driverId, monthIdx);
        if (CollectionUtils.isNotEmpty(drivRightsCache)) {
            return drivRightsCache.stream().filter(o -> rightsTypes.contains(o.getRightsType())).collect(Collectors.toList());
        }
        List<RightsModel> rightsModelsDb = rightsDBDataService.queryDriverRights(driverId, monthIdx);
        if (CollectionUtils.isNotEmpty(rightsModelsDb)) {
            rightsRedisLogic.saveDrivRights(rightsModelsDb, driverId, monthIdx);
            rightsModelsDb = rightsModelsDb.stream().filter(o -> rightsTypes.contains(o.getRightsType())).collect(Collectors.toList());
        }
        return rightsModelsDb;
    }

    @Override
    public List<RightsRecordModel> queryDriverRecords(long driverId, String useStartDate, String useEndDate, List<Integer> rightsTypes) {
        return rightsDBDataService.queryDriverRecords(driverId, useStartDate, useEndDate, rightsTypes);
    }

    @Override
    public LevelModel queryDriverLevel(long driverId, String monthIdx) {
        LevelModel drivLevelCache = rightsRedisLogic.getDrivLevel(driverId, monthIdx);
        if (Objects.nonNull(drivLevelCache)) {
            drivLevelCache.setLevelName(driverLevelHelper.getDriverLevelName(drivLevelCache.getDrivLevel(), drivLevelCache.getLevelName()));
            return drivLevelCache;
        }

        LevelModel drivLevelDb = rightsDBDataService.queryDriverLevel(driverId, monthIdx);
        if (Objects.nonNull(drivLevelDb)) {
            drivLevelDb.setLevelName(driverLevelHelper.getDriverLevelName(drivLevelDb.getDrivLevel(), drivLevelDb.getLevelName()));
            rightsRedisLogic.saveDrivLevel(drivLevelDb, driverId, monthIdx);
        }
        return drivLevelDb;
    }

    @Override
    public void saveRightsRecords(DrivRightsRecordPO recordPO) {
        rightsDBDataService.saveRightsRecords(recordPO);
    }

    @Override
    public void updateDriverRights(DrivRightsPO rightsPO) {
        rightsDBDataService.updateDriverRights(rightsPO);
    }

    @Override
    public List<RightsRecordModel> queryDriverRecordsById(Long id) {
        return rightsDBDataService.queryDriverRecordsById(id);
    }
}
