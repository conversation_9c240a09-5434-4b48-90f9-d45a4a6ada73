package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.helper.DriverDeviceInfoRefresher;
import com.ctrip.dcs.driver.domain.application.service.DriverAccountService;
import com.ctrip.dcs.driver.domain.application.service.DriverDeviceInfoService;
import com.ctrip.dcs.driver.domain.device.DriverDeviceInfoDTO;
import com.ctrip.dcs.driver.domain.infrastructure.model.device.DriverDeviceInfoModel;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.annontation.QmqLogTagPair;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@Component
public class DriverDeviceInfoUpdateListener implements MessageListener {
    private static final Logger logger = LoggerFactory.getLogger(DriverDeviceInfoUpdateListener.class);
    @Autowired
    private DriverDeviceInfoService driverDeviceInfoService;
    @Autowired
    private DriverAccountService driverAccountService;

    @Autowired
    private DriverDeviceInfoRefresher driverDeviceInfoRefresher;

    @Override
    @QmqLogTagPair(key = "driverId")
    @QmqConsumer(prefix = "dcs.driver.device.info.update", consumerGroup = "*********")
    public void onMessage(Message message) {
        try {
            Long driverId = message.getLongProperty("driverId");
            String deviceInfo = message.getStringProperty("deviceInfo");
            DriverDeviceInfoDTO driverDeviceInfo = JacksonUtil.deserialize(deviceInfo, DriverDeviceInfoDTO.class);
            if (Objects.isNull(driverId) || Objects.isNull(driverDeviceInfo)) {
                return;
            }
            if (driverDeviceInfoRefresher.allowRefresh(driverDeviceInfo.getDriverId(), driverDeviceInfo.getAppId())) {
                this.refresh(driverDeviceInfo);
            }
        } catch (Exception e) {
            logger.error("DriverDeviceInfoUpdateListener error", e);
        }
    }

    private void refresh(DriverDeviceInfoDTO driverDeviceInfo) {
        Long driverId = driverDeviceInfo.getDriverId();
        // uid为空，可能是老版本，需查账户获取uid
        if (StringUtils.isBlank(driverDeviceInfo.getUid())) {
            String uid = driverAccountService.getUid(driverId);
            Cat.logEvent(CatEventType.DEVICE_INFO_UPDATE_UID, StringUtils.isNotBlank(uid) ? "notNull" : "null");
            driverDeviceInfo.setUid(uid);
        }
        DriverDeviceInfoModel deviceInfoModel = convert(driverId, driverDeviceInfo);
        driverDeviceInfoService.save(deviceInfoModel);
    }

    private DriverDeviceInfoModel convert(Long driverId, DriverDeviceInfoDTO driverDeviceInfo) {
        DriverDeviceInfoModel driverDeviceInfoModel = new DriverDeviceInfoModel();
        driverDeviceInfoModel.setDriverId(driverId);
        driverDeviceInfoModel.setUid(driverDeviceInfo.getUid());
        driverDeviceInfoModel.setAppId(driverDeviceInfo.getAppId());
        driverDeviceInfoModel.setCid(driverDeviceInfo.getCid());
        driverDeviceInfoModel.setAppVer(driverDeviceInfo.getAppVersion());
        driverDeviceInfoModel.setRnVer(driverDeviceInfo.getRnVersion());
        driverDeviceInfoModel.setOs(driverDeviceInfo.getOsType());
        driverDeviceInfoModel.setOsVer(driverDeviceInfo.getOsVersion());
        driverDeviceInfoModel.setLocal(driverDeviceInfo.getLocal());
        driverDeviceInfoModel.setLoginAccount(driverDeviceInfo.getLoginAccount());
        driverDeviceInfoModel.setLoginType(driverDeviceInfo.getLoginType());
        if (StringUtils.isNotBlank(driverDeviceInfo.getActiveTime())) {
            driverDeviceInfoModel.setActiveTime(LocalDateTime.parse(driverDeviceInfo.getActiveTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        if (StringUtils.isNotBlank(driverDeviceInfo.getLoginTime())) {
            driverDeviceInfoModel.setLoginTime(LocalDateTime.parse(driverDeviceInfo.getLoginTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        return driverDeviceInfoModel;
    }

}
