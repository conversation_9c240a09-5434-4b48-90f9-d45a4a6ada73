package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exam.ApplyExamObjectImpl;
import com.ctrip.dcs.driver.domain.application.http.HttpClient;
import com.ctrip.dcs.driver.domain.application.http.constant.ExamRequestEnum;
import com.ctrip.dcs.driver.domain.application.http.request.*;
import com.ctrip.dcs.driver.domain.application.http.response.*;
import com.ctrip.dcs.driver.domain.infrastructure.constant.CallExamSuccessEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamApplyResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamIsPassedEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideApplyExamDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideExamScoreDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideApplyExamPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideExamScorePO;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideInfoModel;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class GuideExamScheduleLogic {

  @Autowired
  DomainBeanFactory domainBeanFactory;

  @Autowired
  GuideExamScoreDao guideExamScoreDao;

  @Autowired
  GuideApplyExamDao guideApplyExamDao;

  @Autowired
  LockService lockService;

  public static final Integer QUERYDB_PAGE_SIZE = 200;

  private final static int PAGE_SIZE = 1000;

  public static final String PATTERN_DATE_TIME = "yyyyMMddHHmmss";

  /**
   * 司导考试记录缓存预热
   *
   * @param parameter
   */
  @QSchedule("dcs.guide.exam.cache.preheat")
  public void preheatGuideTagExamCache(Parameter parameter) {
    //此方法要在执行任务的入口方法里调用
    TaskMonitor monitor = TaskHolder.getKeeper();
    Logger logger = monitor.getLogger();
    Pair<Long, Long> minAndMaxGuideId = getMinAndMaxGuideId(parameter, logger, monitor);
    if(minAndMaxGuideId == null){
      return;
    }
    Long minId = minAndMaxGuideId.getLeft();
    Long maxId = minAndMaxGuideId.getRight();
    logger.info(String.format("dcs.guide.exam.cache.preheat start minId:%s,maxId:%s", minId, maxId));
    while(maxId >= minId){
      List<GuideInfoModel> guideInfoModels = domainBeanFactory.guideInfoDBDataService()
          .queryGuideInfoListByGuideId(minId, minId + QUERYDB_PAGE_SIZE);
      if(CollectionUtils.isEmpty(guideInfoModels)){
        minId = minId + QUERYDB_PAGE_SIZE;
        continue;
      }
      for (GuideInfoModel guideInfoModel:guideInfoModels){
        lockService.executeInLock(String.format("dcs_guide_tag_exam:%s", guideInfoModel.getGuideId()),1000,
                () -> domainBeanFactory.guideTagExamService().queryDBAndSaveCacheGuideTagExam(guideInfoModel.getGuideId()));
      }
      minId = minId + QUERYDB_PAGE_SIZE;
    }
    logger.info("[dcs.guide.exam.cache.preheat] end");
  }

  private Pair<Long,Long> getMinAndMaxGuideId(Parameter parameter,Logger logger,TaskMonitor monitor){
    if(parameter!=null){
      String strMinId = parameter.getString("minGuideId");
      String strMaxId = parameter.getString("maxGuideId");
      if(StringUtils.isNotEmpty(strMinId) && StringUtils.isNotEmpty(strMaxId)){
        Long longMinId = Long.parseLong(strMinId);
        Long longMaxId = Long.parseLong(strMaxId);
        return Pair.of(longMinId, longMaxId);
      }
    }
    GuideInfoModel guideInfoModel = domainBeanFactory.guideInfoDBDataService().queryMaxGuideId();
    if(guideInfoModel == null){
      logger.warn("dcs.guide.exam.cache.preheat maxGuideIdModel is null");
      monitor.autoAck(false);
      monitor.fail(new Throwable("dcs.guide.exam.cache.preheat maxGuideIdModel is null"));
      return null;
    }
    return Pair.of(0L, guideInfoModel.getGuideId());
  }

  /**
   * 根据主部门查询部门信息
   *
   * @param parameter
   */
  @QSchedule("dcs.guide.exam.query.dept.list")
  public void queryExamDeptList(Parameter parameter) {
    //此方法要在执行任务的入口方法里调用
    TaskMonitor monitor = TaskHolder.getKeeper();
    Logger logger = monitor.getLogger();
    HttpClient httpClient = domainBeanFactory.httpClient();
    String rootDeptId = domainBeanFactory.examInterfaceConfig().getRootDeptId();
    ExamGetDeptInfoRequest request = new ExamGetDeptInfoRequest();
    request.setCodes(new String[]{rootDeptId});
    request.setCurrentPage(1);
    request.setExFields(new String[]{"ContainsChild"});

    String accessToken = getAccessToken();
    if(StringUtils.isEmpty(accessToken)){
      return;
    }

    String requestUrl = getRequestExamUrl(ExamRequestEnum.QUERY_DEPT_INFO,accessToken);

    String responseStr = httpClient.post(requestUrl, JacksonUtil.serialize(request));
    if(StringUtils.isEmpty(responseStr)){
      logger.warn("eexam interface response empty");
      return;
    }
    ExamDeptResponse response = JacksonUtil.deserialize(responseStr, ExamDeptResponse.class);
    if(response != null && CollectionUtils.isNotEmpty(response.getData())){
      List<ExamDeptResData> datas = response.getData();
      Map<String, FromGuideExamInfo> validityExamMap =
          domainBeanFactory.guideExamConfig().queryValidityExamMap();
      List<String> examDeptIds =
          datas.stream()
              .filter(data -> validityExamMap.containsKey(data.getCode()))
              .map(data -> data.getCode())
              .collect(Collectors.toList());
      domainBeanFactory.examRedisLogic().saveExamDeptList(JacksonUtil.serialize(examDeptIds));
      return;
    }
    logger.warn("dcs.guide.exam.query.dept.list httpresponse empty");
    monitor.autoAck(false);
    monitor.fail(new Throwable("dcs.guide.exam.query.dept.list httpresponse empty"));
  }

  private String getAccessToken() {
    String accessToken = domainBeanFactory.examRedisLogic().getAccessToken();
    if(StringUtils.isEmpty(accessToken)){
      accessToken = queryAccessToken();
    }
    return accessToken;
  }

  /**
   * 调用报考接口失败数据重试
   *
   * @param parameter
   */
  @QSchedule("dcs.guide.exam.retry.apply.exam")
  public void retryApplyExam(Parameter parameter) {
    //此方法要在执行任务的入口方法里调用
    TaskMonitor monitor = TaskHolder.getKeeper();
    Logger logger = monitor.getLogger();
    Long count = domainBeanFactory.guideApplyExamDBDataService().countCallExamFailed();
    if(count == 0){
      return;
    }
    AtomicInteger index = new AtomicInteger();
    boolean flag = true;
    while(flag){
      List<GuideApplyExamModel> guideApplyExamModels =
          domainBeanFactory.guideApplyExamDBDataService().queryCallExamFailedRecords(Math.multiplyExact(index.get(), PAGE_SIZE), PAGE_SIZE);
      if(CollectionUtils.isEmpty(guideApplyExamModels)){
        return;
      }
      if(guideApplyExamModels.size() < PAGE_SIZE){
        flag = false;
      }
      Map<String, FromGuideExamInfo> validityExamMap =
          domainBeanFactory.guideExamConfig().queryValidityExamMap();
      List<GuideApplyExamModel> needRetryList = guideApplyExamModels.stream()
          .filter(data -> validityExamMap.containsKey(data.getApplySubject()))
          .collect(Collectors.toList());
      if(CollectionUtils.isEmpty(needRetryList)){
        return;
      }
      String accessToken = getAccessToken();
      if(StringUtils.isEmpty(accessToken)){
        return;
      }
      HttpClient httpClient = domainBeanFactory.httpClient();

      String requestUrl = getRequestExamUrl(ExamRequestEnum.CHANGE_USER_INFO, accessToken);

      needRetryList.stream().forEach(needRetry ->{
        ExamChangeUserRequest request = new ExamChangeUserRequest();
        request.setFields(new String[]{"id","user_name","dept_id"});
        ExamChangeUserRequestData requestData = new ExamChangeUserRequestData();
        requestData.setId(needRetry.getExamAccountId());
        requestData.setDept_id(needRetry.getApplySubject());
        requestData.setUser_name(needRetry.getGuideName());
        request.setData(requestData);
        try{
          String response = httpClient.post(requestUrl,
              JacksonUtil.serialize(request));
          ExamCommonResponse<ExamChangeUserResData> deserialize = JacksonUtil.deserialize(response,
              new TypeToken<ExamCommonResponse<ExamChangeUserResData>>() {
              }.getType());
          if (deserialize != null && deserialize.getData() != null && deserialize.getData().isSuccess()) {
            needRetry.setCallExamSuccess(CallExamSuccessEnum.CALL_SUCCESS.getCode());
            lockService.executeInLock(
                String.format("dcs_guide_apply_exam:%s_%s", needRetry.getGuideId(),
                    needRetry.getApplySubject()), 1000, () -> new ApplyExamObjectImpl(domainBeanFactory,needRetry).saveApplyExam());
          }
        }catch (Exception e){
          logger.warn("failed to retry apply exam ", e);
        }
      });
      index.incrementAndGet();
    }
  }

  /**
   * 查询更新用户报名结果
   *
   * @param parameter
   */
  @QSchedule("dcs.guide.exam.query.user.info")
  public void queryUserInfo(Parameter parameter) {
    //此方法要在执行任务的入口方法里调用
    TaskMonitor monitor = TaskHolder.getKeeper();
    Logger logger = monitor.getLogger();
    AtomicInteger index = new AtomicInteger();
    boolean flag = true;
    while(flag){
      List<GuideApplyExamModel> guideApplyExamModels =
          domainBeanFactory.guideApplyExamDBDataService().queryApplyFailedRecords(Math.multiplyExact(index.get(), PAGE_SIZE), PAGE_SIZE);
      if(CollectionUtils.isEmpty(guideApplyExamModels)){
        return;
      }
      if(guideApplyExamModels.size()<PAGE_SIZE){
        flag = false;
      }
      Map<String, FromGuideExamInfo> validityExamMap =
          domainBeanFactory.guideExamConfig().queryValidityExamMap();
      List<GuideApplyExamModel> needQueryList = guideApplyExamModels.stream()
          .filter(data -> validityExamMap.containsKey(data.getApplySubject()))
          .collect(Collectors.toList());
      if(CollectionUtils.isEmpty(needQueryList)){
        return;
      }
      String accessToken = getAccessToken();
      if(StringUtils.isEmpty(accessToken)){
        return;
      }
      HttpClient httpClient = domainBeanFactory.httpClient();
      String requestUrl = getRequestExamUrl(ExamRequestEnum.QUERY_USER_INFO,accessToken);

      needQueryList.stream().forEach(needQuery->{
        ExamGetUserInfoRequest request = new ExamGetUserInfoRequest();
        request.setUser_id(needQuery.getExamAccountId());
        try{
          String response = httpClient.post(requestUrl, JacksonUtil.serialize(request));
          ExamListCommonResponse<ExamUserInfoResData> deserialize = JacksonUtil.deserialize(response,
              new TypeToken<ExamListCommonResponse<ExamUserInfoResData>>() {}.getType());
          if (deserialize != null && CollectionUtils.isNotEmpty(deserialize.getData())){
            ExamUserInfoResData examUserInfoResData = deserialize.getData().get(0);
            if(needQuery.getApplySubject().equals(examUserInfoResData.getDept_code())){
              needQuery.setApplyResult(ExamApplyResultEnum.APPLY_SUCCESS.getCode());
              lockService.executeInLock(
                  String.format("dcs_guide_apply_exam:%s_%s", needQuery.getGuideId(),
                      needQuery.getApplySubject()), 1000, () -> new ApplyExamObjectImpl(domainBeanFactory,needQuery).saveApplyExam());
            }
          }
        }catch (Exception e){
          logger.warn("failed to query user info", e);
        }
      });
      index.incrementAndGet();
    }

  }

  /**
   * 查询更新用户考试记录信息
   * @param parameter
   */
  @QSchedule("dcs.guide.exam.query.exam.score")
  public void queryExamScoreInfo(Parameter parameter) {
    //此方法要在执行任务的入口方法里调用
    TaskMonitor monitor = TaskHolder.getKeeper();
    Logger logger = monitor.getLogger();
    List<FromGuideExamInfo> fromGuideExamInfos =
        domainBeanFactory.guideExamConfig().getQueryExamScoreGuideExamInfoList();
    Map<String, FromGuideExamInfo> validityExamMap = fromGuideExamInfos.stream().collect(
        Collectors.toMap(FromGuideExamInfo::getDeptId, fromGuideExamInfo -> fromGuideExamInfo));
    String minStartTime =
        fromGuideExamInfos.stream().min(Comparator.comparing(FromGuideExamInfo::getStartTimeSecond))
            .get().getStartTime();
    Timestamp minStartTimestamp = Timestamp.valueOf(minStartTime);
    Long minId = null;
    Long maxId = null;
    boolean retryFlag = false;
    Long startMinId = null;
    String beginDate = null;
    String endDate = null;
    if(parameter != null){
      String strMinId = parameter.getString("minId");
      String strMaxId = parameter.getString("maxId");
      beginDate = parameter.getString("beginDate");
      endDate = parameter.getString("endDate");
      if(StringUtils.isNotEmpty(strMinId)){
        minId = Long.parseLong(strMinId);
        startMinId = minId;
        retryFlag = true;
      }
      if(StringUtils.isNotEmpty(strMaxId)){
        maxId = Long.parseLong(strMaxId);
      }
    }
    minId = minId == null ? domainBeanFactory.guideApplyExamDBDataService().queryMinId(minStartTimestamp):minId;
    maxId = maxId == null ? domainBeanFactory.guideApplyExamDBDataService().queryMaxId(minStartTimestamp):maxId;
    if(minId == null || maxId == null
        || minId.longValue()>maxId.longValue()){
      return;
    }
    String accessToken = getAccessToken();
    if(StringUtils.isEmpty(accessToken)){
      logger.warn(String.format("query exam score accesstoken empty minId:%s,maxId:%s", minId, maxId));
      monitor.autoAck(false);
      monitor.fail(new Throwable(String.format("query exam score accesstoken empty minId:%s,maxId:%s", minId, maxId)));
      return;
    }
    HttpClient httpClient = domainBeanFactory.httpClient();
    String requestUrl = getRequestExamUrl(ExamRequestEnum.QUERY_USER_EXAM_INFO,accessToken);
    logger.info(String.format("[dcs.guide.exam.query.exam.score] start minId:%s,maxId:%s", minId, maxId));
    List<GuideApplyExamModel> guideApplyExamModels = null;
    while(maxId >= minId){
      guideApplyExamModels =
          domainBeanFactory.guideApplyExamDBDataService()
              .queryUpassedBetweenIds(minId, minId+QUERYDB_PAGE_SIZE);
      if(CollectionUtils.isEmpty(guideApplyExamModels)){
        minId = minId + QUERYDB_PAGE_SIZE;
        continue;
      }
      List<GuideApplyExamModel> needQueryList = guideApplyExamModels.stream()
          .filter(data -> validityExamMap.containsKey(data.getApplySubject()))
          .collect(Collectors.toList());
      if(CollectionUtils.isEmpty(needQueryList)){
        minId = minId + QUERYDB_PAGE_SIZE;
        continue;
      }
      Map<String, List<GuideApplyExamModel>> applySubjectMap = needQueryList.stream()
          .collect(Collectors.groupingBy(GuideApplyExamModel::getApplySubject));
      for(String applySubject:applySubjectMap.keySet()){
        List<GuideApplyExamModel> userApplyExamModels = applySubjectMap.get(applySubject);
        ExamGetUserExamInfoRequest request =
            buildExamGetUserExamInfoRequest(applySubject, validityExamMap, applySubjectMap, beginDate, endDate);
        ExamListCommonResponse<ExamScoreInfoData> deserialize =
            getExamScoreInfoHttpResponse(logger,httpClient, requestUrl,request);
        if(deserialize == null){
          logger.warn(String.format("query exam score httpresponse null minId:%s,maxId:%s", minId, maxId));
          monitor.autoAck(false);
          monitor.fail(new Throwable(String.format("query exam score httpresponse null minId:%s,maxId:%s", minId, maxId)));
          return;
        }
        if(!deserialize.isSuccess()){
          logger.warn(String.format("query exam score httpresponse unsuccess minId:%s,maxId:%s", minId, maxId));
          monitor.autoAck(false);
          monitor.fail(new Throwable(String.format("query exam score httpresponse unsuccess minId:%s,maxId:%s", minId, maxId)));
          return;
        }
        if(CollectionUtils.isEmpty(deserialize.getData())){
          continue;
        }
        List<ExamScoreInfoData> allExamScoreList = deserialize.getData();
        String examId = validityExamMap.get(applySubject).getExamId();
        List<ExamScoreInfoData> guideExamScoreList = allExamScoreList.stream()
            .filter(e -> e.getExam_id().equals(examId))
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(guideExamScoreList)){
          continue;
        }
        Map<String, List<ExamScoreInfoData>> examScoreMap =
            guideExamScoreList.stream().collect(Collectors.groupingBy(ExamScoreInfoData::getUser_id));
        boolean needCheckDataFlag = retryFlag && (startMinId != null  && minId.longValue() == startMinId.longValue());
        handleUserExamScoreRecords(applySubject, examScoreMap,userApplyExamModels,needCheckDataFlag);
      }
      minId = minId + QUERYDB_PAGE_SIZE;
    }
    logger.info("[dcs.guide.exam.query.exam.score] end");
  }

  /**
   * 根据用户信息查询更新用户考试记录信息
   * @param parameter
   */
  @QSchedule("dcs.guide.exam.query.exam.score.by.user")
  public void queryExamScoreInfoByUser(Parameter parameter) {
    //此方法要在执行任务的入口方法里调用
    TaskMonitor monitor = TaskHolder.getKeeper();
    Logger logger = monitor.getLogger();
    if(parameter == null){
      return;
    }
    String examAccountId = parameter.getString("examAccountId");
    String applySubject = parameter.getString("applySubject");
    String beginDate = parameter.getString("beginDate");
    String endDate = parameter.getString("endDate");
    if(StringUtils.isEmpty(examAccountId) || StringUtils.isEmpty(applySubject)
     ||StringUtils.isEmpty(beginDate) || StringUtils.isEmpty(endDate)){
      return;
    }
    ExamGetUserExamInfoRequest request = new ExamGetUserExamInfoRequest();
    request.setUser_list(new String[]{examAccountId});
    request.setDept_code_list(new String[]{applySubject});
    request.setBegin_date(beginDate);
    request.setEnd_date(endDate);
    String accessToken = getAccessToken();
    if(StringUtils.isEmpty(accessToken)){
      return;
    }
    HttpClient httpClient = domainBeanFactory.httpClient();
    String requestUrl = getRequestExamUrl(ExamRequestEnum.QUERY_USER_EXAM_INFO,accessToken);

    String responseStr = httpClient.post(requestUrl, JacksonUtil.serialize(request));
    ExamListCommonResponse<ExamScoreInfoData> deserialize = JacksonUtil.deserialize(responseStr,
        new TypeToken<ExamListCommonResponse<ExamScoreInfoData>>() {}.getType());

    if (deserialize != null && CollectionUtils.isNotEmpty(deserialize.getData())){
      List<ExamScoreInfoData> examScoreList = deserialize.getData();
      List<Timestamp> completeTimes = getTimestamps(examScoreList);
      boolean isPassed = examScoreList.stream().filter(e -> e.getIs_passed() == ExamIsPassedEnum.PASSED.getCode()).findAny().map(ExamScoreInfoData::ifPassed).orElse(false);

      List<GuideExamScorePO> needInsertScorePOList =
          examScoreList.stream().map(e -> convert2GuideExamScorePO(e))
              .collect(Collectors.toList());

      if(isPassed){
        GuideApplyExamModel guideApplyExamModel = domainBeanFactory.guideApplyExamDBDataService()
            .queryGuideApplyExamByAccountAndSubject(examAccountId, applySubject);
        if(guideApplyExamModel == null){
          return;
        }
        GuideApplyExamPO applyExamPO =
            buildPassedGuideApplyExamPO(examAccountId, applySubject, guideApplyExamModel);
        lockService.executeInLock(
            String.format("dcs_guide_apply_exam:%s_%s", guideApplyExamModel.getGuideId(),
                guideApplyExamModel.getApplySubject()), 1000, () -> deleteErrorRecordsAndUpdateApplyRecords(needInsertScorePOList,applyExamPO,completeTimes));
      }else {
        deleteErrorRecordsAndAddRightRecords(needInsertScorePOList,examAccountId,applySubject,completeTimes);
      }
    }
    logger.info("[dcs.guide.exam.query.exam.score.by.user] end");
  }

  /**
   * 查询调用接口access_token
   *
   * @param parameter
   */
  @QSchedule("dcs.guide.exam.query.access.token")
  public void queryExamAccessToken(Parameter parameter) {
    //此方法要在执行任务的入口方法里调用
    TaskMonitor monitor = TaskHolder.getKeeper();
    Logger logger = monitor.getLogger();
    String acccessToken = queryAccessToken();
    if(StringUtils.isEmpty(acccessToken)){
      logger.warn("eexam interface response empty");
    }
  }

  private String getRequestExamUrl(ExamRequestEnum examRequestEnum,String accessToken){
    String openapiDomainName = domainBeanFactory.examInterfaceConfig().getOpenapiDomainName();
    String apiDomainName = domainBeanFactory.examInterfaceConfig().getApiDomainName();
    String ssoDomainName = domainBeanFactory.examInterfaceConfig().getSsoDomainName();
    if(ExamRequestEnum.QUERY_DEPT_INFO.equals(examRequestEnum)){
      String queryDeptInfoUrl = domainBeanFactory.examInterfaceConfig().getQueryDeptInfoUrl();
      return openapiDomainName + String.format(queryDeptInfoUrl,accessToken);
    }
    if(ExamRequestEnum.CHANGE_USER_INFO.equals(examRequestEnum)){
      String changeUserInfoUrl = domainBeanFactory.examInterfaceConfig().getChangeUserInfoUrl();
      return apiDomainName + String.format(changeUserInfoUrl,accessToken);
    }
    if(ExamRequestEnum.QUERY_USER_INFO.equals(examRequestEnum)){
      String queryUserInfoUrl = domainBeanFactory.examInterfaceConfig().getQueryUserInfoUrl();
      return openapiDomainName + String.format(queryUserInfoUrl,accessToken);
    }
    if(ExamRequestEnum.QUERY_USER_EXAM_INFO.equals(examRequestEnum)){
      String queryUserExamInfoUrl = domainBeanFactory.examInterfaceConfig().getQueryUserExamInfoUrl();
      return openapiDomainName + String.format(queryUserExamInfoUrl,accessToken);
    }
    if(ExamRequestEnum.OPEN_AUTH_INTERFACE.equals(examRequestEnum)){
      String openAuthInterfaceUrl = domainBeanFactory.examInterfaceConfig().getOpenAuthInterfaceUrl();
      String secret = domainBeanFactory.commonExamSecretUtils().secret();
      String tenantid = domainBeanFactory.examInterfaceConfig().getTenantid();
      return ssoDomainName + String.format(openAuthInterfaceUrl,tenantid,secret);
    }
    return "";
  }

  @Transactional
  public void deleteErrorRecordsAndAddRightRecords(List<GuideExamScorePO> recordPOs,String examAccountId,String applySubject,List<Timestamp> completeTimes) {
    guideExamScoreDao.deleteGuideExamScore(examAccountId,applySubject,completeTimes);
    guideExamScoreDao.batchInsert(recordPOs);
  }

  @Transactional
  public boolean deleteErrorRecordsAndUpdateApplyRecords(List<GuideExamScorePO> recordPOs, GuideApplyExamPO applyExamPO,List<Timestamp> completeTimes) {
    guideExamScoreDao.deleteGuideExamScore(applyExamPO.getExamAccountId(),applyExamPO.getApplySubject(),completeTimes);
    guideExamScoreDao.batchInsert(recordPOs);
    return guideApplyExamDao.update(applyExamPO) > 0;
  }

  private void handleUserExamScoreRecords(String applySubject,
      Map<String, List<ExamScoreInfoData>> examScoreMap,List<GuideApplyExamModel> userApplyExamModels,boolean needCheckDataFlag) {
    Map<String, GuideApplyExamModel> examAccountIdSubjectMap = userApplyExamModels.stream().collect(
        Collectors.toMap(o -> o.getExamAccountId() + o.getApplySubject(),
            fromGuideExamInfo -> fromGuideExamInfo));
    for(String userId: examScoreMap.keySet()){
      List<ExamScoreInfoData> examScoreInfoDataList = examScoreMap.get(userId);
      List<ExamScoreInfoData> orderedExamScoreRecords =
          examScoreInfoDataList.stream().sorted(Comparator.comparing(ExamScoreInfoData::getSubmit_time))
              .collect(Collectors.toList());
      boolean isPassed = orderedExamScoreRecords.stream().filter(e -> e.getIs_passed() == ExamIsPassedEnum.PASSED.getCode()).findAny().map(ExamScoreInfoData::ifPassed).orElse(false);
      List<GuideExamScorePO> needInsertScorePOList =
          orderedExamScoreRecords.stream().map(e -> convert2GuideExamScorePO(e))
              .collect(Collectors.toList());
      if(isPassed){
        GuideApplyExamModel model = examAccountIdSubjectMap.get(userId + applySubject);
        GuideApplyExamPO applyExamPO = buildPassedGuideApplyExamPO(userId, applySubject, model);
        if(!needCheckDataFlag){
          lockService.executeInLock(
              String.format("dcs_guide_apply_exam:%s_%s", model.getGuideId(),
                  model.getApplySubject()), 1000, () -> saveAndUpdateApplyRecords(needInsertScorePOList,applyExamPO));
        }else {
          List<Timestamp> completeTimes = getTimestamps(orderedExamScoreRecords);
          lockService.executeInLock(
              String.format("dcs_guide_apply_exam:%s_%s", model.getGuideId(),
                  model.getApplySubject()), 1000, () -> deleteErrorRecordsAndUpdateApplyRecords(needInsertScorePOList,applyExamPO,completeTimes));
        }
        continue;
      }
      if(!needCheckDataFlag){
        guideExamScoreDao.batchInsert(needInsertScorePOList);
      }else {
        List<Timestamp> completeTimes = getTimestamps(orderedExamScoreRecords);
        deleteErrorRecordsAndAddRightRecords(needInsertScorePOList,userId,applySubject,completeTimes);
      }
    }
  }

  private List<Timestamp> getTimestamps(List<ExamScoreInfoData> orderedExamScoreRecords) {
    List<Timestamp> completeTimes = orderedExamScoreRecords.stream().map(e -> Timestamp.valueOf(
        LocalDateTime.parse(e.getSubmit_time() + "",
            DateTimeFormatter.ofPattern(PATTERN_DATE_TIME)))).collect(Collectors.toList());
    return completeTimes;
  }

  private GuideApplyExamPO buildPassedGuideApplyExamPO(String userId, String applySubject,
      GuideApplyExamModel examAccountIdSubjectMap) {
    GuideApplyExamPO applyExamPO = new GuideApplyExamPO();
    applyExamPO.setExamIsPassed(ExamIsPassedEnum.PASSED.getCode());
    applyExamPO.setExamAccountId(userId);
    applyExamPO.setApplySubject(applySubject);
    applyExamPO.setId(examAccountIdSubjectMap.getId());
    applyExamPO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now(ZoneId.of("Asia/Shanghai"))));
    return applyExamPO;
  }

  @Transactional
  public boolean saveAndUpdateApplyRecords(List<GuideExamScorePO> recordPOs, GuideApplyExamPO applyExamPO) {
    guideExamScoreDao.batchInsert(recordPOs);
    return guideApplyExamDao.update(applyExamPO) > 0;
  }

  private GuideExamScorePO convert2GuideExamScorePO(ExamScoreInfoData examScoreInfoData) {
    GuideExamScorePO po = new GuideExamScorePO();
    po.setExamAccountId(examScoreInfoData.getUser_id());
    po.setApplySubject(examScoreInfoData.getDept_code());
    po.setExamScore(examScoreInfoData.getScore());
    po.setCompleteTime(Timestamp.valueOf(LocalDateTime.parse(examScoreInfoData.getSubmit_time()+"",
        DateTimeFormatter.ofPattern(PATTERN_DATE_TIME))));
    po.setTimeZone(new BigDecimal("8"));
    po.setExamIsPassed(examScoreInfoData.getIs_passed());
    return po;
  }

  private static ExamListCommonResponse<ExamScoreInfoData> getExamScoreInfoHttpResponse(Logger logger,HttpClient httpClient, String requestUrl,ExamGetUserExamInfoRequest request) {
    try{
      String serializeRequest = JacksonUtil.serialize(request);
      String responseStr = httpClient.post(requestUrl, serializeRequest);
      ExamListCommonResponse<ExamScoreInfoData> deserialize = JacksonUtil.deserialize(responseStr,
          new TypeToken<ExamListCommonResponse<ExamScoreInfoData>>() {}.getType());
      logger.info("query exam score request: " + serializeRequest + "  response: " + responseStr);
      return deserialize;
    }catch (Exception e){
      return null;
    }
  }

  private static ExamGetUserExamInfoRequest buildExamGetUserExamInfoRequest(String applySubject,
      Map<String, FromGuideExamInfo> validityExamMap,
      Map<String, List<GuideApplyExamModel>> applySubjectMap, String beginDate, String endDate) {
    ExamGetUserExamInfoRequest request = new ExamGetUserExamInfoRequest();
    List<GuideApplyExamModel> guideApplyExamRecords = applySubjectMap.get(applySubject);
    List<String> examAccountIds =
        guideApplyExamRecords.stream().map(GuideApplyExamModel::getExamAccountId)
            .collect(Collectors.toList());
    request.setUser_list(examAccountIds.toArray(new String[examAccountIds.size()]));
    request.setDept_code_list(new String[]{applySubject});
    request.setExam_id(validityExamMap.get(applySubject).getExamId());
    request.setBegin_date(beginDate);
    request.setEnd_date(endDate);
    return request;
  }

  private String queryAccessToken() {
    HttpClient httpClient = domainBeanFactory.httpClient();
    String requestUrl = getRequestExamUrl(ExamRequestEnum.OPEN_AUTH_INTERFACE,"");
    String responseStr = httpClient.get(requestUrl);
    if(StringUtils.isEmpty(responseStr)){
      return Strings.EMPTY;
    }
    ExamCommonResponse<ExamAccessTokenResData> response = JacksonUtil.deserialize(responseStr,
        new TypeToken<ExamCommonResponse<ExamAccessTokenResData>>() {
        }.getType());
    if(response != null && response.getData() != null){
      String accessToken = response.getData().getAccess_token();
      long expiresIn = response.getData().getExpires_in();
      domainBeanFactory.examRedisLogic().saveAccessToken(accessToken,expiresIn);
      return accessToken;
    }
    return Strings.EMPTY;
  }


}
