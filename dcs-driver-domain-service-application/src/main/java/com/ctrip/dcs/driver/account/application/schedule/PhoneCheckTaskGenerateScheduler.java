package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.basebiz.callcenter.splitservice.contract.EnumCallDirection;
import com.ctrip.basebiz.callcenter.splitservice.contract.NumberDTO;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType;
import com.ctrip.dcs.driver.account.application.helper.AppHelper;
import com.ctrip.dcs.driver.domain.application.mq.dto.Struct;
import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig;
import com.ctrip.dcs.driver.account.domain.constant.PhoneCheckState;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.DriverTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.DriverOrderServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.PhoneNumberServiceProxy;
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.self.order.query.api.QueryOrderListRequestType;
import com.ctrip.dcs.self.order.query.api.QueryOrderListResponseType;
import com.ctrip.dcs.self.order.query.dto.DataSwitch;
import com.ctrip.igt.PaginationDTO;
import com.ctrip.igt.PaginatorDTO;
import com.ctrip.platform.dal.dao.sqlbuilder.SelectSqlBuilder;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.sql.Types;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Component
public class PhoneCheckTaskGenerateScheduler {

	Log log = Log.getInstance(PhoneCheckTaskGenerateScheduler.class);

	RateLimiter rateLimiter = RateLimiter.create(1);



	@Resource
	PhoneCheckConfig config;
	@Resource
	DriverTable driverTable;
	@Resource
	PhoneCheckTable phoneCheckTable;
	@Resource
	PhoneCheckTaskTable phoneCheckTaskTable;
	@Resource
	DriverOrderServiceProxy orderService;
	@Resource
	PhoneNumberServiceProxy phoneNumberService;
	@Resource
	PhoneCheckService phoneCheckService;

	@QSchedule("dcs.driver.account.phone.check.task.generate")
	public void apply(Parameter parameter) {
		try {
			AppHelper.getString(parameter, "config").map(JsonUtil.fromString(Struct.class)).ifPresent(this::apply);
		} catch (Exception e) {
			log.error(e);
		} finally {
			log.info("Finish", JsonUtil.toString(parameter));
		}
	}

	@SneakyThrows
	public void apply(Struct parameter) {
		Long lastDriverId = parameter.getLastDriverId();
		List<DriverEntity> driverRecords = Lists.newArrayList();
		Integer count = 0;
		do {
			SelectSqlBuilder sql = new SelectSqlBuilder();
			sql.and().greaterThan("drv_id", lastDriverId, Types.BIGINT);
			sql.and().inNullable("drv_id", parameter.getDriverIds(), Types.BIGINT);
			sql.and().inNullable("supplier_id", parameter.getSupplierIds(), Types.BIGINT);
			sql.and().inNullable("country_id", parameter.getCountryIds(), Types.BIGINT);
			sql.and().inNullable("city_id", parameter.getCityIds(), Types.BIGINT);
			sql.and().notInNullable("city_id", parameter.getBlackCityIds(), Types.BIGINT);
			sql.and().equalNullable("active", parameter.getDriverActive(), Types.INTEGER);
			sql.and().equalNullable("drv_status", parameter.getDriverStatus(), Types.INTEGER);
			sql.orderBy("drv_id", true);
			sql.atPage(1, parameter.getPageSize());
			log.info("BatchQuery", "LastId={}, PageSize={}", lastDriverId, parameter.getPageSize());
			driverRecords = driverTable.queryMany(sql);
			for (DriverEntity driver : driverRecords) {
				apply(parameter, driver);
				lastDriverId = driver.getDriverId();
				count = count + 1;
			}
		} while (CollectionUtils.isNotEmpty(driverRecords) && count < parameter.getCountLimit());
	}

	protected void apply(Struct parameter, DriverEntity driver) {
		log.info("StartGenerateTask", "DriverId={}", driver.getDriverId());
		try {
			if (BooleanUtils.isNotFalse(parameter.getValidateRepeatCheck()) && validateRepeatCheck(driver.getDriverId(), driver.getPhonePrefix(), driver.getPhoneNumber()) == false) {
				Cat.logEvent("PhoneCheckTaskGenerateScheduler.Validate", "RepeatCheck");
				log.warn("ValidateRepeatCheck", "DriverId={}", driver.getDriverId());
				return;
			}
			// 订单校验配置为开关
			if (BooleanUtils.isNotFalse(parameter.getValidateRecentOrder()) && tryValidateRecentOrderOrFalse(driver.getDriverId(), parameter.getRecentOrderDaysThreshold()) == false) {
				Cat.logEvent("PhoneCheckTaskGenerateScheduler.Validate", "RecentOrder");
				log.warn("ValidateRecentOrder", "DriverId={}", driver.getDriverId());
				return;
			}
			phoneCheckService.generatePhoneCheckTaskByDriver(driver,parameter.getPlanTime());
		} finally {
			log.info("EndGenerateTask", "DriverId={}", driver.getDriverId());
		}
	}

	protected boolean validatePhoneNumber(String phoneNumber) {
		SplitNumberRequestType request = new SplitNumberRequestType();
		request.setNumber(phoneNumber);
		request.setDirection(EnumCallDirection.INBOUND);
		request.setRegionCode("CN");
		SplitNumberResponseType response = phoneNumberService.splitNumber(request);
		return Optional.ofNullable(response)
				.map(SplitNumberResponseType::getResult)
				.map(NumberDTO::isValid)
				.orElse(false);
	}

	protected boolean validateRepeatCheck(Long driverId, String phonePrefix, String phoneNumber) {
		PhoneCheckEntity condition = PhoneCheckEntity.builder()
				.driverId(driverId)
				.phonePrefix(phonePrefix)
				.phoneNumber(phoneNumber)
				.checkState(PhoneCheckState.Succeed)
				.build();
		return phoneCheckTable.count(condition) == 0;
	}

	@SneakyThrows
	protected boolean validateRepeatTask(Long driverId, String phonePrefix, String phoneNumber) {
		SelectSqlBuilder sql = new SelectSqlBuilder().selectCount();
		sql.and().equal("driver_id", driverId, Types.BIGINT);
		sql.and().equal("phone_prefix", phonePrefix, Types.VARCHAR);
		sql.and().equal("phone_number", phoneNumber, Types.VARCHAR);
		sql.and().equal("task_type", 3, Types.INTEGER);
		sql.and().greaterThan("submit_time", LocalDateTime.now().minusDays(config.getRepeatTaskThresholdDays()), Types.TIMESTAMP);
		return phoneCheckTaskTable.count(sql) == 0;
	}

	protected boolean tryValidateRecentOrderOrFalse(Long driverId, Integer recentOrderDaysThreshold) {
		try {
			return validateRecentOrder(driverId, recentOrderDaysThreshold);
		} catch (Exception e) {
			log.error(e);
		}
		return false;
	}

	protected boolean validateRecentOrder(Long driverId, Integer recentOrderDaysThreshold) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		LocalDateTime right = LocalDateTime.now();
		LocalDateTime left = right.minusDays(recentOrderDaysThreshold);
		QueryOrderListRequestType request = new QueryOrderListRequestType();
		request.setDriverId(driverId);
		request.setSysExpectTimeRangeLeftBj(formatter.format(left));
		request.setSysExpectTimeRangeRightBj(formatter.format(right));
		request.setPaginator(new PaginatorDTO(1, 1));
		DataSwitch dataSwitch = new DataSwitch();
		dataSwitch.setBaseDetailSwitch(true);
		request.setDataSwitch(dataSwitch);
		rateLimiter.acquire(); // 这里要控制QPS  由于是单线程job调度 所以单机限流即可
		QueryOrderListResponseType response = orderService.queryOrderList(request);
		return Optional.ofNullable(response)
				.map(QueryOrderListResponseType::getPagination)
				.map(PaginationDTO::getTotalSize)
				.map(AppHelper.greaterThan(0))
				.orElse(false);
	}
}