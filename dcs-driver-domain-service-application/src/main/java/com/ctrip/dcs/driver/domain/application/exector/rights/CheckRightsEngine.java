package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.dcs.driver.domain.rights.CheckRightsRequestType;
import com.ctrip.dcs.driver.domain.rights.CheckRightsResponseType;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@ServiceLogTag(tagKeys = {"driverId"})
public class CheckRightsEngine extends ShoppingExecutor<CheckRightsRequestType, CheckRightsResponseType>
        implements Validator<CheckRightsRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;
    @Autowired
    LockService lockService;
    @Autowired
    SystemQConfig systemQConfig;

    @Override
    public CheckRightsResponseType execute(CheckRightsRequestType request) {
        CheckRightsResponseType response = new CheckRightsResponseType();
        CheckRightsEngine.Executor executor = new CheckRightsEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<CheckRightsRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("date").notNull().notEmpty();
        validator.ruleFor("rightsType").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final CheckRightsEngine owner;
        private final CheckRightsResponseType response;
        private final CheckRightsRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(CheckRightsRequestType request, CheckRightsResponseType response, CheckRightsEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;
            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory,request.driverId, request.date);
        }

        @Override
        protected void buildResponse() {
            String closeRights = this.owner.systemQConfig.getString("close_rights");
            if (!Strings.isBlank(closeRights) && closeRights.contains(request.getRightsType().toString())) {
                response.setCanUse(false);
                return;
            }

            this.owner.lockService.executeInLock(String.format("dcs_driver_use_rights:%s_%s", request.getDriverId(), request.getRightsType()), 1000,()->{
                boolean rightsCanUse = driverRightsObject.checkRightsCanUse(request.rightsType);
                response.setCanUse(rightsCanUse);
                if (rightsCanUse && request.rightsType.equals(RightsTypeEnum.RIGHTS_TYPE_WELFARE.getCode())) {
                    response.setMoney(driverRightsObject.computeWelfare());
                }
                if (rightsCanUse && request.rightsType.equals(RightsTypeEnum.RIGHTS_TYPE_VACATION.getCode())) {
                    response.setVacationLimit(driverRightsObject.vacationLimit());
                }
                return null;
            });

        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(driverRightsObject);
        }
    }

}
