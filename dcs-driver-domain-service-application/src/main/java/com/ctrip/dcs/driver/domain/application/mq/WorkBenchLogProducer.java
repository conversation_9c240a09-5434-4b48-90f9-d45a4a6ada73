package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.WorkBenchLogMessage;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

public final class WorkBenchLogProducer {

    public static final Logger LOGGER = LoggerFactory.getLogger(WorkBenchLogProducer.class);
    public static final String TOPIC = "dcs.order.work.bench.log.created";


    public static void sendMessage(WorkBenchLogMessage workBenchLogMessage) {
        String content = JacksonUtil.serialize(workBenchLogMessage);
        Map<String, Object> properties = new HashMap<>();
        properties.put("content", content);
        QmqProducerProvider.sendMessage(TOPIC, properties);
    }

    public static WorkBenchLogMessage buildMessase(UseRightsCondition condition) {
        WorkBenchLogMessage workBenchLogMessage = new WorkBenchLogMessage();
        workBenchLogMessage.setOrderId(Long.valueOf(condition.getUserOrderId()));
        workBenchLogMessage.setLogType(0);
        workBenchLogMessage.setSourceType(3);
        workBenchLogMessage.setContentType(0);
        workBenchLogMessage.setTitleKey("op.operationlog.title.UseofEquity");
        workBenchLogMessage.setContentKey("op.operationlog.content.UseofEquity");
        Map<String, String> contentParams = new HashMap<>();
        contentParams.put("UsageTime", LocalDateTimeUtils.format(LocalDateTime.now()));
        contentParams.put("EquityType", condition.getRightsName());
        if (condition.getRightsType().equals(RightsTypeEnum.RIGHTS_TYPE_WELFARE.getCode())) {
            contentParams.put("Welfare", Optional.ofNullable(condition.getMoney()).orElse(BigDecimal.ZERO).toString());
            workBenchLogMessage.setContentKey("op.operationlog.content.UseofWelfare");
        }
        workBenchLogMessage.setContentParams(JacksonUtil.serialize(contentParams));
        workBenchLogMessage.setLogTime(System.currentTimeMillis());
        workBenchLogMessage.setOperator("SYSTEM");
        workBenchLogMessage.setAppId(Foundation.app().getAppId());
        return workBenchLogMessage;

    }


    public static WorkBenchLogMessage buildVacationMessase(UseRightsCondition condition) {
        WorkBenchLogMessage workBenchLogMessage = new WorkBenchLogMessage();
        workBenchLogMessage.setOrderId(Long.valueOf(condition.getUserOrderId()));
        workBenchLogMessage.setLogType(0);
        workBenchLogMessage.setSourceType(3);
        workBenchLogMessage.setContentType(0);
        workBenchLogMessage.setTitleKey("op.operationlog.title.UseofEquity");
        workBenchLogMessage.setContentKey("op.operationlog.content.UseofLeaveEquity");
        Map<String, String> contentParams = new HashMap<>();
        contentParams.put("UsageTime", LocalDateTimeUtils.format(LocalDateTime.now()));
        workBenchLogMessage.setContentParams(JacksonUtil.serialize(contentParams));
        workBenchLogMessage.setLogTime(System.currentTimeMillis());
        workBenchLogMessage.setOperator("SYSTEM");
        workBenchLogMessage.setAppId(Foundation.app().getAppId());
        return workBenchLogMessage;

    }
}
