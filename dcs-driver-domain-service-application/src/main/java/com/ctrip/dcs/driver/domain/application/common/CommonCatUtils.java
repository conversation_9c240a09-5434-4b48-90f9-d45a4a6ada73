package com.ctrip.dcs.driver.domain.application.common;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Message;
import com.dianping.cat.message.Transaction;

import java.util.Objects;
import java.util.concurrent.Callable;

public final class CommonCatUtils {

  public static final String CAT_SHARK = "Shark";

  public static <T> T catTransaction(String type, String name, Callable<T> callable, T defaultValue) {

    T result = defaultValue;

    if (Objects.isNull(callable)) {
      return result;
    }

    Transaction transaction = Cat.newTransaction(type, name);

    try {
      result = callable.call();
      transaction.setStatus(Message.SUCCESS);
    } catch (Exception exception) {
      CommonLogger.INSTANCE.error(exception);
      Cat.logError(exception);
      transaction.setStatus(exception);
    } finally {
      transaction.complete();
    }

    return result;
  }
}
