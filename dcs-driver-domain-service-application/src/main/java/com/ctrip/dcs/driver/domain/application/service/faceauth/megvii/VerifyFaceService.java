package com.ctrip.dcs.driver.domain.application.service.faceauth.megvii;

import com.ctrip.dcs.driver.account.domain.config.MegviiQConfig;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyParamDTO;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyResultDTO;
import com.ctrip.dcs.driver.domain.application.service.faceauth.megvii.utils.EncryptionUtils;
import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import com.ctrip.tour.driver.utility.json.JacksonUtil;
import com.ctrip.tour.driver.utility.log.LogHelper;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;

import java.io.ByteArrayOutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
public class VerifyFaceService {

    private static final QunarAsyncClient httpClient = new QunarAsyncClient();

    private static final String LINE_BREAK = "\r\n";

    @Autowired
    private MegviiQConfig megviiQConfig;

    public V5VerifyResultDTO verify(V5VerifyParamDTO params) throws Exception {
        Map<String, String> logTag = new HashMap<>();
        logTag.put("driverId", String.valueOf(params.getUuid()));

        QHttpOption option = new QHttpOption();
        String boundary = "--" + System.currentTimeMillis();
        option.addHeader("Content-Type", "multipart/form-data; boundary=" + boundary);
        byte[] body = writeFormDataParams(params, boundary);
        option.setPostBodyData(body);
        String verifyUrl = megviiQConfig.getMegvSGPHost() + megviiQConfig.getSubmitVerifyUrl();
        ListenableFuture<Response> responseFuture = httpClient.post(verifyUrl, option);
        String res = responseFuture.get().getResponseBody();

        LogHelper.info("megviiCmpImg", "verify api res: " + res, logTag);

        V5VerifyResultDTO result = JacksonUtil.toJavaObject(res, V5VerifyResultDTO.class);
        result.setRes(res);
        return result;
    }

    private byte[] writeFormDataParams(V5VerifyParamDTO params, String formBoundary) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PrintWriter writer = new PrintWriter(new OutputStreamWriter(baos, StandardCharsets.UTF_8), true);

        String sign = EncryptionUtils.genSign(
                KMSUtils.key(megviiQConfig.getKmsTokenApiKey()),
                KMSUtils.key(megviiQConfig.getKmsTokenApiSecret()),
                megviiQConfig.getSignExpiredSeconds());

        writeStringFormField(writer, formBoundary, "sign", sign);
        writeStringFormField(writer, formBoundary, "sign_version", "hmac_sha1");
        writeStringFormField(writer, formBoundary, "biz_no", params.getBizNo());
        writeStringFormField(writer, formBoundary, "comparison_type", params.getComparisonType());
        writeStringFormField(writer, formBoundary, "data_type", params.getDataType());
        writeStringFormField(writer, formBoundary, "verify_id", params.getVerifyId());
        // RSA加密
        writeStringFormField(writer, formBoundary, "encryption_type", "2");
        writeStringFormField(writer, formBoundary, "biz_token", params.getBizToken());
        if (ArrayUtils.isNotEmpty(params.getImage())) {
            writeFileFormField(writer, baos, formBoundary, "image", params.getImageFileName(), encryptFile(params.getImage()).getBytes());
        }
        writeStringFormField(writer, formBoundary, "uuid", params.getUuid());
        if (ArrayUtils.isNotEmpty(params.getImageRef1())) {
            writeFileFormField(writer, baos, formBoundary, "image_ref1", params.getImageRef1FileName(), encryptFile(params.getImageRef1()).getBytes());
        }
//        if (ArrayUtils.isNotEmpty(params.getImageRef2())) {
//            writeFileFormField(writer, baos, formBoundary, "image_ref2", params.getImageRef2FileName(), encryptFile(params.getImageRef2()).getBytes());
//        }

        writer.append("--").append(formBoundary).append(LINE_BREAK);
        writer.flush();
        writer.close();

        return baos.toByteArray();
    }

    private void writeStringFormField(PrintWriter writer, String formBoundary, String fieldName, String filedValue) {
        if (StringUtils.isBlank(filedValue)) {
            return;
        }
        writer.append("--").append(formBoundary).append(LINE_BREAK);
        writer.append(String.format("Content-Disposition: form-data; name=\"%s\"", fieldName))
                .append(LINE_BREAK)
                .append(LINE_BREAK);
        writer.append(filedValue)
                .append(LINE_BREAK);
        writer.flush();
    }

    private void writeFileFormField(PrintWriter writer,
                                    ByteArrayOutputStream baos,
                                    String formBoundary,
                                    String fieldName,
                                    String fileName,
                                    byte[] content) throws Exception {
        if (ArrayUtils.isEmpty(content)) {
            return;
        }
        writer.append("--").append(formBoundary).append(LINE_BREAK);
        writer.append(String.format("Content-Disposition: form-data; name=\"%s\"; filename=\"%s\"", fieldName, fileName))
                .append(LINE_BREAK);
        writer.append("Content-Type: ")
                .append(guessMimeType(fileName))
                .append(LINE_BREAK)
                .append(LINE_BREAK);
        writer.flush();

        baos.write(content);
        writer.flush();

        writer.write(LINE_BREAK);
        writer.flush();
    }

    private String guessMimeType(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        return URLConnection.guessContentTypeFromName(fileName);
    }


//    private String encryptText(String text) throws Exception {
//        String publicKeyStr = KMSUtils.key(megviiQConfig.getReqEncryptionPubKeyToken());
//        return Base64.getEncoder().encodeToString(EncryptionUtils.encryptByRsaPubKey(publicKeyStr, text.getBytes(StandardCharsets.UTF_8)));
//    }

    private String encryptFile(byte[] file) throws Exception {
        String publicKeyStr = KMSUtils.key(megviiQConfig.getReqEncryptionPubKeyToken());
        return Base64.getEncoder().encodeToString(EncryptionUtils.encodeFile(file, publicKeyStr));
    }

}
