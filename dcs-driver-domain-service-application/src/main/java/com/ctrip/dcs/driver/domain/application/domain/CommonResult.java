package com.ctrip.dcs.driver.domain.application.domain;

/**
 * 通用返回结果
 */
public final class CommonResult {
  private boolean success;
  private String code;
  private String msg;
  private String uid;

  private CommonResult(Builder builder) {
    success = builder.success;
    code = builder.code;
    msg = builder.msg;
    uid =builder.uid;
  }

  public static Builder newBuilder() {
    return new Builder();
  }

  public boolean isSuccess() {
    return success;
  }

  public String getCode() {
    return code;
  }

  public String getMsg() {
    return msg;
  }

  public String getUid() {
    return uid;
  }

  public static final class Builder {
    private String uid;
    private boolean success;
    private String code;
    private String msg;

    private Builder() {
    }

    public Builder withSuccess(boolean val) {
      success = val;
      return this;
    }

    public Builder withCode(String val) {
      code = val;
      return this;
    }

    public Builder withMsg(String val) {
      msg = val;
      return this;
    }

    public Builder withUid(String val) {
      uid = val;
      return this;
    }

    public CommonResult build() {
      return new CommonResult(this);
    }
  }
}