package com.ctrip.dcs.driver.domain.application.domain.exam;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exector.SaveApplyExamExecutor;
import com.ctrip.dcs.driver.domain.infrastructure.constant.CallExamSuccessEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamApplyResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamIsPassedEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import com.ctrip.dcs.driver.value.exam.ApplyExamObject;
import com.google.gson.annotations.Expose;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 */
public class ApplyExamObjectImpl extends ApplyExamObject {
  private final DomainBeanFactory domainBeanFactory;

  public ApplyExamObjectImpl(DomainBeanFactory domainBeanFactory,
      GuideApplyExamModel guideApplyExamModel) {
    this.domainBeanFactory = domainBeanFactory;
    this.id = guideApplyExamModel.getId() ;
    this.guideId = guideApplyExamModel.getGuideId();
    this.examAccountId = guideApplyExamModel.getExamAccountId();
    this.guideName = guideApplyExamModel.getGuideName();
    this.account = guideApplyExamModel.getAccount();
    this.applySubject = guideApplyExamModel.getApplySubject();
    this.applyTime = guideApplyExamModel.getApplyTime();
    this.timeZone = guideApplyExamModel.getTimeZone();
    this.subjectName = guideApplyExamModel.getSubjectName();
    this.applyResult = ExamApplyResultEnum.getByCode(guideApplyExamModel.getApplyResult());
    this.examIsPassed = ExamIsPassedEnum.getByCode(guideApplyExamModel.getExamIsPassed());
    this.callExamSuccess = CallExamSuccessEnum.getByCode(guideApplyExamModel.getCallExamSuccess());
  }

  @Expose
  private Long id;

  @Expose
  private Long guideId;

  @Expose
  private String examAccountId;

  @Expose
  private String guideName;

  @Expose
  private String account;

  @Expose
  private String applySubject;

  @Expose
  private String applyTime;

  @Expose
  private BigDecimal timeZone;

  @Expose
  private String subjectName;

  @Expose
  private ExamApplyResultEnum applyResult;

  @Expose
  private ExamIsPassedEnum examIsPassed;

  @Expose
  private CallExamSuccessEnum callExamSuccess;

  @Override
  public long id() {
    return this.id;
  }

  @Override
  public long guideId() {
    return this.guideId;
  }

  @Override
  public String examAccountId() {
    return this.examAccountId;
  }

  @Override
  public String guideName() {
    return this.guideName;
  }

  @Override
  public String account() {
    return this.account;
  }

  @Override
  public String applySubject() {
    return this.applySubject;
  }

  @Override
  public String parentApplySubject(){
    if(this.applySubject.length()<=4){
      return this.applySubject;
    }
    return this.applySubject.substring(0,this.applySubject.length()-4);
  }

  @Override
  public String applyTime() {
    return this.applyTime;
  }

  @Override
  public BigDecimal timeZone() {
    return this.timeZone;
  }

  @Override
  public String subjectName() {
    return this.subjectName;
  }

  @Override
  public ExamApplyResultEnum applyResult() {
    return this.applyResult;
  }

  @Override
  public ExamIsPassedEnum examIsPassed() {
    return this.examIsPassed;
  }

  @Override
  public CallExamSuccessEnum callExamSuccess() {
    return this.callExamSuccess;
  }

  @Override
  public void setCallExamSuccess(CallExamSuccessEnum callExamSuccess) {
    this.callExamSuccess = callExamSuccess;
  }

  @Override
  public boolean saveApplyExam() {
    return new SaveApplyExamExecutor(this, this.domainBeanFactory).doWork(null);
  }
}
