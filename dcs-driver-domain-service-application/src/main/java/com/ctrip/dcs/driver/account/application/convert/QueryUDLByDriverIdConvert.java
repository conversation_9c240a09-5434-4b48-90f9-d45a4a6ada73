package com.ctrip.dcs.driver.account.application.convert;
/*
作者：pl.yang
创建时间：2025/2/25-上午11:00-2025
*/


import cn.hutool.core.collection.CollUtil;
import com.ctrip.dcs.driver.account.domain.condition.RegisterDriverUdlCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.DriverUDL;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryUDLByDriverIdResponseType;
import com.ctrip.dcs.driver.domain.account.RegisterDirverUdl;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: QueryUDLByDriverIdConvert
 * @Package com.ctrip.dcs.driver.account.application.convert
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/2/25 上午11:00
 */

@Component
public class QueryUDLByDriverIdConvert {
    public static final Logger logger = LoggerFactory.getLogger(QueryUDLByDriverIdConvert.class);

    public List<String> convertSourceId(QueryUDLByDriverIdRequestType queryUDLByDriverIdRequestType) {
        List<String> sourceIds = new ArrayList<>();
        if (StringUtils.isNotBlank(queryUDLByDriverIdRequestType.getDriverId())) {
            sourceIds.add(queryUDLByDriverIdRequestType.getDriverId());
        }
        if (queryUDLByDriverIdRequestType.getDriverIds() != null) {
            sourceIds.addAll(queryUDLByDriverIdRequestType.getDriverIds());
        }
        return sourceIds;
    }

    public QueryUDLByDriverIdResponseType convertAccountUDL(List<AccountUDLDo> accountUDLResult) {
        QueryUDLByDriverIdResponseType responseType = new QueryUDLByDriverIdResponseType();
        if (CollectionUtils.isNotEmpty(accountUDLResult)) {
            List<DriverUDL> collect = convertUdl(accountUDLResult);
            responseType.setDriverUdL(collect);
        }
        return responseType;
    }

    private static @NotNull List<DriverUDL> convertUdl(List<AccountUDLDo> accountUDLResult) {
        return accountUDLResult.stream()
                .filter(t ->t!=null&& StringUtils.isNotBlank(t.getSourceId()) && StringUtils.isNotBlank(t.getUid()))
                .map(t -> {
                    DriverUDL driverUDL = new DriverUDL();
                    driverUDL.setDriverid(t.getSourceId());
                    driverUDL.setUdl(getOrCreate(t,t.getUdl()));
                    driverUDL.setUid(t.getUid());
                    driverUDL.setIsOversea(t.getIsOversea());
                    return driverUDL;

                })
                .filter(t -> {
                    metricUdl(t);
                    return true;
                })
                //按照司机uid 去重
                .collect(Collectors.toMap(DriverUDL::getUid, t -> t, (existing, replacement) -> existing)).values().stream().toList();
    }

    private static String getOrCreate(AccountUDLDo t, String udl) {
        if(StringUtils.isBlank(udl)){
            logger.warn("accountUDLResult.getUdl() is empty,sourceId:{}", t.getUid());
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "UDL_Create_New_UDL_CN_CSPD");
            return Constants.UDL_CN_CSPD;
        }
        return udl;
    }

    //埋点udl情况
    private static void metricUdl(DriverUDL t) {
        if (StringUtils.isEmpty(t.getDriverid())) {
            logger.warn("accountUDLResult.getDriverid() is empty,sourceId:{}", t.getDriverid());
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "driver_Empty");
        }
        if (StringUtils.isEmpty(t.getUdl())) {
            logger.warn("accountUDLResult.getUdl() is empty,sourceId:{}", t.getUdl());
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "UDL_Empty");
        }
        if (StringUtils.isEmpty(t.getUid())) {
            logger.warn("accountUDLResult.getUid() is empty,uid:{}", t.getUid());
            Cat.logEvent(CatEventType.ACCOUNT_UDL_QUEYR, "UID_Empty");
        }
    }

    public List<String> convertUid(QueryUDLByDriverIdRequestType queryUDLByDriverIdRequestType) {
        List<String> uids = new ArrayList<>();
        if (StringUtils.isNotBlank(queryUDLByDriverIdRequestType.getUid())) {
            uids.add(queryUDLByDriverIdRequestType.getUid());
        }
        if(CollUtil.isNotEmpty(queryUDLByDriverIdRequestType.getUids())){
            uids.addAll(queryUDLByDriverIdRequestType.getUids());
        }
        return uids;
    }

    public RegisterDriverUdlCondition convertRegisterDriverUdlCondition(RegisterDirverUdl registerDirverUdl) {
        return RegisterDriverUdlCondition.builder()
                .driverId(registerDirverUdl.getDriverId())
                .cityId(registerDirverUdl.getCityId())
                .build();
    }
}
