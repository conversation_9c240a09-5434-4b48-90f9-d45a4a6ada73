package com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * v5verify接口请求参数
 */
@Getter
@Setter
public class V5VerifyResultDTO {

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("biz_no")
    private String bizNo;

    @JsonProperty("time_used")
    private Integer timeUsed;

    @JsonProperty("user_faced_time")
    private String userFacedTime;

    private String error;

    @JsonProperty("result_code")
    private Integer resultCode;

    @JsonProperty("result_message")
    private String resultMessage;

    private List<String> images;

    /**
     * 完成响应报文
     */
    private String res;

    /* 省略部分字段 */

}
