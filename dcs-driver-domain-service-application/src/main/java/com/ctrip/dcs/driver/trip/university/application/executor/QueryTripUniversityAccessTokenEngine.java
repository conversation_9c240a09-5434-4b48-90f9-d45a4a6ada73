package com.ctrip.dcs.driver.trip.university.application.executor;

import com.ctrip.dcs.driver.domain.trip.university.QueryTripUniversityAccessTokenRequestType;
import com.ctrip.dcs.driver.domain.trip.university.QueryTripUniversityAccessTokenResponseType;
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenRequestDTO;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryTripUniversityAccessTokenEngine extends ShoppingExecutor<QueryTripUniversityAccessTokenRequestType, QueryTripUniversityAccessTokenResponseType> {

    @Autowired
    private TripUniversityService tripUniversityService;

    @Override
    public QueryTripUniversityAccessTokenResponseType execute(QueryTripUniversityAccessTokenRequestType request) {
       return convertResponse(tripUniversityService.getAccessToken(convert(request)));
    }

    private TripUniversityAccessTokenRequestDTO convert(QueryTripUniversityAccessTokenRequestType request) {
        return new TripUniversityAccessTokenRequestDTO(request.getUid(), request.getDrvId());
    }

    private QueryTripUniversityAccessTokenResponseType convertResponse(TripUniversityAccessTokenDTO accessToken) {
        QueryTripUniversityAccessTokenResponseType responseType = new QueryTripUniversityAccessTokenResponseType();
        responseType.setAccessToken(accessToken.getAccessToken());
        responseType.setTenantId(accessToken.getTenantId());
        return ServiceResponseUtils.success(responseType);
    }
}
