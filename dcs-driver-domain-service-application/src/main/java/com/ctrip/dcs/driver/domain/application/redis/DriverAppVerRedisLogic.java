package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class DriverAppVerRedisLogic {
    private static final Logger logger = LoggerFactory.getLogger(DriverAppVerRedisLogic.class);
    private static final String DRIVER_VERSION_NEW_REDIS_KEY = "version_new:%s";
    @Autowired
    private DirectorRedis directorRedis;

    /**
     * 获取司机客户端版本
     */
    public Pair<String, String> getAppRnVer(Long driverId) {
        if (Objects.isNull(driverId)) {
            return Pair.of(Strings.EMPTY, Strings.EMPTY);
        }
        String versionStr = directorRedis.get(String.format(DRIVER_VERSION_NEW_REDIS_KEY, driverId));
        logger.info("DriverVer:" + driverId, versionStr);
        if (StringUtils.isBlank(versionStr) || !versionStr.contains("#")) {
            return Pair.of(Strings.EMPTY, Strings.EMPTY);
        }
        String[] versionArr = versionStr.split("#");
        return Pair.of(versionArr[0], versionArr[1]);
    }

}
