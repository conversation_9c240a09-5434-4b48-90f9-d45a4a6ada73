package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.supply.driver.dto.Balance;
import com.ctrip.dcs.supply.driver.dto.BalanceRecordDTO;
import com.ctrip.dcs.supply.driver.dto.CardInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 司机钱包相关逻辑
 * */
public interface FinanceConvertService {

    /**
     * 查询司机可提现余额
     * */
    Pair<String, Balance> queryDriverWithdrawBalance(long driverId, boolean isOldProcess);

    /**
     * 查询司机银行卡列表
     * */
    List<CardInfo> queryDriverCardList(long driverId);

    /**
     * 司机提现
     * */
    String driverWithdraw(WithdrawModel withdrawModel);

    /**
     * 	司机密码重置
     * */
    String driverResetPsw(WithdrawModel withdrawModel);

    /**
     * 将结算侧返回的CODE映射到司机端
     * 并取得CODE相应的描述
     * */
    String convertDriverFinanceResult(String serviceResultCode);

    /**
     * 查询司机提现记录
     * */
    List<BalanceRecordDTO> queryDriverBalanceRecord(long driverId, String beginDate, String endDate);

    /**
     * 并取得CODE相应的描述
     * */
    String getFinanceResult(String code);
}
