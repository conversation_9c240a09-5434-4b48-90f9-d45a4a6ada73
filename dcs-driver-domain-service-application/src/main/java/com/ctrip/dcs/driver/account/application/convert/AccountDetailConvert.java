package com.ctrip.dcs.driver.account.application.convert;

import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import cn.hutool.core.collection.CollUtil;
import com.ctrip.dcs.driver.account.domain.condition.PhoneInfoCondition;
import com.ctrip.dcs.driver.domain.account.AccountDetailDTO;
import com.ctrip.dcs.driver.domain.account.AccountIdentitySourceDTO;
import com.ctrip.dcs.driver.domain.account.BatchQueryAccountByMobilePhoneRequestType;
import com.ctrip.dcs.driver.domain.account.PhoneInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;
@Component
public class AccountDetailConvert {

    public static AccountDetailDTO convert(AccountInfoDTO account) {
        AccountDetailDTO accountDetailDTO = new AccountDetailDTO();
        accountDetailDTO.setUid(account.getUid());
        accountDetailDTO.setUdl(account.getUdl());
        accountDetailDTO.setCountryCode(account.getCountryCode());
        accountDetailDTO.setPhoneNumber(account.getPhoneNumber());
        accountDetailDTO.setEmail(account.getEmail());
        accountDetailDTO.setName(account.getName());
        accountDetailDTO.setIdCardNo(account.getIdCardNo());
        accountDetailDTO.setPayoneerAccountId(account.getPayoneerAccountId());
        accountDetailDTO.setPpmAccountId(account.getPpmAccountId());
        accountDetailDTO.setIsOversea(account.getIsOversea());
        List<AccountIdentitySourceDTO> identitySourceDTOList = account.getIdentityDTOList().stream().map(o -> new AccountIdentitySourceDTO(o.getSource(), o.getSourceId(), o.isValid())).collect(Collectors.toList());
        accountDetailDTO.setIdentityList(identitySourceDTOList);
        // 支付渠道类型，境内ppm账户，境外派安盈账户
        accountDetailDTO.setPayChannelType(account.payChannelTypeV1());
        accountDetailDTO.setWithdrawalStatus(ObjectUtils.defaultIfNull(account.getWithdrawStatus(), 1));
        accountDetailDTO.setModifyUser(account.getModifyUser());
        return accountDetailDTO;
    }

    public static List<AccountDetailDTO> batchConvert(List<AccountInfoDTO> accountList) {
        if (CollUtil.isEmpty(accountList)) {
            return null;
        }
        return accountList.stream().map(AccountDetailConvert::convert).collect(Collectors.toList());
    }

    public List<PhoneInfoCondition> batchConvertPhoneInfo(BatchQueryAccountByMobilePhoneRequestType requestType) {
        if (requestType == null || requestType.getPhoneInfoList() == null) {
            return null;
        }
        return requestType.getPhoneInfoList().stream().map(AccountDetailConvert::convertPhoneInfo).collect(Collectors.toList());

    }

    private static PhoneInfoCondition convertPhoneInfo(PhoneInfo t) {
        return PhoneInfoCondition.builder().countryCode(t.getCountryCode()).phoneNumber(t.getPhoneNumber()).build();
    }

}
