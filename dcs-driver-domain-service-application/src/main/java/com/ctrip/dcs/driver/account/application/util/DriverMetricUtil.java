package com.ctrip.dcs.driver.account.application.util;
/*
作者：pl.yang
创建时间：2025/3/17-下午4:44-2025
*/


import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.AccountDetailDTO;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DirverMetricUtil
 * @Package com.ctrip.dcs.driver.account.infrastructure.utils
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/3/17 下午4:44
 */


public class DriverMetricUtil {
    public static void metricUdlIsValid(AccountDetailDTO accountDetail) {
        try {
            if(accountDetail!=null){
                inner_metricUdlIsValid(accountDetail.getUdl(), accountDetail.isIsOversea());
            }
        } catch (Throwable e) {
            LoggerFactory.getLogger(DriverMetricUtil.class).warn(e);
        }
    }

    private static void inner_metricUdlIsValid(String udl, Boolean isOversea) {
        ServiceExecuteContext current = ServiceExecuteContext.getCurrent();
        String operationName = "";
        if (current != null) {
            operationName = current.getOperationName();
        }
        Cat.logEvent(CatEventType.QUERY_BY_UID_UDL, operationName + "_" +udl + "_" + isOversea);
    }

    public static void batchMetricUdlIsValid(List<AccountDetailDTO> accountDetailDTOS) {
        try {
            for (AccountDetailDTO accountDetail : accountDetailDTOS) {
                metricUdlIsValid(accountDetail);
            }
        } catch (Throwable e) {
            LoggerFactory.getLogger(DriverMetricUtil.class).warn(e);
        }
    }

    public static void batchMetricUdlIsValid2(List<AccountInfoDTO> accountList) {
        try {
            for (AccountInfoDTO accountDetail : accountList) {
                inner_metricUdlIsValid(accountDetail.getUdl(), BooleanUtils.toBoolean(accountDetail.getIsOversea()));
            }
        } catch (Throwable e) {
            LoggerFactory.getLogger(DriverMetricUtil.class).warn(e);
        }
    }
}
