package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.basebiz.softpbxcloudportal.contact.SendAppUserInfoRequestType;
import com.ctrip.basebiz.softpbxcloudportal.contact.SendAppUserInfoResponseType;
import com.ctrip.dcs.driver.domain.application.service.ExtensionService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.platform.SoftpbxCloudServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.model.extension.IVRInfoModel;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;


/**
 * 对接文档：http://conf.ctripcorp.com/pages/viewpage.action?pageId=640388873
 * */
@Component
public class SoftpbxCloudServiceProxyImpl implements ExtensionService {

  @Autowired
  private SoftpbxCloudServiceProxy softpbxCloudServiceProxy;

  @Override
  public String sendAppUserInfo(IVRInfoModel ivrInfoModel){
    SendAppUserInfoRequestType requestType = new SendAppUserInfoRequestType();
    requestType.reqUuid = UUID.randomUUID().toString();
    requestType.uid = String.valueOf(ivrInfoModel.getDriverId());
    requestType.appId = ivrInfoModel.getAppId();
    requestType.pageId = ivrInfoModel.getPageId();
    if(ivrInfoModel.getIvrExtDataInfoModel() != null) {
      // JSON
      requestType.extData = JacksonUtil.serialize(ivrInfoModel.getIvrExtDataInfoModel());
    }
    SendAppUserInfoResponseType responseType;
    try {
      responseType = softpbxCloudServiceProxy.sendAppUserInfo(requestType);
      if(responseType != null && "1".equals(responseType.resultCode)){
        return requestType.reqUuid;
      }
    }catch (Exception e) {
      return Strings.EMPTY;
    }
    return Strings.EMPTY;
  }
}
