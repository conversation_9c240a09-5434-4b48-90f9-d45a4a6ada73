package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.enums.BankCardResultEnum;
import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardAreaTypeEnum;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.dcs.driver.gateway.PaymentRouterRepository;
import com.ctrip.dcs.driver.gateway.YeePayRepository;
import com.ctrip.dcs.driver.value.bank.BankCardResultModel;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBankCardRecordDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankCardRecordPO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.domain.account.BindAccountBankCardRequestType;
import com.ctrip.dcs.driver.domain.account.BindAccountBankCardResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.scm.merchant.interfaces.message.CheckDriverBandCardValidResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;

//绑定境内银行卡
@Component
@ServiceLogTag(tagKeys= {"driverId","uid","bankCardNo","bankPhoneNumber"})
public class BindAccountBankCardEngine extends ShoppingExecutor<BindAccountBankCardRequestType, BindAccountBankCardResponseType> implements Validator<BindAccountBankCardRequestType> {

    @Autowired
    AccountBankCardRecordDao accountBankCardRecordDao;

    @Autowired
    CardInfoManagementRepository cardInfoManagementService;

    @Autowired
    AccountService accountService;

    @Autowired
    LockService lockService;

    @Autowired
    DriverAccountConfig driverAccountConfig;

    @Autowired
    YeePayRepository rhbSettlementBillServiceProxy;

    @Autowired
    PaymentRouterRepository paymentRouterApiService;

    @Autowired
    DriverWalletInfoConfig driverWalletInfoConfig;

    @Override
    public void validate(AbstractValidator<BindAccountBankCardRequestType> validator) {
        validator.ruleFor("driverId").notNull().greaterThan(0L);
        validator.ruleFor("uid").notNull().notEmpty();
        validator.ruleFor("bankCardNo").notNull().notEmpty();
        validator.ruleFor("bankPhoneNumber").notNull().notEmpty();
    }

    @Override
    public BindAccountBankCardResponseType execute(BindAccountBankCardRequestType request) {
        BindAccountBankCardResponseType responseType =
                lockService.executeInLock(String.format(Constants.BANKCARD_LOCK_KEY_PATTERN, request.uid),
                driverAccountConfig.getAccountLockWaitMills(),
                () -> bindBankCard(request));

        if(responseType == null) {
            responseType = new BindAccountBankCardResponseType();
            return ServiceResponseUtils.fail(responseType);
        }

        if(responseType.responseResult == null) {
            return ServiceResponseUtils.fail(responseType);
        }

        if("200".equals(responseType.responseResult.returnCode)) {
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType, responseType.responseResult.returnCode);
    }

    /**
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
     * 绑卡校验
     * 1、uid反查
     * 2、存在已绑定的卡
     * 3、服务：银行卡号格式是否正确、是否储蓄卡（调用携程公共route-api）
     * 4、是否本人银行卡（调用结算，结算调用易宝）
     * 5、金融保存银行卡（加密）
     * 6、绑卡（保存金融加密后卡号）
     * */
    protected BindAccountBankCardResponseType bindBankCard(BindAccountBankCardRequestType request){
        BindAccountBankCardResponseType responseType = new BindAccountBankCardResponseType();
        responseType.responseResult = new ResponseResult();
        responseType.responseResult.returnCode = "500";
        try {
            //1、uid不存在
            AccountInfoDTO accountInfo = accountService.getAccountInfoByUID(request.uid);
            if(Objects.isNull(accountInfo)){
                responseType.responseResult.returnCode = "624";
                return responseType;
            }

            //2、存在已绑定的卡，返回错误提示
            AccountBankCardRecordPO accountBankCardRecord = accountBankCardRecordDao.queryAccountBindingBankCard(request.uid,  request.bankCardNo);
            if (Objects.nonNull(accountBankCardRecord)) {
                responseType.responseResult.returnCode = "621";
                return responseType;
            }

            //3、公共银行卡通道信息
            BankCardResultModel checkResult = paymentRouterApiService.queryBankChannelInfo(request.uid, request.bankCardNo,false);
            if(checkResult == null) {
                responseType.responseResult.returnCode = BankCardResultEnum.ERROR.getCode();
                return responseType;
            }
            if(!BankCardResultEnum.OK.equals(checkResult.getResultCode())){
                responseType.responseResult.returnCode = checkResult.getResultCode().getCode();
                return responseType;
            }

            //4、结算——易宝流程
            CheckDriverBandCardValidResponseType settlementCheckResult = rhbSettlementBillServiceProxy.checkDriverBandCardValid(
                    request.getDriverId(), accountInfo.getName(), accountInfo.getIdCardNo(),
                    accountInfo.getPhoneNumber(), request.bankPhoneNumber, request.bankCardNo);
            if(settlementCheckResult == null) {
                responseType.responseResult.returnCode = BankCardResultEnum.SETTLEMENT_FAILED.getCode();
                return responseType;
            }

            this.dealSettlementCheckResult(settlementCheckResult, responseType);
            if(!BankCardResultEnum.OK.getCode().equals(responseType.responseResult.returnCode)){
                return responseType;
            }

            //5、金融加密
            String saveBankCardNo = cardInfoManagementService.createCardInfo(accountInfo.getName(), request.bankCardNo);
            if (StringUtils.isBlank(saveBankCardNo)) {
                responseType.responseResult.returnCode = "622";
                return responseType;
            }

            //6、绑卡流水
            AccountBankCardRecordPO insertAccountBankCardRecord = this.buildAccountBankCardRecord(request, checkResult.getCardBankName(), saveBankCardNo);
            int result = accountBankCardRecordDao.insert(insertAccountBankCardRecord);
            if (result <= 0) {
                responseType.responseResult.returnCode = "623";
                return responseType;
            }
            responseType.responseResult.returnCode = "200";
        }catch (Exception e) {
            responseType.responseResult.returnCode = "624";
            return responseType;
        }
        return responseType;
    }

    /**
     * 处理结算返回信息
     * */
    private void dealSettlementCheckResult(CheckDriverBandCardValidResponseType settlementResult, BindAccountBankCardResponseType responseType) {
        if(settlementResult == null || settlementResult.responseResult == null) {
            return;
        }

        //validBankCard 是否有效卡 0-无效，1-有效
        if("200".equals(settlementResult.responseResult.returnCode) && ObjectUtils.defaultIfNull(settlementResult.validBankCard, 0) == 1) {
            responseType.responseResult.returnCode = "200";
            return;
        }

        if(StringUtils.isBlank(settlementResult.businessCode)) {
            responseType.responseResult.returnCode = "617";
            responseType.thirdReturnMessage = settlementResult.businessMsg;
            return;
        }

        responseType.thirdReturnCode = settlementResult.businessCode;
        responseType.thirdReturnMessage = settlementResult.businessMsg;
        responseType.responseResult.returnCode = driverWalletInfoConfig.getSettlementResultCode(settlementResult.businessCode);
    }



    /**
     * 绑卡信息
     * */
    private AccountBankCardRecordPO buildAccountBankCardRecord(BindAccountBankCardRequestType requestType, String saveBankCardName, String saveBankCardNo) {
        AccountBankCardRecordPO accountBankCardRecord = new AccountBankCardRecordPO();
        accountBankCardRecord.setUid(requestType.uid);

        AccountInfoDTO accountInfo = accountService.getAccountInfoByUID(requestType.uid);
        if(Objects.nonNull(accountInfo)){
            //名字修改下
            accountBankCardRecord.setName(accountInfo.getName());
            accountBankCardRecord.setIdCardNo(accountInfo.getIdCardNo());
        }
        accountBankCardRecord.setBankCardNo(saveBankCardNo);
        accountBankCardRecord.setBankPhoneNumber(requestType.bankPhoneNumber);
        accountBankCardRecord.setBankName(saveBankCardName);
        accountBankCardRecord.setOperateType(1);
        accountBankCardRecord.setOperateTime(Timestamp.valueOf(LocalDateTime.now()));
        accountBankCardRecord.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.now()));
        accountBankCardRecord.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
        accountBankCardRecord.setAreaType(BankCardAreaTypeEnum.domesticdriver_domesticcard.getCode());
        accountBankCardRecord.setDriverId(requestType.driverId+"");
        return accountBankCardRecord;
    }
}
