package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceDriverObjectImpl;
import com.ctrip.dcs.driver.domain.finance.SendFinanceVerificationRequestType;
import com.ctrip.dcs.driver.domain.finance.SendFinanceVerificationResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发送验证码
 */
@Component
public class SendFinanceVerificationEngine extends ShoppingExecutor<SendFinanceVerificationRequestType, SendFinanceVerificationResponseType>
        implements Validator<SendFinanceVerificationRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<SendFinanceVerificationRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final SendFinanceVerificationEngine owner;
        private final SendFinanceVerificationResponseType response;
        private final SendFinanceVerificationRequestType request;
        private FinanceDriverObject financeDriverObject;

        private Executor(SendFinanceVerificationRequestType request, SendFinanceVerificationResponseType response, SendFinanceVerificationEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.responseResult.returnCode = FinanceResultEnum.OK.getCode();
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            financeDriverObject = new FinanceDriverObjectImpl(this.request.driverId, this.request.source, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            if(FinanceResultEnum.isValidError(this.financeDriverObject.validResult())) {
                response.responseResult.returnCode = this.financeDriverObject.validResult();
                return false;
            }
            return true;
        }

        @Override
        protected void buildResponse() {
            response.responseResult.returnCode = financeDriverObject.sendFinanceVerification();
        }
    }

    @Override
    public SendFinanceVerificationResponseType execute(SendFinanceVerificationRequestType request) {
        SendFinanceVerificationResponseType response = new SendFinanceVerificationResponseType();
        Executor executor = new Executor(request, response, this);
        if(executor.validate()){
            executor.buildResponse();
        }
        if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
            response.responseResult.returnMessage = this.domainBeanFactory.financeConvertService().getFinanceResult(response.responseResult.returnCode);
            return ServiceResponseUtils.fail(response, response.responseResult.returnCode, response.responseResult.returnMessage);
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public SendFinanceVerificationResponseType onException(SendFinanceVerificationRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("SendFinanceVerification error", ex);
        return super.onException(req, ex);
    }
}
