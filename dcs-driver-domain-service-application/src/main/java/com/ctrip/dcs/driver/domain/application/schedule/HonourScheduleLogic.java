package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.service.MedalInfoService;
import com.ctrip.dcs.driver.domain.application.service.RankInfoService;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;
import java.util.concurrent.ExecutorService;

@Component
public class HonourScheduleLogic {

    @Autowired
    private RankInfoService rankInfoService;

    @Autowired
    private MedalInfoService medalInfoService;

    @Autowired
    @Qualifier("CommonThreadPool")
    private ExecutorService executorService;

    /**
     * 获取最新勋章信息
     * */
    @QSchedule("dcs.driver.honour.medal.update")
    public void syncHonourInfoFromBIDB(Parameter parameter) {
        CommonLogger.INSTANCE.info("qschedule", "dcs.driver.honour.medal.update start");
        medalInfoService.updateMedalInfo();
        CommonLogger.INSTANCE.info("qschedule", "dcs.driver.honour.medal.update end");
    }

    /**
     * 获取最新排行榜
     * 1、查询最新批次（所有城市相同）
     * 2、获取form开放排行榜城市
     * 3、查询该批次下没个城市的排行榜数据，分为周榜、月榜缓存
     * */
    @QSchedule("dcs.driver.honour.rank.update")
    public void syncHRankingInfoFromBIDB(Parameter parameter) {
        rankInfoService.updateRankListForSchedule();
    }

    /**
     * 周排行更新通知
     * 1、获取form开放排行榜城市
     * 2、查询该城市下所有status = 1的司机
     * 3、200人/每批次发送通知
     * 4、设置redis小红点提醒
     * */
    @QSchedule("dcs.driver.honour.rank.week.update.notice")
    public void weekRankUpdateNotice(Parameter parameter) {
        executorService.submit(() -> {
            rankInfoService.sendRankUpdateNotice(true);
        });
    }

    /**
     * 月排行更新通知
     * 逻辑同上
     * */
    @QSchedule("dcs.driver.honour.rank.month.update.notice")
    public void monthRankUpdateNotice(Parameter parameter) {
        executorService.submit(() -> {
            rankInfoService.sendRankUpdateNotice(false);
        });
    }

    /**
     * 手动刷新数据
     * */
    @QSchedule("dcs.driver.domain.data.update")
    public void refreshDomainCache(Parameter parameter) {
        String strType = parameter.getString("type");
        switch (strType){
            case "1":
                medalInfoService.updateMedalInfo();
                break;
            case "2":
                String strCityIds = parameter.getString("cityIds");
                if(StringUtils.isNotEmpty(strCityIds)) {
                    List<Long> rankCityList = ShoppingCollectionUtils.toList
                            (strCityIds.split(","), (ShoppingFuncUtils.Func1<String, Long>) Long::parseLong);
                    if(CollectionUtils.isNotEmpty(rankCityList)) {
                        rankInfoService.updateRankListForManual(rankCityList);
                    }
                }
                break;
            default:
                break;
        }
    }
}
