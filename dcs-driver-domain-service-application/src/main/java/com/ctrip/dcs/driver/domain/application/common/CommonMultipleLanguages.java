package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctrip.ibu.platform.shark.sdk.service.l10n.entity.L10nNumberFormatOption;
import com.ctrip.igt.framework.common.language.LanguageContext;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public final class CommonMultipleLanguages {

  @Autowired
  private CommonlTemplateUtils igtRestfulTemplateUtils;

  private final L10nNumberFormatOption operationsWithOutScale;
  private final L10nNumberFormatOption operationsWithScale;

  private CommonMultipleLanguages() {
    this.operationsWithOutScale = new L10nNumberFormatOption();
    this.operationsWithOutScale.setUseGrouping(true);
    this.operationsWithOutScale.setMaximumFractionDigits(2);
    this.operationsWithOutScale.setMinimumFractionDigits(0);

    this.operationsWithScale = new L10nNumberFormatOption();
    this.operationsWithScale.setUseGrouping(true);
    this.operationsWithScale.setMaximumFractionDigits(2);
    this.operationsWithScale.setMinimumFractionDigits(2);
  }

  public String content(String key, Map<String, Object> args) {
    return this.igtRestfulTemplateUtils.evaluate(this.getContent(key), args);
  }

  public String getContent(String key) {
    return this.getContent(key, LanguageContext.getLanguage().getLanguageCode());
  }

  public String getContent(String key, String locale) {
    if (StringUtils.isBlank(key)) {
      return StringUtils.EMPTY;
    }
    String content = CommonCatUtils.catTransaction(CommonCatUtils.CAT_SHARK, key,
            () -> Shark.getByLocale(key, locale), StringUtils.EMPTY);
    CommonLogger.INSTANCE.infoFormat("shark", "key[%s] locale[%s] content[%s]",
            new Object[] {key, locale, content});
    return ObjectUtils.defaultIfNull(content, StringUtils.EMPTY);
  }

}
