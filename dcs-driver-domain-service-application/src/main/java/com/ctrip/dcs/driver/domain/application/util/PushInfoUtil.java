package com.ctrip.dcs.driver.domain.application.util;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.setting.ServiceTimeModel;
import com.ctrip.dcs.driver.domain.setting.DriverPushConfigInfoDTO;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;

public class PushInfoUtil {

    public static final String DRIVER_PUSH_CONFIG_REDIS_KEY = "driver:pushconfig:%s";
    public static final int DRIVER_PUSH_CONFIG_REDIS_EXPIRE_TIME = 864000;  // 10 days
    public static final String DRIVER_LANGUAGE_REDIS_KEY = "language:%s";

    /**
     * 配置KEY
     * */
    public static String getPushConfigKey(long driverId) {
        return String.format("%s_%s", "dcs_driver", String.format(DRIVER_PUSH_CONFIG_REDIS_KEY, driverId));
    }

    /**
     * 数据转换
     * */
    public static DriverPushConfigInfoDTO convertToPushInfo(DriverOrderPushConfigPO orderPushConfig) {
        DriverPushConfigInfoDTO driverPushConfigInfo = new DriverPushConfigInfoDTO();
        driverPushConfigInfo.serviceTimeFrom = Strings.EMPTY;
        driverPushConfigInfo.serviceTimeTo = Strings.EMPTY;
        driverPushConfigInfo.orderTyps = new ArrayList<>();
        driverPushConfigInfo.drvIntendVehicleTypes = new ArrayList<>();
        driverPushConfigInfo.drvOrderDistance = 0;
        driverPushConfigInfo.orderPushStatus = false;
        driverPushConfigInfo.drvLanguage = Strings.EMPTY;
        try {
            driverPushConfigInfo.driverId = orderPushConfig.getDrvId();
            // 是否开启抢单
            driverPushConfigInfo.orderPushStatus = BooleanUtils.toBooleanDefaultIfNull(orderPushConfig.getOrderPushStatus(), false);
            // 用车时间
            if (StringUtils.isNotBlank(orderPushConfig.getServiceTime())) {
                ServiceTimeModel serviceTimeModel = JacksonUtil.deserialize(orderPushConfig.getServiceTime(), ServiceTimeModel.class);
                driverPushConfigInfo.serviceTimeFrom = serviceTimeModel.getFrom();
                driverPushConfigInfo.serviceTimeTo = serviceTimeModel.getTo();
            }
            // 订单类型
            if(StringUtils.isNotBlank(orderPushConfig.getOrderTyps())){
                List<String> orderTypeList = JacksonUtil.deserialize(orderPushConfig.getOrderTyps(),
                        new TypeToken<List<String>>() {}.getType());
                driverPushConfigInfo.orderTyps = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(orderTypeList)) {
                    orderTypeList.forEach(t -> {
                        driverPushConfigInfo.orderTyps.add(convertServiceType(t));
                    });
                }
            }
            // 车型
            if(StringUtils.isNotBlank(orderPushConfig.getDrvIntendVehicleType())){
                driverPushConfigInfo.drvIntendVehicleTypes = JacksonUtil.deserialize(orderPushConfig.getDrvIntendVehicleType(),
                        new TypeToken<List<Integer>>() {}.getType());
            }
            // 上车点距离
            driverPushConfigInfo.drvOrderDistance = ObjectUtils.defaultIfNull(orderPushConfig.getDrvOrderDistance(), 0);
            //司机配置语言
            driverPushConfigInfo.drvLanguage = ObjectUtils.defaultIfNull(orderPushConfig.getDrvLanguage(), Strings.EMPTY);
        }catch (Exception e){
            CommonLogger.INSTANCE.warn("convertToPushInfo error", e.getMessage());
        }
        return driverPushConfigInfo;
    }

    /**
     * 订单类型枚举转换
     * */
    private static int convertServiceType(String pushConfigType){
        switch (pushConfigType){
            case "1717":    // 接机
                return 2;
            case "1718":    // 送机
                return 1;
            case "1617":    // 接站
                return 5;
            case "1618":    // 送站
                return 4;
            case "appoint": // 预约打车
                return 12;
            default:
                return 0;
        }
    }
}
