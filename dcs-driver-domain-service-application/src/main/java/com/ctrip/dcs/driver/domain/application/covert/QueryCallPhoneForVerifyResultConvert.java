package com.ctrip.dcs.driver.domain.application.covert;
import com.google.common.collect.Lists;

import com.ctrip.dcs.driver.domain.account.PhoneInfo;
import com.ctrip.dcs.driver.domain.application.dto.PhoneInfoDTO;
import com.ctrip.dcs.driver.domain.application.condition.QueryCallPhoneForVerifyResultCondition;
import com.ctrip.model.QueryCallPhoneForVerifyResultRequestType;
import com.ctrip.model.QueryCallPhoneForVerifyResultResponseType;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryCallPhoneForVerifyResultConvert {

    public QueryCallPhoneForVerifyResultCondition buildCondition(QueryCallPhoneForVerifyResultRequestType requestType) {
        QueryCallPhoneForVerifyResultCondition condition = new QueryCallPhoneForVerifyResultCondition();
        condition.setCallTaskId(requestType.getCallTaskId());
        condition.setPhoneInfoDTOS(convert(requestType.getPhoneInfoList()));
        condition.setDeadline(requestType.getDeadline());
        return condition;
    }

    private List<PhoneInfoDTO> convert(List<PhoneInfo> phoneInfoList) {
        List<PhoneInfoDTO> phoneInfoDTOlist=Lists.newArrayList();
        for (PhoneInfo phoneInfo :phoneInfoList) {
        	phoneInfoDTOlist.add(convertFromPhoneInfo(phoneInfo));
        }
        return phoneInfoDTOlist;
    }

    private PhoneInfoDTO convertFromPhoneInfo(PhoneInfo phoneInfo) {
        PhoneInfoDTO phoneInfoDTO = new PhoneInfoDTO();
        phoneInfoDTO.setCountryCode(phoneInfo.getCountryCode());
        phoneInfoDTO.setPhoneNumber(phoneInfo.getPhoneNumber());
        return phoneInfoDTO;
    }

    public QueryCallPhoneForVerifyResultResponseType buildResponse(String code) {
        QueryCallPhoneForVerifyResultResponseType response = new QueryCallPhoneForVerifyResultResponseType();
        response.setCallResultStatus(code);
        return response;
    }
}
