package com.ctrip.dcs.driver.domain.application.domain.rigths;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel;
import com.ctrip.dcs.driver.value.rights.RightsRecordObject;
import com.google.gson.annotations.Expose;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 */
public class RightsRecordObjectImpl extends RightsRecordObject {
    private final DomainBeanFactory domainBeanFactory;

    public RightsRecordObjectImpl(DomainBeanFactory domainBeanFactory, RightsRecordModel rightsRecordModel) {
        this.domainBeanFactory = domainBeanFactory;

        this.rightsId = rightsRecordModel.getRightsIssueId();
        this.drivId = rightsRecordModel.getDrivId();
        this.rightsType = RightsTypeEnum.getByCode(rightsRecordModel.getRightsType());
        this.rightsName = rightsRecordModel.getRightsName();
        this.useLevel = LevelEnum.getByCode(rightsRecordModel.getUseLevel());
        this.levelName = rightsRecordModel.getLevelName();
        this.userOrderId = rightsRecordModel.getUserOrderId();
        this.purchaseOrderId = rightsRecordModel.getPurchaseOrderId();
        this.supplyOrderId = rightsRecordModel.getSupplyOrderId();
        this.punishOrderId = rightsRecordModel.getPunishOrderId();
        this.money = rightsRecordModel.getMoney();
        this.rightsUsedTime = rightsRecordModel.getRightsUseTime();
    }

    @Expose
    private Long rightsId;
    @Expose
    private Long drivId;
    @Expose
    private RightsTypeEnum rightsType;
    @Expose
    private String rightsName;
    @Expose
    private LevelEnum useLevel;
    @Expose
    private String levelName;
    @Expose
    private String userOrderId;
    @Expose
    private String purchaseOrderId;
    @Expose
    private String supplyOrderId;
    @Expose
    private String punishOrderId;
    @Expose
    private String money;
    @Expose
    private LocalDateTime rightsUsedTime;

    @Override
    public Long rightsId() {
        return this.rightsId;
    }

    @Override
    public Long drivId() {
        return this.drivId;
    }

    @Override
    public RightsTypeEnum rightsType() {
        return this.rightsType;
    }

    @Override
    public String rightsName() {
        return this.rightsName;
    }

    @Override
    public LevelEnum useLevel() {
        return this.useLevel;
    }

    @Override
    public String levelName() {
        return this.levelName;
    }

    @Override
    public String userOrderId() {
        return this.userOrderId;
    }

    @Override
    public String purchaseOrderId() {
        return this.purchaseOrderId;
    }

    @Override
    public String supplyOrderId() {
        return this.supplyOrderId;
    }

    @Override
    public String punishOrderId() {
        return this.punishOrderId;
    }

    @Override
    public String money() {
        return this.money;
    }

    @Override
    public LocalDateTime rightsUsedTime() {
        return this.rightsUsedTime;
    }
}
