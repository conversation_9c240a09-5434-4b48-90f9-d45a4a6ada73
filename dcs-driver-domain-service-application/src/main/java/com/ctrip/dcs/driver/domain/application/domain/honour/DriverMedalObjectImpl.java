package com.ctrip.dcs.driver.domain.application.domain.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonStringUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exector.SaveNoticeMedalExecutor;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalRankingPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormHonourWallDriverMedalInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.DynamicMedalInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.value.honour.DriverMedalObject;
import com.ctrip.dcs.driver.value.honour.MedalObject;
import com.google.gson.annotations.Expose;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class DriverMedalObjectImpl extends DriverMedalObject {

    private final DomainBeanFactory domainBeanFactory;
    private final boolean isQueryNew;

    private List<FormHonourWallDriverMedalInfo> driverFormMedalInfo = null;             //1
    private List<MedalBasicInfoModel> biDriverMedalStaticInfo = null;                   //2
    private DynamicMedalInfoModel dynamicInfoModel = null;                              //3
    private Map<String, DimPrdTrhDriverMedalRankingPO> biDriverMedalRankInfo = null;    //4
    private List<String> noticedMedalList = new ArrayList<>();                          //5
    private GoldMedalDriverModel goldMedalDriverModel = null;                           //6

    /**
     * 领域数据说明
     * 依赖如下数据：
     * 1、司机端 form配置
     * 2、BI dim_prd_trh_driver_medal_info
     * 3、BI adm_prd_trh_driver_honor_info
     * 4、BI dim_prd_trh_driver_medal_ranking
     * 5、司机端 driv_honour_medal_notice_record
     * 6、BI金牌司机数据 tourAIOneServiceProxy.getData
     * 单独查询勋章个数依赖：1&2&6（先查询缓存）
     * 查询勋章列表依赖：    1&2&3&6
     * 查询新勋章依赖：      1&2&3&4&5&6
     * */
    public DriverMedalObjectImpl(DomainBeanFactory domainBeanFactory, long driverId, int queryType) {
        this.domainBeanFactory = domainBeanFactory;
        this.isQueryNew = queryType == 2;
        this.driverId = driverId;
        this.queryGoldMedalInfo();
        this.queryDriverMedalCount();
    }

    @Expose
    private long driverId;
    @Expose
    private int totalCount;
    @Expose
    private LocalDateTime batchDataTime;

    @Override
    public long driverId() { return this.driverId; }

    @Override
    public int totalCount(){return this.totalCount;}

    @Override
    public LocalDateTime batchDataTime() {
        return this.batchDataTime;
    }

    @Override
    public MedalObject[] queryMedalList(){
        this.loadData();
        MedalObject[] localMedalList = new MedalObject[Math.addExact(biDriverMedalStaticInfo.size(), driverFormMedalInfo.size())];

        AtomicInteger index = new AtomicInteger();
        for(MedalBasicInfoModel m : biDriverMedalStaticInfo) {
            MedalObject medalObject = new MedalObjectImpl(this.domainBeanFactory, this.dynamicInfoModel, m, this.isQueryNew);
            if(this.isQueryNew) {
                if(medalObject.isNotice() && !this.noticedMedalList.contains(medalObject.code())) {
                    DimPrdTrhDriverMedalRankingPO driverMedalRankingPO = this.biDriverMedalRankInfo.get(m.getMedalCode());
                    if (Objects.nonNull(driverMedalRankingPO)) {
                        LocalDate localDate = LocalDateTimeUtils
                                .localDate(driverMedalRankingPO.getMedalGradeGetTime());
                        if (localDate.equals(LocalDate.now())) {
                            medalObject.updateNewMedalInfo(driverMedalRankingPO.getMedalGradeGetRank(),
                                    driverMedalRankingPO.getGradeGetDrvCnt());
                            localMedalList[index.getAndIncrement()] = medalObject;
                        }
                    }
                }
            } else {
                localMedalList[index.getAndIncrement()] = medalObject;
            }
        }

        for(FormHonourWallDriverMedalInfo medalInfo : driverFormMedalInfo) {
            MedalObject medalObject;
            boolean isGoldMedal = CommonStringUtils.GOLD_MEDAL.equalsIgnoreCase(medalInfo.getCode());       //金牌司机
            if(isGoldMedal) {
                medalObject = new MedalObjectImpl(this.domainBeanFactory, medalInfo, this.goldMedalDriverModel);
            } else {
                medalObject = new MedalObjectImpl(this.domainBeanFactory, medalInfo);
            }

            if(this.isQueryNew) {
                if(medalObject.isNotice()) {
                    String codeKey = medalObject.code();
                    if(isGoldMedal) {
                        codeKey = String.format("%s^%s", medalObject.code(), medalObject.useMonth());
                    }
                    if(!this.noticedMedalList.contains(codeKey)) {
                        localMedalList[index.getAndIncrement()] = medalObject;
                    }
                }
            } else {
                localMedalList[index.getAndIncrement()] = medalObject;
            }
        }

        MedalObject[] medalList = Arrays.copyOfRange(localMedalList, 0 ,index.get());
        // 查询过新勋章以后，需要更新勋章提示信息
        if (this.isQueryNew && medalList.length > 0) {
            this.domainBeanFactory.executorService().submit(() -> {
                new SaveNoticeMedalExecutor(this, this.domainBeanFactory).doWork(medalList);
            });
        }

        return medalList;
    }

    /**
     * 预查询数据
     * */
    private void loadData() {
        //1,2,3
        loadDriverFormMedalInfo();
        loadDriverMedalStaticInfo();
        loadBiDriverMedalInfo();

        if(this.isQueryNew) {
            // 4
            biDriverMedalRankInfo = domainBeanFactory.honourDBDataService().queryDriverMedalRankInfo(this.driverId);
            // 5
            noticedMedalList = domainBeanFactory.honourDBDataService().queryNoticedMedalList(this.driverId);
        }
    }

    /**
     * 荣誉墙配置信息
     * */
    private void loadDriverFormMedalInfo() {
        this.driverFormMedalInfo = domainBeanFactory.honourFormConfig().getHonourWallMedalInfo(this.driverId, this.isQueryNew, this.goldMedalDriverModel);
        if(CollectionUtils.isEmpty(this.driverFormMedalInfo)) {
            this.driverFormMedalInfo = new ArrayList<>();
        }
    }

    /**
     * BI勋章基础配置信息
     * */
    private void loadDriverMedalStaticInfo() {
        biDriverMedalStaticInfo = domainBeanFactory.honourRedisLogic().queryCurrentMedalBasicInfo();
        if(CollectionUtils.isEmpty(biDriverMedalStaticInfo)) {
            // 补偿查询
            List<MedalBasicInfoModel> medalBasicInfoModels = this.domainBeanFactory.medalInfoService().queryBasicMedalInfo();
            this.biDriverMedalStaticInfo = medalBasicInfoModels;
            this.domainBeanFactory.honourRedisLogic().saveCurrentMedalBasicInfo(medalBasicInfoModels);
        }
    }

    /**
     * 司机荣誉信息
     * */
    private void loadBiDriverMedalInfo() {
        AdmPrdTrhDriverHonorInfoPO biMedalInfo = domainBeanFactory.honourDBDataService().queryMaxBatchDataDetail(this.driverId);
        this.convertToModelInfo(biMedalInfo);
        if(Objects.nonNull(biMedalInfo)) {
            this.batchDataTime = LocalDateTimeUtils.localDateTime(biMedalInfo.getDataTime());
            // 同步更新勋章总数
            this.getAndUpdateBiMedalCount(biMedalInfo);
        }
    }

    /**
     * 查询勋章总数 : BI勋章数 + 荣誉墙点亮的勋章数（honourConfig + 本月是否金牌司机）
     * */
    private void queryDriverMedalCount() {
        long cacheMedalCount = this.getAndUpdateBiMedalCount(null);
        int goldMedalSumCount = 0;
        if(this.goldMedalDriverModel != null && this.goldMedalDriverModel.getTotalCount() > 0){
            goldMedalSumCount = 1;
        }
        long formMedalCount = Math.addExact(
                this.domainBeanFactory.honourFormConfig().getHonourWallMedalCount(driverId),
                goldMedalSumCount);
        this.totalCount = (int)Math.addExact(cacheMedalCount, formMedalCount);
    }

    /**
     * BI勋章数
     * */
    private long getAndUpdateBiMedalCount(AdmPrdTrhDriverHonorInfoPO biMedalInfo) {
        long medalCount = 0;
        if(Objects.nonNull(biMedalInfo)) {
            medalCount = biMedalInfo.getMedalGetCnt();
            this.domainBeanFactory.honourRedisLogic().saveDriverMedalCount(driverId, medalCount);
        } else {
            medalCount = this.domainBeanFactory.honourRedisLogic().getDriverMedalCount(driverId);
            if(medalCount == -1){
                medalCount = this.domainBeanFactory.honourDBDataService().queryDriverMedalCount(driverId);
                this.domainBeanFactory.honourRedisLogic().saveDriverMedalCount(driverId, medalCount);
            }
        }
        return medalCount;
    }

    /**
     * 只抽取需要的数据
     * */
    private void convertToModelInfo(AdmPrdTrhDriverHonorInfoPO biDriverMedalInfo) {
        this.dynamicInfoModel = new DynamicMedalInfoModel();
        if(Objects.isNull(biDriverMedalInfo)) {
            return;
        }
        if(CollectionUtils.isNotEmpty(this.biDriverMedalStaticInfo)) {
            biDriverMedalStaticInfo.forEach(m -> {
                switch (m.getType()){
                    case SERVICEDAYS:
                        this.dynamicInfoModel.setServiceDay(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getOrderDay(), 0L).intValue());
                        this.dynamicInfoModel.setServiceDayGrade(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getOrderDayMedalGrade(), 0L).intValue());
                        break;
                    case FINISHORDERS:
                        this.dynamicInfoModel.setOrderCount(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getOrderCnt(), 0).intValue());
                        this.dynamicInfoModel.setOrderCountGrade(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getOrderCntMedalGrade(), 0L).intValue());
                        break;
                    case GOODCOMMENTS:
                        this.dynamicInfoModel.setGoodComment(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getGoodComment(), 0).intValue());
                        this.dynamicInfoModel.setGoodCommentGrade(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getGoodCommentMedalGrade(), 0L).intValue());
                        break;
                    case GOODMANNER:
                        this.dynamicInfoModel.setGoodManner(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getTdhfwjTagMedal(), 0) > 0);
                        break;
                    case GOODCAR:
                        this.dynamicInfoModel.setGoodCar(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getCnzjTagMedal(), 0) > 0);
                        break;
                    case GOODMAP:
                        this.dynamicInfoModel.setGoodMap(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getHdtrlzTagMedal(), 0) > 0);
                        break;
                    case GOODLUGGAGE:
                        this.dynamicInfoModel.setGoodLuggage(
                                ObjectUtils.defaultIfNull(biDriverMedalInfo.getZdbnxlTagMedal(), 0) > 0);
                        break;
                    default:
                        break;
                }
            });
        }
    }

    /**
     * 查询金牌司机信息
     * */
    private void queryGoldMedalInfo(){
        //读缓存
        this.goldMedalDriverModel = this.domainBeanFactory.honourRedisLogic().getDriverGoldMedalInfo(this.driverId);

        if(Objects.nonNull(this.goldMedalDriverModel)){
            return;
        }

        this.goldMedalDriverModel = this.domainBeanFactory.medalInfoService().getGoldMedalDriverInfo(this.driverId);
        if(Objects.nonNull(this.goldMedalDriverModel)){
            //保存
            this.domainBeanFactory.honourRedisLogic().saveDriverGoldMedalInfo(this.driverId, this.goldMedalDriverModel);
        }
    }
}
