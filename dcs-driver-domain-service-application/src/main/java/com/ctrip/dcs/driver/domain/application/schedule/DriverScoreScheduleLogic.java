package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.domain.application.redis.PointInfoRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverPointRestfulShowInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Component
public class DriverScoreScheduleLogic {

    @Autowired
    private PointInfoRedisLogic pointInfoRedisLogic;

    @Autowired
    DriverPointRestfulShowInfoDao driverPointRestfulShowInfoDao;

    /**
     * 更新司机弹窗信息
     * 示例：{"driverIdList":"123456,456789","popType":2}
     * */
    @QSchedule("dcs.driver.score.pop.update")
    public void updateDriverScorePopInfo(Parameter parameter) {
        //此方法要在执行任务的入口方法里调用
        TaskMonitor monitor = TaskHolder.getKeeper();
        //记录任务日志
        Logger logger = monitor.getLogger();
        logger.info("dcs.driver.score.pop.update start");

        if(parameter == null){
            return;
        }

        String driverIdStr = StringUtils.defaultIfBlank(parameter.getString("driverIdList"), Strings.EMPTY);
        int popType = Integer.parseInt(StringUtils.defaultIfBlank(parameter.getString("popType"), "0"));

        if(StringUtils.isBlank(driverIdStr) || popType <= 0){
            return;
        }

        List<Long> driverIdList = ShoppingCollectionUtils.toList
                (driverIdStr.split(","), (ShoppingFuncUtils.Func1<String, Long>) Long::parseLong);
        if(CollectionUtils.isNotEmpty(driverIdList)){
            driverIdList.forEach(d ->{
                updateDriverPopInfo(d, popType);
                logger.info((String.format("updateDriverPopInfo [driverId:%s][popType:%s]", d, popType)));
            });
        }
        logger.info(String.format("[dcs.driver.score.pop.update][driverIdStr:%s][popType:%s]", driverIdStr, popType));
    }

    /**
     * 1、清除首页引导弹窗记录
     * 2、清除司机分首页过期分弹窗记录
     * */
    private void updateDriverPopInfo(long driverId, int popType) {
        try {
            DriverPointRestfulShowInfoPO driverPointRestfulShowInfoPO = driverPointRestfulShowInfoDao.findOne(driverId);
            if(Objects.isNull(driverPointRestfulShowInfoPO)){
                return;
            }

            switch (popType) {
                case 1:
                    driverPointRestfulShowInfoPO.setNovicePointGuidePop(0);
                    driverPointRestfulShowInfoPO.setNovicePointGuidePopTime(Timestamp.valueOf(LocalDateTime.now()));
                    driverPointRestfulShowInfoDao.update(driverPointRestfulShowInfoPO);
                    pointInfoRedisLogic.clearDriverPointInfo(driverId);
                    break;
                case 2:
                    driverPointRestfulShowInfoPO.setNovicePointVoidancePop(0);
                    driverPointRestfulShowInfoPO.setNovicePointVoidancePopTime(Timestamp.valueOf(LocalDateTime.now()));
                    driverPointRestfulShowInfoDao.update(driverPointRestfulShowInfoPO);
                    pointInfoRedisLogic.clearDriverPointInfo(driverId);
                    break;
                default:
                    break;
            }
        }catch (Exception e){
            LoggerFactory.getLogger(DriverScoreScheduleLogic.class).error(e.getMessage());
        }
    }
}
