package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.dcs.driver.value.account.AccountUidInfo;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper;
import com.ctrip.dcs.driver.account.infrastructure.gateway.EmailServiceGateway;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.EmailNotifyConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLogType;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.account.infrastructure.value.SendEmailCondition;
import com.ctrip.dcs.driver.account.infrastructure.value.emil.EmailConfigInfo;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.service.GmsTransportDomainProxyService;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideAllInfoDTO;
import com.ctrip.dcs.go.log.Log;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class GuideBindMobilePhoneSchedule {
    Log log = Log.getInstance(GuideBindMobilePhoneSchedule.class);
    @Autowired
    private AccountMapperDao accountMapperDao;
    @Autowired
    private GmsTransportDomainProxyService gmsTransportDomainProxyService;
    @Autowired
    private UserCenterAccountGateway userCenterAccountGateway;
    @Autowired
    private DirectorRedis directorRedis;
    @Autowired
    private EmailServiceGateway emailServiceGateway;
    @Autowired
    private EmailNotifyConfig emailNotifyConfig;
    @Autowired
    private DriverAccountConfig driverAccountConfig;
    @Autowired
    private AccountChangeLogHelper accountChangeLogHelper;

    private static final String PROGRESS_KEY = "dcs.driver.account.guider.bind.mobilephone.job.progress";

    private static final long PROCESS_KEY_EXPIRE_SECONDS = 30 * 24 * 3600;

    @QSchedule("dcs.driver.account.guider.bind.mobilephone.job")
    public void onExecute(Parameter parameter) {
        List<Pair<String, String>> failedGuideList = Lists.newArrayList();
        // 指定uid
        String uids = parameter.getString("uidList");
        if (StringUtils.isNotBlank(uids)) {
            log.info("bind guide mobile phone start", "uidList : " + uids);
            List<String> uidList = Lists.newArrayList(uids.split(","));
            if (CollectionUtils.isNotEmpty(uidList)) {
                List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryByUid(uidList);
                accountMapperPOList = accountMapperPOList.stream()
                        .filter(o -> AccountSouceEnum.isGuide(o.getSource()))
                        .sorted(Comparator.comparing(o -> Long.valueOf(o.getSourceId())))
                        .collect(Collectors.toList());
                batchBindMobilePhone(accountMapperPOList, failedGuideList);
            }
            sendBindEmail(failedGuideList);
            return;
        }

        TaskMonitor taskMonitor = TaskHolder.getKeeper();

        Long startId = getStartId(parameter);
        log.info("bind guide mobile phone start", "start id : " + startId);
        while (true) {
            if (taskMonitor.isStopped()) {
                log.error("bind guide mobile phone job stop", "current id : " + startId);
                break;
            }
            log.info("bind guide mobile phone", "current id : " + startId);
            List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryByPage(startId, 100);
            if (CollectionUtils.isEmpty(accountMapperPOList)) {
                break;
            }
            batchBindMobilePhone(accountMapperPOList, failedGuideList);
            startId = accountMapperPOList.get(accountMapperPOList.size() - 1).getId();
            directorRedis.set(PROGRESS_KEY, startId, PROCESS_KEY_EXPIRE_SECONDS);
        }
        sendBindEmail(failedGuideList);
        log.info("bind guide mobile phone finish", "last id : " + startId);
    }

    private Long getStartId(Parameter parameter) {
        String startId = parameter.getString("startId");
        if (StringUtils.isNotBlank(startId)) {
            log.info("bind guide mobile phone get start id from param", startId);
            return Long.valueOf(startId);
        }
        String redisStartId = directorRedis.get(PROGRESS_KEY);
        if (StringUtils.isNotBlank(redisStartId)) {
            log.info("bind guide mobile phone get start id from redis", redisStartId);
            return Long.valueOf(redisStartId);
        }
        log.info("bind guide mobile phone get start id from 0", "0");
        return 0L;
    }

    private void sendBindEmail(List<Pair<String, String>> failedGuideList) {
        EmailConfigInfo emailConfig = emailNotifyConfig.getSyncGuideAccountResultConfig();
        if (CollectionUtils.isEmpty(failedGuideList) || emailConfig == null) {
            return;
        }
        String guideList = failedGuideList.stream().map(o -> String.format("guide id: %s, desc : %s", o.getLeft(), o.getRight()))
                .collect(Collectors.joining("<br/>"));
        SendEmailCondition sendEmailCondition = new SendEmailCondition();
        sendEmailCondition.setSendCode(emailConfig.getSendCode());
        sendEmailCondition.setSender(emailConfig.getSenderEmail());
        sendEmailCondition.setReceiver(Lists.newArrayList(emailConfig.getReceiverEmails().split(",")));
        sendEmailCondition.setSubject(emailConfig.getSubjectTemplate());
        sendEmailCondition.setBodyContent(String.format(emailConfig.getContentTemplate(), "bind phone", guideList));
        sendEmailCondition.setBodyHtml(true);
        emailServiceGateway.sendEmail(sendEmailCondition);
    }

    private void batchBindMobilePhone(List<AccountMapperPO> accountMapperPOList, List<Pair<String, String>> failedGuideList) {
        for (AccountMapperPO accountMapperPO : accountMapperPOList) {
            // 筛选向导身份
            if (!AccountSouceEnum.isGuide(accountMapperPO.getSource())) {
                continue;
            }
            try {
                GuideAllInfoDTO guideAllInfoDTO = gmsTransportDomainProxyService.queryGuideAllInfo(Long.parseLong(accountMapperPO.getSourceId()));
                if (guideAllInfoDTO == null) {
                    failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), "no guide info"));
                    Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "no_guide");
                    log.warn("bind guide mobile phone failed", "guide detail info is null, guide id: " + accountMapperPO.getSourceId());
                    continue;
                }

                String guideUid = guideAllInfoDTO.getBuid();
                String guideTelAreaCode = guideAllInfoDTO.getTelAreaCode();
                String guideTelephoneNum = guideAllInfoDTO.getTelephoneNum();
                AccountInfoResponseType accountInfo = userCenterAccountGateway.getAccountByUid(guideUid);
                if (accountInfo == null) {
                    failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), "no account, guide uid : " + guideUid));
                    Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "no_account");
                    log.warn("bind guide mobile phone failed", "guide account not exist : " + guideUid);
                    continue;
                }
                if (StringUtils.isNotBlank(accountInfo.getPhoneNumber())) {
                    // 账户已绑定了手机号，则不绑定
                    if (ObjectUtils.notEqual(guideTelAreaCode, accountInfo.getCountryCode()) ||
                            ObjectUtils.notEqual(guideTelephoneNum, accountInfo.getPhoneNumber())) {
                        // 手机号不一致
                        Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "phone_conflict");
                        failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), String.format("account has bound another phone, uid : %s, guide phone: %s, account phone : %s",
                                guideUid, guideTelAreaCode + "-" + guideTelephoneNum, accountInfo.getCountryCode() + "-" + accountInfo.getPhoneNumber())));
                        log.warn("bind guide mobile phone failed", String.format("uid has bind another phone, uid : %s, guide phone: %s, account phone : %s",
                                guideUid, guideTelAreaCode + "-" + guideTelephoneNum, accountInfo.getCountryCode() + "-" + accountInfo.getPhoneNumber()));
                    } else {
                        // 手机号一致，跳过
                        Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "same_phone");
                        log.info("bind guide mobile phone success", "phone has bind");
                    }
                    continue;
                }
                // 用户未绑定手机号，查询手机号是否绑定uid
                AccountUidInfo uidByPhone = userCenterAccountGateway.queryAccountUidByPhone(guideTelAreaCode, guideTelephoneNum);
                if (uidByPhone != null && ObjectUtils.notEqual(uidByPhone.getUid(), guideUid)) {
                    // 手机号已经绑定了uid，且uid与向导的uid不一致， 不绑定，跳过
                    Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "uid_conflict");
                    failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), String.format("phone has bound another uid, guide uid : %s, guide phone: %s, another uid : %s",
                            guideUid, guideTelAreaCode + "-" + guideTelephoneNum, uidByPhone)));
                    log.warn("bind guide mobile phone failed", String.format("phone has bind another uid, guide uid : %s, guide phone: %s, another uid : %s",
                            guideUid, guideTelAreaCode + "-" + guideTelephoneNum, uidByPhone));
                    continue;
                }
                // uid未绑定手机号，且手机号也未绑定uid，则说明可以绑定
                boolean result = userCenterAccountGateway.bindMobilePhone(guideUid, guideTelAreaCode, guideTelephoneNum, "SYSTEM");
                if (result) {
                    // 绑定成功
                    Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "bind_success");
                    log.info("bind guide mobile phone success", String.format("uid : %s, phone : %s", guideUid, guideTelAreaCode + "-" + guideTelephoneNum));
                    accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.BIND_PHONE, "System", accountMapperPO.getUid(), "", guideTelAreaCode + "-" + guideTelephoneNum));
                } else {
                    // 绑定失败，可能是手机号错误
                    failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), String.format("bind guide mobile phone failed, uid : %s, phone : %s", guideUid, guideTelAreaCode + "-" + guideTelephoneNum)));
                    Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "bind_failed");
                    log.warn("bind guide mobile phone failed", String.format("uid : %s, phone : %s", guideUid, guideTelAreaCode + "-" + guideTelephoneNum));
                }
                if (driverAccountConfig.getJobSleepMills() > 0) {
                    try {
                        Thread.sleep(driverAccountConfig.getJobSleepMills());
                    } catch (Exception e) {
                        log.warn("sleep exception", e);
                    }
                }
            } catch (Exception e) {
                // 绑定失败，可能是手机号错误
                failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), "bind guide mobile phone unknown exception, uid: %s" + accountMapperPO.getUid()));
                Cat.logEvent(CatEventType.GUIDE_BIND_PHONE, "bind_exception");
                log.warn("bind guide mobile phone exception", String.format("uid : %s", accountMapperPO.getUid()), e, Maps.newHashMap());
            }
        }
    }

}
