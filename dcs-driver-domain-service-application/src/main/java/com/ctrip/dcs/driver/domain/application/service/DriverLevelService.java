package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo;

import java.math.BigDecimal;
import java.util.List;

public interface DriverLevelService {
    FormLevelInfo calcDriverLevel(Long driverId, Long cityId, BigDecimal servicePoint, BigDecimal activityPoint, BigDecimal safePoint);

    FormLevelInfo getDefaultForm(Long cityId, List<FormLevelInfo> formLevelInfos);
}
