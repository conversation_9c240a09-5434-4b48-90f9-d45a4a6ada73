package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.account.application.schedule.PhoneCheckTaskProcessScheduler;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.domain.application.condition.CallPhoneForVerifyCondition;
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CallPhoneForVerifyService {
    @Resource
    private PhoneCheckTaskFactory phoneCheckTaskFactory;
    @Resource
    private PhoneCheckTaskTable phoneCheckTaskTable;
    @Resource
    private PhoneCheckTaskProcessScheduler scheduler;

    public Long call(CallPhoneForVerifyCondition condition) {
        DriverEntity virtualDriver = this.createVirtualDriver(condition);
        PhoneCheckTaskEntity task = phoneCheckTaskFactory.applyPhoneCallTask(virtualDriver, condition.getLocale());
        phoneCheckTaskTable.insert(task);
        scheduler.call(task);
        return task.getId();
    }

    private DriverEntity createVirtualDriver(CallPhoneForVerifyCondition condition) {
        DriverEntity driver = new DriverEntity();
        driver.setPhonePrefix(condition.getCountryCode());
        driver.setPhoneNumber(condition.getPhoneNumber());
        driver.setDriverId(-1L);
        return driver;
    }
}
