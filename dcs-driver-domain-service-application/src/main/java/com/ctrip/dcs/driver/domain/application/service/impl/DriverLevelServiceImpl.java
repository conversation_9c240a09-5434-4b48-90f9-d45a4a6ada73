package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.DriverBizGrayService;
import com.ctrip.dcs.driver.domain.application.service.DriverLevelService;
import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.DriverGreyConfig;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
public class DriverLevelServiceImpl implements DriverLevelService {
    @Resource
    private CityRepository cityRepository;
    @Resource
    SystemQConfig systemQConfig;
    @Resource
    private LevelConfig levelConfig;
    @Resource
    private DriverBizGrayService driverBizGrayService;

    private static final String DEFAULT_LEVEL_NAME_KEY = "default_levevl_name";
    private static final String DEFAULT_OVERSEA_LEVEL_NAME_KEY = "default_ovearsea_levevl_name";

    /**
     * 计算司机等级（仅参考服务分和活跃分和安全分，不考虑排名）
     */
    @Override
    public FormLevelInfo calcDriverLevel(Long driverId, Long cityId, BigDecimal servicePoint, BigDecimal activityPoint, BigDecimal safePoint) {
        List<FormLevelInfo> formLevelInfos = levelConfig.getCityDriverLevel(cityId);
        FormLevelInfo defaultForm = getDefaultForm(cityId, formLevelInfos);
        if (CollectionUtils.isEmpty(formLevelInfos)) {
            return defaultForm;
        }
        safePoint = Optional.ofNullable(safePoint).orElse(BigDecimal.ZERO);
        // 保证等级从小到大排序
        int index = -1;
        // for loop 找到第1次不满足条件跳出循环，记录上一次等级信息
        boolean levelWithSafePointGray = driverBizGrayService.queryBizGraySwitch(DriverGreyConfig.LEVEL_WITH_SAFE_POINT, driverId, null, cityId, null, null, true);
        for (FormLevelInfo formLevelInfo : formLevelInfos) {
            boolean levelMatch = levelWithSafePointGray ? isLevelMatch(formLevelInfo, servicePoint, activityPoint, safePoint) : isLevelMatch(formLevelInfo, servicePoint, activityPoint);
            if (levelMatch) {
                index++;
            } else {
                break;
            }
        }

        return index == -1 ? defaultForm : formLevelInfos.get(index);
    }

    private boolean isLevelMatch(FormLevelInfo formLevelInfo, BigDecimal servicePoint, BigDecimal activityPoint) {
        return servicePoint.compareTo(Optional.ofNullable(formLevelInfo.getDriverPointLow()).orElse(BigDecimal.ZERO)) >= 0
                && activityPoint.compareTo(Optional.ofNullable(formLevelInfo.getActivityLow()).orElse(BigDecimal.ZERO)) >= 0;
    }

    private boolean isLevelMatch(FormLevelInfo formLevelInfo, BigDecimal servicePoint, BigDecimal activityPoint, BigDecimal safePoint) {
        return isLevelMatch(formLevelInfo, servicePoint, activityPoint) && safePoint.compareTo(Optional.ofNullable(formLevelInfo.getSafePointLow()).orElse(BigDecimal.ZERO)) >= 0;
    }

    @Override
    public FormLevelInfo getDefaultForm(Long cityId, List<FormLevelInfo> formLevelInfos) {
        if (CollectionUtils.isNotEmpty(formLevelInfos) && (formLevelInfos.get(0).getLevel() == LevelEnum.LEVEL_TYPE_BRONZE.getCode() ||
                formLevelInfos.get(0).getLevel() == LevelEnum.LEVEL_TYPE_REGULAR_MEDAL.getCode())) {
            return formLevelInfos.get(0);
        }

        City city = cityRepository.findOne(cityId);
        if (city != null && !city.isChineseMainland()) {
            // 境外
            FormLevelInfo formLevelInfo = new FormLevelInfo();
            formLevelInfo.setId(0);
            formLevelInfo.setLevel(LevelEnum.LEVEL_TYPE_REGULAR_MEDAL.getCode());
            formLevelInfo.setLevelName(systemQConfig.getString(DEFAULT_OVERSEA_LEVEL_NAME_KEY));
            return formLevelInfo;
        }

        FormLevelInfo formLevelInfo = new FormLevelInfo();
        formLevelInfo.setId(0);
        formLevelInfo.setLevel(0);
        formLevelInfo.setLevelName(systemQConfig.getString(DEFAULT_LEVEL_NAME_KEY));
        return formLevelInfo;
    }
}
