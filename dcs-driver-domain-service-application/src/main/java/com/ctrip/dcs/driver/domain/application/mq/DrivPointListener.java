package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.driver.domain.application.service.DriverLevelService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DriverPointTotalInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DriverPointTotalInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;
import java.util.Optional;

@Component
public class DrivPointListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DrivPointListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    @Autowired
    private DriverPointTotalInfoDao dao;

    @Autowired
    private DriverLevelService driverLevelService;

    @Override
    @QmqConsumer(prefix = "dcs.canal.driverpoint.data.change", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        String data = message.getStringProperty("dataChange");
        DataChange dataChange = JacksonUtil.deserialize(data, DataChange.class);
        if (Objects.isNull(dataChange)) {
            return;
        }
        if (!dataChange.getTableName().equals("driver_point_total_info")) {
            return;
        }
        try {
            if (dataChange.isInsert()) {
                DriverPointTotalInfoPO po = buildPointPo(dataChange);
                dao.insertDriverPointTotal(po.getId(), po.getDrivLevel(), po.getDriverId(), po.getCityId(), po.getOrderInfoPoint(), po.getRewardInfoPoint(), po.getDriverStatus(), po.getCreateTime(), po.getDatachangeLasttime(), po.getSafePoint());
            } else if (dataChange.isUpate()) {
                DriverPointTotalInfoPO driverPointTotalInfoPO = dao.queryByPk(Long.valueOf(dataChange.getBeforeColumnValue("id")));
                if (Objects.isNull(driverPointTotalInfoPO) || driverPointTotalInfoPO.getDatachangeLasttime().after(Timestamp.valueOf(dataChange.getAfterColumnValue("datachange_lasttime")))) {
                    return;
                }
                DriverPointTotalInfoPO po = buildPointPo(dataChange);
                dao.updateDriverPointTotal(po.getCityId(), po.getDrivLevel(), po.getOrderInfoPoint(), po.getRewardInfoPoint(), po.getSafePoint(), po.getDriverStatus(), po.getDatachangeLasttime(), po.getId());
            } else {
                dao.deleteDriverPointTotal(Long.valueOf(dataChange.getBeforeColumnValue("id")));
            }
        } catch (Exception e) {
            LOGGER.warn(e);
        }
    }

    private DriverPointTotalInfoPO buildPointPo(DataChange dataChange) {
        String cityId = dataChange.getAfterColumnValue("city_id");
        String driverId = dataChange.getAfterColumnValue("driver_id");
        BigDecimal orderPoint = new BigDecimal(dataChange.getAfterColumnValue("order_info_point"));
        BigDecimal activityPoint = new BigDecimal(dataChange.getAfterColumnValue("reward_info_point"));
        BigDecimal safePoint = new BigDecimal(Optional.ofNullable(dataChange.getAfterColumnValue("safe_point")).orElse("0.00"));
        FormLevelInfo driverLevel = driverLevelService.calcDriverLevel(Long.valueOf(driverId), Long.valueOf(cityId), orderPoint, activityPoint, safePoint);
        DriverPointTotalInfoPO driverPointTotalInfoPO = new DriverPointTotalInfoPO();
        driverPointTotalInfoPO.setId(Long.valueOf(dataChange.getAfterColumnValue("id")));
        driverPointTotalInfoPO.setDrivLevel(driverLevel.getLevel());
        driverPointTotalInfoPO.setDriverId(driverId);
        driverPointTotalInfoPO.setCityId(cityId);
        driverPointTotalInfoPO.setOrderInfoPoint(orderPoint);
        driverPointTotalInfoPO.setRewardInfoPoint(activityPoint);
        driverPointTotalInfoPO.setSafePoint(safePoint);
        driverPointTotalInfoPO.setDriverStatus(Integer.valueOf(dataChange.getAfterColumnValue("driver_status")));
        driverPointTotalInfoPO.setCreateTime(Timestamp.valueOf(dataChange.getAfterColumnValue("create_time")));
        driverPointTotalInfoPO.setDatachangeLasttime(Timestamp.valueOf(dataChange.getAfterColumnValue("datachange_lasttime")));
        return driverPointTotalInfoPO;
    }

}
