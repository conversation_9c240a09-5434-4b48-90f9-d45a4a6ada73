package com.ctrip.dcs.driver.domain.application.exector.freeze;

import com.ctrip.dcs.driver.domain.infrastructure.adapter.db.DriverFreezeAdapter;
import com.ctrip.dcs.driver.domain.infrastructure.condition.freeze.DriverApplyFreezeRecordCondition;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.DriverApplyFreezeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.CreateDriverApplyFreezeRecordRequestType;
import com.ctrip.model.CreateDriverApplyFreezeRecordResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Component
public class CreateDriverApplyFreezeRecordEngine extends ShoppingExecutor<CreateDriverApplyFreezeRecordRequestType, CreateDriverApplyFreezeRecordResponseType> {

    @Autowired
    private DriverFreezeAdapter driverFreezeAdapter;

    @Override
    public CreateDriverApplyFreezeRecordResponseType execute(CreateDriverApplyFreezeRecordRequestType request) {
        driverFreezeAdapter.createDriverApplyFreezeRecord(convert2Record(request));
        return ServiceResponseUtils.success(new CreateDriverApplyFreezeRecordResponseType());
    }

    private DriverApplyFreezeRecordCondition convert2Record(CreateDriverApplyFreezeRecordRequestType request) {
        DriverApplyFreezeRecordCondition record = new DriverApplyFreezeRecordCondition();
        record.setDriverId(request.getDriverId());
        record.setApplyFreezeReason(request.getApplyFreezeReason());
        record.setApplyFreezeHours(request.getApplyFreezeHours());
        LocalDateTime localDateTime = LocalDateTimeUtils.localDateTime(request.getApplyFreezeTime(), LocalDateTime.now());
        record.setApplyFreezeTime(Timestamp.valueOf(localDateTime));
        record.setApplyFreezeResult(request.getApplyFreezeResult());
        record.setApplyFreezeResultDesc(request.getApplyFreezeResultDesc());
        record.setDeviceId(request.getDeviceId());
        return record;
    }
}
