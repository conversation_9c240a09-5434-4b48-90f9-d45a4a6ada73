package com.ctrip.dcs.driver.trip.university.application.schedule;

import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverForDisCardDTO;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


@Component
public class UpdateTripUniversityUserPhoneAndEmailWhenDuplicateSchedule {

    @Autowired
    TripUniversityService tripUniversityService;

    @Autowired
    AccountMapperDao accountMapperDao;

    @Autowired
    @Qualifier("TripUniversityThreadPool")
    ExecutorService executorService;

    @Autowired
    TripUniversityQconfig tripUniversityQconfig;

    @QSchedule("update.trip.university.driver.phone.and.email.when.duplicate.job")
    public void onExecute(Parameter parameter) {

        String drvIds = parameter.getString("drvIds");
        String phoneSuffix = Optional.ofNullable(parameter.getString("phoneSuffix")).orElse("0");
        String emailPrefix = Optional.ofNullable(parameter.getString("emailPrefix")).orElse("0");
        updateDriverInfo(Arrays.stream(drvIds.split(",")).map(drvId -> TripUniversityDriverForDisCardDTO.builder().emailPrefix(emailPrefix).phoneSuffix(phoneSuffix).drvId(drvId).build()).collect(Collectors.toList()));
    }

    @SneakyThrows
    private void updateDriverInfo(List<TripUniversityDriverForDisCardDTO> list) {
        Logger logger = TaskHolder.getKeeper().getLogger();
        TaskHolder.getKeeper().setRateCapacity(list.size());

        AtomicInteger count = new AtomicInteger();
        Lists.partition(list, tripUniversityQconfig.getSyncBatchSize()).forEach(item -> {
            try{
                //同步
                CompletableFuture.allOf(list.stream().map(
                    data -> CompletableFuture.supplyAsync(
                      () -> tripUniversityService.updateDrvPhoneAndEmailForDuplicateProcess(data), executorService))
                  .toArray(CompletableFuture[]::new)).get();
                count.addAndGet(item.size());
                TaskHolder.getKeeper().setRate(count.get());
                logger.info("update.trip.university.driver.phone.and.email.when.duplicate.job, List: {}", item);
            }catch (Exception e) {
                logger.info("dcs.sync.driver.info.to.trip.university.job.error {}", e.getMessage());
                Cat.logEvent("dcs.sync.driver.info.to.trip.university.job.error", e.getMessage());
            }
            });
    }
}
