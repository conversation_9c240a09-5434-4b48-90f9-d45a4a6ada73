package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.BatchQueryAccountByUIDRequestType;
import com.ctrip.dcs.driver.domain.account.BatchQueryAccountByUIDResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class BatchQueryAccountByUIDEngine extends ShoppingExecutor<BatchQueryAccountByUIDRequestType, BatchQueryAccountByUIDResponseType> implements Validator<BatchQueryAccountByUIDRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public BatchQueryAccountByUIDResponseType execute(BatchQueryAccountByUIDRequestType request) {


        BatchQueryAccountByUIDResponseType resp = new BatchQueryAccountByUIDResponseType();
        resp.setAccountDetailList(Lists.newArrayList());
        List<AccountInfoDTO> accountList = accountService.batchGetAccountInfoByUID(request.getUidList());
        if (CollectionUtils.isNotEmpty(accountList)) {
            resp.setAccountDetailList(accountList.stream().map(AccountDetailConvert::convert).collect(Collectors.toList()));
        }
        Cat.logEvent(CatEventType.BATCH_QUERY_BY_UID_RESULT, CollectionUtils.isNotEmpty(accountList) ? "1" : "0");
        DriverMetricUtil.batchMetricUdlIsValid2(accountList);
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public Boolean isEmptyResult(BatchQueryAccountByUIDResponseType resp) {
        return !(resp!=null&&CollectionUtils.isNotEmpty(resp.getAccountDetailList()));

    }
}
