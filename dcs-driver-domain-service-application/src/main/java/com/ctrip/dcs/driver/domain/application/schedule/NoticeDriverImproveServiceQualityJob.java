package com.ctrip.dcs.driver.domain.application.schedule;

import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.DriverOrderServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverNoticeConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.ImproveServiceQualityConfig;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.dto.QuestionResultDTO;
import com.ctrip.dcs.driver.domain.application.util.CollectionUtil;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.QuestionResultDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.QuestionResultPO;
import com.ctrip.dcs.driver.message.dto.PushMessageDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.utils.DateUtil;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.self.order.query.api.BatchQueryDriverOrderBaseInfoRequestType;
import com.ctrip.dcs.self.order.query.api.BatchQueryDriverOrderBaseInfoResponseType;
import com.ctrip.dcs.self.order.query.dto.DriverBaseOrderInfoDTO;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.language.Language;
import com.ctrip.igt.framework.common.language.LanguageContext;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class NoticeDriverImproveServiceQualityJob {
    private static final com.ctrip.igt.framework.common.clogging.Logger LOGGER = LoggerFactory.getLogger(NoticeDriverImproveServiceQualityJob.class);
    public static final String LOG_TITLE = "noticeImproveServiceQualityJob";

    public static final String AM_START_TIME = "yyyy-MM-dd 20:00:00";
    public static final String AM_END_TIME = "yyyy-MM-dd 09:59:59";

    public static final String PM_START_TIME = "yyyy-MM-dd 10:00:00";
    public static final String PM_END_TIME = "yyyy-MM-dd 19:59:59";

    private static final Integer mainLandCode = 0;
    private static final String DRIVER_IMPROVE_SERVICE_NOTICE_TEMPLATE_ID = "4092";

    @Autowired
    private QuestionResultDao questionResultDao;
    @Autowired
    private CommonMultipleLanguages languages;
    @Autowired
    private DriverOrderServiceProxy driverOrderService;
    @Autowired
    private DriverNoticeConfig config;

    /**
     * 提醒司机提高服务质量
     * 备注：这其实是一个OLAP型的需求，但是为了加快上线，只能放在这里了
     * idev:http://conf.ctripcorp.com/pages/viewpage.action?pageId=3985293559
     */
    @QSchedule("dcs.driver.improve.service.quality.job")
    public void execJob(Parameter parameter) {
        TaskMonitor taskMonitor = TaskHolder.getKeeper();
        Logger logger = taskMonitor.getLogger();
        logger.info(LOG_TITLE, "job starting");
        // step1: 获取指定范围内负面评论的用户订单号。根据目前的统计，现在数据量在200左右，不需要走分页
        List<QuestionResultPO> questionResults = this.queryNegativeQuestions();
        if (CollectionUtils.isEmpty(questionResults)) {
            logger.info(LOG_TITLE, "no negative question results found");
            return;
        }
        logger.info("questionResults:{}", JsonUtil.toString(questionResults));
        if (questionResults.size() >= 10000) {
            // 这本来是个olap型的业务，超过这个量要考虑移走(目前线上最高是400)
            Cat.logEvent("NoticeDriverImproveServiceQualityJob", "BIG_SIZE");
        }

        // step2: 根据用户订单号查询司机单信息，下游限制了查询量，要进行批量查询
        Map<String/*用户订单号*/, List<DriverBaseOrderInfoDTO>> driverOrderMap = this.queryAllDriverOrderInfo(questionResults);
        if (Objects.isNull(driverOrderMap)) {
            logger.info(LOG_TITLE, "can't find user order info");
            return;
        }

        // step3：填充司机信息 & 异步推送到司机端
        questionResults.stream()
                .map(result -> this.covert2DTO(result, driverOrderMap))
                .filter(Objects::nonNull)// 可能没查到订单信息
                .filter(QuestionResultDTO::isMainLand)// 只处理境内的
                .collect(Collectors.groupingBy(QuestionResultDTO::getDriverId)) // 根据司机id分组，保证一个司机只发一次消息
                .forEach(this::sendDriverImproveServiceMsg);// 发送消息

        logger.info(LOG_TITLE, "job finished");
    }

    private List<QuestionResultPO> queryNegativeQuestions() {
        // 确认查询数据时间范围
        LocalDateTime jobExecTime = LocalDateTime.now();
        int currentHour = jobExecTime.getHour();
        String startTime;
        String endTime;
        boolean amExec = currentHour < 12;// 是否上午执行
        if (amExec) {
            // 昨天20点到今天10点的数据
            LocalDateTime startDate = jobExecTime.minusDays(1);
            startTime = DateUtil.formatDate(startDate, AM_START_TIME);
            endTime = DateUtil.formatDate(jobExecTime, AM_END_TIME);
        } else {
            // 今天10点到今天20点的数据
            startTime = DateUtil.formatDate(jobExecTime, PM_START_TIME);
            endTime = DateUtil.formatDate(jobExecTime, PM_END_TIME);
        }

        // 确定查哪些问题
        List<Long> configQuestionIds = config.getImproveServiceQualityConfigs().stream()
                .map(ImproveServiceQualityConfig::getQuestionIds)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        List<QuestionResultPO> questionResults = questionResultDao.queryNegativeResult(configQuestionIds, startTime, endTime);
        return questionResults;
    }

    private Map<String, List<DriverBaseOrderInfoDTO>> queryAllDriverOrderInfo(List<QuestionResultPO> questionResults) {
        List<String> userOrderIds = questionResults.stream()
                .map(QuestionResultPO::getBusinessId)
                .distinct()
                .collect(Collectors.toList());

        List<DriverBaseOrderInfoDTO> driverOrderList = CollectionUtil.slidingWindow(userOrderIds, 200).stream()
                .map(this::pageQueryDriverOrderInfo)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        return driverOrderList.stream().collect(Collectors.groupingBy(DriverBaseOrderInfoDTO::getUserOrderId));
    }

    private List<DriverBaseOrderInfoDTO> pageQueryDriverOrderInfo(List<String> userOrderIds) {
        List<String> categoryList = Lists.newArrayList();
        categoryList.add("airport_pickup");// 接机
        categoryList.add("airport_dropoff");// 接站
        categoryList.add("station_pickup");// 送机
        categoryList.add("station_dropoff");// 送站
        categoryList.add("point_to_point");// 点对点

        List<Integer> statusList = Lists.newArrayList();
        statusList.add(700);// 确认款项（完成履约）

        BatchQueryDriverOrderBaseInfoRequestType request = new BatchQueryDriverOrderBaseInfoRequestType();
        request.setUserOrderIds(userOrderIds);
        request.setCategoryList(categoryList);
        request.setStatusList(statusList);

        try {
            BatchQueryDriverOrderBaseInfoResponseType response = driverOrderService.batchQueryDriverOrderBaseInfo(request);
            if (CollectionUtils.isEmpty(response.getDriverOrderList())) {
                return null;
            }
            return response.getDriverOrderList();
        } catch (Exception e) {
            // olap型业务，非核心
            LOGGER.warn(e.getMessage(), e);
        }
        return null;
    }

    private void sendDriverImproveServiceMsg(Long driverId, List<QuestionResultDTO> questions) {
        // 目前仅支持中文,后续支持境外的话，要根据司机id查locale
        String noticeContent = this.createNoticeContent(questions,"zh-CN");
        PushMessageDTO messageTemplate = new PushMessageDTO();
        messageTemplate.setDriverIds(Lists.newArrayList(driverId));
        messageTemplate.setTemplateId(DRIVER_IMPROVE_SERVICE_NOTICE_TEMPLATE_ID);
        Map<String, String> sharkValues = Maps.newHashMap();
        sharkValues.put("notice", noticeContent);// 请司机师傅严格按照平台服务标准，${notice}，否则可能产生判罚并扣分
        messageTemplate.setSharkValues(sharkValues);

        String data = JsonUtil.toString(messageTemplate);
        Map<String, Object> qmqContent = Maps.newHashMap();
        qmqContent.put("data", data);
        QmqProducerProvider.sendMessage(Constants.DRIVER_PUSH_MESSAGE_TOPIC, qmqContent);
        LOGGER.info("sendDriverImproveServiceMsg", data);
    }

    private String createNoticeContent(List<QuestionResultDTO> questions,String locale) {
        // 相同的提示内容进行去重
        List<String> noticeContentSharkKeys = questions.stream()
                .map(QuestionResultDTO::getNoticeContentSharkKey)
                .distinct()
                .collect(Collectors.toList());

        // 多个提示进行聚合
        StringBuilder notice = new StringBuilder();
        for (String noticeContentSharkKey : noticeContentSharkKeys) {
            String noticeContent = languages.getContent(noticeContentSharkKey,locale);
            notice.append(noticeContent).append("、");
        }
        notice.deleteCharAt(notice.length() - 1); // 去掉最后的"、"
        return notice.toString();
    }

    private QuestionResultDTO covert2DTO(QuestionResultPO po, Map<String, List<DriverBaseOrderInfoDTO>> driverOrderMap) {
        List<DriverBaseOrderInfoDTO> driverOrderInfos = driverOrderMap.get(po.getBusinessId());
        if (CollectionUtils.isEmpty(driverOrderInfos)) {
            return null;
        }
        DriverBaseOrderInfoDTO driverOrderInfo = driverOrderInfos.get(0);// 取第一个就可以了
        QuestionResultDTO resultDTO = new QuestionResultDTO();
        resultDTO.setUserOrderId(po.getBusinessId());
        resultDTO.setQuestionId(po.getQuestionId());
        resultDTO.setNoticeContentSharkKey(this.getNoticeContentSharkKey(po.getQuestionId()));
        // 根据用户订单查到司机单信息，补充司机信息(小问卷的表，没有落司机信息)
        resultDTO.setDriverId(driverOrderInfo.getDrvId());
        resultDTO.setMainLand(mainLandCode.equals(driverOrderInfo.getBizAreaType()));
        return resultDTO;
    }

    private String getNoticeContentSharkKey(Long questionId) {
        List<ImproveServiceQualityConfig> improveServiceQualityConfigs = config.getImproveServiceQualityConfigs();
        for (ImproveServiceQualityConfig configItem : improveServiceQualityConfigs) {
            List<Long> questionIds = configItem.getQuestionIds();
            if (questionIds.contains(questionId)) {
                return configItem.getNoticeSharkKey();
            }
        }
        return null;
    }
}
