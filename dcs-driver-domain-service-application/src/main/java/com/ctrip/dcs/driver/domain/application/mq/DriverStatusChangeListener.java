package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLogType;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverAccountParamConfig;
import com.ctrip.dcs.tms.transport.mq.model.DriverMQDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

@Component
public class DriverStatusChangeListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DriverStatusChangeListener.class);

    public static final String CONSUMER_GROUP = "*********";


    @Autowired
    UserCenterAccountGateway userCenterAccountGateway;

    @Autowired
    DriverAccountParamConfig driverAccountParamConfig;

    @Autowired
    private AccountService accountService;
    @Autowired
    private AccountChangeLogHelper accountChangeLogHelper;

    @Override
    @QmqConsumer(prefix = "dcs.tms.transport.driver.state.changed", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        // 上线
        if (message.getTags().contains("tag_driverstate_online")) {
            DriverMQDTO driverDto = buildDto(message);
            if (Objects.isNull(driverDto)) {
                return;
            }
            // 所有的都尝试去更新身份有效性
            if (StringUtils.isNotBlank(driverDto.getUid())) {
                accountService.updateAccountIdentityState(driverDto.getUid(), AccountSouceEnum.DRIVER_SOURCE, true);
            }

            if (!driverAccountParamConfig.isGrey(driverDto.getDrvId(), driverDto.getCityId())) {
                return;
            }
            // todo 司导一期上线保持现状，需要去用户中心解冻，后续所有账户都会解冻，可以删除
            boolean result = userCenterAccountGateway.unfreezeAccount(driverDto.getUid());
            if (result) {
                accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.UN_FREEZE_ACCOUNT, AccountSouceEnum.DRIVER_SOURCE.getName(),
                        StringUtils.defaultIfBlank(driverDto.getUid(), ""), "", ""));
                LOGGER.info("driver state change valid unfreeze account success", driverDto.getUid());
            } else {
                LOGGER.info("driver state change valid unfreeze account failed", driverDto.getUid());
            }
        }

        // 下线
        if (message.getTags().contains("tag_driverstate_offline")) {
            DriverMQDTO driverDto = buildDto(message);
            if (Objects.isNull(driverDto)) {
                return;
            }
            // 所有的都尝试去更新身份有效性
            if (StringUtils.isNotBlank(driverDto.getUid())) {
                accountService.updateAccountIdentityState(driverDto.getUid(), AccountSouceEnum.DRIVER_SOURCE, false);
            }
        }

    }

    private DriverMQDTO buildDto(Message message) {
        String data = message.getStringProperty("content");
        DriverMQDTO driverDto = JacksonUtil.deserialize(data, DriverMQDTO.class);
        return driverDto;
    }


}
