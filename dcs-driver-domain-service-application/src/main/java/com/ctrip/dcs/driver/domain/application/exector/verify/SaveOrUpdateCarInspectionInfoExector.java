package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverCarInspectionRecordDO;
import com.ctrip.dcs.driver.domain.task.SaveOrUpdateCarInspectionInfoRequestType;
import com.ctrip.dcs.driver.domain.task.SaveOrUpdateCarInspectionInfoResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class SaveOrUpdateCarInspectionInfoExector extends ShoppingExecutor<SaveOrUpdateCarInspectionInfoRequestType, SaveOrUpdateCarInspectionInfoResponseType>
        implements Validator<SaveOrUpdateCarInspectionInfoRequestType> {

    @Autowired
    DriverTaskAdapter driverTaskAdapter;

    @Override
    public SaveOrUpdateCarInspectionInfoResponseType execute(SaveOrUpdateCarInspectionInfoRequestType request) {
        SaveOrUpdateCarInspectionInfoResponseType responseType = new SaveOrUpdateCarInspectionInfoResponseType();

        List<DriverCarInspectionRecordDO> carInspectionRecordList = this.toCarInspectionRecordList(request);
        driverTaskAdapter.updateCarInspectionInfo(carInspectionRecordList);

        return ServiceResponseUtils.success(responseType);
    }

    private List<DriverCarInspectionRecordDO> toCarInspectionRecordList(SaveOrUpdateCarInspectionInfoRequestType request) {
        List<DriverCarInspectionRecordDO> carInspectionRecordList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(request.carInspectionList)){
            request.carInspectionList.forEach(r -> {
                DriverCarInspectionRecordDO driverCarInspectionRecordDO = new DriverCarInspectionRecordDO();
                driverCarInspectionRecordDO.setDriverId(request.driverId);
                driverCarInspectionRecordDO.setTaskId(request.taskId);
                driverCarInspectionRecordDO.setTaskStepKey(r.taskStepKey);
                driverCarInspectionRecordDO.setTaskStepValue(r.taskStepValue);
                carInspectionRecordList.add(driverCarInspectionRecordDO);
            });
        }
        return carInspectionRecordList;
    }

    @Override
    public void onExecuted(SaveOrUpdateCarInspectionInfoRequestType req, SaveOrUpdateCarInspectionInfoResponseType resp) {
        super.onExecuted(req, resp);
        Cat.logEvent(CatEventType.DRIVER_CARINSPECTION_SAVE, resp.getResponseResult().getReturnCode());
    }

    @Override
    public void validate(AbstractValidator<SaveOrUpdateCarInspectionInfoRequestType> validator) {
        validator.ruleFor("driverId").notNull().greaterThan(0L);
        validator.ruleFor("taskId").notNull().notEmpty();
        validator.ruleFor("carInspectionList").notNull();
    }

}
