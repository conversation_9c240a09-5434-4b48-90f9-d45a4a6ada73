package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.go.log.Log;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;

@Component
public class RefreshAccountCacheSchedule {
    Log log = Log.getInstance(RefreshAccountCacheSchedule.class);
    @Autowired
    private AccountService accountService;

    @QSchedule("dcs.driver.account.refresh.account.cache.job")
    public void onExecute(Parameter parameter) {

        // 指定uid
        String uids = parameter.getString("uidList");
        if (StringUtils.isBlank(uids)) {
            return;
        }
        List<String> uidList = Lists.newArrayList(uids.split(","));
        if (CollectionUtils.isEmpty(uidList)) {

            return;
        }
        for (String uid : uidList) {
            accountService.refreshAccountCache(uid);
        }
    }
}
