package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.dcs.driver.account.application.condition.RefreshUdlByDriverIdCondition;
import com.ctrip.dcs.driver.account.application.service.RefreshUdlByDriveridService;
import com.ctrip.dcs.driver.account.application.service.RefreshUdlService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.Arrays;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
public class AccountUdlRefreshSchedule {
    public static final Logger logger = LoggerFactory.getLogger(AccountUdlRefreshSchedule.class);


    @Autowired
    RefreshUdlService refreshUdlService;
    @Autowired
    RefreshUdlByDriveridService refreshUdlByDriveridService;


    //uid 刷新数据
    @QSchedule("dcs.driver.account.refresh.udl.job")
    public void onExecute(Parameter parameter) {
        // 指定uid
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString());
        try {
            logger.info("dcs.driver.account.refresh.udl.job start");
            String uids = parameter.getString("uidList");
            if (StringUtils.isNotBlank(uids)) {
                refreshUdlService.refreshUdlByUid(parameter, uids);
                return;
            }
            // 全量刷新
            String string = parameter.getString("refreshALLUdl");
            if (Boolean.parseBoolean(string)) {
                refreshUdlService.refreshAllUdl(parameter);
            }
        } finally {
            LoggerContext.destroy();
        }

    }


    @QSchedule("dcs.driver.account.refresh.udl.byDrierId.job")
    public void onExecuteByDriverId(Parameter parameter) {
        // 指定uid
        LoggerContext.newContext().withGlobalTraceId(UUID.randomUUID().toString());
        try {
            logger.info("dcs.driver.account.refresh.udl.job start");
            RefreshUdlByDriverIdCondition refreshUdlByDriverIdCondition = convertCondition(parameter);
            if(!refreshUdlByDriverIdCondition.isRefreshALLUdl()){
                refreshUdlByDriveridService.refreshUdlByDriverId(refreshUdlByDriverIdCondition);
            }  else {
                refreshUdlByDriveridService.refreshAllUdlByDriverId(refreshUdlByDriverIdCondition);
            }
        } finally {
            LoggerContext.destroy();
        }

    }

    private RefreshUdlByDriverIdCondition convertCondition(Parameter parameter) {
        RefreshUdlByDriverIdCondition refreshUdlByDriverIdCondition = new RefreshUdlByDriverIdCondition();
        refreshUdlByDriverIdCondition.setDriverIds(Arrays.stream(parameter.getString("driverIds").split(",")).collect(Collectors.toList()));
        refreshUdlByDriverIdCondition.setDbList(Arrays.stream(parameter.getString("dbList").split(",")).collect(Collectors.toList()));
        refreshUdlByDriverIdCondition.setRefreshALLUdl(Boolean.parseBoolean(parameter.getString("refreshALLUdl")));
        return refreshUdlByDriverIdCondition;
    }


}
