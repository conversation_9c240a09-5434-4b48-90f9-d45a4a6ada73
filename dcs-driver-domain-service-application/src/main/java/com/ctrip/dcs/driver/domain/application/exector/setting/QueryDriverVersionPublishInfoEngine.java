package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivVersionPublishInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivVersionPublishInfoPO;
import com.ctrip.dcs.driver.domain.setting.DriverVersionPublishInfoDTO;
import com.ctrip.dcs.driver.domain.setting.QueryDriverVersionPublishInfoRequestType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverVersionPublishInfoResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 查询司机端版本发布信息
 */
@Component
public class QueryDriverVersionPublishInfoEngine extends ShoppingExecutor<QueryDriverVersionPublishInfoRequestType, QueryDriverVersionPublishInfoResponseType>
        implements Validator<QueryDriverVersionPublishInfoRequestType> {

    @Autowired
    DrivVersionPublishInfoDao drivVersionPublishInfoDao;

    @Override
    public void validate(AbstractValidator<QueryDriverVersionPublishInfoRequestType> validator) {

    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverVersionPublishInfoEngine owner;
        private final QueryDriverVersionPublishInfoResponseType response;

        private Executor(QueryDriverVersionPublishInfoRequestType request, QueryDriverVersionPublishInfoResponseType response, QueryDriverVersionPublishInfoEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.driverVersionPublishInfoList = new ArrayList<>();
        }

        private void QueryDriverPublishVersionInfo() {
            List<DrivVersionPublishInfoPO> drivVersionPublishInfoPOList = this.owner.drivVersionPublishInfoDao.selectAll();
            if(CollectionUtils.isNotEmpty(drivVersionPublishInfoPOList)){
                drivVersionPublishInfoPOList.stream().forEach(m -> {
                    DriverVersionPublishInfoDTO versionInfo = new DriverVersionPublishInfoDTO();
                    versionInfo.packageVersion = m.getPackageVersion();
                    versionInfo.innerVersion = m.getInnerVersion();
                    versionInfo.platformType = m.getPlatformType();
                    versionInfo.appType = ObjectUtils.defaultIfNull(m.getAppType(), 0);
                    versionInfo.isFullPublish = m.getIsFullPublish();
                    versionInfo.grayCityList = m.getGrayCityList();
                    versionInfo.updateDesc = m.getUpdateDesc();
                    versionInfo.updateDescEn = m.getUpdateDescEn();
                    versionInfo.downloadUrl = m.getDownloadUrl();
                    versionInfo.isH5DownloadUri = m.getIsH5DownloadUri();
                    versionInfo.isLowestVersion = m.getIsLowestVersion();
                    versionInfo.channelInfo = StringUtils.defaultIfBlank(m.getChannelInfo(), Strings.EMPTY);
                    if(Objects.nonNull(m.getDatachangeLasttime())) {
                        versionInfo.datachangeLasttime = m.getDatachangeLasttime().getTime();
                    }
                    this.response.driverVersionPublishInfoList.add(versionInfo);
                });
            }
        }

        @Override
        protected boolean validate() {
            return true;
        }

        @Override
        protected void buildResponse() {
            if(!this.validate()){
                return;
            }

            this.QueryDriverPublishVersionInfo();
        }
    }

    @Override
    public QueryDriverVersionPublishInfoResponseType execute(QueryDriverVersionPublishInfoRequestType request) {
        QueryDriverVersionPublishInfoResponseType response = new QueryDriverVersionPublishInfoResponseType();
        Executor executor = new Executor(request, response, this);
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverVersionPublishInfoResponseType onException(QueryDriverVersionPublishInfoRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverVersionPublishInfoEngine error", ex);
        return super.onException(req, ex);
    }
}
