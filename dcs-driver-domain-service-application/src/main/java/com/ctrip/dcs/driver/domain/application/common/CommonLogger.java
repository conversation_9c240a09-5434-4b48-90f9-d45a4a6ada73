package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.shopping.utils.ShoppingActionUtils;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerContext;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class CommonLogger {

  private static final String ES_SCENARIO = "igt-jnt";
  public static final String CLIENT_ID_LOG_TAG = "ClientId";
  public static final String CTRIP_ORDER_ID_LOG_TAG = "CtripOrderId";
  private static final String CTRIP_DRIVER_ID_LOG_TAG = "DriverId";
  public static final CommonLogger INSTANCE = new CommonLogger();
  public static final int LOG_MESSAGE_MAX_SIZE = 30000;

  private final Logger logger = LoggerFactory.getLogger(this.getClass());

  private void catLog(String title, String message) {
    Map<String, String> indexedTags = Maps.newHashMap();

    indexedTags.put("appid", Foundation.app().getAppId());
    indexedTags.put("igt.ctripOrderId", Long.toString(this.ctripOrderId()));
    indexedTags.put("igt.driverId", this.driverId());
    indexedTags.put("igt.clientId", this.clientId());
    indexedTags.put("igt.title", title);
    Map<String, String> storedTags = Maps.newHashMap();
    indexedTags.put("igt.message", message);
    Cat.logTags(ES_SCENARIO, indexedTags, storedTags);
  }

  private String exceptionMessage(Throwable throwable) {
    if (Objects.isNull(throwable)) {
      return StringUtils.EMPTY;
    }
    StringBuilder message = new StringBuilder();
    message.append(throwable.getMessage());
    message.append(System.lineSeparator());
    ShoppingActionUtils.tryActions(throwable.getStackTrace(), o -> {
      message.append(o.toString());
      message.append(System.lineSeparator());
    });
    return message.toString();
  }

  private String[] splite(String message) {
    if (StringUtils.isBlank(message)) {
      return ArrayUtils.EMPTY_STRING_ARRAY;
    }
    return CommonStringUtils.splite(message, LOG_MESSAGE_MAX_SIZE);
  }

  public String clientId() {
    return this.logTagValue(CLIENT_ID_LOG_TAG);
  }

  public long ctripOrderId() {
    return NumberUtils.toLong(this.logTagValue(CTRIP_ORDER_ID_LOG_TAG), 0);
  }

  public String driverId() {
    return this.logTagValue(CTRIP_DRIVER_ID_LOG_TAG);
  }

  public String logTagValue(String logTagKey) {
    LoggerContext loggerContext = LoggerContext.getCurrent();
    if (Objects.isNull(loggerContext)) {
      return StringUtils.EMPTY;
    }

    Map<String, String> tags = loggerContext.getGlobalLogTag();
    if (MapUtils.isEmpty(tags)) {
      return StringUtils.EMPTY;
    }

    return tags.getOrDefault(logTagKey, StringUtils.EMPTY);
  }

  public void info(String title, String message) {
    ShoppingActionUtils.tryActions(this.splite(message), o -> {
      this.logger.info(title, o);
      this.catLog(title, o);
    });
  }

  public void info(String title, String message, Map<String, String> tags) {
    ShoppingActionUtils.tryActions(this.splite(message), o -> {
      this.logger.info(title, o, tags);
      this.catLog(title, o);
    });
  }

  public void warn(String title, String message) {
    ShoppingActionUtils.tryActions(this.splite(message), o -> {
      this.logger.warn(title, o);
      this.catLog(title, o);
    });
  }

  public void warn(String title, Throwable throwable) {
    this.logger.warn(title, throwable);
    this.catLog(title, this.exceptionMessage(throwable));
  }

  public void error(Throwable throwable) {
    final String title = this.defaultTitle();
    this.logger.error(title, throwable);
    this.catLog(title, this.exceptionMessage(throwable));
  }

  private String defaultTitle() {
    return StringUtils.EMPTY;
  }

  public void infoFormat(String title, String message, Object... args) {
    message = String.format(message, args);
    ShoppingActionUtils.tryActions(this.splite(message), o -> {
      this.logger.info(title, o);
      this.catLog(title, o);
    });
  }

}