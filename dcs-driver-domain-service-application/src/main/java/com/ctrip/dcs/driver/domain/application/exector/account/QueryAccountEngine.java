package com.ctrip.dcs.driver.domain.application.exector.account;

import com.ctrip.dcs.driver.value.account.AccountUidInfo;
import com.ctrip.dcs.driver.domain.account.QueryAccountRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountResponseType;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.domain.infrastructure.dto.account.AccountBaseDto;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryAccountEngine extends ShoppingExecutor<QueryAccountRequestType, QueryAccountResponseType> {

    @Autowired
    private UserCenterAccountGateway userCenterAccountGateway;

    @Autowired
    private AccountMapperDao accountMapperDao;
    @Autowired
    DirectorRedis directorRedis;

    private static final String DRIVER_INFO_UID_REDIS_KEY = "driverinfo:uid:%s";


    @Override
    public QueryAccountResponseType execute(QueryAccountRequestType request) {
        QueryAccountResponseType response = new QueryAccountResponseType();
        String uid = request.getUid();
        if(StringUtils.isBlank(uid)){
            AccountUidInfo accountUidInfo = userCenterAccountGateway.queryAccountUid(buildParam(request));
            uid = Optional.ofNullable(accountUidInfo).map(AccountUidInfo::getUid).orElse(null);
        }
        if(StringUtils.isNotBlank(uid)){
            List<AccountMapperPO> accountMapperList = accountMapperDao.queryByUid(uid);
            Map<String, List<AccountMapperPO>> map = accountMapperList.stream().collect(groupingBy(AccountMapperPO::getSource));
            if(map.containsKey(AccountSouceEnum.GUIDE_SOURCE.getName())){
                response.setGuideId(map.get(AccountSouceEnum.GUIDE_SOURCE.getName()).get(0).getSourceId());
            }
            if(map.containsKey(AccountSouceEnum.DRIVER_SOURCE.getName())){
                response.setDriverId(map.get(AccountSouceEnum.DRIVER_SOURCE.getName()).get(0).getSourceId());
                directorRedis.hset(String.format(DRIVER_INFO_UID_REDIS_KEY, uid), AccountSouceEnum.DRIVER_SOURCE.getName(),map.get(AccountSouceEnum.DRIVER_SOURCE.getName()).get(0).getSourceId(),2764800);//32天
            }
            // 司导id使用单独字段，原因：考虑老版本的兼容性，一个人只有司导身份的话，在老版本客户端是不能登录的，如果共用driverId一个字段，使用方无法区分身份
            if(map.containsKey(AccountSouceEnum.DRIVER_GUIDE.getName())){
                String driverGuideId = map.get(AccountSouceEnum.DRIVER_GUIDE.getName()).get(0).getSourceId();
                response.setDriverGuideId(driverGuideId);
                // 注意，这里field没有写错，因为同一个用户司机id和司导id一样，所以这里设置缓存时，field也传Driver类型，跟司机保持一致，再查询缓存时就无需关注具体身份
                directorRedis.hset(String.format(DRIVER_INFO_UID_REDIS_KEY, uid), AccountSouceEnum.DRIVER_SOURCE.getName(), driverGuideId,2764800);//32天
            }
        }
        response.setUid(uid);
        return ServiceResponseUtils.success(response);
    }

    private AccountBaseDto buildParam(QueryAccountRequestType request) {
        AccountBaseDto accountBaseDto = new AccountBaseDto();
        accountBaseDto.setAccountType(ObjectUtils.defaultIfNull(request.getAccountType(), 0));
        accountBaseDto.setCountryCode(request.getCountryCode());
        accountBaseDto.setPhoneNumber(request.getPhoneNumber());
        accountBaseDto.setEmail(request.getEmail());
        return accountBaseDto;
    }

    @Override
    public Boolean isEmptyResult(QueryAccountResponseType resp) {
        return !(resp!=null&&StringUtils.isNotBlank(resp.getUid()));

    }
}
