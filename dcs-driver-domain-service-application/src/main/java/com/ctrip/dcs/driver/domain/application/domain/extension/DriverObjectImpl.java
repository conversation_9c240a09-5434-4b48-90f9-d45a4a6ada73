package com.ctrip.dcs.driver.domain.application.domain.extension;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.value.extension.DriverObject;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.owasp.csrfguard.util.Strings;

/**
 * 司机
 * */
public class DriverObjectImpl extends DriverObject {
  private final DomainBeanFactory domainBeanFactory;

  public DriverObjectImpl(long driverId, DomainBeanFactory domainBeanFactory) {
    this.domainBeanFactory = domainBeanFactory;

    DriverInfo driverInfo = this.domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverId);
    if(driverInfo == null){
      return;
    }
    this.driverId = ObjectUtils.defaultIfNull(driverInfo.driverId, 0L);
    this.driverPhone = ObjectUtils.defaultIfNull(driverInfo.driverPhone, Strings.EMPTY);
    this.cityId = ObjectUtils.defaultIfNull(driverInfo.cityId, 0L);
  }

  public long driverId;
  public String driverPhone;
  public long cityId;

  @Override
  public long driverId(){
    return this.driverId;
  }

  @Override
  public String driverPhone(){
    return this.driverPhone;
  }

  @Override
  public long cityId(){
    return this.cityId;
  }
}
