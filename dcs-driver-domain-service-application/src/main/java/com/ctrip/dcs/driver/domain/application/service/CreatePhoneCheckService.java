package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable;
import com.ctrip.dcs.driver.domain.application.condition.CreatePhoneCheckCondition;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CreatePhoneCheckService {
    @Resource
    private PhoneCheckTable phoneCheckTable;

    public boolean create(CreatePhoneCheckCondition condition) {
        PhoneCheckEntity phoneCheckEntity = this.buildPhoneCheckEntity(condition);
        int insert = phoneCheckTable.insert(phoneCheckEntity);
        return insert > 0;
    }

    private PhoneCheckEntity buildPhoneCheckEntity(CreatePhoneCheckCondition condition) {
        PhoneCheckEntity phoneCheck = new PhoneCheckEntity();
        phoneCheck.setPhonePrefix(condition.getCountryCode());
        phoneCheck.setPhoneNumber(condition.getPhoneNumber());
        phoneCheck.setCheckType(condition.getCheckType());
        phoneCheck.setCheckState(condition.getCheckState());
        return phoneCheck;
    }
}
