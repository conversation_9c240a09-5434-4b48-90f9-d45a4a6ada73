package com.ctrip.dcs.driver.domain.application.exector.freeze;

import com.ctrip.dcs.driver.domain.infrastructure.adapter.db.DriverFreezeAdapter;
import com.ctrip.dcs.driver.domain.infrastructure.condition.freeze.DriverFrozenBufferCondition;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.CreateDriverFrozenBufferRequestType;
import com.ctrip.model.CreateDriverFrozenBufferResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

@Component
public class CreateDriverFrozenBufferEngine extends ShoppingExecutor<CreateDriverFrozenBufferRequestType, CreateDriverFrozenBufferResponseType> {
    @Autowired
    private DriverFreezeAdapter driverFreezeAdapter;

    @Override
    public CreateDriverFrozenBufferResponseType execute(CreateDriverFrozenBufferRequestType request) {
        driverFreezeAdapter.createDriverFrozenBuffer(covert(request));
        return ServiceResponseUtils.success(new CreateDriverFrozenBufferResponseType());
    }

    private DriverFrozenBufferCondition covert(CreateDriverFrozenBufferRequestType request) {
        DriverFrozenBufferCondition buffer = new DriverFrozenBufferCondition();
        buffer.setDriverId(request.getDriverId());
        buffer.setDeviceId(request.getDeviceId());
        buffer.setReasonType(request.getReasonType());
        Integer bufferDays = request.getBufferDays();
        buffer.setBufferStartTime(Timestamp.valueOf(LocalDateTimeUtils.localDateTime(request.getBufferStartTime())));
        buffer.setBufferEndTime(Timestamp.valueOf(LocalDateTimeUtils.localDateTime(request.getBufferEndTime())));
        buffer.setBufferDays(bufferDays);
        return buffer;
    }
}
