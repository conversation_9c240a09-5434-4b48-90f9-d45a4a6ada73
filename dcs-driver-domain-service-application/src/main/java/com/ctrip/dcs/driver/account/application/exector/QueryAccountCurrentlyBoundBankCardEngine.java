package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.QueryAccountCurrentlyBoundBankCardConvert;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.domain.account.AccountBankCardDTO;
import com.ctrip.dcs.driver.domain.account.QueryAccountBankCardByDriverIdResponseType;
import com.ctrip.dcs.driver.domain.account.QueryAccountCurrentlyBoundBankCardRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountCurrentlyBoundBankCardResponseType;
import com.ctrip.dcs.driver.gateway.AccountBankCardRecordRepository;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTag(tagKeys= {"uid"})
public class QueryAccountCurrentlyBoundBankCardEngine extends AbDriverDomainShoppingExecutor<QueryAccountCurrentlyBoundBankCardRequestType, QueryAccountCurrentlyBoundBankCardResponseType> implements Validator<QueryAccountCurrentlyBoundBankCardRequestType> {

    public static final Logger logger = LoggerFactory.getLogger(QueryAccountCurrentlyBoundBankCardEngine.class);


    @Autowired
    AccountBankCardRecordRepository recordRepository;
    @Autowired
    AccountService accountService;
    @Autowired
    QueryAccountCurrentlyBoundBankCardConvert convert;

    @Override
    public QueryAccountCurrentlyBoundBankCardResponseType execute(QueryAccountCurrentlyBoundBankCardRequestType request) {
        QueryAccountCurrentlyBoundBankCardResponseType resp = new QueryAccountCurrentlyBoundBankCardResponseType();
        resp.accountBankCard = new AccountBankCardDTO();


        BaseResult<AccountBankCardDo> accountBankCardRecord = recordRepository.queryAccountBindingLastBankCardV2(request.uid);
        if (accountBankCardRecord.isFail()){
            return ServiceResponseUtils.fail(resp,  accountBankCardRecord.getCode(), accountBankCardRecord.getMessage());
        }
        //再从金融那边拆线获取其他数据
        resp = convert.convertResponse(request, accountBankCardRecord.getData());

        if (StringUtils.isBlank(resp.accountBankCard.bankCardNo)) {
            return ServiceResponseUtils.fail(new QueryAccountCurrentlyBoundBankCardResponseType(),"500","bankCardNo is null");
        }
        return ServiceResponseUtils.success(resp);
    }


    @Override
    public void validate(AbstractValidator<QueryAccountCurrentlyBoundBankCardRequestType> validator) {
        validator.ruleFor("uid").notNull().notEmpty();
    }

    @Override
    public Boolean isEmptyResult(QueryAccountCurrentlyBoundBankCardResponseType resp) {
        return !(resp!=null&&resp.getAccountBankCard()!=null&&resp.getAccountBankCard().getUid()!=null);
    }
}
