package com.ctrip.dcs.driver.account.application.mq;
/*
作者：pl.yang
创建时间：2025/5/14-下午8:37-2025
*/


import com.ctrip.dcs.driver.account.application.service.AccountBankCardService;
import com.ctrip.dcs.driver.account.domain.condition.DriverWithDrawCondition;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DriverWithDrawResultListener
 * @Package com.ctrip.dcs.driver.account.application.mq
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/14 下午8:37
 */

@Component
public class DriverWithDrawResultListener {
    public static final Logger logger = LoggerFactory.getLogger(DriverWithDrawResultListener.class);
    @Autowired
    AccountBankCardService accountBankCardService;

    /**
     *   目前只有提现失败的消息
     * @param message
     */
    @QmqConsumer(prefix = Constants.DRIVER_WITHDRAW_RESULT, consumerGroup = "*********")
    public void onMessage(Message message) {
        DriverWithDrawCondition condition= convertDriverWithDrawCondition(message);
        logger.info(condition.getDriverId()+"");
        accountBankCardService.driverWithDrawNotify(condition);
    }

    private DriverWithDrawCondition convertDriverWithDrawCondition(Message message) {
        DriverWithDrawCondition condition = new DriverWithDrawCondition();
        condition.setDriverId(message.getLongProperty("driverId"));
        condition.setReasonCode(message.getStringProperty("reasonCode"));
        condition.setMoney(message.getStringProperty("money"));
        condition.setDatetime(message.getStringProperty("datetime"));
        return condition;
    }
}
