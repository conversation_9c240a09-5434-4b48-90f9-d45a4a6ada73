package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.domain.rights.mq.UseRightsMessage;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.util.Objects;

@Component
public class DriverRightsUseListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DriverRightsUseListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    @Autowired
    DomainBeanFactory domainBeanFactory;

    @Override
    @QmqConsumer(prefix = "dcs.driver.rights.use", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        String data = message.getStringProperty("content");
        UseRightsMessage useRightsMessage = JacksonUtil.deserialize(data, UseRightsMessage.class);
        if(Objects.isNull(useRightsMessage)){
            return;
        }
        UseRightsCondition useRightsCondition=buildReq(useRightsMessage);
        DriverRightsObject driverRightsObject=new DriverRightsObjectImpl(domainBeanFactory, useRightsMessage.getDriverId(), "");
        driverRightsObject.useRights(useRightsCondition);
    }

    private UseRightsCondition buildReq(UseRightsMessage useRightsMessage) {
        UseRightsCondition useRightsCondition = new UseRightsCondition();
        useRightsCondition.setDriverId(useRightsMessage.getDriverId());
        useRightsCondition.setRightsType(useRightsMessage.getRightsType());
        useRightsCondition.setUserOrderId(useRightsMessage.getUserOrderId());
        useRightsCondition.setPurchaseOrderId(useRightsMessage.getPurchaseOrderId());
        useRightsCondition.setSupplyOrderId(useRightsMessage.getSupplyOrderId());
        if(useRightsMessage.getRightsType()== RightsTypeEnum.RIGHTS_TYPE_WITHDRAW.getCode()){
            useRightsCondition.setSupplyOrderId(LocalDateTimeUtils.todayStr());
        }
        useRightsCondition.setPunishOrderId(useRightsMessage.getPunishOrderId());
        useRightsCondition.setMoney(useRightsMessage.getMoney());
        return useRightsCondition;
    }
}
