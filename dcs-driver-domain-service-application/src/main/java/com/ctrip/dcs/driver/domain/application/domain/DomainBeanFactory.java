package com.ctrip.dcs.driver.domain.application.domain;

import com.ctrip.dcs.driver.domain.application.common.CommonCryptographicUtils;
import com.ctrip.dcs.driver.domain.application.common.CommonExamSSOUtils;
import com.ctrip.dcs.driver.domain.application.common.CommonExamSecretUtils;
import com.ctrip.dcs.driver.domain.application.common.DriverLevelHelper;
import com.ctrip.dcs.driver.domain.application.http.HttpClient;
import com.ctrip.dcs.driver.domain.application.redis.ExamRedisLogic;
import com.ctrip.dcs.driver.domain.application.redis.HonourRedisLogic;
import com.ctrip.dcs.driver.domain.application.redis.RightsRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.*;
import com.ctrip.dcs.driver.domain.application.service.impl.InfrastructureServiceProxyImpl;
import com.ctrip.dcs.driver.domain.application.service.impl.TmsTransportServiceProxyImpl;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.infrastructure.IMServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.*;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.HonourFormConfig;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;

@Component
public class DomainBeanFactory {
    @Autowired
    private ArchCoreInfoRepository archCoreInfoService;
    @Autowired
    private GmsTransportDomainProxyService gmsTransportDomainProxyService;
    @Autowired
    private HonourDBDataService honourDBDataService;
    @Autowired
    private HonourFormConfig honourFormConfig;
    @Autowired
    private RightsRepoService rightsRepoService;
    @Autowired
    private RightsDBDataService rightsDBDataService;
    @Autowired
    private GuideApplyExamDBDataService guideApplyExamDBDataService;
    @Autowired
    private GuideExamScoreDBDataService guideExamScoreDBDataService;
    @Autowired
    private GuideInfoDBDataService guideInfoDBDataService;
    @Autowired
    private LevelConfig levelConfig;
    @Autowired
    private RightsConfig rightsConfig;
    @Autowired
    private WelfareConfig welfareConfig;
    @Autowired
    private DriverLevelGreyConfig driverLevelGreyConfig;
    @Autowired
    private GuideExamConfig guideExamConfig;
    @Autowired
    private ExamInterfaceConfig examInterfaceConfig;
    @Autowired
    private HonourRedisLogic honourRedisLogic;
    @Autowired
    private RightsRedisLogic rightsRedisLogic;
    @Autowired
    private ExamRedisLogic examRedisLogic;
    @Autowired
    private TmsTransportServiceProxyImpl tmsTransportServiceProxy;
    @Autowired
    private MedalInfoService medalInfoService;
    @Autowired
    private RankInfoService rankInfoService;
    @Autowired
    private GuideTagExamService guideTagExamService;
    @Autowired
    private CommonCryptographicUtils commonCryptographicUtils;
    @Autowired
    private HonourImgConfig honourImgConfig;
    @Autowired
    private CommonExamSecretUtils commonExamSecretUtils;
    @Autowired
    private CommonExamSSOUtils commonExamSSOUtils;

    @Autowired
    @Qualifier("CommonThreadPool")
    private ExecutorService executorService;

    @Autowired
    private FinanceConfig financeConfig;
    @Autowired
    private InfrastructureServiceProxyImpl infrastructureServiceProxy;
    @Autowired
    private FinanceConvertService financeConvertService;

    @Autowired
    private IMServiceProxy imServiceProxy;
    @Autowired
    private HttpClient httpClient;

    @Autowired
    TourService tourService;
    @Autowired
    DriverLevelHelper driverLevelHelper;
    @Autowired
    GeoGateway geoGateway;

    @Autowired
    TripUniversityQconfig tripUniversityQconfig;

    public GmsTransportDomainProxyService gmsTransportDomainServiceProxy() {
        return this.gmsTransportDomainProxyService;
    }

    public TmsTransportServiceProxyImpl tmsTransportServiceProxy() {
        return this.tmsTransportServiceProxy;
    }

    public ArchCoreInfoRepository archCoreInfoService() {
        return archCoreInfoService;
    }

    public HonourFormConfig honourFormConfig() {
        return honourFormConfig;
    }

    public HonourRedisLogic honourRedisLogic() {
        return honourRedisLogic;
    }

    public HonourDBDataService honourDBDataService() {
        return honourDBDataService;
    }

    public MedalInfoService medalInfoService() {
        return medalInfoService;
    }

    public RankInfoService rankInfoService() {
        return rankInfoService;
    }

    public GuideTagExamService guideTagExamService() {return guideTagExamService;}

    public CommonCryptographicUtils commonCryptographicUtils() {
        return commonCryptographicUtils;
    }

    public CommonExamSecretUtils commonExamSecretUtils(){return commonExamSecretUtils;}

    public CommonExamSSOUtils commonExamSSOUtils(){return commonExamSSOUtils;}

    public HonourImgConfig honourImgConfig() {
        return honourImgConfig;
    }

    public ExecutorService executorService() {
        return executorService;
    }

    public InfrastructureServiceProxyImpl infrastructureServiceProxy() {
        return infrastructureServiceProxy;
    }

    public FinanceConvertService financeConvertService() {
        return financeConvertService;
    }

    public RightsRepoService rightsRepoService() {
        return rightsRepoService;
    }

    public RightsDBDataService rightsDBDataService() {
        return rightsDBDataService;
    }

    public GuideApplyExamDBDataService guideApplyExamDBDataService(){
        return guideApplyExamDBDataService;
    }

    public GuideExamScoreDBDataService guideExamScoreDBDataService(){
        return guideExamScoreDBDataService;
    }

    public GuideInfoDBDataService guideInfoDBDataService(){
        return guideInfoDBDataService;
    }

    public RightsRedisLogic rightsRedisLogic() {
        return rightsRedisLogic;
    }

    public ExamRedisLogic examRedisLogic() {
        return examRedisLogic;
    }

    public LevelConfig levelConfig() {
        return levelConfig;
    }

    public RightsConfig rightsConfig() {
        return rightsConfig;
    }

    public WelfareConfig welfareConfig() {
        return welfareConfig;
    }

    public DriverLevelGreyConfig driverLevelGreyConfig() {
        return driverLevelGreyConfig;
    }

    public FinanceConfig financeConfig() {
    return financeConfig;
  }

    public GuideExamConfig guideExamConfig() {return guideExamConfig;}

    public ExamInterfaceConfig examInterfaceConfig() {return examInterfaceConfig;}

    public HttpClient httpClient() {return httpClient;}

    public IMServiceProxy imServiceProxy() { return imServiceProxy; }

    public TourService tourService() { return tourService; }

    public TripUniversityQconfig tripUniversityQconfig() { return tripUniversityQconfig; }
    public DriverLevelHelper driverLevelHelper() {
        return driverLevelHelper;
    }

    public GeoGateway geoGateway() {
        return geoGateway;
    }
}
