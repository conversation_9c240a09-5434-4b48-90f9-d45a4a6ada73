package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.BatchQueryAccountByMobilePhoneRequestType;
import com.ctrip.dcs.driver.domain.account.BatchQueryAccountByMobilePhoneResponseType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByMobilePhoneRequestType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ServiceLogTag(tagKeys = {"countryCode", "phoneNumber"})
public class BatchQueryAccountByMobilePhoneEngine extends ShoppingExecutor<BatchQueryAccountByMobilePhoneRequestType, BatchQueryAccountByMobilePhoneResponseType> implements Validator<QueryAccountByMobilePhoneRequestType> {

    @Autowired
    private AccountService accountService;
    @Autowired
    AccountDetailConvert accountDetailConvert;

    @Override
    public BatchQueryAccountByMobilePhoneResponseType execute(BatchQueryAccountByMobilePhoneRequestType request) {


        BatchQueryAccountByMobilePhoneResponseType resp = new BatchQueryAccountByMobilePhoneResponseType();
        try {

            List<AccountInfoDTO> accountList = accountService.batchGetAccountInfoByMobilePhone(accountDetailConvert.batchConvertPhoneInfo(request));

            resp.setAccountDetailList(AccountDetailConvert.batchConvert(accountList));

            Cat.logEvent(CatEventType.QUERY_BY_PHONE_RESULT, CollectionUtils.isNotEmpty(accountList) ? "1" : "0");
            return ServiceResponseUtils.success(resp);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ServiceResponseUtils.fail(resp);
    }

    @Override
    public void onFinally(BatchQueryAccountByMobilePhoneRequestType req, BatchQueryAccountByMobilePhoneResponseType resp, Exception ex) {
        DriverMetricUtil.batchMetricUdlIsValid(resp.getAccountDetailList());
    }

    @Override
    public Boolean isEmptyResult(BatchQueryAccountByMobilePhoneResponseType resp) {
        return !(resp != null && CollectionUtils.isNotEmpty(resp.getAccountDetailList()));
    }
}
