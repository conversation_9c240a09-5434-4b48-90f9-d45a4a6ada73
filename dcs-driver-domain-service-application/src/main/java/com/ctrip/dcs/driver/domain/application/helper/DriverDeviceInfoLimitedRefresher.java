package com.ctrip.dcs.driver.domain.application.helper;

import cn.hutool.core.util.ObjectUtil;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.BusinessConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class DriverDeviceInfoLimitedRefresher implements DriverDeviceInfoRefresher {

    private static final String LINKER = "#";

    private static final String DRIVER_DEVICE_INFO_REFRESH_KEY = "driver_device_info_refresh_%s";
    @Autowired
    private DirectorRedis redisClient;
    @Autowired
    private BusinessConfiguration configuration;

    @Override
    public boolean allowRefresh( Long driverId,String appId) {
        if (ObjectUtil.isNull(driverId) || StringUtils.isBlank(appId)) {
            return false;
        }
        String key = String.format(DRIVER_DEVICE_INFO_REFRESH_KEY, driverId + LINKER + appId);
        long currentUpdateTime = System.currentTimeMillis();
        if (StringUtils.isBlank(redisClient.get(key))) {
            Long configIntervalSeconds = configuration.getDriverDeviceConfig().getInfoRefreshIntervalSeconds();
            redisClient.set(key, String.valueOf(currentUpdateTime), configIntervalSeconds);
            return true;
        }
        return false;
    }
}
