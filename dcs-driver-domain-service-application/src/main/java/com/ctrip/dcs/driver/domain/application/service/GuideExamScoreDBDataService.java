package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideExamScoreModel;

import java.util.List;
import java.util.Map;

public interface GuideExamScoreDBDataService {
    Map<String, List<GuideExamScoreModel>> queryPassedGuideExamScoreInfoMapByAccount(String examAccountId);

    Long countByExamAccountIdApplySubject(String examAccountId, String applySubject);

}
