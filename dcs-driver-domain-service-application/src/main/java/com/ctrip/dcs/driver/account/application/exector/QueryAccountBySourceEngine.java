package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.QueryAccountBySourceRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountBySourceResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTag(tagKeys= {"source","sourceId"})
public class QueryAccountBySourceEngine extends ShoppingExecutor<QueryAccountBySourceRequestType, QueryAccountBySourceResponseType> implements Validator<QueryAccountBySourceRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public QueryAccountBySourceResponseType execute(QueryAccountBySourceRequestType request) {


        QueryAccountBySourceResponseType resp = new QueryAccountBySourceResponseType();

        AccountInfoDTO account = accountService.getAccountInfoBySource(request.getSource(), request.getSourceId());
        if (account != null) {
            resp.setAccountDetail(AccountDetailConvert.convert(account));
        }
        Cat.logEvent(CatEventType.QUERY_BY_SOURCE_RESULT, account != null ? "1" : "0");
        return ServiceResponseUtils.success(resp);
    }
    @Override
    public void onFinally(QueryAccountBySourceRequestType req, QueryAccountBySourceResponseType resp, Exception ex) {
        DriverMetricUtil.metricUdlIsValid( resp.getAccountDetail());
    }

    @Override
    public Boolean isEmptyResult(QueryAccountBySourceResponseType resp) {
        return !(resp!=null&&resp.getAccountDetail()!=null&&resp.getAccountDetail().getUid()!=null);
    }
}
