package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.domain.CommonResult;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.igt.ResponseResult;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 领域行为执行器
 */
public abstract class DomainExecutor<D, T, R> {

  protected D owner;

  protected DomainBeanFactory domainBeanFactory;

  public DomainExecutor(D owner, DomainBeanFactory domainBeanFactory) {
    this.owner = owner;
    this.domainBeanFactory = domainBeanFactory;
  }

  public abstract R doWork(T t);

  public CommonResult convert(ResponseResult r) {
    if (r != null && BooleanUtils.isTrue(r.isSuccess()) && "200".equals(r.returnCode)) {
      return CommonResult.newBuilder().withSuccess(true).withCode("200").build();
    }
    return CommonResult.newBuilder().withSuccess(false)
        .withCode(r != null ? StringUtils.defaultIfEmpty(r.returnCode, "500") : "500")
        .withMsg(r != null ? StringUtils.defaultIfEmpty(r.returnMessage, "") : "").build();
  }
}
