package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.domain.application.condition.NotifyVerificationCodeByCallPhoneCondition;
import com.ctrip.dcs.driver.domain.application.domain.PhoneCheckTaskFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class NotifyVerificationCodeByCallPhoneService {
    @Resource
    private PhoneCheckService phoneCheckService;
    @Resource
    private PhoneCheckTaskFactory phoneCheckTaskFactory;
    @Resource
    private PhoneCheckTaskTable phoneCheckTaskTable;

    public boolean call(NotifyVerificationCodeByCallPhoneCondition condition) {
        DriverEntity virtualDriver = this.createVirtualDriver(condition);
        // 创建一个拨打IVR的的任务
        PhoneCheckTaskEntity task = phoneCheckTaskFactory.applyVerificationCodeTask(virtualDriver, condition.getVerificationCode(), condition.getLocale());
        phoneCheckTaskTable.insert(task);
        // 发起IVR外呼
        return phoneCheckService.ivrCall(task);
    }

    /**
     * 创建一个虚拟的司机，这里主要是为了复用之前通知语音验证码的逻辑
     *
     * @param condition
     * @return
     */
    private DriverEntity createVirtualDriver(NotifyVerificationCodeByCallPhoneCondition condition) {
        DriverEntity driver = new DriverEntity();
        driver.setPhonePrefix(condition.getCountryCode());
        driver.setPhoneNumber(condition.getPhoneNumber());
        driver.setDriverId(-1L);
        return driver;
    }
}
