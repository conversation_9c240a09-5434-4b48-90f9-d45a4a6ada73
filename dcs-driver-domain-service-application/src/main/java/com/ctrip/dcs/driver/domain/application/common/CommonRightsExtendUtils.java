package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.ReassignExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.VacationExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.WelfareExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import qunar.agile.Strings;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

public final class CommonRightsExtendUtils {

    /**
     * 获取改派每周使用次数
     *
     * @param extend
     * @return
     */
    public static int getReassignUseCount(String extend) {
        ReassignExtendDto extendDto = JacksonUtil.deserialize(extend, ReassignExtendDto.class);
        if (Objects.isNull(extendDto) || Strings.isBlank(extendDto.getUseTime())) {
            return 0;
        }
        LocalDateTime reassignDate = LocalDateTimeUtils.localDateTime(extendDto.getUseTime());
        if (reassignDate.isBefore(LocalDateTimeUtils.firstDayOfWeek()) || reassignDate.isAfter(LocalDateTimeUtils.lastDayOfWeek())) {
            return 0;
        }

        return extendDto.getWeekUseCount();
    }

    public static BigDecimal getWelfareTotal(String extend) {
        WelfareExtendDto welfareExtendDto = JacksonUtil.deserialize(extend, WelfareExtendDto.class);
        if (Objects.isNull(welfareExtendDto)) {
            return BigDecimal.ZERO;
        }

        return Optional.ofNullable(welfareExtendDto.getWelfareUse()).orElse(BigDecimal.ZERO);
    }

    public static String buildExtend(int rightsType, String extend) {
        switch (RightsTypeEnum.getByCode(rightsType)) {
            case RIGHTS_TYPE_WELFARE:
                WelfareExtendDto dto = new WelfareExtendDto();
                dto.setWelfareLimit(Strings.isBlank(extend) ? BigDecimal.ZERO : new BigDecimal(extend));
                dto.setWelfareUse(BigDecimal.ZERO);
                dto.setWelfareFreeze(BigDecimal.ZERO);
                return JacksonUtil.serialize(dto);
            case RIGHTS_TYPE_VACATION:
                VacationExtendDto vacationExtendDto =new VacationExtendDto();
                vacationExtendDto.setVacationLimit(Integer.valueOf(extend));
                vacationExtendDto.setVacationUse(0);
                return JacksonUtil.serialize(vacationExtendDto);
        }
        return StringUtils.EMPTY;
    }

    public static String buildExtendForUpdate(RightsModel rightsModel, BigDecimal money, int vacationUsed) {
        switch (RightsTypeEnum.getByCode(rightsModel.getRightsType())) {
            case RIGHTS_TYPE_WELFARE:
                WelfareExtendDto welfareExtendDto = JacksonUtil.deserialize(rightsModel.getExtend(), WelfareExtendDto.class);
                if (Objects.nonNull(welfareExtendDto)) {
                    welfareExtendDto.setWelfareUse(welfareExtendDto.getWelfareUse().add(Optional.ofNullable(money).orElse(BigDecimal.ZERO)));
                    welfareExtendDto.setWelfareLimit(welfareExtendDto.getWelfareLimit());
                    welfareExtendDto.setWelfareFreeze(Optional.ofNullable(welfareExtendDto.getWelfareFreeze()).orElse(BigDecimal.ZERO).subtract(Optional.ofNullable(money).orElse(BigDecimal.ZERO)));
                    return JacksonUtil.serialize(welfareExtendDto);
                }
            case RIGHTS_TYPE_REASSIGN:
                int reassignUseCount = getReassignUseCount(rightsModel.getExtend());
                ReassignExtendDto extendDto = new ReassignExtendDto();
                extendDto.setUseTime(LocalDateTimeUtils.format(LocalDateTime.now()));
                extendDto.setWeekUseCount(reassignUseCount + 1);
                return JacksonUtil.serialize(extendDto);
            case RIGHTS_TYPE_VACATION:
                VacationExtendDto vacation = JacksonUtil.deserialize(rightsModel.getExtend(), VacationExtendDto.class);
                VacationExtendDto vacationExtendDto = new VacationExtendDto();
                vacationExtendDto.setVacationLimit(vacation.getVacationLimit());
                vacationExtendDto.setVacationUse(vacation.getVacationUse() + vacationUsed);
                return JacksonUtil.serialize(vacationExtendDto);
        }
        return StringUtils.EMPTY;
    }

    public static String buildExtendForWelfareFreeze(String extend, BigDecimal money) {
        WelfareExtendDto welfareExtendDto = JacksonUtil.deserialize(extend, WelfareExtendDto.class);
        if (Objects.nonNull(welfareExtendDto)) {
            welfareExtendDto.setWelfareUse(welfareExtendDto.getWelfareUse());
            welfareExtendDto.setWelfareLimit(welfareExtendDto.getWelfareLimit());
            welfareExtendDto.setWelfareFreeze(Optional.ofNullable(welfareExtendDto.getWelfareFreeze()).orElse(BigDecimal.ZERO).add(Optional.ofNullable(money).orElse(BigDecimal.ZERO)));
            return JacksonUtil.serialize(welfareExtendDto);
        }
        return StringUtils.EMPTY;
    }

    public static String buildExtendForReverse(String extend, BigDecimal money) {
        WelfareExtendDto welfareExtendDto = JacksonUtil.deserialize(extend, WelfareExtendDto.class);
        if (Objects.nonNull(welfareExtendDto)) {
            welfareExtendDto.setWelfareUse(welfareExtendDto.getWelfareUse().subtract(money));
            welfareExtendDto.setWelfareLimit(welfareExtendDto.getWelfareLimit());
            welfareExtendDto.setWelfareFreeze(welfareExtendDto.getWelfareFreeze());
            return JacksonUtil.serialize(welfareExtendDto);
        }
        return StringUtils.EMPTY;
    }
}
