package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.dcs.driver.value.account.AccountUidInfo;
import com.ctrip.dcs.driver.account.domain.condition.PhoneInfoCondition;
import com.ctrip.dcs.driver.account.application.dto.RegisterAccountResult;
import com.ctrip.dcs.driver.domain.application.domain.CommonResult;
import com.ctrip.dcs.driver.domain.infrastructure.dto.account.AccountBaseDto;

import java.util.List;

public interface UserCenterAccountGateway {

    AccountUidInfo queryAccountUidByPhone(String countryCode, String phoneNumber);
    List<AccountUidInfo> batchQueryAccountUidByPhone(List<PhoneInfoCondition> phoneInfoConditionList);

    AccountUidInfo queryAccountUidByEmail(String email);

    AccountUidInfo queryAccountUid(AccountBaseDto accountBaseDto);

    AccountInfoResponseType getAccountByUid(String uid);

    RegisterAccountResult registerNewAccount(AccountBaseDto accountBaseDto);

    CommonResult bindEmail(AccountBaseDto accountBaseDto, String uid);

    boolean bindEmail(String uid, String newEmail, String operator);

    boolean unBindEmail(String uid, String operator);

    boolean unBindPhone(String uid, String operator);

    CommonResult bindMobilePhone(AccountBaseDto accountBaseDto, String uid);

    boolean bindMobilePhone(String uid, String countryCode, String phoneNumber, String operator);

    boolean unfreezeAccount(String uid);


}
