package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.honour.RankObjectImpl;
import com.ctrip.dcs.driver.domain.honour.SubmitDriverLikeRequestType;
import com.ctrip.dcs.driver.domain.honour.SubmitDriverLikeResponseType;
import com.ctrip.dcs.driver.value.honour.RankObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 保存司机点赞记录
 */
@Component
public class SubmitDriverLikeEngine extends ShoppingExecutor<SubmitDriverLikeRequestType, SubmitDriverLikeResponseType>
        implements Validator<SubmitDriverLikeRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<SubmitDriverLikeRequestType> validator) {
        validator.ruleFor("refs").notNull().notEmpty();
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("likedDriverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final SubmitDriverLikeEngine owner;
        private final SubmitDriverLikeResponseType response;
        private final SubmitDriverLikeRequestType request;
        private RankObject rankObject;

        private Executor(SubmitDriverLikeRequestType request, SubmitDriverLikeResponseType response, SubmitDriverLikeEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;
            this.rankObject = new RankObjectImpl(this.owner.domainBeanFactory, this.request.driverId, this.request.refs);
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(this.rankObject);
        }

        @Override
        protected void buildResponse() {
            // 保存点赞记录
            this.rankObject.saveRankLike(this.request.likedDriverId);
        }
    }

    @Override
    public SubmitDriverLikeResponseType execute(SubmitDriverLikeRequestType request) {
        SubmitDriverLikeResponseType response = new SubmitDriverLikeResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public SubmitDriverLikeResponseType onException(SubmitDriverLikeRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("SubmitDriverLike error", ex);
        return super.onException(req, ex);
    }
}
