package com.ctrip.dcs.driver.account.application.convert;
/*
作者：pl.yang
创建时间：2025/5/8-下午1:49-2025
*/


import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.domain.account.AccountBankCardDTO;
import com.ctrip.dcs.driver.domain.account.QueryAccountBankCardByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountBankCardByDriverIdResponseType;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: QueryAccountBankCardByDriverIdConvert
 * @Package com.ctrip.dcs.driver.account.application.convert
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 下午1:49
 */

@Component
public class QueryAccountBankCardByDriverIdConvert {
    @Autowired
    CardInfoManagementRepository cardInfoManagementService;
    @Autowired
    BankCardInfoDTOConvert convertBankCardInfoConvert;

    public QueryAccountBankCardByDriverIdResponseType convertResponse(QueryAccountBankCardByDriverIdRequestType request, AccountBankCardDo accountBankCardRecord) {
        QueryAccountBankCardByDriverIdResponseType resp = new QueryAccountBankCardByDriverIdResponseType();
        resp.accountBankCard = new AccountBankCardDTO();
        resp.accountBankCard.uid = accountBankCardRecord.getUid();
        //3、查询卡号（传输加密）
        resp.accountBankCard.bankCardNo = cardInfoManagementService.getSinglePayCardInfo(accountBankCardRecord.getCardNo());

        resp.accountBankCard.yeepayRequestId = accountBankCardRecord.getRequestId();
        resp.accountBankCard.bankCardStatus = accountBankCardRecord.getCardStatusCode();
        resp.accountBankCard.bankCardId =accountBankCardRecord.getCardNo();
        resp.accountBankCard.bankName = accountBankCardRecord.getBankName();
        resp.accountBankCard.cardAreaType = accountBankCardRecord.getBankCardAreaTypeEnumCode();
        resp.accountBankCard.bankPhoneNumber = accountBankCardRecord.getPhoneNo();
        resp.accountBankCard.cardAreaType = accountBankCardRecord.getBankCardAreaTypeEnumCode();
        resp.accountBankCard.bankCardInfo = convertBankCardInfoConvert.convertBankCardInfo(accountBankCardRecord);
        return resp;
    }
}
