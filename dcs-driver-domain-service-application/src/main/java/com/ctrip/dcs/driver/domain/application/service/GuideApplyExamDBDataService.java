package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideApplyExamPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideApplyExamModel;
import java.sql.Timestamp;
import java.util.List;

public interface GuideApplyExamDBDataService {

    void insertGuideApplyExam(GuideApplyExamPO guideApplyExamPO);

    GuideApplyExamModel queryGuideApplyExamByAccountAndSubject(String examAccountId, String applySubject);

    List<GuideApplyExamModel> queryGuideApplyExamByAccount(String examAccountId);

    List<GuideApplyExamModel> queryPassedGuideApplyExamByAccount(String examAccountId);

    Long countCallExamFailed();

    List<GuideApplyExamModel> queryCallExamFailedRecords(int startIndex, int count);

    List<GuideApplyExamModel> queryApplyFailedRecords(int startIndex, int count);


    void updateGuideApplyExam(GuideApplyExamPO guideApplyExamPO);


    Long queryMaxId(Timestamp time);

    Long queryMinId(Timestamp time);

    List<GuideApplyExamModel> queryUpassedBetweenIds(Long startId,Long endId);
}
