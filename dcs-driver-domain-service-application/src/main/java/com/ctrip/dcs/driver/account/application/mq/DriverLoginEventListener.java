package com.ctrip.dcs.driver.account.application.mq;

import com.ctrip.dcs.driver.account.domain.enums.LoginTypeEnum;
import com.ctrip.dcs.driver.domain.application.service.PhoneCheckService;
import com.ctrip.dcs.driver.domain.login.DriverLoginSuccessMessage;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/11 17:54
 * @Description: 司机登录成功消息监听消费
 */
@Component
public class DriverLoginEventListener implements MessageListener {
    Log log = Log.getInstance(DriverLoginEventListener.class);

    @Resource
    private PhoneCheckService phoneCheckService;

    @Override
    @QmqConsumer(prefix = "dcs.driver.login.info", consumerGroup = "*********")
    public void onMessage(Message msg) {
        DriverLoginSuccessMessage loginMsg = JsonUtil.fromString(msg.getStringProperty("data"), DriverLoginSuccessMessage.class);
        if (Objects.isNull(loginMsg)) {
            return;
        }
        if (!LoginTypeEnum.verifyPhone(loginMsg.getLoginType())) {
            log.info("DriverLoginEventListener notValid phone", JsonUtil.toString(loginMsg));
            return;
        }
        phoneCheckService.updateDriverPhoneCheckRes(loginMsg);

    }
}
