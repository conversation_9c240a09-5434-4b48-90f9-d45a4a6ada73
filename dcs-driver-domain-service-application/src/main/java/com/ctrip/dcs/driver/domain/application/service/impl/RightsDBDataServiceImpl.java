package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.RightsDBDataService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class RightsDBDataServiceImpl implements RightsDBDataService {

    @Autowired
    DrivRightsDao drivRightsDao;

    @Autowired
    DrivLevelDao drivLevelDao;

    @Autowired
    DrivRightsRecordDao drivRightsRecordDao;

    @Override
    public List<RightsModel> queryDriverRights(long driverId, String monthIdx) {
        List<DrivRightsPO> drivRightsPOS = drivRightsDao.queryDriverRights(driverId, monthIdx);
        if (CollectionUtils.isEmpty(drivRightsPOS)) {
            return Collections.emptyList();
        }
        return drivRightsPOS.stream().map(po -> {
            RightsModel model = new RightsModel();
            model.setId(po.getId());
            model.setRightsConfigId(po.getRightsConfigId());
            model.setRightsName(po.getRightsName());
            model.setRightsDesc(po.getRightsDesc());
            model.setCityId(po.getCityId());
            model.setMonthIdx(po.getMonthIdx());
            model.setDrivId(po.getDrivId());
            model.setDrivLevel(po.getDrivLevel());
            model.setRightsType(po.getRightsType());
            model.setUseLimit(po.getUseLimit());
            model.setUseCount(po.getUseCount());
            model.setRightsStatus(po.getRightsStatus());
            model.setRightsStratTime(LocalDateTimeUtils.format(po.getRightsStratTime()));
            model.setRightsEndTime(LocalDateTimeUtils.format(po.getRightsEndTime()));
            model.setRightsIssueTime(LocalDateTimeUtils.format(po.getDatachangeCreatetime()));
            model.setExtend(po.getExtend());

            return model;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RightsRecordModel> queryDriverRecords(long driverId, String useStartDate, String useEndDate, List<Integer> rightsTypes) {
        List<DrivRightsRecordPO> drivRightsRecordPOS = drivRightsRecordDao.queryDriverRecords(driverId, Timestamp.valueOf(useStartDate), Timestamp.valueOf(useEndDate));
        return drivRightsRecordPOS.stream().filter(o -> o.getIsDeleted() == 0).filter(recordPO -> rightsTypes.contains(recordPO.getRightsType())).map(this::convert2RightsRecordModel).collect(Collectors.toList());
    }

    @Override
    public LevelModel queryDriverLevel(long driverId, String monthIdx) {
        DrivLevelPO drivLevelPO = drivLevelDao.queryDriverLevel(driverId, monthIdx);
        if (Objects.isNull(drivLevelPO)) {
            return null;
        }
        return convert2LevelModel(drivLevelPO);
    }

    @Override
    public void saveRightsRecords(DrivRightsRecordPO recordPO) {
        drivRightsRecordDao.insert(recordPO);
    }

    @Override
    public void updateDriverRights(DrivRightsPO rightsPO) {
        drivRightsDao.update(rightsPO);
    }

    @Override
    public List<RightsRecordModel> queryDriverRecordsById(long id) {
        return drivRightsRecordDao.queryDriverRecordsByRightsId(id).stream().filter(o -> o.getIsDeleted() == 0).map(this::convert2RightsRecordModel).collect(Collectors.toList());
    }

    @Override
    public List<RightsRecordModel> queryDriverRecordsBySupplyOrderId(long driverId, int rightType, String supplyOrderId) {
        return drivRightsRecordDao.queryDriverRecordsBySupplyOrderId(driverId,rightType,supplyOrderId).stream().map(this::convert2RightsRecordModel).collect(Collectors.toList());
    }

    @Override
    public List<RightsRecordModel> querDriverRecordsByDriver(Integer rightsType, Long id) {
        return drivRightsRecordDao.queryDriverRecordsByDriver(id,rightsType).stream().filter(o -> o.getIsDeleted() == 0).map(this::convert2RightsRecordModel).collect(Collectors.toList());
    }

    private RightsRecordModel convert2RightsRecordModel(DrivRightsRecordPO record) {
        RightsRecordModel model = new RightsRecordModel();
        model.setRightsIssueId(record.getRightsId());
        model.setDrivId(record.getDrivId());
        model.setRightsType(record.getRightsType());
        model.setUseLevel(record.getUseLevel());
        model.setRightsName(record.getRightsName());
        model.setLevelName(record.getLevelName());
        model.setUserOrderId(record.getUserOrderId());
        model.setPurchaseOrderId(record.getPurchaseOrderId());
        model.setSupplyOrderId(record.getSupplyOrderId());
        model.setPunishOrderId(record.getPunishOrderId());
        model.setMoney(record.getMoney());
        model.setRightsUseTime(record.getDatachangeCreatetime().toLocalDateTime());

        return model;
    }

    private LevelModel convert2LevelModel(DrivLevelPO drivLevelPO) {
        LevelModel levelModel = new LevelModel();
        levelModel.setLevelConfigId(drivLevelPO.getLevelConfigId());
        levelModel.setLevelName(drivLevelPO.getLevelName());
        levelModel.setCityId(drivLevelPO.getCityId());
        levelModel.setMonthIdx(drivLevelPO.getMonthIdx());
        levelModel.setDrivId(drivLevelPO.getDrivId());
        levelModel.setDrivLevel(drivLevelPO.getDrivLevel());
        levelModel.setDrivPoint(Strings.isBlank(drivLevelPO.getDrivPoint()) ? null : new BigDecimal(drivLevelPO.getDrivPoint()));
        levelModel.setDrivActivity(Strings.isBlank(drivLevelPO.getDrivActivity()) ? null : new BigDecimal(drivLevelPO.getDrivActivity()));
        levelModel.setDrivRank(Strings.isBlank(drivLevelPO.getDrivRank()) ? null : Integer.valueOf(drivLevelPO.getDrivRank()));
        return levelModel;
    }
}
