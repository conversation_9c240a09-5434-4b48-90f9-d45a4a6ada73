package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsVerifyEventDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.TmsVerifyEventPO;
import com.ctrip.dcs.driver.domain.verify.DriverIsNeedVerifyRequestType;
import com.ctrip.dcs.driver.domain.verify.DriverIsNeedVerifyResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 司导是否需要人脸识别
 */
@Component
public class QueryDriverIsNeedVerifyEngine extends ShoppingExecutor<DriverIsNeedVerifyRequestType, DriverIsNeedVerifyResponseType>
        implements Validator<DriverIsNeedVerifyRequestType> {

    @Autowired
    private TmsVerifyEventDao tmsVerifyEventDao;

    @Override
    public void validate(AbstractValidator<DriverIsNeedVerifyRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverIsNeedVerifyEngine owner;
        private final DriverIsNeedVerifyResponseType response;
        private final DriverIsNeedVerifyRequestType request;

        private Executor(DriverIsNeedVerifyRequestType request, DriverIsNeedVerifyResponseType response, QueryDriverIsNeedVerifyEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;
            this.response.verifyFlag = 1;
        }

        @Override
        protected boolean validate() {
            return true;
        }

        @Override
        protected void buildResponse() {
            List<TmsVerifyEventPO> verifyEvents = this.owner.tmsVerifyEventDao.findNeedVerifyEvents(request.driverId);
            if(CollectionUtils.isEmpty(verifyEvents)){
                return;
            }

            //验证开始时间大于当前时间，不需要认证
            List<TmsVerifyEventPO> subVerifyEvents = verifyEvents.stream()
                    .filter(e -> e.getVerifyFlag() != null && e.getVerifyFlag() == 2)
                    .filter(e -> e.getVerifyStartTime() != null && e.getVerifyStartTime().before(new Date()))
                    .collect(Collectors.toList());
            //认证标识 1不需要认证(没有可以认证的事件) |2 需要人脸认证
            if(CollectionUtils.isNotEmpty(subVerifyEvents)){
                response.verifyFlag = 2;
            }
        }
    }

    @Override
    public DriverIsNeedVerifyResponseType execute(DriverIsNeedVerifyRequestType request) {
        DriverIsNeedVerifyResponseType response = new DriverIsNeedVerifyResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public DriverIsNeedVerifyResponseType onException(DriverIsNeedVerifyRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("DriverIsNeedVerify error", ex);
        return super.onException(req, ex);
    }
}
