package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.util.LanguageUtil;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.DriverGreyConfig;
import com.ctrip.dcs.driver.message.api.PushMessageRequestType;
import com.ctrip.dcs.im.groupchat.dto.GroupChatMessageDto;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.ctrip.igt.RequestHeader;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.language.Language;
import com.ctrip.igt.framework.common.language.LanguageContext;
import com.ctrip.igt.im.interfaces.service.implus.message.ImPlusQueryImUrlRequestType;
import com.ctrip.igt.im.interfaces.service.implus.message.ImPlusQueryImUrlResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;

/**
 * 项目：
 * 文档地址：http://conf.ctripcorp.com/pages/viewpage.action?pageId=1850752360
 * */
@Component
public class DriverImInfoListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DriverImInfoListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    private static final String DRIVER_VERSION_REDIS_KEY = "version_new:%s";

    @Autowired
    DomainBeanFactory domainBeanFactory;

    @Autowired
    DirectorRedis directorRedis;

    @Autowired
    DriverGreyConfig driverGreyConfig;

    @Autowired
    @Qualifier("CommonThreadPool")
    ExecutorService executorService;

    @Autowired
    DriverMessageServiceProxy driverMessageServiceProxy;

    @Autowired
    CommonMultipleLanguages commonMultipleLanguages;

    @Override
    @QmqConsumer(prefix = "dcs.im.groupchat.message.push", consumerGroup = CONSUMER_GROUP, idempotentChecker = "redisIdempotent")
    public void onMessage(Message message) {
        if(Objects.isNull(message)){
            return;
        }
        String data = message.getStringProperty("content");
        if(StringUtils.isBlank(data)){
            return;
        }
        GroupChatMessageDto dataInfo;
        try{
            dataInfo = JacksonUtil.deserialize(data, GroupChatMessageDto.class);
        } catch (Exception e){
            LOGGER.error(String.format("drivermessage error. [%s]", data));
            return;
        }
        if(Objects.isNull(dataInfo)){
            return;
        }
        //过滤订单号为空或消息为空数据
        if(StringUtils.isBlank(dataInfo.orderId) || StringUtils.isBlank(dataInfo.messageBody)){
            return;
        }
        //过滤sendRole 是司机或者 toRole不是司机的都
        if(ObjectUtils.defaultIfNull(dataInfo.sendRole, 0) == 2 || ObjectUtils.defaultIfNull(dataInfo.toRole, 0) != 2){
            return;
        }
        //过滤无司机ID数据
        if(StringUtils.isBlank(dataInfo.driverId) || !StringUtils.isNumeric(dataInfo.driverId)){
            return;
        }

        //0:普通文本消息；1：图片消息
        int msgType = ObjectUtils.defaultIfNull(dataInfo.messageType, 0);
        switch (msgType){
            case 0:
            case 1:
            case 1009:
                break;
            default:
                return;
        }

        executorService.submit(() -> {
            this.pushMessage(dataInfo);
        });
    }

    /**
     * 生成推送信息
     * */
    protected void pushMessage(GroupChatMessageDto dataInfo){
        long driverId = Long.parseLong(dataInfo.driverId);
        if(!this.isSupportPush(driverId)){
            return;
        }

        if(!this.isSupportIm(driverId, dataInfo.orderId, dataInfo.driverOrderId)){
            return;
        }

        PushMessageRequestType requestType = new PushMessageRequestType();
        requestType.templateId = "1115";
        Map<String, String> data = new HashMap<>();
        data.put("orderId", dataInfo.driverOrderId);
        data.put("customeroid", dataInfo.orderId);
        requestType.setData(data);

        Map<String, String> sharkData = new HashMap<>();
        String groupTitle = StringUtils.defaultIfBlank(dataInfo.groupTitle, Strings.EMPTY);
        //ApiResult{code=20001, msg='title length should <= 50', data=null}
        if(StringUtils.isNotBlank(groupTitle) && groupTitle.length() > 35){
            groupTitle = String.format("%s...", groupTitle.substring(0,35));
        }
        sharkData.put("grouptitle", groupTitle);

        String messageBody = dataInfo.messageBody;
        int messageType = ObjectUtils.defaultIfNull(dataInfo.messageType, 0);
        switch (messageType){
            case 1:
            case 1009:
                // 查询司机语言设置
                String redisLanguage = directorRedis.get(String.format(LanguageUtil.DRIVER_LANGUAGE_REDIS_KEY, driverId));
                String contextLanguage = LanguageUtil.isMultiLanguage(redisLanguage) ? LanguageUtil.DRIVER_LANGUAGE_VALUE_EN : LanguageUtil.DRIVER_LANGUAGE_VALUE_ZH;
                LanguageContext.setLanguage(Language.newBuilder().withLanguageCode(contextLanguage).build());
                String sharkKey;
                if(messageType == 1){
                    sharkKey ="driverMeassage.instantmessage.notification.piccontent";  //1    提示 [图片]
                } else {
                    sharkKey = "driverMeassage.instantmessage.notification.revoke";     //1009 提示 消息已撤回
                }
                messageBody = commonMultipleLanguages.getContent(sharkKey);
                break;
            default:
                if (StringUtils.isNotBlank(messageBody) && messageBody.length() > 200) {
                    //ApiResult{code=20001, msg='body length should <= 256', data=null}
                    messageBody = String.format("%s...", messageBody.substring(0, 200));
                }
                break;
        }
        sharkData.put("messagebody", messageBody);
        requestType.setSharkValues(sharkData);
        requestType.setGuideIds(Lists.newArrayList());
        requestType.setDriverIds(Lists.newArrayList(driverId));
        driverMessageServiceProxy.pushMessage(requestType);
    }

    /**
     * 是否支持IM消息push
     * 是否支持城市灰度
     * */
    private boolean isSupportPush(long driverId){
        BigDecimal rnVersion = this.getDriverVersion(driverId);
        Pair<Long, Long> driverInfo = this.getDriverCityAndCountry(driverId);
        if(driverInfo == null){
            return false;
        }

        return driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, driverInfo.getLeft(), driverInfo.getRight(), rnVersion);
    }

    /**
     * 查询司机客户端版本号，数据由司机端100027977写入
     */
    private BigDecimal getDriverVersion(long driverId) {
        String versionStr = this.directorRedis.get(String.format(DRIVER_VERSION_REDIS_KEY, driverId));
        if (StringUtils.isNotEmpty(versionStr)) {
            String[] versionArr = versionStr.split("#");
            if (versionArr.length == 2) {
                return new BigDecimal(versionArr[1]);
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 查询司机城市、国家信息
     * */
    private Pair<Long, Long> getDriverCityAndCountry(long driverId){
        DriverInfo driverInfo = this.domainBeanFactory.tmsTransportServiceProxy().queryDriver(driverId);
        if(driverInfo == null) {
            return null;
        }
        return Pair.of(driverInfo.cityId, driverInfo.countryId);
    }

    /**
     * 是否支持IM
     * */
    private boolean isSupportIm(long driverId, String ctripOrderId, String driverOrderId) {
        ImPlusQueryImUrlRequestType requestType = new ImPlusQueryImUrlRequestType();
        requestType.requestHeader = new RequestHeader();
        requestType.serverfrom = 2;
        requestType.supplyOrderId = StringUtils.defaultIfBlank(driverOrderId, Strings.EMPTY);
        requestType.ctripOrderId = ctripOrderId;
        requestType.setDriverId(String.valueOf(driverId));
        ImPlusQueryImUrlResponseType responseType = null;
        try {
            responseType = this.domainBeanFactory.imServiceProxy().imPlusQueryImUrl(requestType);
        } catch (Exception e) {
            LOGGER.error(String.format("imPlusQueryImUrl error. [%s] [%s]", ctripOrderId, driverId));
        }
        return responseType != null && BooleanUtils.isTrue(responseType.isSupportIM);
    }
}
