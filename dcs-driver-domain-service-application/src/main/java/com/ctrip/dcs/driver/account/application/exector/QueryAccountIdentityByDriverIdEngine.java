package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.domain.account.AccountIdentitySourceDTO;
import com.ctrip.dcs.driver.domain.account.QueryAccountIdentityByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountIdentityByDriverIdResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@ServiceLogTag(tagKeys= {"driverId"})
public class QueryAccountIdentityByDriverIdEngine extends ShoppingExecutor<QueryAccountIdentityByDriverIdRequestType, QueryAccountIdentityByDriverIdResponseType> implements Validator<QueryAccountIdentityByDriverIdRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public QueryAccountIdentityByDriverIdResponseType execute(QueryAccountIdentityByDriverIdRequestType request) {


        QueryAccountIdentityByDriverIdResponseType resp = new QueryAccountIdentityByDriverIdResponseType();
        resp.setIdentityList(Lists.newArrayList());

        List<AccountMapperPO> identityList = accountService.getIdentityByDriverId(request.getDriverId());
        if (CollectionUtils.isNotEmpty(identityList)) {
            List<AccountIdentitySourceDTO> identitySourceDTOList = identityList.stream().map(o -> new AccountIdentitySourceDTO(o.getSource(), o.getSourceId(), Objects.equals(o.getIsValid(), 1))).collect(Collectors.toList());
            resp.setUid(identityList.get(0).getUid());
            resp.setIdentityList(identitySourceDTOList);
        }
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public Boolean isEmptyResult(QueryAccountIdentityByDriverIdResponseType resp) {
        return !(resp!=null&&CollectionUtils.isNotEmpty(resp.getIdentityList()));

    }
}
