package com.ctrip.dcs.driver.domain.application.exector.control;


import com.ctrip.dcs.common.enums.AppGuidanceTypeEnum;
import com.ctrip.dcs.driver.domain.application.service.DriverAppControlService;
import com.ctrip.dcs.driver.domain.guidance.QueryDriverAppGuidanceRequestType;
import com.ctrip.dcs.driver.domain.guidance.QueryDriverAppGuidanceResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class QueryDriverAppGuidanceEngine extends ShoppingExecutor<QueryDriverAppGuidanceRequestType, QueryDriverAppGuidanceResponseType> implements Validator<QueryDriverAppGuidanceRequestType> {
    @Autowired
    private DriverAppControlService appControlService;

    @Override
    public void validate(AbstractValidator<QueryDriverAppGuidanceRequestType> validator) {
        validator.ruleFor("uid").notNull().notEmpty();
        validator.ruleFor("guidanceTypes").notNull().notEmpty();
    }

    @Override
    public QueryDriverAppGuidanceResponseType execute(QueryDriverAppGuidanceRequestType request) {
        QueryDriverAppGuidanceResponseType responseType = new QueryDriverAppGuidanceResponseType();
        List<String> notSupportTypes = AppGuidanceTypeEnum.filter(request.getGuidanceTypes());
        if (CollectionUtils.isNotEmpty(notSupportTypes)) {
            return ServiceResponseUtils.fail(responseType, "400", "not support type:" + notSupportTypes);
        }
        responseType.setData(appControlService.queryDriverAppGuidance(request.getUid(), request.getGuidanceTypes()));
        return ServiceResponseUtils.success(responseType);
    }

}
