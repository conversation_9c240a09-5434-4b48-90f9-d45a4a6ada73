package com.ctrip.dcs.driver.account.application.convert;
/*
作者：pl.yang
创建时间：2025/5/8-上午10:03-2025
*/


import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.domain.account.AccountBankCardDTO;
import com.ctrip.dcs.driver.domain.account.QueryAccountCurrentlyBoundBankCardRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountCurrentlyBoundBankCardResponseType;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: QueryAccountCurrentlyBoundBankCardConvert
 * @Package com.ctrip.dcs.driver.account.application.convert
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 上午10:03
 */

@Component
public class QueryAccountCurrentlyBoundBankCardConvert {
    @Autowired
    CardInfoManagementRepository cardInfoManagementService;
    @Autowired
    BankCardInfoDTOConvert convertBankCardInfoConvert;


    public QueryAccountCurrentlyBoundBankCardResponseType convertResponse(QueryAccountCurrentlyBoundBankCardRequestType request, AccountBankCardDo accountBankCardRecord) {
        QueryAccountCurrentlyBoundBankCardResponseType resp = new QueryAccountCurrentlyBoundBankCardResponseType();
        if(accountBankCardRecord==null){
            return ServiceResponseUtils.fail(resp,"204");
        }


        resp.accountBankCard = new AccountBankCardDTO();

        //2、查询卡号（传输加密）
        resp.accountBankCard.bankCardNo = cardInfoManagementService.getSinglePayCardInfo(accountBankCardRecord.getCardNo());

        //3、其他信息
        resp.accountBankCard.uid = request.uid;

        resp.accountBankCard.yeepayRequestId = accountBankCardRecord.getRequestId();
        resp.accountBankCard.bankCardStatus = accountBankCardRecord.getCardStatusCode();
        //支付方式

        //1、查询
        resp.accountBankCard.cardAreaType = accountBankCardRecord.getBankCardAreaTypeEnumCode();
        resp.accountBankCard.bankCardId = accountBankCardRecord.getCardNo();
        resp.accountBankCard.bankName = accountBankCardRecord.getBankName();
        resp.accountBankCard.bankPhoneNumber = accountBankCardRecord.getPhoneNo();
        resp.accountBankCard.cardAreaType = accountBankCardRecord.getBankCardAreaTypeEnumCode();
        resp.accountBankCard.bankCardInfo = convertBankCardInfoConvert.convertBankCardInfo(accountBankCardRecord);

        return resp;
    }


}
