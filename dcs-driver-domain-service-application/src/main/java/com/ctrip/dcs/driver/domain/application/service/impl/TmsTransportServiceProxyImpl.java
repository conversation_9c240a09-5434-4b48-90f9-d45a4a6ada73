package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.TmsTransportProxyService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.transport.TmsTransportServiceProxy;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.api.regulation.OldDriverInfo;
import com.ctrip.dcs.tms.transport.api.regulation.QueryHistoryDrvDataRequestType;
import com.ctrip.dcs.tms.transport.api.regulation.QueryHistoryDrvDataResponseType;
import com.ctrip.dcs.tms.transport.api.resource.driver.DrvBase;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOAResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class TmsTransportServiceProxyImpl implements TmsTransportProxyService {

    @Autowired
    private TmsTransportServiceProxy tmsTransportServiceProxy;

    @Override
    public DriverInfo queryDriver(long id) {
        DriverInfoSOARequestType requestType = new DriverInfoSOARequestType();
        requestType.driverIds = String.valueOf(id);
        DriverInfoSOAResponseType responseType;
        try {
            responseType = tmsTransportServiceProxy.queryDriver(requestType);
        } catch (Exception e) {
            return null;
        }
        if (Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.driverList)) {
            return responseType.driverList.get(0);
        }
        return null;
    }

    /**
     * 根据城市查询司机
     * 筛选
     */
    @Override
    public List<Long> queryDriverList(long cityId) {
        QueryDriver4BaseSOARequestType requestType = new QueryDriver4BaseSOARequestType();
        requestType.cityIdList = Collections.singletonList(cityId);
        requestType.drvStatusList = Arrays.asList(1, 2);
        requestType.proLineList = Collections.singletonList(1); //产线(1.接送机站,2,打车,3.包车)
        QueryDriver4BaseSOAResponseType responseType;
        try {
            responseType = tmsTransportServiceProxy.queryDriver4Base(requestType);
        } catch (Exception e) {
            return Collections.emptyList();
        }
        if (Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.data)) {
            return responseType.data.stream().filter(d -> CollectionUtils.isNotEmpty(d.getProLineIdList()) && d.getProLineIdList().contains(1)).map(DrvBase::getDrvId).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public OldDriverInfo queryOldDriver(String driverPhone) {
        QueryHistoryDrvDataRequestType requestType = new QueryHistoryDrvDataRequestType();
        requestType.drvPhone = driverPhone;
        QueryHistoryDrvDataResponseType responseType;
        try {
            responseType = tmsTransportServiceProxy.queryHistoryDrvData(requestType);
        } catch (Exception e) {
            return null;
        }
        if (Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.infoList)) {
            return responseType.infoList.get(0);
        }
        return null;
    }

    @Override
    public QueryDrvDetailDTOSOA queryDriverDetail(Long driverId) {
        QueryDrvDetailSOARequestType requestType = new QueryDrvDetailSOARequestType();
        requestType.setDrvId(driverId);
        QueryDrvDetailSOAResponseType responseType;
        try {
            responseType = tmsTransportServiceProxy.queryDrvDetail(requestType);
        } catch (Exception e) {
            return null;
        }
        if (responseType != null && responseType.getData() != null) {
            return responseType.getData();
        }
        return null;
    }
}
