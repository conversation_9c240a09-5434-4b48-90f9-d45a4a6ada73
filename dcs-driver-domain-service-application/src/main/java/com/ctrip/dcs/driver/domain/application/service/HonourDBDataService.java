package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalRankingPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourMedalNoticeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourRankLikeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;

import java.util.List;
import java.util.Map;

/**
 * 聚合勋章有关的查询、更新
 * */
public interface HonourDBDataService {

    /**
     * 通用 查询最新数据批次
     * */
    AdmPrdTrhDriverHonorInfoPO queryMaxBatchDataInfo();
    /**
     * 通用 查询最新数据
     * */
    AdmPrdTrhDriverHonorInfoPO queryMaxBatchDataDetail(long driverId);

    /**
     * 勋章相关 勋章总数
     * */
    int queryDriverMedalCount(long driverId);

    /**
     * 勋章相关 提醒过的勋章信息
     * */
    DrivHonourMedalNoticeRecordPO queryNoticedMedalInfo(long driverId);
    /**
     * 勋章相关 提醒过的勋章列表
     * */
    List<String> queryNoticedMedalList(long driverId);
    /**
     * 勋章相关 插入提醒过的勋章信息
     * */
    int insertNoticedMedalInfo(DrivHonourMedalNoticeRecordPO drivHonourMedalNoticeRecordPO);
    /**
     * 勋章相关 更新提醒过的勋章信息
     * */
    int updateNoticedMedalInfo(DrivHonourMedalNoticeRecordPO drivHonourMedalNoticeRecordPO);

    /**
     * 勋章相关 查询勋章基础配置
     * */
    List<DimPrdTrhDriverMedalInfoPO> queryBasicMedalInfo();

    /**
     * 勋章相关 查询新勋章的排行信息
     * */
    Map<String, DimPrdTrhDriverMedalRankingPO> queryDriverMedalRankInfo(long driverId);

    /**
     * 排行榜相关 查询城市排行榜
     * */
    List<RankOriginalDataModel> queryCityRankList(long batchId, long cityId, boolean isWeek);

    /**
     * 点赞相关 查询点赞信息
     * */
    DrivHonourRankLikeRecordPO searchHonourRankLikeInfo(int rankRefs, long driverId);
    /**
     * 点赞相关 插入点赞信息
     * */
    int insertHonourRankLikeInfo(DrivHonourRankLikeRecordPO drivHonourRankLikeRecordPO);
    /**
     * 点赞相关 更新点赞信息
     * */
    int updateHonourRankLikeInfo(DrivHonourRankLikeRecordPO drivHonourRankLikeRecordPO);
}
