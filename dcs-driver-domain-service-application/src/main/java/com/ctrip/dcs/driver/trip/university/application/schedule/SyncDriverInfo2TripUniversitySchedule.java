package com.ctrip.dcs.driver.trip.university.application.schedule;

import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.trip.university.infrastructure.utils.DateUtil;
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityQscheduleParameter;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import static com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants.*;


@Component
public class SyncDriverInfo2TripUniversitySchedule {

    @Autowired
    TripUniversityService tripUniversityService;

    @Autowired
    private AccountBaseInfoDao accountBaseInfoDao;

    @Autowired
    AccountMapperDao accountMapperDao;

    @Autowired
    @Qualifier("TripUniversityThreadPool")
    ExecutorService executorService;

    @Autowired
    TripUniversityQconfig tripUniversityQconfig;

    @QSchedule("dcs.sync.driver.info.to.trip.university.job")
    public void onExecute(Parameter parameter) {

        String type = DEFAULT_TYPE;
        String from = null;
        String to = null;
        String drvIds = null;
        if (parameter != null) {
            type = Optional.ofNullable(parameter.getString("type")).orElse(DEFAULT_TYPE);
            from = parameter.getString("from");
            to = parameter.getString("to");
            drvIds = parameter.getString("drvIds");
        }

        Logger logger = TaskHolder.getKeeper().getLogger();
        logger.info("dcs.sync.driver.info.to.trip.university.job start {}, {}, {}, {}", type, from, to, drvIds);

        switch(type) {
            case DEFAULT_TYPE:
                syncFromAccountBaseInfo();
                break;
            case BY_DRV_IDS:
                syncByDrvIds(drvIds);
                break;
            case BY_DATE:
                syncByDate(from, to);
                break;
        }
    }

    private void syncByDate(String from, String to) {
        DriverSyncDrvInfoToTripUniversityQscheduleParameter condition = new DriverSyncDrvInfoToTripUniversityQscheduleParameter();
        condition.setDrvIdList(Lists.newArrayList(0L));
        condition.setType(BY_DATE);
        condition.setFrom(DateUtil.string2Timestamp(from, FORMAT));
        condition.setTo(DateUtil.string2Timestamp(to, FORMAT));
        syncFromAccountBaseInfo(condition);
    }

    private void syncByDrvIds(String drvIds) {
        DriverSyncDrvInfoToTripUniversityQscheduleParameter condition = new DriverSyncDrvInfoToTripUniversityQscheduleParameter();
        condition.setDrvIdList(Lists.newArrayList(Arrays.stream(drvIds.split(",")).map(Long::parseLong).collect(
          Collectors.toList())));
        condition.setType(BY_DRV_IDS);
        syncFromAccountBaseInfo(condition);
    }

    private void syncFromAccountBaseInfo() {
        DriverSyncDrvInfoToTripUniversityQscheduleParameter condition = new DriverSyncDrvInfoToTripUniversityQscheduleParameter();
        condition.setDrvIdList(Lists.newArrayList(0L));
        condition.setType(DEFAULT_TYPE);
        condition.setFrom(DateUtil.string2Timestamp("1970-01-01", FORMAT));
        condition.setTo(new Timestamp(new Date().getTime()));
        syncFromAccountBaseInfo(condition);
    }

    @SneakyThrows
    private void syncFromAccountBaseInfo(DriverSyncDrvInfoToTripUniversityQscheduleParameter condition) {
        Logger logger = TaskHolder.getKeeper().getLogger();

        try{
            int pageSize = tripUniversityQconfig.getSyncBatchSize();
            AtomicReference<Long> drvId = new AtomicReference<>(0L);
            List<AccountBaseInfoPO> resultList = null;

            do {
                switch (condition.getType()) {
                    case DEFAULT_TYPE:
                    case BY_DATE:
                        resultList = accountBaseInfoDao.batchQueryPageByDateRange(drvId.get(), condition.getFrom(),
                          condition.getTo(), pageSize);
                        break;
                    case BY_DRV_IDS:
                        List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryBySource(
                          Lists.newArrayList(AccountSouceEnum.DRIVER_SOURCE.getName(), AccountSouceEnum.DRIVER_GUIDE.getName()),
                          condition.getDrvIdList().stream().map(String::valueOf).collect(Collectors.toList()));
                        resultList = accountBaseInfoDao.batchQueryByUID(
                          accountMapperPOList.stream().map(AccountMapperPO::getUid).collect(Collectors.toList()));
                        break;
                    default:
                        return;
                }

                //同步
                CompletableFuture.allOf(resultList.stream().map(
                    item -> CompletableFuture.supplyAsync(
                      () -> tripUniversityService.syncDriverInfoToTripUniversity(convert(item)), executorService))
                  .toArray(CompletableFuture[]::new)).get();

                // 获取最大ID
                resultList.forEach(item -> {
                    drvId.set(Math.max(drvId.get(), item.getId()));
                });

                logger.info("dcs.sync.driver.info.to.trip.university.job synced drvId: {}", drvId.get());

            } while (CollectionUtils.isNotEmpty(resultList) && !Objects.equals(BY_DRV_IDS, condition.getType()));
        }catch (Exception e) {
            logger.info("dcs.sync.driver.info.to.trip.university.job.error {}", e.getMessage());
            Cat.logEvent("dcs.sync.driver.info.to.trip.university.job.error", e.getMessage());
        }
    }

    private TripUniversityDriverDTO convert(AccountBaseInfoPO accountBaseInfoPO) {
        TripUniversityDriverDTO tripUniversityDriverDTO = new TripUniversityDriverDTO();
        tripUniversityDriverDTO.setFailedLogId(accountBaseInfoPO.getId());
        tripUniversityDriverDTO.setUid(accountBaseInfoPO.getUid());
        return tripUniversityDriverDTO;
    }

}
