package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceDriverObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceObjectImpl;
import com.ctrip.dcs.driver.domain.finance.FinanceWithdrawRequestType;
import com.ctrip.dcs.driver.domain.finance.FinanceWithdrawResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 提现
 */
@Component
public class FinanceWithdrawEngine extends ShoppingExecutor<FinanceWithdrawRequestType, FinanceWithdrawResponseType>
        implements Validator<FinanceWithdrawRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<FinanceWithdrawRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("bankCardInfo").notNull().notEmpty();
        validator.ruleFor("password").notNull().notEmpty();
        validator.ruleFor("amount").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final FinanceWithdrawEngine owner;
        private final FinanceWithdrawResponseType response;
        private final FinanceWithdrawRequestType request;
        private FinanceDriverObject financeDriverObject;

        private Executor(FinanceWithdrawRequestType request, FinanceWithdrawResponseType response, FinanceWithdrawEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.responseResult.returnCode = FinanceResultEnum.OK.getCode();
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            financeDriverObject = new FinanceDriverObjectImpl(this.request.driverId, this.request.source, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            if(FinanceResultEnum.isValidError(this.financeDriverObject.validResult())) {
                response.responseResult.returnCode = this.financeDriverObject.validResult();
                return false;
            }
            return true;
        }

        @Override
        protected void buildResponse() {
            FinanceObject financeObject = new FinanceObjectImpl(this.request.driverId,
                    false, this.owner.domainBeanFactory);

            response.responseResult.returnCode = financeObject.withdraw(
                    this.request.password,
                    this.request.amount,
                    this.request.bankCard,
                    this.request.bankCardInfo,
                    this.financeDriverObject.driverName(),
                    this.request.bankId);
        }
    }

    @Override
    public FinanceWithdrawResponseType execute(FinanceWithdrawRequestType request) {
        FinanceWithdrawResponseType response = new FinanceWithdrawResponseType();
        Executor executor = new Executor(request, response, this);
        executor.buildResponse();
        if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
            response.responseResult.returnMessage = this.domainBeanFactory.financeConvertService().getFinanceResult(response.responseResult.returnCode);
            return ServiceResponseUtils.fail(response, response.responseResult.returnCode, response.responseResult.returnMessage);
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public FinanceWithdrawResponseType onException(FinanceWithdrawRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("FinanceWithdraw error", ex);
        return super.onException(req, ex);
    }
}
