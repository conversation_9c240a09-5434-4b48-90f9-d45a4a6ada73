package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.exam.GuideTagExamDTO;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class ExamRedisLogic {
  private static final String EXAM_ACCESS_TOKEN_REDIS_KEY = "exam_interface_access_token";

  private static final String EXAM_DEPT_LIST_REDIS_KEY = "exam_dept_list";

  private static final String EXAM_USER_SCORE_RECORDS = "exam_user_score_records:%s_%s";

  private static final String GUIDE_PASSED_EXAM_INFO_KEY = "guide_passed_exam_info:%s";

  private static final long GUIDE_PASSED_EXAM_INFO_EXPIRED = 24 * 60 * 60;


  @Autowired
  DirectorRedis directorRedis;

  /**
   * 保存access_token
   */
  public void saveAccessToken(String accessToken, long seconds) {
    directorRedis.set(EXAM_ACCESS_TOKEN_REDIS_KEY, accessToken, seconds);
  }

  /**
   * 查询access_token
   */
  public String getAccessToken() {
    return directorRedis.get(EXAM_ACCESS_TOKEN_REDIS_KEY, String.class);
  }


  /**
   * 保存考试部门列表
   */
  public void saveExamDeptList(String examDeptList) {
    directorRedis.set(EXAM_DEPT_LIST_REDIS_KEY, examDeptList, 4000);
  }

  /**
   * 保存向导考试记录信息
   */
  public void saveGuidePassedExamInfo(long guideId,String passedExamInfo){
    directorRedis.set(String.format(GUIDE_PASSED_EXAM_INFO_KEY,guideId),passedExamInfo, RandomUtils.nextLong(0L,1000L) + GUIDE_PASSED_EXAM_INFO_EXPIRED);
  }

  /**
   * 查询向导考试记录信息
   */
  public GuideTagExamDTO getGuideTagExam(long guideId) {
    String rightStr = directorRedis.get(String.format(GUIDE_PASSED_EXAM_INFO_KEY,guideId));
    if (Strings.isBlank(rightStr)) {
      return null;
    }
    return JacksonUtil.deserialize(rightStr, new TypeToken<GuideTagExamDTO>(){}.getType());
  }

  /**
   * 删除向导考试记录信息
   */
  public void deleteGuidePassedExamInfo(long guideId){
    directorRedis.remove(String.format(GUIDE_PASSED_EXAM_INFO_KEY,guideId));
  }


}
