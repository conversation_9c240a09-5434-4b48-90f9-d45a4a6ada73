package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceDriverObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceObjectImpl;
import com.ctrip.dcs.driver.domain.finance.BankCardInfoDTO;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBankCardListRequestType;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBankCardListResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.BankCardModel;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 查询司机银行卡列表
 */
@Component
public class QueryDriverBankCardListEngine extends ShoppingExecutor<QueryDriverBankCardListRequestType, QueryDriverBankCardListResponseType>
        implements Validator<QueryDriverBankCardListRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<QueryDriverBankCardListRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverBankCardListEngine owner;
        private final QueryDriverBankCardListResponseType response;
        private final QueryDriverBankCardListRequestType request;
        private FinanceObject financeObject;
        private FinanceDriverObject financeDriverObject;

        private Executor(QueryDriverBankCardListRequestType request, QueryDriverBankCardListResponseType response, QueryDriverBankCardListEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.responseResult.returnCode = FinanceResultEnum.OK.getCode();
            this.request = request;
            this.invokeService();
        }

        private void invokeService() {
            financeDriverObject = new FinanceDriverObjectImpl(this.request.driverId, this.request.source, this.owner.domainBeanFactory);
            financeObject = new FinanceObjectImpl(this.request.driverId,
                    false, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            if (FinanceResultEnum.isValidError(this.financeDriverObject.validResult())) {
                response.responseResult.returnCode = this.financeDriverObject.validResult();
                return false;
            }
            return Objects.nonNull(financeObject);
        }

        @Override
        protected void buildResponse() {
            response.bankCardList = new ArrayList<>();
            List<BankCardModel> bankCardModels = financeObject.bankCardList();
            if(CollectionUtils.isNotEmpty(bankCardModels)){
                bankCardModels.forEach(c -> {
                    BankCardInfoDTO bankCardInfo = new BankCardInfoDTO();
                    bankCardInfo.bankId = c.getBankId();
                    bankCardInfo.bankCard = c.getBankCard();
                    bankCardInfo.bankName = c.getBankName();
                    response.bankCardList.add(bankCardInfo);
                });
            }
        }
    }

    @Override
    public QueryDriverBankCardListResponseType execute(QueryDriverBankCardListRequestType request) {
        QueryDriverBankCardListResponseType response = new QueryDriverBankCardListResponseType();
        Executor executor = new Executor(request, response, this);
        if(executor.validate()){
            executor.buildResponse();
        }
        if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
            response.responseResult.returnMessage = this.domainBeanFactory.financeConvertService().getFinanceResult(response.responseResult.returnCode);
            return ServiceResponseUtils.fail(response, response.responseResult.returnCode, response.responseResult.returnMessage);
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverBankCardListResponseType onException(QueryDriverBankCardListRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverBankCardList error", ex);
        return super.onException(req, ex);
    }
}
