package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.DialRecordTask;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskRequestType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskResponseType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitTaskStatus;
import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig;
import com.ctrip.dcs.driver.account.domain.constant.PhoneCheckTaskState;
import com.ctrip.dcs.driver.account.domain.value.PhoneCheckVoiceCallTask;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.soa.VoiceCallServiceProxy;
import com.ctrip.dcs.driver.trip.university.infrastructure.utils.CoreInfoUtil;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.dianping.cat.Cat;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

@Component
public class PhoneCheckTaskProcessScheduler {

	Log log = Log.getInstance(PhoneCheckTaskProcessScheduler.class);

	@Resource
	PhoneCheckTaskTable phoneCheckTaskTable;
	@Resource
	VoiceCallServiceProxy voiceCallServiceProxy;
	@Resource
	PhoneCheckConfig phoneCheckConfig;

	@QSchedule("dcs.driver.account.phone.check.task.process")
	public void apply(Parameter parameter) {
		phoneCheckTaskTable.queryMany("task_type = 3 and task_state = 0 and plan_time < ? limit 100", LocalDateTime.now()).stream().forEach(this::apply);
	}

	protected void apply(PhoneCheckTaskEntity task) {
		switch (task.getTaskType()) {
			case 3:
				call(task);
				break;
			default:
				Cat.logEvent("PhoneCheckTaskProcessScheduler","NotSupportTaskType");
		}
	}

	// 向IVR平台提交拨号任务
	public void call(PhoneCheckTaskEntity task) {
		PhoneCheckVoiceCallTask content = JsonUtil.fromString(task.getTaskContent(), PhoneCheckVoiceCallTask.class);
		SubmitOutboundTaskRequestType request = new SubmitOutboundTaskRequestType();
		request.setReqUuid(UUID.randomUUID().toString());
		request.setBatchID(String.valueOf(task.getId()));
		request.setMaxRetryCount(phoneCheckConfig.getMaxRetryCount());
		request.setRetryInterval(phoneCheckConfig.getRetryIntervalSeconds());
		request.setMaxAlertTime(phoneCheckConfig.getMaxAlertSeconds());
		request.setMaxConnectTime(phoneCheckConfig.getMaxDurationSeconds());
		DialRecordTask call = new DialRecordTask();
		call.setRecordGUID(UUID.randomUUID().toString());
		call.setDnis("00" + task.getPhonePrefix() + CoreInfoUtil.decrypt(task.getPhoneNumber(), KeyType.Phone));
		call.setSkillGroup(phoneCheckConfig.getSkillGroupId());
		String flow = null, text = null;
		for (String language : Optional.ofNullable(content.getDriverLanguages()).orElse(Collections.emptyList())) {
			flow = phoneCheckConfig.getSkillFlowId(language);
			text = phoneCheckConfig.getSpeechTemplate(language);
			if (StringUtils.isNotBlank(flow) && StringUtils.isNotBlank(text)) {
				break;
			}
		}
		// 如果表里没有配置该语言 则终止该任务
		if (StringUtils.isBlank(flow) || StringUtils.isBlank(text)) {
			task.setExecuteTime(LocalDateTime.now());
			task.setFinishTime(LocalDateTime.now());
			task.setTaskState(PhoneCheckTaskState.Failed);
			task.setTaskResult("Not Support Language");
			phoneCheckTaskTable.update(task);
			return;
		}
		call.setContent(JsonUtil.toString(ImmutableMap.of("IvrFlow", flow, "TTS1", generateSpeechText(text, task.getPhonePrefix(), task.getPhoneNumber()))));
		request.setTaskList(Lists.newArrayList(call));
		SubmitOutboundTaskResponseType response = voiceCallServiceProxy.submitOutboundTask(request);
		String code = Optional.ofNullable(response).map(SubmitOutboundTaskResponseType::getTaskListStatus).orElse(Collections.emptyList())
				.stream()
				.findFirst()
				.map(SubmitTaskStatus::getResultCode)
				.orElse(null);
		task.setTaskResult(JsonUtil.toString(response));
		task.setExecuteTime(LocalDateTime.now());
		if (StringUtils.equalsIgnoreCase(code, "0") ) {
			task.setTaskState(PhoneCheckTaskState.Started);
		} else {
			task.setFinishTime(LocalDateTime.now());
			task.setTaskState(PhoneCheckTaskState.Failed);
		}
		phoneCheckTaskTable.update(task);
	}

	// 生产TTS文本
	protected String generateSpeechText(String template, String phonePrefix, String phoneNumber) {
		String out = template;
		String realPhoneNumber = CoreInfoUtil.decrypt(phoneNumber, KeyType.Phone);
		String joinPhoneNumber = StringUtils.join(StringUtils.split(realPhoneNumber, ""), ",");
		String joinPhonePrefix = StringUtils.join(StringUtils.split(phonePrefix, ""), ",");
		out = StringUtils.replace(template, "{phoneNumber}", joinPhoneNumber);
		out = StringUtils.replace(out, "{phonePrefix}", joinPhonePrefix);
		return out;
	}

}