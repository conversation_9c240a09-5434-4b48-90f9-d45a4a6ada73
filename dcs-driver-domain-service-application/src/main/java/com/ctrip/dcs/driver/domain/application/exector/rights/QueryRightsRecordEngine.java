package com.ctrip.dcs.driver.domain.application.exector.rights;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.rigths.DriverRightsObjectImpl;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordRequestType;
import com.ctrip.dcs.driver.domain.rights.QueryRightsRecordResponseType;
import com.ctrip.dcs.driver.domain.rights.RightsRecordDTO;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.driver.value.rights.RightsRecordObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryRightsRecordEngine extends ShoppingExecutor<QueryRightsRecordRequestType, QueryRightsRecordResponseType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public QueryRightsRecordResponseType execute(QueryRightsRecordRequestType request) {
        QueryRightsRecordResponseType response = new QueryRightsRecordResponseType();
        QueryRightsRecordEngine.Executor executor = new QueryRightsRecordEngine.Executor(request, response, this);
        if (!executor.validate()) {
            return ServiceResponseUtils.fail(response, "510", "missing parameter");
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryRightsRecordEngine owner;
        private final QueryRightsRecordResponseType response;
        private final QueryRightsRecordRequestType request;
        private DriverRightsObject driverRightsObject;

        private Executor(QueryRightsRecordRequestType request, QueryRightsRecordResponseType response, QueryRightsRecordEngine owner) {
            super(request);
            this.owner = owner;
            this.response = response;
            this.request = request;

            driverRightsObject = new DriverRightsObjectImpl(this.owner.domainBeanFactory, Optional.ofNullable(request.driverId).orElse(0L), Strings.EMPTY);
        }

        @Override
        protected void buildResponse() {
            List<RightsRecordObject> rightsRecordObjects = driverRightsObject.queryDriverRightsRecord(request.useStartDate, request.useEndDate, request.rightsTypes, request.id);
            if (CollectionUtils.isEmpty(rightsRecordObjects)) {
                return;
            }
            response.records = rightsRecordObjects.stream().map(recordObject -> {
                RightsRecordDTO record = new RightsRecordDTO();
                record.setDriverId(recordObject.drivId());
                record.setRightsType(recordObject.rightsType().getCode());
                record.setRightsName(recordObject.rightsName());
                record.setUseLevel(recordObject.useLevel().getCode());
                record.setLevelName(recordObject.levelName());
                record.setUseDate(LocalDateTimeUtils.format(recordObject.rightsUsedTime()));
                record.setUserOrderId(recordObject.userOrderId());
                record.setPurchaseOrderId(recordObject.purchaseOrderId());
                record.setSupplyOrderId(recordObject.supplyOrderId());
                record.setPunishOrderId(recordObject.punishOrderId());
                record.setMoney(recordObject.money());

                return record;
            }).collect(Collectors.toList());
        }

        @Override
        protected boolean validate() {
            if (Objects.isNull(request.id)) {
                return Objects.nonNull(request.driverId) && CollectionUtils.isNotEmpty(request.rightsTypes) && Strings.isNotBlank(request.useEndDate) && Strings.isNotBlank(request.useStartDate);
            }
            return request.id > 0L;
        }
    }
}
