package com.ctrip.dcs.driver.trip.university.application.service.impl;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountIdentityDTO;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.transport.TmsTransportServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao;
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants;
import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityDriverIdentityEnum;
import com.ctrip.dcs.driver.trip.university.infrastructure.gateway.TripUniversityGateway;
import com.ctrip.dcs.driver.trip.university.infrastructure.utils.JsonUtil;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityFailedLogDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityAccessTokenRequestDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverForDisCardDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityUserBatchResultStatusEnum;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityUserData;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityUserDataResponse;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXLDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXLResultDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXLUserDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXingLiUserResult;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhiXingLiUserResultResponse;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.ZhixlUserResponseDTO;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvCodeByDrvIdSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.QueryDrvCodeByDrvIdSOAResponseType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;
import com.ctrip.platform.dal.dao.helper.JsonUtils;
import com.ctriposs.baiji.exception.BaijiRuntimeException;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants.SYNC_TO_TRIP_UNIVERSITY;

/**
 * <AUTHOR>
 */
@Service
public class TripUniversityServiceImpl implements TripUniversityService {

  public static final Logger logger = LoggerFactory.getLogger(TripUniversityServiceImpl.class);

  @Autowired
  TripUniversityGateway tripUniversityGateway;

  @Autowired
  DirectorRedis directorRedis;

  @Autowired
  DomainBeanFactory domainBeanFactory;

  @Autowired
  @Qualifier("TripUniversityThreadPool")
  ExecutorService executorService;

  @Autowired
  AccountService accountService;

  @Autowired
  DriverOrderPushConfigDao driverOrderPushConfigDao;

  @Autowired
  TmsTransportServiceProxy tmsTransportServiceProxy;

  private  static final String DRVKEY = "drv_key_";//司机key,为了司机端查询code

  private  static final String CODEKEY = "code_key_";//code key,携挰大学第三方(智行力返查drvId是否有效)

  public static final int TEN_MINIUTES = 10 * 60;
  public static final int TIME_OUT = -999;
  public static final int CALL_EXCEPTION = -998;

  private static final Long NOT_FOUND_DRIVER_ID = -1L;

  public static final String CAT_SUCCESS = "0";
  public static final String CAT_FAILED = "-1";

  @Override
  public boolean syncDriverInfoToTripUniversity(TripUniversityDriverDTO driverDTO) {
    try {
      if (!grayControl(driverDTO)) {
        logAndReportEvent("driver-sync-to-trip-university-not-in-gray", driverDTO);
        return false;
      }
      AccountInfoDTO accountInfoDTO = queryAccountBaseInfo(driverDTO.getDrvId(), driverDTO.getUid());
      if (accountInfoDTO == null) {
        logAndReportEvent("driver-not-found", driverDTO);
        return false;
      }

      if (CollectionUtils.isEmpty(accountInfoDTO.getIdentityDTOList())) {
        logAndReportEvent("driver-identity-not-found", driverDTO);
        return false;
      }

      if (driverDTO.getRetryCount() >= getMaxRetryCount()) {
        if(driverDTO.getRetryCount() == getMaxRetryCount()) {
          logAndReportEvent("failed-retry-arrived-max-retry-count", driverDTO);
        }else {
          logAndReportEvent("failed-retry-over-max-retry-count", driverDTO);
        }
        return false;
      }

      populateDriverDTO(driverDTO, accountInfoDTO);

      if (Objects.equals(driverDTO.getDrvId(), NOT_FOUND_DRIVER_ID) || Objects.isNull(driverDTO.getIdentityEnum())) {
        logAndReportEvent("driver-identity-not-found", driverDTO);
        return false;
      }

      Transaction t = Cat.newTransaction(SYNC_TO_TRIP_UNIVERSITY, SYNC_TO_TRIP_UNIVERSITY);
      try {
        ZhixlUserResponseDTO responseDTO = setBatchZhiXingLiUser(convert2ZhixlDTO(driverDTO));
        ZhiXingLiUserResultResponse zhiXingLiUserResult = getZhiXingLiUserResultWithRetry2(responseDTO);

        if (!zhiXingLiUserResult.isSuccess()) {
          t.setStatus(CAT_FAILED);
          saveSyncFailedLog(driverDTO, zhiXingLiUserResult.getCode(), "get-response-error:" + Optional.ofNullable(zhiXingLiUserResult.getMessage()).orElse(String.valueOf(zhiXingLiUserResult.getCode())));
        } else if (CollectionUtils.isNotEmpty(zhiXingLiUserResult.getData().getError_msg())) {
          t.setStatus(CAT_FAILED);
          saveSyncFailedLog(driverDTO, responseDTO.getCode(), "get-response-error:" + zhiXingLiUserResult.getData().getError_msg());
        } else {
          t.setStatus(CAT_SUCCESS);
          tripUniversityGateway.deleteFailedLog(driverDTO.getUid());
        }
        return true;
      }finally{
        t.complete();
      }
    } catch (Exception e) {
      logger.error(SYNC_TO_TRIP_UNIVERSITY, e);
      saveSyncFailedLog(driverDTO, 0, e.getMessage());
    }
    return false;
  }

  protected boolean grayControl(TripUniversityDriverDTO driverDTO) {
    List<Long> tripUniversityGrayDrvIdList = domainBeanFactory.tripUniversityQconfig().getTripUniversityGrayDrvIdList();
    if (tripUniversityGrayDrvIdList.contains(-1L) || (driverDTO.getDrvId() != null && tripUniversityGrayDrvIdList.contains(driverDTO.getDrvId()))) {
        return true;
    }
    return false;
  }

  protected void logAndReportEvent(String event, TripUniversityDriverDTO driverDTO) {
    logger.info(SYNC_TO_TRIP_UNIVERSITY, "{}, driverId:{}, uid:{}", event, driverDTO.getDrvId(), driverDTO.getUid());
    Cat.logEvent(SYNC_TO_TRIP_UNIVERSITY, event);
  }

  protected int getMaxRetryCount() {
    return domainBeanFactory.tripUniversityQconfig().getFailedMaxRetryCount();
  }

  protected void populateDriverDTO(TripUniversityDriverDTO driverDTO, AccountInfoDTO accountInfoDTO) {
    driverDTO.setName(accountInfoDTO.getName());
    driverDTO.setIdCardNo(accountInfoDTO.getIdCardNo());
    driverDTO.setCountryCode(accountInfoDTO.getCountryCode());
    driverDTO.setPhoneNumber(accountInfoDTO.getPhoneNumber());
    driverDTO.setEmail(accountInfoDTO.getEmail());
    driverDTO.setUid(accountInfoDTO.getUid());
    driverDTO.setDrvId(getDriverId(accountInfoDTO));
    driverDTO.setOversea(accountInfoDTO.getIsOversea());
    driverDTO.setIdentityEnum(getIdentityEnum(accountInfoDTO));
  }

  protected TripUniversityDriverIdentityEnum getIdentityEnum(AccountInfoDTO accountInfoDTO) {
    boolean driver = false;
    boolean driverGuide = false;
    for (AccountIdentityDTO identityDTO : Optional.ofNullable(accountInfoDTO.getIdentityDTOList()).orElse(Lists.newArrayList())) {
      if (AccountSouceEnum.isDriver(identityDTO.getSource())) {
        driver = true;
      }
      if (AccountSouceEnum.isDriverGuide(identityDTO.getSource())) {
        driverGuide = true;
      }
    }

    if (driver && driverGuide) {
      return TripUniversityDriverIdentityEnum.DRIVER_AND_DRIVER_GUIDE;
    }

    if (driver) {
      return TripUniversityDriverIdentityEnum.DRIVER;
    }

    if (driverGuide) {
      return TripUniversityDriverIdentityEnum.DRIVER_GUIDE;
    }
    return null;
  }

  protected ZhiXingLiUserResultResponse getZhiXingLiUserResultWithRetry2(ZhixlUserResponseDTO responseDTO) {
    logger.info("getZhiXingLiUserResultWithRetry2:{}", responseDTO.getRespon_id());
    int callDurationConfig = domainBeanFactory.tripUniversityQconfig().getBatchUserResultSleepMilliseconds();
    AtomicReference<ZhiXingLiUserResultResponse> zhiXingLiUserResult = new AtomicReference<>();

    //构建执行逻辑
    Callable<Boolean> callable = () -> {
      zhiXingLiUserResult.set(getZhiXingLiUserResult(responseDTO));

      //如果失败则退出
      if(!zhiXingLiUserResult.get().isSuccess()) {
        return true;
      }

      //如果结束则退出
      if (zhiXingLiUserResult.get().isSuccess() && TripUniversityUserBatchResultStatusEnum.COMPLETED.getCode().equals(
        zhiXingLiUserResult.get().getData().getCurrent_status())) {
        return true;
      }
      //retry
      return false;
    };

    // 构建 重试机制
    Retryer<Boolean> retryer = RetryerBuilder.<Boolean>newBuilder()
      .retryIfResult(b -> Objects.equals(b, false)) // 如果结果为 false 则重试
      .retryIfException()
      .withWaitStrategy(WaitStrategies.fixedWait(callDurationConfig, TimeUnit.MILLISECONDS))
      .withStopStrategy(StopStrategies.stopAfterDelay(domainBeanFactory.tripUniversityQconfig().getBatchUserResultWaitMilliseconds(), TimeUnit.MILLISECONDS))
      .build();

    try {
      // 执行带有重试机制的操作
      retryer.call(callable);
      if (zhiXingLiUserResult.get().isSuccess() && !TripUniversityUserBatchResultStatusEnum.COMPLETED.getCode().equals(
        zhiXingLiUserResult.get().getData().getCurrent_status())) {
        return ZhiXingLiUserResultResponse.builder().success(false).message(responseDTO.getRespon_id()).code(TIME_OUT).build();
      }
      return zhiXingLiUserResult.get();
    } catch (Exception e) {
      logger.warn("getZhiXingLiUserResultWithRetry2_error", e);
      return ZhiXingLiUserResultResponse.builder().success(false).message(responseDTO.getRespon_id()).code(CALL_EXCEPTION).message(e.getMessage()).build();
    }
  }

  protected void saveSyncFailedLog(TripUniversityDriverDTO driverDTO, int code, String message) {
    tripUniversityGateway.saveSyncFailedLog(convert(driverDTO, code, message));
  }

  protected ZhiXLDTO convert2ZhixlDTO(TripUniversityDriverDTO driverDTO) {
    return ZhiXLDTO.build(TripUniversityConstants.userFields, ZhiXLUserDTO.buildDTO(driverDTO, getDepartId(driverDTO.getIdentityEnum())));
  }

  protected String getDepartId(TripUniversityDriverIdentityEnum identityEnum) {
    return domainBeanFactory.tripUniversityQconfig().getDepartIdMap(identityEnum.getName());
  }

  protected AccountInfoDTO queryAccountBaseInfo(Long driverId, String uid) {
    return Objects.nonNull(uid) ? accountService.getAccountInfoByUID(uid) : accountService.getAccountByDriverId(driverId);
  }

  protected DriverSyncDrvInfoToTripUniversityFailedLogDTO convert(TripUniversityDriverDTO driverDTO, int code, String message) {
      DriverSyncDrvInfoToTripUniversityFailedLogDTO failedLogDTO = new DriverSyncDrvInfoToTripUniversityFailedLogDTO();
      failedLogDTO.setId(driverDTO.getFailedLogId());
      failedLogDTO.setUid(driverDTO.getUid());
      failedLogDTO.setDrvId(driverDTO.getDrvId());
      failedLogDTO.setRetryCount(driverDTO.getRetryCount() + 1);
      failedLogDTO.setCreateUser("system");
      failedLogDTO.setModifyUser("system");
      failedLogDTO.setFailedReasonCode(code);
      failedLogDTO.setFailedReasonDetail(message);
    return failedLogDTO;
  }

  @Override
  public TripUniversityAccessTokenDTO getAccessToken(TripUniversityAccessTokenRequestDTO requestDTO) {
    String accessToken = queryDrvCodeByDrvId(getDrvId(requestDTO.getUid(), requestDTO.getDrvId()));
    Cat.logEvent("trip-university", StringUtils.isEmpty(accessToken) ? CAT_FAILED : CAT_SUCCESS);
    return new TripUniversityAccessTokenDTO(accessToken, domainBeanFactory.tripUniversityQconfig().gettTenantId());
  }

  protected Long getDrvId(String uid, Long drvId) {
    if (drvId != null) {
      return drvId;
    }

    AccountInfoDTO accountInfoDTO = accountService.getAccountInfoByUID(uid);
    if (accountInfoDTO == null || CollectionUtils.isEmpty(accountInfoDTO.getIdentityDTOList())) {
      return -1L;
    }

    return getDriverId(accountInfoDTO);
  }

  protected Long getDriverId(AccountInfoDTO accountInfoDTO) {
    return Optional.ofNullable(accountInfoDTO.getIdentityDTOList()).orElse(Lists.newArrayList()).stream().filter(
        accountIdentityDTO -> AccountSouceEnum.isDriver(accountIdentityDTO.getSource()) || AccountSouceEnum.isDriverGuide(
          accountIdentityDTO.getSource())).map(accountIdentityDTO -> Long.valueOf(accountIdentityDTO.getSourceId()))
      .findFirst().orElse(NOT_FOUND_DRIVER_ID);
  }

  @Override
  public void updateTripUniversityAccountAsync(TripUniversityDriverDTO requestDTO) {
    // 异步处理
    Map<String, Object> properties = Maps.newHashMap();
    properties.put("uid", requestDTO.getUid());
    properties.put("drvId", requestDTO.getDrvId());
    QmqProducerProvider.sendMessage(TripUniversityConstants.SUBJECT_PUSH_DATA_TRIP_UNIVERSITY, properties);
  }

  public String queryDrvCodeByDrvId(Long drvId) {
    String drvKey = DRVKEY + drvId;//司机key,为了司机端查询code
    String drvCode =  directorRedis.get(drvKey);
    if(StringUtils.isEmpty(drvCode)){
      if(domainBeanFactory.tripUniversityQconfig().isCreateDrvCodeSwitch()) {
        String randomCode = RandomStringUtils.randomAlphabetic(20);
        String codeKey = CODEKEY + randomCode;//code key,携挰大学第三方(智行力返查drvId是否有效)
        drvCode = randomCode;
        directorRedis.set(drvKey,randomCode, TEN_MINIUTES);
        directorRedis.set(codeKey, TripUniversityConstants.CTRIP_UNIVERSITY_USERID + drvId, TEN_MINIUTES);
      }else {
        QueryDrvCodeByDrvIdSOARequestType requestType = new QueryDrvCodeByDrvIdSOARequestType();
        requestType.setDrvId(drvId);
        QueryDrvCodeByDrvIdSOAResponseType responseType =
          tmsTransportServiceProxy.queryDrvCodeByDrvId(requestType);
        drvCode = responseType.data;
        directorRedis.set(drvKey, drvCode, TEN_MINIUTES);
        directorRedis.set(CODEKEY + drvCode, TripUniversityConstants.CTRIP_UNIVERSITY_USERID + drvId, TEN_MINIUTES);
      }

    }
    return drvCode;
  }

  @Override
  public Result<String> queryDrvIdByCode(String drvCode) {
    String codeKey = CODEKEY + drvCode;
    String redisVal = directorRedis.get(codeKey);
    if (StringUtils.isEmpty(redisVal)) {
      return Result.Builder.<String>newResult().success().withData("").build();
    }
    return Result.Builder.<String>newResult().success().withData(redisVal).build();
  }

  @Override
  public Result<String> updateDrvPhoneAndEmailForDuplicateProcess(TripUniversityDriverForDisCardDTO dto) {
    TripUniversityUserDataResponse userData = getZhiXingLiUser(dto.getDrvId());
    if (userData.isSuccess() && CollectionUtils.isNotEmpty(userData.getData())) {
      TripUniversityUserData tripUniversityUserData = userData.getData().get(0);
      dto.setEmail(StringUtils.isNotEmpty(tripUniversityUserData.getEmail()) ? tripUniversityUserData.getEmail() : null);
      dto.setPhoneNumber(StringUtils.isNotEmpty(tripUniversityUserData.getMobile_phone()) ? tripUniversityUserData.getMobile_phone() : null);
      dto.setName(StringUtils.isNotEmpty(tripUniversityUserData.getUser_name()) ? tripUniversityUserData.getUser_name() : null);
      setBatchZhiXingLiUser(ZhiXLDTO.build(TripUniversityConstants.simpleUserFields, ZhiXLUserDTO.buildDTOForDiscard(dto)));
    }
    return Result.Builder.<String>newResult().success().build();
  }

  public String getCtripUniveToken() {
    try {
      String resultStr = domainBeanFactory.httpClient().get(domainBeanFactory.tripUniversityQconfig().getZhixlTokenUrl() + getParams(domainBeanFactory.tripUniversityQconfig().getZhixlAccount()));
      if(org.apache.commons.lang3.StringUtils.isEmpty(resultStr)){
        return "";
      }
      ZhiXLResultDTO resultDto =  JsonUtil.fromJson(resultStr, new TypeReference<ZhiXLResultDTO>() {});
      if(resultDto == null || !Objects.equals(resultDto.getCode(), 200)|| !resultDto.getSuccess() || resultDto.getData() == null){
        logger.warn("getCtripUniveTokenFail","result:{}",JacksonUtil.serialize(resultDto));
        return "";
      }
      return resultDto.getData().getAccess_token();
    } catch (Exception e) {
      throw new BaijiRuntimeException(e);
    }
  }

  public static String getParams(Map<String,String> paramsMap) {
    if (MapUtils.isEmpty(paramsMap)) {
      return "";
    }
    StringBuffer params = new StringBuffer();
    for (String key : paramsMap.keySet()) {
      if (params.length() > 1) {
        params.append("&");
      }
      params.append(key).append("=").append(paramsMap.get(key).trim());
    }
    return params.toString();
  }

  public ZhixlUserResponseDTO setBatchZhiXingLiUser(ZhiXLDTO zhiXLDTO) {
    Map<String, Object> paramsMap = Maps.newHashMap();
    paramsMap.put("fields", zhiXLDTO.getFields());
    paramsMap.put("data", Lists.newArrayList(zhiXLDTO.getData()));
    logger.info(SYNC_TO_TRIP_UNIVERSITY," start body params:{}", JacksonUtil.serialize(paramsMap));
    String url = domainBeanFactory.tripUniversityQconfig().getZhixlApiBatchCreateUserInterfaceUrl()+"?access_token="+this.getCtripUniveToken() + "&exchangeType=user";
    logger.info(SYNC_TO_TRIP_UNIVERSITY," url :{}", url);

    try {
      String result = domainBeanFactory.httpClient().post(url,JacksonUtil.serialize(paramsMap));
      logger.info(SYNC_TO_TRIP_UNIVERSITY," post result:{}",result);
      return JsonUtil.fromJson(result, new TypeReference<ZhixlUserResponseDTO>(){});
    } catch (Exception e) {
      Cat.logEvent("setBatchZhiXingLiUser_Exception",e.getMessage());
      return ZhixlUserResponseDTO.builder().success(false).msg(e.getMessage()).build();
    }
  }

  public TripUniversityUserDataResponse getZhiXingLiUser(String uid) {
    logger.info("get-trip-university-user","start params:{}", uid);
    try {
      String url = domainBeanFactory.tripUniversityQconfig().getZhixlGetUserUrl()+"?access_token="+this.getCtripUniveToken();
      logger.info("get-trip-university-user"," url :{}", url);
      String result = domainBeanFactory.httpClient().post(url, JsonUtils.toJson(Collections.singletonMap("user_id", uid)));
      logger.info("get-trip-university-user","get result:{}",result);
      return JsonUtil.fromJson(result, new TypeReference<TripUniversityUserDataResponse>(){});
    } catch (Exception e) {
      Cat.logEvent("getZhiXingLiUser_Exception",e.getMessage());
      return TripUniversityUserDataResponse.builder().success(false).msg(e.getMessage()).build();
    }
  }


  public ZhiXingLiUserResultResponse getZhiXingLiUserResult(ZhixlUserResponseDTO dto) {
    logger.info(SYNC_TO_TRIP_UNIVERSITY,"get response params:{}", dto.getRespon_id());
    Transaction t = Cat.newTransaction(SYNC_TO_TRIP_UNIVERSITY, "get-sync-result");
    try {
      // 如果创建失败，则直接返回
      if (!dto.isSuccess()) {
        ZhiXingLiUserResult zhiXingLiUserResult = new ZhiXingLiUserResult();
        zhiXingLiUserResult.setCurrent_status(TripUniversityUserBatchResultStatusEnum.COMPLETED.getCode());
        zhiXingLiUserResult.setError_msg(Lists.newArrayList(Collections.singletonMap(dto.getRespon_id(), dto.getMsg())));
        return ZhiXingLiUserResultResponse.builder().success(false).code(dto.getCode()).data(zhiXingLiUserResult).build();
      }

      String reqUrl = domainBeanFactory.tripUniversityQconfig().getZhixlBatchUserResultApiInterfaceUrl()+"?access_token=" + this.getCtripUniveToken() + "&respon_id=" + dto.getRespon_id();
      // 获取创建结果
      String result = domainBeanFactory.httpClient().get(reqUrl);
      logger.info(SYNC_TO_TRIP_UNIVERSITY,"get response result:{}",result);
      return JsonUtil.fromJson(result, new TypeReference<ZhiXingLiUserResultResponse>() {
        });
    } catch (Exception e) {
      Cat.logEvent("pushCtripUniversity_User_Exception",e.getMessage());
      return ZhiXingLiUserResultResponse.builder().success(false).build();
    }finally {
      t.complete();
    }
  }
}
