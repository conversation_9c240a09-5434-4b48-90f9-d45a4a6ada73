package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.GuideInfoDBDataService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply.GuideInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply.GuideInfoModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class GuideInfoDBDataServiceImpl implements GuideInfoDBDataService {

  @Autowired
  private GuideInfoDao guideInfoDao;

  @Override
  public GuideInfoModel queryMaxGuideId(){
    List<GuideInfoPO> guideInfoPOS = guideInfoDao.queryMaxGuideIdInfo();
    if(CollectionUtils.isEmpty(guideInfoPOS)){
      return null;
    }
    return convert2GuideInfoModel(guideInfoPOS.get(0));
  }

  @Override
  public List<GuideInfoModel> queryGuideInfoListByGuideId(Long startGuideId,Long endGuideId){
    List<GuideInfoPO> guideInfoPOS =
        guideInfoDao.queryGuideInfoListByGuideId(startGuideId, endGuideId);
    if(CollectionUtils.isEmpty(guideInfoPOS)){
      return Collections.emptyList();
    }
    return guideInfoPOS.stream().map(po -> convert2GuideInfoModel(po)).collect(Collectors.toList());
  }



  private GuideInfoModel convert2GuideInfoModel(GuideInfoPO record) {
    GuideInfoModel model = new GuideInfoModel();
    model.setGuideId(record.getGuideId());
    return model;
  }
}
