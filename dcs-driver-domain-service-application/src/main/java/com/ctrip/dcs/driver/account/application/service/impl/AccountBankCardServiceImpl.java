package com.ctrip.dcs.driver.account.application.service.impl;
/*
作者：pl.yang
创建时间：2025/4/28-上午11:32-2025
*/


import cn.hutool.core.text.CharSequenceUtil;
import com.ctrip.dcs.driver.account.application.service.AccountBankCardService;
import com.ctrip.dcs.driver.account.domain.condition.BindOverseaBankCardCondition;
import com.ctrip.dcs.driver.account.domain.condition.DriverBankCardBindResultCondition;
import com.ctrip.dcs.driver.account.domain.condition.DriverWithDrawCondition;
import com.ctrip.dcs.driver.account.domain.condition.UnbindBankCardCondition;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.service.DomesticBankCardService;
import com.ctrip.dcs.driver.account.domain.service.OverseaBankCardService;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardAreaTypeEnum;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.common.CommonMultipleLanguages;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.gateway.AccountBankCardRecordRepository;
import com.ctrip.dcs.driver.gateway.DriverAppPushMessageRepository;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.driver.value.PushMessageDo;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountBankCardServiceImpl
 * @Package com.ctrip.dcs.driver.account.application.service.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 上午11:32
 */

@Component
public class AccountBankCardServiceImpl implements AccountBankCardService {
    public static final Logger logger = LoggerFactory.getLogger(AccountBankCardServiceImpl.class);
    @Autowired
    LockService lockService;
    @Autowired
    OverseaBankCardService overseaBankCardService;
    @Autowired
    DriverAccountConfig driverAccountConfig;
    @Autowired
    AccountBankCardRecordRepository accountBankCardRecordRepository;
    @Autowired
    DomesticBankCardService domesticBankCardService;
    @Autowired
    AccountService accountService;
    @Autowired
    DriverAppPushMessageRepository driverAppPushMessageRepository;
    @Autowired
    PushConfigRedisLogic pushConfigRedisLogic;
    @Autowired
    CommonMultipleLanguages commonMultipleLanguages;
    @Autowired
    DriverWalletInfoConfig driverWalletInfoConfig;



    @Override
    public BaseResult<Void> bindOverseaBankCard(BindOverseaBankCardCondition bankCardCondition) {

        //需要境内，境外 分布式锁 一致把锁呢
        return lockService.executeInLock(String.format(Constants.BANKCARD_OVERSEA_LOCK_KEY_PATTERN, bankCardCondition.uid),
                driverAccountConfig.getAccountLockWaitMills(), () -> overseaBankCardService.doBindOverseaBankCard(bankCardCondition));
    }

    //解绑银行卡
    @Override
    public BaseResult<Void> unbindBankCard(UnbindBankCardCondition condition) {
        //查询绑卡记录
        AccountBankCardDo accountBankCardRecord = accountBankCardRecordRepository.queryAccountBankCard(condition.getUid(), condition.getBankCardId());
        if (Objects.isNull(accountBankCardRecord)) {
            return BaseResult.failResultCat("204", "account barkCard not exist", CatEventType.Unbind_Domestic_BankCard, "account barkCard not exist");
        }
        if (accountBankCardRecord.getBankCardAreaTypeEnumCode()==null||accountBankCardRecord.getBankCardAreaTypeEnumCode()== BankCardAreaTypeEnum.domesticdriver_domesticcard.getCode()) {
            //境内司机境内卡
            logger.info("unbind domestic bank card", CharSequenceUtil.format("cardId:{} ,uid:{}", condition.getBankCardId(), condition.getUid()));
            Cat.logEvent(CatEventType.Unbind_Domestic_BankCard, "unbind domestic bank card");
            return domesticBankCardService.doUnbindBankCard(condition, accountBankCardRecord);
        } else {
            //境外司机，境内卡，境外卡
            logger.info("unbind oversea bank card", CharSequenceUtil.format("cardId:{} ,uid:{}", condition.getBankCardId(), condition.getUid()));
            Cat.logEvent(CatEventType.Unbind_Oversea_BankCard, "unbind oversea bank card");
            return overseaBankCardService.doOverseaUnbindBankCard(condition, accountBankCardRecord);
        }

    }

    @Override
    public BaseResult<AccountBankCardDo> queryAccountBankCard(Long driverId,String bankCardNO) {
        //1、反查uid
        AccountInfoDTO accountInfo = accountService.getAccountByDriverId(driverId);
        if (Objects.isNull(accountInfo) || StringUtils.isBlank(accountInfo.getUid())) {
            return BaseResult.failResult("204", "account not exist");
        }
        if (StringUtils.isNotBlank(bankCardNO)) {
            //2、查询数据库对比请求信息
            return BaseResult.successResult(accountBankCardRecordRepository.queryAccountBankCard(accountInfo.getUid(), bankCardNO));
        } else {
            return accountBankCardRecordRepository.queryAccountBindingLastBankCardV2(accountInfo.getUid());
        }

    }

    //http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
    @Override
    public void driverBankCardBindResultHandle(DriverBankCardBindResultCondition condition) {
        if (condition==null) {
            return;
        }

        BaseResult<AccountBankCardDo> accountBankCardDoBaseResult = accountBankCardRecordRepository.queryByRequestId(condition.getRequestId());
        if (Objects.isNull(accountBankCardDoBaseResult) || accountBankCardDoBaseResult.getData() == null || accountBankCardDoBaseResult.isFail()) {
            Cat.logEvent(CatEventType.Unbind_Oversea_BankCard, "notQuery");
            logger.warn("query account bank card by requestId fail");
            return;
        }
        AccountBankCardDo data = accountBankCardDoBaseResult.getData();
        AccountInfoDTO accountInfoByUID = accountService.getAccountInfoByUID(data.getUid());
        if ("SUCCESS".equals(condition.getStatus())) {
            //设置绑定成功
            data.getCardStatus().bindBankSuccess();
            accountBankCardRecordRepository.updateBankCardRecordStatus(data);
            //通知到端上
            Cat.logEvent(CatEventType.Unbind_Oversea_BankCard, "bindBankSuccess");
            sendBankCardPushMessage(true, accountInfoByUID, data, condition.getReasonCode());
        } else {
            //绑卡失败
            data.getCardStatus().bindBankFailed();
            accountBankCardRecordRepository.updateBankCardRecordStatus(data);
            Cat.logEvent(CatEventType.Unbind_Oversea_BankCard, "bindBankFailed-" + condition.getReasonCode());
            sendBankCardPushMessage(false, accountInfoByUID, data, condition.getReasonCode());
        }

    }

    @Override
    public void driverWithDrawNotify(DriverWithDrawCondition condition) {

        PushMessageDo pushMessageDTO = buildDriverWithDrawNotifyPushMessage(condition);

        driverAppPushMessageRepository.sendPushMessage(pushMessageDTO);
    }

    private PushMessageDo buildDriverWithDrawNotifyPushMessage(DriverWithDrawCondition condition) {

        PushMessageDo pushMessageDTO = new PushMessageDo();
        pushMessageDTO.setDriverIds(Lists.newArrayList(condition.getDriverId()));
        pushMessageDTO.setTemplateId(driverAccountConfig.getDriverWithDrawNotifyFailTemplateId());
        Map<String, String> sharkValues = Maps.newHashMap();
        sharkValues.put("reason", createFailedReason(condition.getReasonCode(), condition.getDriverId()));
        //todo 没有做国际化
        sharkValues.put("ymd", condition.getDatetime());
        sharkValues.put("money", condition.getMoney());
        pushMessageDTO.setSharkValues(sharkValues);
        pushMessageDTO.setData(null);
        return pushMessageDTO;


    }

    private void sendBankCardPushMessage(boolean isSuccess, AccountInfoDTO accountInfoByUID, AccountBankCardDo data, String reasonCode) {
        if (accountInfoByUID == null) {
            logger.warn("sendBankCardSuccessPushMessage fail", "accountInfoByUID is null," + data.getUid());
            return;
        }
        //发送模板消息
        Long driverId = accountInfoByUID.getDriverOrDriverGuideDriverId();
        if (driverId == null) {
            logger.warn("sendBankCardSuccessPushMessage fail", "driverId is null," + data.getUid());
            return;
        }
        PushMessageDo pushMessageDTO = isSuccess ? buildSuccessPushMessage(driverId) : buildFailedPushMessage(reasonCode, driverId);
        driverAppPushMessageRepository.sendPushMessage(pushMessageDTO);
    }

    public @NotNull PushMessageDo buildSuccessPushMessage(Long driverId) {
        PushMessageDo pushMessageDTO = new PushMessageDo();
        pushMessageDTO.setDriverIds(Lists.newArrayList(driverId));
        //1056
        pushMessageDTO.setTemplateId(driverAccountConfig.getBandOversCardSuccessTemplateId());
        Map<String, String> sharkValues = Maps.newHashMap();
        //App推送标题 driver.oversea.bind.bankcard.success.title  银行卡绑定成功
        //App推送内容 driver.oversea.bind.bankcard.success.content 银行卡已成功绑定，后续将用于提现操作
        //App推送 udl  ${test}
        pushMessageDTO.setSharkValues(sharkValues);
        pushMessageDTO.setData(null);
        return pushMessageDTO;
    }

    private @NotNull PushMessageDo buildFailedPushMessage(String reasonCode, Long driverId) {
        PushMessageDo pushMessageDTO = new PushMessageDo();
        pushMessageDTO.setDriverIds(Lists.newArrayList(driverId));
        //1057
        pushMessageDTO.setTemplateId(driverAccountConfig.getBandOversCardFailTemplateId());
        Map<String, String> sharkValues = Maps.newHashMap();
        //App推送标题 driver.oversea.bind.bankcard.failed.title  银行卡绑定成功
        //App推送内容 driver.oversea.bind.bankcard.failed.content 银行卡已成功绑定，后续将用于提现操作
        //App推送 udl
        sharkValues.put("reason", createFailedReason(reasonCode, driverId));
        pushMessageDTO.setSharkValues(sharkValues);
        pushMessageDTO.setData(null);
        return pushMessageDTO;
    }

    private String createFailedReason(String reasonCode, Long driverId) {
        String sharkKey =getSharkKeyByReasonCode(reasonCode);
        if(StringUtils.isEmpty(sharkKey)){
            logger.warn("createFailedReason fail", "sharkKey is null," + reasonCode + "," + driverId);
        }
        return getContent(driverId, sharkKey);
    }

    private String getSharkKeyByReasonCode(String reasonCode) {
        return driverWalletInfoConfig.getOverseaBindCardFailReasonSharkKey(reasonCode);
    }

    private String getContent(Long driverId, String sharkLKey) {
        String locale = pushConfigRedisLogic.getDriverLanguage(driverId);
        return commonMultipleLanguages.getContent(sharkLKey, locale);
    }


}
