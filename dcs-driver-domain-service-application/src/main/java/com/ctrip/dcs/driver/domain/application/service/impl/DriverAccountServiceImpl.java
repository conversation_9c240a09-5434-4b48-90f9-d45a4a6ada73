package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByDriverIdResponseType;
import com.ctrip.dcs.driver.domain.application.service.DriverAccountService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.account.DriverDomainServiceProxy;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DriverAccountServiceImpl implements DriverAccountService {
    private static final Logger logger = LoggerFactory.getLogger(DriverAccountServiceImpl.class);
    @Autowired
    private DriverDomainServiceProxy driverDomainServiceProxy;

    @Override
    public String getUid(Long driverId) {
        try {
            QueryAccountByDriverIdRequestType requestType = new QueryAccountByDriverIdRequestType();
            requestType.setDriverId(driverId);
            QueryAccountByDriverIdResponseType responseType = driverDomainServiceProxy.queryAccountByDriverId(requestType);
            if (responseType != null && responseType.getAccountDetail() != null) {
                String uid = responseType.getAccountDetail().getUid();
                Cat.logEvent(CatEventType.QUERY_UID_BY_DRIVER_ID_RESULT, StringUtils.isNotBlank(uid) ? "notNull" : "null");
                return uid;
            }
        } catch (Exception e) {
            logger.error("driverDomainServiceProxy.queryAccountByDriverId", e);
        }
        return Strings.EMPTY;
    }
}
