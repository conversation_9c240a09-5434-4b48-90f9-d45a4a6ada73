package com.ctrip.dcs.driver.domain.application.service.faceauth.megvii.utils;

import cn.hutool.crypto.asymmetric.AsymmetricAlgorithm;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import org.apache.commons.lang3.ArrayUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;
import java.util.Base64;
import java.util.Random;

public class EncryptionUtils {

    /**
     * 生成签名字段
     */
    public static String genSign(String apiKey,  String secretKey, long expiredSeconds) throws Exception {
        long now = System.currentTimeMillis() / 1000;
        int rdm = Math.abs(new Random().nextInt());
        String plainText = String.format("a=%s&b=%d&c=%d&d=%d", api<PERSON>ey, now + expiredSeconds, now, rdm);

        byte[] hmacDigest = HmacSha1(plainText, secretKey);
        byte[] signContent = new byte[hmacDigest.length + plainText.getBytes().length];

        System.arraycopy(hmacDigest, 0, signContent, 0, hmacDigest.length);
        System.arraycopy(plainText.getBytes(), 0, signContent, hmacDigest.length, plainText.getBytes().length);

        return Base64.getEncoder().encodeToString(signContent).replaceAll("[\\s*\t\n\r]", "");
    }

    public static byte[] HmacSha1(byte[] binaryData, String key) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), "HmacSHA1");
        mac.init(secretKey);
        return mac.doFinal(binaryData);
    }

    public static byte[] HmacSha1(String plainText, String key) throws Exception {
        return HmacSha1(plainText.getBytes(), key);
    }

//    // 解密图片
//    public static void decryptimage(String image, RSA rsapri, File resFile){
//        byte[] input = Base64.getDecoder().decode(image);
//        ByteBuffer byteBuffer = ByteBuffer.wrap(input, input.length - 4, 4);
//        byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
//        int len = byteBuffer.getInt();
//        try {
////            byte[] decrypt = sm2Engine.processBlock(input, 0, len);
//            byte[] c2LengthByte = new byte[len];
//            System.arraycopy(input, 0, c2LengthByte, 0, len);
//            byte[] decrypt = rsapri.decrypt(c2LengthByte, KeyType.PrivateKey);
//            FileOutputStream fos = new FileOutputStream(resFile);
//            fos.write(decrypt);
//            fos.write(input, len, input.length - len - 4);
//            fos.close();
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//
//    }
//    public static String decrypt(String raw, String pubKeyStr) {
//        pubKeyStr = normalizePubKeyStr(pubKeyStr);
//        RSA rsa = new RSA(AsymmetricAlgorithm.RSA_ECB_PKCS1.getValue(), null, pubKeyStr);
//
//        byte[] b64 = Base64.getDecoder().decode(StrUtil.utf8Bytes(raw));
//        //先生成C4，c4是用来校验前面的字符串有无被篡改的，按理说后面的对称+非对称已经可以保证没有被篡改过了，为了性能考虑，注释掉这个
//        //byte[] c4 = new byte[16];
//        //System.arraycopy(b64, b64.length - 16, c4, 0, 16);
//
//        byte[] c2LengthByte = new byte[4];
//        System.arraycopy(b64, b64.length - 20, c2LengthByte, 0, 4);
//
//        int c2Length = ByteUtil.bytesToInt(c2LengthByte);
//        byte[] c2 = new byte[c2Length];
//        System.arraycopy(b64, b64.length - 20 - c2Length, c2, 0, c2Length);
//
//        byte[] c1 = new byte[b64.length - 20 - c2Length];
//        System.arraycopy(b64, 0, c1, 0, c1.length);
//
//        byte[] aesKey = rsa.decrypt(c2, KeyType.PrivateKey);
//        byte[] iv = new byte[16];
//        return decryptAES(c1, aesKey, iv);
//    }
//
//
//    public static String decryptAES(byte[] c1, byte[] aesKey, byte[] iv) {
//        try {
//            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
//            SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
//            IvParameterSpec ivSpec = new IvParameterSpec(iv);
//
//            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
//
//            byte[] original = cipher.doFinal(c1);
//
//            return StrUtil.utf8Str(original).trim();
//        } catch (Exception e) {
//            throw new RuntimeException(e);
//        }
//    }

    public static byte[] encodeFile(byte[] fileBuf, String pubKeyStr) throws Exception {
        pubKeyStr = normalizePubKeyStr(pubKeyStr);
        RSA rsa = new RSA(AsymmetricAlgorithm.RSA_ECB_PKCS1.getValue(), null, pubKeyStr);

        int fileLen = fileBuf.length;
        int encryptLen = Math.min(fileLen, 1024);

        byte[] buff = Arrays.copyOf(fileBuf, encryptLen);
        byte[] plain = Arrays.copyOfRange(fileBuf, encryptLen, fileLen);

        byte[] encrypt = rsa.encrypt(buff, KeyType.PublicKey);

        ByteBuffer byteBuffer = ByteBuffer.allocate(4);
        byteBuffer.order(ByteOrder.LITTLE_ENDIAN);
        byteBuffer.putInt(encrypt.length);

        return ArrayUtils.addAll(ArrayUtils.addAll(encrypt, plain), byteBuffer.array());
    }

    private static String normalizePubKeyStr(String pubKeyStr) {
        return pubKeyStr.replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");
    }

}
