package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.redis.PointInfoRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.NoticeService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverPointRestfulShowInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;

@Component
public class MessageConsumer {

  @Autowired
  DriverPointRestfulShowInfoDao driverPointRestfulShowInfoDao;

  @Autowired
  PointInfoRedisLogic pointInfoRedisLogic;

  @Autowired
  NoticeService noticeService;

  @Autowired
  LockService lockService;

  public static final Logger LOGGER = LoggerFactory.getLogger(MessageConsumer.class);

  public static final String CONSUMER_GROUP = "100038374";

  private static final String DRIVER_MESSAGE_MANUAL_SEND = "dcs.driver.message.manual.send";

  /**
   * 司机端弹窗消息
   * */
  @QmqConsumer(prefix = "dcs.driver.terminal.point.pop.callback", consumerGroup = CONSUMER_GROUP)
  public void onPointPopCallback(Message message) {
    try {
      long driverId = message.getLongProperty("driverId");
      int popType = message.getIntProperty("popType");
      if(driverId <= 0 || popType <= 0){
        return;
      }

      boolean isNew = false;
      DriverPointRestfulShowInfoPO driverPointRestfulShowInfoPO = driverPointRestfulShowInfoDao.findOne(driverId);
      if(Objects.isNull(driverPointRestfulShowInfoPO)){
        driverPointRestfulShowInfoPO = new DriverPointRestfulShowInfoPO();
        driverPointRestfulShowInfoPO.setDriverId(driverId);
        isNew = true;
      }
      switch (popType) {
        case 1:
          driverPointRestfulShowInfoPO.setNovicePointGuidePop(1);
          driverPointRestfulShowInfoPO.setNovicePointGuidePopTime(Timestamp.valueOf(LocalDateTime.now()));
          break;
        case 2:
          driverPointRestfulShowInfoPO.setNovicePointVoidancePop(1);
          driverPointRestfulShowInfoPO.setNovicePointVoidancePopTime(Timestamp.valueOf(LocalDateTime.now()));
          break;
        default:
          break;
      }
      if(popType == 1 || popType == 2){
        if (isNew) {
          driverPointRestfulShowInfoDao.insert(driverPointRestfulShowInfoPO);
        } else {
          driverPointRestfulShowInfoDao.update(driverPointRestfulShowInfoPO);
        }
      }
      pointInfoRedisLogic.clearDriverPointInfo(driverId);
      LOGGER.info("MessageConsumer", String.format("[dcs.driver.terminal.point.pop.callback][driverId:%s][popType:%s]", driverId, popType));
    }catch (Exception e){
      LOGGER.error("MessageConsumer", e);
    }
  }

  @QmqConsumer(prefix = DRIVER_MESSAGE_MANUAL_SEND, consumerGroup = CONSUMER_GROUP, idempotentChecker = "redisIdempotent")
  public void onManualPushNotice(Message message) {
    try {
      int messageType = Integer.parseInt(message.getStringProperty("messageType"));
      if(messageType == 2){
        //公告更新通知
        lockService.executeInLock("dcs.driver.manual.notice.update", 1000, () -> noticeService.noticeUpdate(Long.parseLong(message.getStringProperty("data"))));
      }
    }catch (Exception e) {
      LOGGER.error(e);
    }
  }
}
