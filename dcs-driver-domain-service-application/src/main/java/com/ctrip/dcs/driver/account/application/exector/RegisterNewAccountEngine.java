package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.common.AccountExceptionCode;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLogType;
import com.ctrip.dcs.driver.account.infrastructure.value.RegisterNewAccountParam;
import com.ctrip.dcs.driver.domain.account.RegisterNewAccountRequestType;
import com.ctrip.dcs.driver.domain.account.RegisterNewAccountResponseType;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ServiceLogTag(tagKeys= "sourceId")
public class RegisterNewAccountEngine extends ShoppingExecutor<RegisterNewAccountRequestType, RegisterNewAccountResponseType> implements Validator<RegisterNewAccountRequestType> {

    @Autowired
    private AccountService accountService;
    @Autowired
    private AccountChangeLogHelper accountChangeLogHelper;

    @Override
    public void validate(AbstractValidator<RegisterNewAccountRequestType> validator) {
        validator.ruleFor("source").notNull().notEmpty();
        validator.ruleFor("sourceId").notNull().notEmpty();
        validator.ruleFor("countryCode").notNull().notEmpty();
        validator.ruleFor("phoneNumber").notNull().notEmpty();
        validator.ruleFor("isOversea").notNull().notEmpty();
        validator.ruleFor("isValid").notNull().notEmpty();
    }

    @Override
    public RegisterNewAccountResponseType execute(RegisterNewAccountRequestType request) {

        RegisterNewAccountResponseType response = new RegisterNewAccountResponseType();
        if (request == null || StringUtils.isAnyBlank(request.getSource(), request.getSourceId(), request.getCountryCode(), request.getPhoneNumber()) || request.isIsOversea() == null || request.isIsValid() == null) {
            return ServiceResponseUtils.fail(response, AccountExceptionCode.PARAM_INVALID.getCode(), "invalid param");
        }
        if (BooleanUtils.isFalse(request.isIsOversea()) && StringUtils.isBlank(request.getIdCardNo())) {
            return ServiceResponseUtils.fail(response, AccountExceptionCode.PARAM_INVALID.getCode(), "id card no is null");
        }

        RegisterNewAccountParam param = new RegisterNewAccountParam();
        param.setSource(request.getSource());
        param.setSourceId(request.getSourceId());
        param.setCountryCode(request.getCountryCode());
        param.setPhoneNumber(request.getPhoneNumber());
        param.setEmail(request.getEmail());
        param.setName(request.getName());
        param.setIdCardNo(request.getIdCardNo());
        param.setPayoneerAccountId(request.getPayoneerAccountId());
        param.setIsOversea(request.isIsOversea());
        param.setValid(request.isIsValid());
        param.setModifyUser(request.getModifyUser());
        param.setCityId(request.getCityId());

        AccountBaseInfoPO accountBaseInfoPO = accountService.register(param);

        response.setUid(accountBaseInfoPO.getUid());
        response.setPpmAccountId(accountBaseInfoPO.getPpmAccountId());
        response.setUdl(accountBaseInfoPO.getProviderDataLocation());
        accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.REGISTER_NEW_ACCOUNT, request.getSource(), accountBaseInfoPO.getUid(), "", JsonUtil.toString(accountBaseInfoPO)));
        return ServiceResponseUtils.success(response);
    }


}
