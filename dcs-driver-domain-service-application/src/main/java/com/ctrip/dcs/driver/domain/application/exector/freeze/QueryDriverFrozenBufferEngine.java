package com.ctrip.dcs.driver.domain.application.exector.freeze;

import com.ctrip.dcs.driver.domain.infrastructure.adapter.db.DriverFreezeAdapter;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.QueryDriverFrozenBufferRequestType;
import com.ctrip.model.QueryDriverFrozenBufferResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryDriverFrozenBufferEngine extends ShoppingExecutor<QueryDriverFrozenBufferRequestType, QueryDriverFrozenBufferResponseType> {
    @Autowired
    private DriverFreezeAdapter driverFreezeAdapter;

    @Override
    public QueryDriverFrozenBufferResponseType execute(QueryDriverFrozenBufferRequestType requestType) {
        String bufferEndTime = driverFreezeAdapter.queryDriverFrozenBufferEndTime(requestType.getDriverId());
        QueryDriverFrozenBufferResponseType responseType = new QueryDriverFrozenBufferResponseType();
        responseType.setEndTime(bufferEndTime);
        return ServiceResponseUtils.success(responseType);
    }
}
