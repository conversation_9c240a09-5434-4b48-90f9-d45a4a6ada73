package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.honour.DirectorObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.honour.DriverMedalObjectImpl;
import com.ctrip.dcs.driver.domain.honour.QueryDirectorHonourSettingRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryDirectorHonourSettingResponseType;
import com.ctrip.dcs.driver.value.honour.DirectorObject;
import com.ctrip.dcs.driver.value.honour.DriverMedalObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 查询司机荣誉配置
 */
@Component
public class QueryDirectorHonourSettingEngine extends ShoppingExecutor<QueryDirectorHonourSettingRequestType, QueryDirectorHonourSettingResponseType>
        implements Validator<QueryDirectorHonourSettingRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<QueryDirectorHonourSettingRequestType> validator) {
        validator.ruleFor("directorId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("directorType").notNull().notEmpty().greaterThan(0);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDirectorHonourSettingEngine owner;
        private final QueryDirectorHonourSettingResponseType response;
        private final QueryDirectorHonourSettingRequestType request;
        private DirectorObject directorObject;

        private Executor(QueryDirectorHonourSettingRequestType request, QueryDirectorHonourSettingResponseType response, QueryDirectorHonourSettingEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            // init response
            response.isShowMedal = false;
            response.medalCount = 0;
            response.isShowRank = false;
            response.isNoticeRank = false;
            response.totalMedalCount = 0;
            directorObject = new DirectorObjectImpl
                    .Builder(this.request.directorId, this.request.directorType, this.owner.domainBeanFactory).build();
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(directorObject);
        }

        @Override
        protected void buildResponse() {
            // 勋章
            if(this.owner.domainBeanFactory.honourFormConfig().isShowMedal()) {
                if(this.directorObject.isSupportHonourMedal()){
                    response.isShowMedal = true;
                    DriverMedalObject driverMedalObject = new DriverMedalObjectImpl(this.owner.domainBeanFactory, this.request.directorId, 0);
                    response.medalCount = driverMedalObject.totalCount();
                    //勋章总数为BI勋章总数 + 荣誉墙勋章总数
                    response.totalMedalCount = Math.addExact(
                            this.owner.domainBeanFactory.honourRedisLogic().queryCurrentMedalCount(),
                            this.owner.domainBeanFactory.honourFormConfig().getHonourWallMedalTotalCount());
                }
            }

            // 排行榜
            List<Long> rankOpenCityList = this.owner.domainBeanFactory.honourFormConfig().getRankOpenCity();
            if(this.directorObject.isSupportHonourRank(rankOpenCityList)){
                response.isShowRank = true;
                response.isNoticeRank = this.owner.domainBeanFactory.honourRedisLogic().isNoticeRankUpdate(this.directorObject.id());
            }
        }
    }

    @Override
    public QueryDirectorHonourSettingResponseType execute(QueryDirectorHonourSettingRequestType request) {
        QueryDirectorHonourSettingResponseType response = new QueryDirectorHonourSettingResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDirectorHonourSettingResponseType onException(QueryDirectorHonourSettingRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDirectorHonourSetting error", ex);
        return super.onException(req, ex);
    }
}
