package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceDriverObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceObjectImpl;
import com.ctrip.dcs.driver.domain.finance.OldProcessFinanceRequestType;
import com.ctrip.dcs.driver.domain.finance.OldProcessFinanceResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 老司机查询余额&提现
 */
@Component
public class OldProcessFinanceEngine extends ShoppingExecutor<OldProcessFinanceRequestType, OldProcessFinanceResponseType>
        implements Validator<OldProcessFinanceRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<OldProcessFinanceRequestType> validator) {
        validator.ruleFor("type").notNull().notEmpty().greaterThan(0).lessThan(3);
        validator.ruleFor("driverPhone").notNull().notEmpty();
        validator.ruleFor("driverName").notNull().notEmpty();
        validator.ruleFor("identitycode").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final OldProcessFinanceEngine owner;
        private final OldProcessFinanceResponseType response;
        private final OldProcessFinanceRequestType request;
        private FinanceDriverObject financeDriverObject;
        private FinanceObject financeObject;

        private Executor(OldProcessFinanceRequestType request, OldProcessFinanceResponseType response, OldProcessFinanceEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.responseResult.returnCode = FinanceResultEnum.OK.getCode();
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            financeDriverObject = new FinanceDriverObjectImpl(this.request.driverPhone, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            // 司机查询无结果
            if(FinanceResultEnum.isValidError(this.financeDriverObject.validResult())) {
                response.responseResult.returnCode = this.financeDriverObject.validResult();
                return false;
            }
            response.driverId = this.financeDriverObject.driverId();
            // 司机姓名不匹配
            if(!this.request.driverName.equalsIgnoreCase(financeDriverObject.driverName())){
                response.responseResult.returnCode = FinanceResultEnum.NAME_CHECK_FAIL.getCode();
                return false;
            }
            // 司机身份证号不匹配
            if(StringUtils.isBlank(financeDriverObject.identityCardId())){
                response.responseResult.returnCode = FinanceResultEnum.IDENTITYCODE_FAIL.getCode();
                return false;
            }
            if(!financeDriverObject.identityCardId().equalsIgnoreCase(this.request.identitycode) &&
                    !financeDriverObject.identityCardId().equalsIgnoreCase(
                            this.owner.domainBeanFactory.archCoreInfoService().encryptIdCard(this.request.identitycode))){
                response.responseResult.returnCode = FinanceResultEnum.IDENTITYCODE_WRONG.getCode();
                return false;
            }

            // 司机已迁移
            if(this.financeDriverObject.isRegulation()){
                response.responseResult.returnCode = FinanceResultEnum.DRIVER_REGULATION.getCode();
                return false;
            }
            return true;
        }

        @Override
        protected void buildResponse() {
            if(this.request.type == 2) {
                // 初始化
                this.financeObject = new FinanceObjectImpl(this.financeDriverObject.driverId(),
                        true, this.owner.domainBeanFactory);

                // 强制重置密码
                String randomPassword = String.format("%s%s%s",
                        RandomStringUtils.random(2, true, false),
                        RandomStringUtils.random(2, false, true),
                        RandomStringUtils.random(2, true, false));

                response.responseResult.returnCode =
                        this.financeObject.resetPassword(randomPassword, this.financeDriverObject.identityCardId());
                if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
                    return;
                }

                // 提现
                response.responseResult.returnCode = financeObject.withdraw(
                                randomPassword,
                                this.request.withdrawAmount,
                                this.request.bankCardId,
                                StringUtils.right(this.request.bankCardId, 4),
                                this.request.driverName, Strings.EMPTY);

                if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
                    return;
                }
            } else {
                this.financeObject = new FinanceObjectImpl(this.financeDriverObject.driverId(),
                        true, this.owner.domainBeanFactory);
            }

            // 查询余额
            this.financeObject.queryBalance();
            if(this.request.type == 1 && FinanceResultEnum.isValidError(financeObject.balanceCode())){
                response.responseResult.returnCode = financeObject.balanceCode();
            }
            response.balanceAmount = financeObject.balanceAmount();
            response.maxWithdrawAmount = financeObject.maxWithDrawAmount();
            response.maxWithdrawCount = financeObject.withDrawLimitCount();
            response.todayWithdrawCount = financeObject.withDrawCount();
            response.canWithdraw = response.balanceAmount.compareTo(BigDecimal.ONE) >= 0 &&
                    Math.subtractExact(response.maxWithdrawCount, response.todayWithdrawCount) > 0;
        }
    }

    @Override
    public OldProcessFinanceResponseType execute(OldProcessFinanceRequestType request) {
        OldProcessFinanceResponseType response = new OldProcessFinanceResponseType();
        Executor executor = new Executor(request, response, this);
        if(executor.validate()){
            executor.buildResponse();
        }
        if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
            response.responseResult.returnMessage = this.domainBeanFactory.financeConvertService().getFinanceResult(response.responseResult.returnCode);
            response.withdrawTip = String.format("%s_%s",
                    response.responseResult.returnCode,
                    ObjectUtils.defaultIfNull(response.responseResult.returnMessage, Strings.EMPTY));
            return ServiceResponseUtils.fail(response, response.responseResult.returnCode, response.responseResult.returnMessage);
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public OldProcessFinanceResponseType onException(OldProcessFinanceRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("OldProcessFinance error", ex);
        return super.onException(req, ex);
    }
}
