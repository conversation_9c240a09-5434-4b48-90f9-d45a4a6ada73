package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.QueryAccountBankCardByDriverIdConvert;
import com.ctrip.dcs.driver.account.application.service.AccountBankCardService;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.domain.account.AccountBankCardDTO;
import com.ctrip.dcs.driver.domain.account.QueryAccountBankCardByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountBankCardByDriverIdResponseType;
import com.ctrip.dcs.driver.gateway.AccountBankCardRecordRepository;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QueryAccountBankCardByDriverIdEngine extends AbDriverDomainShoppingExecutor<QueryAccountBankCardByDriverIdRequestType, QueryAccountBankCardByDriverIdResponseType> implements Validator<QueryAccountBankCardByDriverIdRequestType> {

    @Autowired
    CardInfoManagementRepository cardInfoManagementService;

    @Autowired
    AccountService accountService;

    @Autowired
    AccountBankCardRecordRepository accountBankCardRecordRepository;
    @Autowired
    QueryAccountBankCardByDriverIdConvert queryAccountBankCardByDriverIdConvert;

    @Autowired
    AccountBankCardService accountBankCardService;

    @Override
    public QueryAccountBankCardByDriverIdResponseType execute(QueryAccountBankCardByDriverIdRequestType request) {
        QueryAccountBankCardByDriverIdResponseType resp = new QueryAccountBankCardByDriverIdResponseType();
        resp.accountBankCard = new AccountBankCardDTO();
        //注意 如果指给了司机id就查询当前司机最新有效的银行卡信息
        //如果司机id和银行卡id了，则查询银行卡id对应的银行卡信息，不看银行卡的状态
        BaseResult<AccountBankCardDo> accountBankCardDoBaseResult = accountBankCardService.queryAccountBankCard(request.driverId, request.bankCardId/*给的银行卡密文*/);
        if (accountBankCardDoBaseResult.isFail()) {
            return ServiceResponseUtils.fail(resp, accountBankCardDoBaseResult.getCode(), accountBankCardDoBaseResult.getMessage());
        }
        AccountBankCardDo data = accountBankCardDoBaseResult.getData();
        if(data==null){
            return ServiceResponseUtils.fail(resp,"500", "AccountBankCardDo not query");
        }

        if (StringUtils.isNotBlank(request.getBankCardId())&&!StringUtils.equals(data.getCardNo(), request.getBankCardId())) {
            return ServiceResponseUtils.fail(resp);
        }

        resp = queryAccountBankCardByDriverIdConvert.convertResponse(request, data);

        if (StringUtils.isBlank(resp.accountBankCard.bankCardNo)) {
            return ServiceResponseUtils.fail(new QueryAccountBankCardByDriverIdResponseType(), "500", "bankCardNo is null");
        }
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public void validate(AbstractValidator<QueryAccountBankCardByDriverIdRequestType> validator) {

        validator.ruleFor("driverId").notNull().greaterThanOrEqualTo(0L);
    }
}
