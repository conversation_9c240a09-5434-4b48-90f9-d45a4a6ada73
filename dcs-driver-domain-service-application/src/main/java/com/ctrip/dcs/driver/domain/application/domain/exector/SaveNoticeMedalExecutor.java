package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.common.CommonStringUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourMedalNoticeRecordPO;
import com.ctrip.dcs.driver.value.honour.DriverMedalObject;
import com.ctrip.dcs.driver.value.honour.MedalObject;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

public class SaveNoticeMedalExecutor extends DomainExecutor<DriverMedalObject, MedalObject[], String> {

  public SaveNoticeMedalExecutor(DriverMedalObject owner, DomainBeanFactory domainBeanFactory) {
    super(owner, domainBeanFactory);
  }

  @Override
  public String doWork(MedalObject[] medalObjects) {
    if(medalObjects.length > 0) {
      boolean isNew = false;
      DrivHonourMedalNoticeRecordPO medalNoticeRecord = this.domainBeanFactory.honourDBDataService().queryNoticedMedalInfo(this.owner.driverId());
      if(Objects.isNull(medalNoticeRecord)){
        medalNoticeRecord = new DrivHonourMedalNoticeRecordPO();
        medalNoticeRecord.setDriverId(this.owner.driverId());
        isNew = true;
      }

      List<String> newCommonMedalList = new ArrayList<>();
      List<String> newActionMedalList = new ArrayList<>();
      List<String> newFormMedalList = new ArrayList<>();
      List<String> newFormSpecialList = new ArrayList<>();
      Arrays.stream(medalObjects).forEach(m ->{
        if(Objects.nonNull(m)) {
          if (m.type().isCommonType()) {
            newCommonMedalList.add(m.code());
          } else if (m.type().isGoogAction()) {
            newActionMedalList.add(m.code());
          } else if (m.type().isHonourWall()) {
            if(CommonStringUtils.GOLD_MEDAL.equalsIgnoreCase(m.code())) {
              newFormSpecialList.add(String.format("%s^%s", m.code(), StringUtils.defaultIfBlank(m.useMonth(), StringUtils.EMPTY)));
            } else {
              newFormMedalList.add(m.code());
            }
          }
        }
      });

      medalNoticeRecord.setCommonMedalNotice(this.bulidSaveInfo(medalNoticeRecord.getCommonMedalNotice(), newCommonMedalList, false, null));
      medalNoticeRecord.setActiveMedalNotice(this.bulidSaveInfo(medalNoticeRecord.getActiveMedalNotice(), newActionMedalList, true, null));
      medalNoticeRecord.setFormMedalNotice(this.bulidSaveInfo(medalNoticeRecord.getFormMedalNotice(), newFormMedalList, false, newFormSpecialList));
      medalNoticeRecord.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
      int value = 0;
      if(isNew) {
        value = this.domainBeanFactory.honourDBDataService().insertNoticedMedalInfo(medalNoticeRecord);
      } else {
        value = this.domainBeanFactory.honourDBDataService().updateNoticedMedalInfo(medalNoticeRecord);
      }
      CommonLogger.INSTANCE.info(this.getClass().getSimpleName(),
              String.format("DrivHonourMedalNoticeRecord result : [%s] [%s]", this.owner.driverId(), value));
    }
    return StringUtils.EMPTY;
  }

  /**
   * 提示的勋章
   * */
  protected String bulidSaveInfo(String originalInfo, List<String> newMedalCodeList, boolean isAvtive, List<String> newFormSpecialList) {
    if(CollectionUtils.isEmpty(newMedalCodeList) && CollectionUtils.isEmpty(newFormSpecialList)){
      return originalInfo;
    }

    final List<String> saveMedalList = StringUtils.isNotBlank(originalInfo) ?
            ShoppingCollectionUtils.toList(originalInfo.split(",")) : new ArrayList<>();

    newMedalCodeList.forEach(m -> {
      if(!saveMedalList.contains(m)){
        String code = m;
        if(isAvtive) {
          code = String.format("%s^%s", code, LocalDate.now().getYear());
        }
        saveMedalList.add(code);
      }
    });

    if(CollectionUtils.isNotEmpty(newFormSpecialList)){
      newFormSpecialList.forEach(m -> {
        if(m.contains(CommonStringUtils.GOLD_MEDAL)){
          //删除历史提醒
          saveMedalList.removeIf(iteratorCode -> iteratorCode.contains(CommonStringUtils.GOLD_MEDAL) && !iteratorCode.equalsIgnoreCase(m));
        }
        saveMedalList.add(m);
      });
    }

    return String.join(",", saveMedalList);
  }
}
