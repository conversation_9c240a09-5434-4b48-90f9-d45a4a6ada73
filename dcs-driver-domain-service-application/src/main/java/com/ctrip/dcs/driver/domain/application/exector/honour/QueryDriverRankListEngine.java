package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.honour.DirectorObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.honour.RankObjectImpl;
import com.ctrip.dcs.driver.domain.honour.HonourRankInfoDTO;
import com.ctrip.dcs.driver.domain.honour.QueryDriverRankListRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryDriverRankListResponseType;
import com.ctrip.dcs.driver.value.honour.DirectorObject;
import com.ctrip.dcs.driver.value.honour.DriverRankObject;
import com.ctrip.dcs.driver.value.honour.RankObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 查询司机排行
 */
@Component
public class QueryDriverRankListEngine extends ShoppingExecutor<QueryDriverRankListRequestType, QueryDriverRankListResponseType>
        implements Validator<QueryDriverRankListRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<QueryDriverRankListRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("type").notNull().notEmpty().greaterThan(0);
        validator.ruleFor("pageNo").notNull().notEmpty().greaterThan(0);
        validator.ruleFor("pageSize").notNull().notEmpty().greaterThan(0);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverRankListEngine owner;
        private final QueryDriverRankListResponseType response;
        private final QueryDriverRankListRequestType request;
        private DirectorObject directorObject;
        private RankObject rankObject;

        private Executor(QueryDriverRankListRequestType request, QueryDriverRankListResponseType response, QueryDriverRankListEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            // init response
            response.type = request.type;
            response.refs = Strings.EMPTY;
            response.updateTime = Strings.EMPTY;
            response.pageNo = request.pageNo;
            response.totalPage = 0;
            response.pageSize = request.pageSize;
            response.driverRank = null;
            response.rankList = new ArrayList<>();

            //特殊操作，有请求，需要清除小红点记录
            this.owner.domainBeanFactory.honourRedisLogic().cleartNoticeRankUpdate(this.request.driverId);

            directorObject = new DirectorObjectImpl.Builder(this.request.driverId, 1,
                    this.owner.domainBeanFactory).build();
        }

        @Override
        protected boolean validate() {
            if(Objects.isNull(directorObject)){
                return false;
            }
            if(!this.owner.domainBeanFactory.honourFormConfig().isRankOpenCity(directorObject.cityId())){
                return false;
            }
            return true;
        }

        @Override
        protected void buildResponse() {
            rankObject = new RankObjectImpl(this.owner.domainBeanFactory,
                    this.directorObject.cityId(),
                    this.request.type,
                    this.request.driverId);

            if(StringUtils.isBlank(rankObject.rankRefs())){
                return;
            }

            response.refs = rankObject.rankRefs();
            response.updateTime = rankObject.batchDataTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            int totalCount = rankObject.totalCount();
            if(totalCount > 0) {
                DriverRankObject currentDriverRank = rankObject.queryCurrentDriverRankInfo();
                if (Objects.nonNull(currentDriverRank)) {
                    response.setDriverRank(this.convertDriverRank(currentDriverRank));
                }

                response.totalPage = BigDecimal.valueOf(totalCount)
                        .divide(BigDecimal.valueOf(this.request.pageSize),0, RoundingMode.UP)
                        .intValue();
                // 数据起始index
                DriverRankObject[] rankList = rankObject.queryRankList(
                        Math.min(totalCount, (request.pageNo - 1) * request.pageSize),
                        Math.min(totalCount, request.pageNo * request.pageSize));
                response.setRankList(this.converRankList(rankList));
            }
       }

       /**
        * 司机的排行榜信息
        * */
       private HonourRankInfoDTO convertDriverRank(DriverRankObject currentDriverRank) {
           HonourRankInfoDTO driverRank = new HonourRankInfoDTO();
           driverRank.driverId = this.request.driverId;
           driverRank.driverHeadImg = directorObject.driverHeadImg();
           driverRank.driverName = directorObject.name();

           driverRank.rank = currentDriverRank.rank();
           driverRank.orderCount = currentDriverRank.orderCount();
           driverRank.likeCount = currentDriverRank.likeCount();
           return driverRank;
       }

       /**
        * 司机的排行榜列表
        * */
       private List<HonourRankInfoDTO> converRankList(DriverRankObject[] rankList) {
           if(Objects.isNull(rankList) || rankList.length == 0) {
               return Collections.emptyList();
           }
           return ShoppingCollectionUtils.toList(rankList, r -> {
               HonourRankInfoDTO rank = new HonourRankInfoDTO();
               rank.driverId = r.driverId();
               rank.driverHeadImg = Strings.EMPTY;
               rank.driverName = r.maskName();
               rank.rank = r.rank();
               rank.orderCount = r.orderCount();
               rank.likeCount = r.likeCount();
               return rank;
           });
       }
    }

    @Override
    public QueryDriverRankListResponseType execute(QueryDriverRankListRequestType request) {
        QueryDriverRankListResponseType response = new QueryDriverRankListResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverRankListResponseType onException(QueryDriverRankListRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverRankList error", ex);
        return super.onException(req, ex);
    }
}
