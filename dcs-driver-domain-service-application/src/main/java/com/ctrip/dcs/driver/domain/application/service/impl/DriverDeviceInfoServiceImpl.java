package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.service.DriverDeviceInfoService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverDeviceInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverDeviceInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.device.DriverDeviceInfoModel;
import com.dianping.cat.Cat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class DriverDeviceInfoServiceImpl implements DriverDeviceInfoService {
    @Autowired
    private DriverDeviceInfoDao driverDeviceInfoDao;

    @Override
    public void save(DriverDeviceInfoModel model) {
        DriverDeviceInfoPO driverDeviceInfoPO = driverDeviceInfoDao.findOne(model.getDriverId());
        if (driverDeviceInfoPO == null) {
            driverDeviceInfoPO = new DriverDeviceInfoPO();
        }
        driverDeviceInfoPO.setDriverId(model.getDriverId());
        driverDeviceInfoPO.setUid(model.getUid());
        driverDeviceInfoPO.setCid(model.getCid());
        driverDeviceInfoPO.setAppId(model.getAppId());
        driverDeviceInfoPO.setAppVersion(model.getAppVer());
        driverDeviceInfoPO.setRnVersion(model.getRnVer());
        driverDeviceInfoPO.setLocal(model.getLocal());
        driverDeviceInfoPO.setOsType(model.getOs());
        driverDeviceInfoPO.setOsVersion(model.getOsVer());
        driverDeviceInfoPO.setLoginTime(model.getLoginTime());
        driverDeviceInfoPO.setLoginAccount(model.getLoginAccount());
        driverDeviceInfoPO.setLoginType(model.getLoginType());
        if (Objects.nonNull(model.getActiveTime())) {
            driverDeviceInfoPO.setActiveTime(model.getActiveTime());
        }
        if (Objects.isNull(driverDeviceInfoPO.getId())) {
            driverDeviceInfoDao.insert(driverDeviceInfoPO);
        } else {
            driverDeviceInfoDao.update(driverDeviceInfoPO);
        }
    }

    @Override
    public DriverDeviceInfoModel queryDeviceInfo(Long driverId) {
        DriverDeviceInfoPO deviceProfilePO = driverDeviceInfoDao.findOne(driverId);
        if (deviceProfilePO == null) {
            Cat.logEvent(CatEventType.DEVICE_INFO_QUERY, "empty");
            return null;
        }
        DriverDeviceInfoModel model = new DriverDeviceInfoModel();
        model.setUid(deviceProfilePO.getUid());
        model.setAppId(deviceProfilePO.getAppId());
        model.setCid(deviceProfilePO.getCid());
        model.setAppVer(deviceProfilePO.getAppVersion());
        model.setRnVer(deviceProfilePO.getRnVersion());
        model.setOs(deviceProfilePO.getOsType());
        model.setOsVer(deviceProfilePO.getOsVersion());
        model.setLocal(deviceProfilePO.getLocal());
        model.setLoginAccount(deviceProfilePO.getLoginAccount());
        model.setLoginType(deviceProfilePO.getLoginType());
        model.setLoginTime(deviceProfilePO.getLoginTime());
        model.setActiveTime(deviceProfilePO.getActiveTime());
        Cat.logEvent(CatEventType.DEVICE_INFO_QUERY, "success");
        return model;
    }

    @Override
    public DriverDeviceInfoModel queryDeviceInfo(String uid) {
        DriverDeviceInfoPO deviceProfilePO = driverDeviceInfoDao.findOne(uid);
        if (deviceProfilePO == null) {
            Cat.logEvent(CatEventType.DEVICE_INFO_QUERY, "empty");
            return null;
        }
        DriverDeviceInfoModel model = new DriverDeviceInfoModel();
        model.setUid(deviceProfilePO.getUid());
        model.setAppId(deviceProfilePO.getAppId());
        model.setCid(deviceProfilePO.getCid());
        model.setAppVer(deviceProfilePO.getAppVersion());
        model.setRnVer(deviceProfilePO.getRnVersion());
        model.setOs(deviceProfilePO.getOsType());
        model.setOsVer(deviceProfilePO.getOsVersion());
        model.setLocal(deviceProfilePO.getLocal());
        model.setLoginAccount(deviceProfilePO.getLoginAccount());
        model.setLoginType(deviceProfilePO.getLoginType());
        model.setLoginTime(deviceProfilePO.getLoginTime());
        model.setActiveTime(deviceProfilePO.getActiveTime());
        Cat.logEvent(CatEventType.DEVICE_INFO_QUERY, "success");
        return model;
    }
}
