package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum;
import com.ctrip.igt.framework.common.language.LanguageContext;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DriverLevelHelper {

    @Autowired
    CommonMultipleLanguages commonMultipleLanguages;

    /**
     * 根据等级获取司机等级名称（青铜、铜牌等），境外支持多语言
     */
    public String getDriverLevelName(Integer level, String sourceLevelName) {
        if (LevelEnum.isOverseaLevel(level)) {
            // 境外转换多语言
            LevelEnum levelEnum = LevelEnum.getByCode(level);
            if (levelEnum != null && StringUtils.isNotBlank(levelEnum.getLevelSharkKey()) && LanguageContext.getLanguage() != null) {
                return commonMultipleLanguages.getContent(levelEnum.getLevelSharkKey());
            }
        }
        return sourceLevelName;
    }
}
