package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.WelfareExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;


public class CheckRightsCanUseExecutor extends DomainExecutor<DriverRightsObject, RightsModel, Boolean> {
    private final int REASSIGN_WEEK_COUNT = 4;

    public CheckRightsCanUseExecutor(DriverRightsObject owner, DomainBeanFactory domainBeanFactory) {
        super(owner, domainBeanFactory);
    }

    @Override
    public Boolean doWork(RightsModel rightsModel) {
        //权益私有rule校验
        switch (RightsTypeEnum.getByCode(rightsModel.getRightsType())) {
            //福利金 不能超过福利金上限
            case RIGHTS_TYPE_WELFARE:
                return welfareCanUse(rightsModel.getExtend());
            //改派 每周使用次数不超过4次
            case RIGHTS_TYPE_REASSIGN:
                return reassignCanUse(rightsModel.getExtend());
            default:
                return true;
        }
    }

    private Boolean welfareCanUse(String extend) {
        WelfareExtendDto welfareExtendDto = JacksonUtil.deserialize(extend, WelfareExtendDto.class);
        if (Objects.isNull(welfareExtendDto)) {
            return false;
        }

        BigDecimal welfareTotal = Optional.ofNullable(welfareExtendDto.getWelfareUse()).orElse(BigDecimal.ZERO);
        BigDecimal welfareLimit = Optional.ofNullable(welfareExtendDto.getWelfareLimit()).orElse(BigDecimal.ZERO);
        BigDecimal welfareFreeze = Optional.ofNullable(welfareExtendDto.getWelfareFreeze()).orElse(BigDecimal.ZERO);
        return welfareTotal.add(welfareFreeze).compareTo(welfareLimit) < 0;
    }

    private Boolean reassignCanUse(String extend) {
        int reassignUseCount = CommonRightsExtendUtils.getReassignUseCount(extend);
        return reassignUseCount < REASSIGN_WEEK_COUNT;
    }

}
