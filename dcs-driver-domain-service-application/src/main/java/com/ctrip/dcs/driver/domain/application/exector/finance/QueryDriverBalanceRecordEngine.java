package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceDriverObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceObjectImpl;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBalanceRecordRequestType;
import com.ctrip.dcs.driver.domain.finance.QueryDriverBalanceRecordResponseType;
import com.ctrip.dcs.driver.domain.finance.WithdrawRecordDTO;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.BalanceRecordModel;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 提现记录
 */
@Component
public class QueryDriverBalanceRecordEngine extends ShoppingExecutor<QueryDriverBalanceRecordRequestType, QueryDriverBalanceRecordResponseType>
        implements Validator<QueryDriverBalanceRecordRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<QueryDriverBalanceRecordRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("beginDate").notNull().notEmpty();
        validator.ruleFor("endDate").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverBalanceRecordEngine owner;
        private final QueryDriverBalanceRecordResponseType response;
        private final QueryDriverBalanceRecordRequestType request;
        private FinanceObject financeObject;
        private FinanceDriverObject financeDriverObject;

        private Executor(QueryDriverBalanceRecordRequestType request, QueryDriverBalanceRecordResponseType response, QueryDriverBalanceRecordEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.responseResult.returnCode = FinanceResultEnum.OK.getCode();
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            financeDriverObject = new FinanceDriverObjectImpl(this.request.driverId, this.request.source, this.owner.domainBeanFactory);
            financeObject = new FinanceObjectImpl(this.request.driverId,
                    false, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            if (FinanceResultEnum.isValidError(this.financeDriverObject.validResult())) {
                response.responseResult.returnCode = this.financeDriverObject.validResult();
                return false;
            }
            return Objects.nonNull(financeObject);
        }

        @Override
        protected void buildResponse() {
            response.recordList = new ArrayList<>();
            List<BalanceRecordModel> recordDTOS = financeObject.balanceRecordList(this.request.beginDate, this.request.endDate);
            if(CollectionUtils.isNotEmpty(recordDTOS)){
                recordDTOS.forEach(r -> {
                    WithdrawRecordDTO record = new WithdrawRecordDTO();
                    record.recordId = r.getRecordId();
                    record.amount = r.getAmount();
                    record.bankString = r.getBankString();
                    record.recordTime = r.getRecordTime();
                    record.applyStatus = r.getApplyStatus();
                    record.applyStatusDesc = r.getApplyStatusDesc();
                    response.recordList.add(record);
                });
            }
        }
    }

    @Override
    public QueryDriverBalanceRecordResponseType execute(QueryDriverBalanceRecordRequestType request) {
        QueryDriverBalanceRecordResponseType response = new QueryDriverBalanceRecordResponseType();
        Executor executor = new Executor(request, response, this);
        if(executor.validate()){
            executor.buildResponse();
        }
        if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
            response.responseResult.returnMessage = this.domainBeanFactory.financeConvertService().getFinanceResult(response.responseResult.returnCode);
            return ServiceResponseUtils.fail(response, response.responseResult.returnCode, response.responseResult.returnMessage);
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverBalanceRecordResponseType onException(QueryDriverBalanceRecordRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverBalanceRecord error", ex);
        return super.onException(req, ex);
    }
}
