package com.ctrip.dcs.driver.domain.application.exector.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.honour.CommemorationObjectImpl;
import com.ctrip.dcs.driver.domain.honour.QueryDirectorCommemorationRequestType;
import com.ctrip.dcs.driver.domain.honour.QueryDirectorCommemorationResponseType;
import com.ctrip.dcs.driver.value.honour.CommemorationObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 查询纪念日
 */
@Component
public class QueryDirectorCommemorationEngine extends ShoppingExecutor<QueryDirectorCommemorationRequestType, QueryDirectorCommemorationResponseType>
        implements Validator<QueryDirectorCommemorationRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<QueryDirectorCommemorationRequestType> validator) {
        validator.ruleFor("directorId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("directorType").notNull().notEmpty().greaterThan(0);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDirectorCommemorationEngine owner;
        private final QueryDirectorCommemorationResponseType response;
        private final QueryDirectorCommemorationRequestType request;
        private CommemorationObject commemorationObject;

        private Executor(QueryDirectorCommemorationRequestType request, QueryDirectorCommemorationResponseType response, QueryDirectorCommemorationEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;

            initResponse();

            commemorationObject = new CommemorationObjectImpl
                    .Builder(this.request.directorId,
                    this.request.directorType,
                    this.owner.domainBeanFactory).build();

            if(!this.validate()){
                return;
            }
        }

        @Override
        protected void buildResponse() {
            if(Boolean.TRUE.equals(commemorationObject.isBirthday())) {
                this.response.type = 1;
            } else if(Boolean.TRUE.equals(commemorationObject.isActiveDay())) {
                //激活日判断 : 当生日当天=激活天数/周年，开机时展示生日；所以生日的时候就不进行激活日期的判断了
                this.response.type = 2;
                this.response.activeDayCount = commemorationObject.activeDayCount();
                this.response.activeDayType = commemorationObject.activeDayType();
            }
            this.response.directorName = commemorationObject.showName();
            this.response.image = commemorationObject.showImg();
        }

        private void initResponse() {
            this.response.type = 0;
            this.response.activeDayCount = 0;
            this.response.activeDayType = 0;
            this.response.directorName = Strings.EMPTY;
        }

        @Override
        protected boolean validate() {
            return Objects.nonNull(commemorationObject);
        }
    }

    @Override
    public QueryDirectorCommemorationResponseType execute(QueryDirectorCommemorationRequestType request) {
        QueryDirectorCommemorationResponseType response = new QueryDirectorCommemorationResponseType();
        Executor executor = new Executor(request, response, this);
        if(!executor.validate()){
            return ServiceResponseUtils.success(response);
        }
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDirectorCommemorationResponseType onException(QueryDirectorCommemorationRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDirectorCommemoration error", ex);
        return super.onException(req, ex);
    }
}
