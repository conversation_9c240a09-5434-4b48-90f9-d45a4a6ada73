package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.infrastructure.common.AccountExceptionCode;
import com.ctrip.dcs.driver.domain.account.SyncAccountRequestType;
import com.ctrip.dcs.driver.domain.account.SyncAccountResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.stereotype.Component;

@Component
public class SyncAccountEngine extends ShoppingExecutor<SyncAccountRequestType, SyncAccountResponseType> implements Val<PERSON>tor<SyncAccountRequestType> {

    @Override
    public SyncAccountResponseType execute(SyncAccountRequestType request) {
        SyncAccountResponseType response = new SyncAccountResponseType();
        return ServiceResponseUtils.fail(response, AccountExceptionCode.PARAM_INVALID.getCode(), "This api has been taken offline");
    }

}
