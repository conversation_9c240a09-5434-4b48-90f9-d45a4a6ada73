package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.service.DriverLevelService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivLevelDAlDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverLevelGreyConfig;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.LevelConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.platform.dal.dao.DalHints;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.time.LocalDateTime;

@Component
public class DrivRegisterListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DrivRegisterListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    @Autowired
    private DriverLevelGreyConfig driverLevelGreyConfig;

    @Autowired
    private LevelConfig levelConfig;

    @Autowired
    private DriverLevelService driverLevelService;

    @Autowired
    DrivLevelDAlDao drivLevelDAlDao;

    @Autowired
    private GeoGateway geoGateway;

    @Override
    @QmqConsumer(prefix = "dcs.tms.transport.driver.register.qmq", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        try {
            Long drvId = message.getLongProperty("drvId");
            Long cityId = message.getLongProperty("cityId");
            String proLineList = message.getStringProperty("proLineList");
            //不在灰度城市
            if (!proLineList.contains("1") || !driverLevelGreyConfig.isInGrey(cityId)) {
                return;
            }
            drivLevelDAlDao.insertDuplicateUpdate(DalHints.createIfAbsent(null), buildRecord(drvId, cityId));
        } catch (Exception e) {
            LOGGER.warn("failed to insertDuplicateUpdate dirv_level", e);
        }
    }

    private DrivLevelPO buildRecord(Long drvId, Long cityId) {
        FormLevelInfo defaultForm = driverLevelService.getDefaultForm(cityId, levelConfig.getCityDriverLevel(cityId));
        DrivLevelPO drivLevelPO = new DrivLevelPO();
        drivLevelPO.setLevelConfigId(defaultForm.getId());
        drivLevelPO.setLevelName(defaultForm.getLevelName());
        drivLevelPO.setDrivId(drvId);
        drivLevelPO.setCityId(cityId);
        drivLevelPO.setDrivLevel(defaultForm.getLevel());
        LocalDateTime localCurrentTime = geoGateway.getLocalCurrentTime(cityId);
        drivLevelPO.setMonthIdx(LocalDateTimeUtils.monthIndexStr(ObjectUtils.defaultIfNull(localCurrentTime, LocalDateTime.now())));
        return drivLevelPO;

    }
}
