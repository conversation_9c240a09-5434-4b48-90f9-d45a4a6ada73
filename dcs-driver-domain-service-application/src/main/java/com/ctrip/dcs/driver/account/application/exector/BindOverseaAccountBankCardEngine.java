package com.ctrip.dcs.driver.account.application.exector;
/*
作者：pl.yang
创建时间：2025/4/28-上午11:07-2025
*/


import com.ctrip.dcs.driver.account.application.convert.BindOverseaAccountBankCardConvert;
import com.ctrip.dcs.driver.account.application.service.AccountBankCardService;
import com.ctrip.dcs.driver.account.domain.condition.BindOverseaBankCardCondition;
import com.ctrip.dcs.driver.domain.account.BindOverseaAccountBankCardRequestType;
import com.ctrip.dcs.driver.domain.account.BindOverseaAccountBankCardResponseType;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BindOverseaAccountBankCardEngine
 * @Package com.ctrip.dcs.driver.account.application.exector
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 上午11:07
 */

@Component
@ServiceLogTag(tagKeys= {"driverId","uid","bankCardInfo"})
public class BindOverseaAccountBankCardEngine extends AbDriverDomainShoppingExecutor<BindOverseaAccountBankCardRequestType, BindOverseaAccountBankCardResponseType> {

    @Autowired
    BindOverseaAccountBankCardConvert bindOverseaAccountBankCardConvert;
    @Autowired
    AccountBankCardService accountBankCardService;
    @Override
    public BindOverseaAccountBankCardResponseType execute(BindOverseaAccountBankCardRequestType bindOverseaAccountBankCardRequestType) {
        BindOverseaAccountBankCardResponseType responseType = new BindOverseaAccountBankCardResponseType();
        //type 标识 境内卡。
        BindOverseaBankCardCondition condition = bindOverseaAccountBankCardConvert.convert(bindOverseaAccountBankCardRequestType);
        BaseResult<Void> bindBankCardResult = accountBankCardService.bindOverseaBankCard(condition);
        if (bindBankCardResult.isSuccess()) {
            return ServiceResponseUtils.success(responseType);
        } else {
            return ServiceResponseUtils.fail(responseType, bindBankCardResult.getCode(), bindBankCardResult.getMessage());
        }
    }
}
