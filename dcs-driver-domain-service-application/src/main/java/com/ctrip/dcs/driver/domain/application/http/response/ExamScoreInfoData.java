package com.ctrip.dcs.driver.domain.application.http.response;

import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamIsPassedEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 */

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ExamScoreInfoData {
  private String user_id;

  private String dept_code;

  private BigDecimal score;

  private long submit_time;

  private int is_passed;

  private String exam_id;

  public boolean ifPassed(){
    return is_passed == ExamIsPassedEnum.PASSED.getCode();
  }
}
