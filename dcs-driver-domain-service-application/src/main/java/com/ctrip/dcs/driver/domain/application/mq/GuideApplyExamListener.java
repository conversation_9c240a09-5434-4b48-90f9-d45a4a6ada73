package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.arch.canal.DataChange;
import com.ctrip.dcs.driver.domain.application.redis.ExamRedisLogic;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamIsPassedEnum;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import java.util.Objects;

@Component
public class GuideApplyExamListener implements MessageListener {

  public static final Logger LOGGER = LoggerFactory.getLogger(GuideApplyExamListener.class);

  public static final String CONSUMER_GROUP = "100038374";

  @Autowired
  private ExamRedisLogic examRedisLogic;

  @Override
  @QmqConsumer(prefix = "dcs.canal.dcsguidesupplydb.data.changed", consumerGroup = CONSUMER_GROUP)
  public void onMessage(Message message) {
    String data = message.getStringProperty("dataChange");
    DataChange dataChange = JacksonUtil.deserialize(data, DataChange.class);
    if (Objects.isNull(dataChange)) {
      return;
    }
    if(!"guide_apply_exam".equals(dataChange.getTableName())){
      return;
    }
    try {
      if(dataChange.isDelete()){
        Long guideId = Long.valueOf(dataChange.getBeforeColumnValue("guide_id"));
        examRedisLogic.deleteGuidePassedExamInfo(guideId);
      }else if(dataChange.isUpate()){
        int beforeExamIsPassed = Integer.valueOf(dataChange.getBeforeColumnValue("exam_is_passed"));
        int afterExamIsPassed = Integer.valueOf(dataChange.getAfterColumnValue("exam_is_passed"));
        if(beforeExamIsPassed == ExamIsPassedEnum.UNPASSED.getCode() && afterExamIsPassed ==  ExamIsPassedEnum.PASSED.getCode()){
          Long guideId = Long.valueOf(dataChange.getBeforeColumnValue("guide_id"));
          examRedisLogic.deleteGuidePassedExamInfo(guideId);
        }
      }
    } catch (Exception e) {
      LOGGER.warn(e);
    }
  }
}
