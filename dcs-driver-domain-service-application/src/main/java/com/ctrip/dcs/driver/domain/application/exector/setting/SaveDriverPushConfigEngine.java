package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.application.util.PushInfoUtil;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.setting.ServiceTimeModel;
import com.ctrip.dcs.driver.domain.setting.DriverPushConfigInfoDTO;
import com.ctrip.dcs.driver.domain.setting.SaveDriverPushConfigRequestType;
import com.ctrip.dcs.driver.domain.setting.SaveDriverPushConfigResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 保存司机推送配置
 */
@Component
public class SaveDriverPushConfigEngine extends ShoppingExecutor<SaveDriverPushConfigRequestType, SaveDriverPushConfigResponseType>
        implements Validator<SaveDriverPushConfigRequestType> {

    @Autowired
    private DriverOrderPushConfigDao driverOrderPushConfigDao;

    @Autowired
    private PushConfigRedisLogic pushConfigRedisLogic;


    @Override
    public void validate(AbstractValidator<SaveDriverPushConfigRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final SaveDriverPushConfigEngine owner;
        private final SaveDriverPushConfigResponseType response;
        private final SaveDriverPushConfigRequestType request;

        private Executor(SaveDriverPushConfigRequestType request, SaveDriverPushConfigResponseType response, SaveDriverPushConfigEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;
        }

        /**
         * 保存 drv_driver_order_push_config 数据
         * */
        private void SaveDriverPushConfigInfo() {
            try {
                DriverOrderPushConfigPO configPO = this.owner.driverOrderPushConfigDao.findOne(this.request.driverId);
                boolean isNew = false;
                if (Objects.isNull(configPO)){
                    configPO = new DriverOrderPushConfigPO();
                    configPO.setDrvId(this.request.driverId);
                    configPO.setOrderPushStatus(false);
                    configPO.setServiceTime(Strings.EMPTY);
                    configPO.setOrderTyps(Strings.EMPTY);
                    configPO.setDrvIntendVehicleType(Strings.EMPTY);
                    configPO.setDrvOrderDistance(0);
                    configPO.setCreateUser(Strings.EMPTY);
                    configPO.setModifyUser(Strings.EMPTY);
                    isNew = true;
                }

                // 是否开启抢单
                if(Objects.nonNull(request.orderPushStatus)) {
                    configPO.setOrderPushStatus(BooleanUtils.toBooleanDefaultIfNull(request.orderPushStatus, false));
                }
                // 用车时间
                if(StringUtils.isNotBlank(request.serviceTimeFrom) && StringUtils.isNotBlank(request.serviceTimeTo)){
                    ServiceTimeModel serviceTimeModel = new ServiceTimeModel();
                    serviceTimeModel.setFrom(request.serviceTimeFrom);
                    serviceTimeModel.setTo(request.serviceTimeTo);
                    configPO.setServiceTime(JacksonUtil.serialize(serviceTimeModel));
                }
                // 订单类型
                if(Objects.nonNull(request.orderTyps)) {
                    configPO.setOrderTyps(request.orderTyps);
                }
                // 车型
                if(Objects.nonNull(request.drvIntendVehicleType)) {
                    configPO.setDrvIntendVehicleType(request.drvIntendVehicleType);
                }
                // 上车点距离
                if(Objects.nonNull(request.drvOrderDistance)) {
                    configPO.setDrvOrderDistance(request.drvOrderDistance);
                }

                int result = 0;
                if(isNew) {
                    result = this.owner.driverOrderPushConfigDao.insert(configPO);
                } else {
                    result = this.owner.driverOrderPushConfigDao.update(configPO);
                }

                if(result > 0) {
                    //更新缓存
                    DriverPushConfigInfoDTO driverPushConfigInfo = PushInfoUtil.convertToPushInfo(configPO);
                    this.owner.pushConfigRedisLogic.saveDriverPushConfig(this.request.driverId, driverPushConfigInfo);
                    this.owner.pushConfigRedisLogic.updateDriverLanguage(this.request.driverId, configPO.getDrvLanguage());
                }
            } catch (Exception exception) {
                CommonLogger.INSTANCE.error(exception);
            }
        }

        @Override
        protected boolean validate() {
            return true;
        }

        @Override
        protected void buildResponse() {
            if(!this.validate()){
                return;
            }
            this.SaveDriverPushConfigInfo();
        }
    }

    @Override
    public SaveDriverPushConfigResponseType execute(SaveDriverPushConfigRequestType request) {
        SaveDriverPushConfigResponseType response = new SaveDriverPushConfigResponseType();
        Executor executor = new Executor(request, response, this);
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public SaveDriverPushConfigResponseType onException(SaveDriverPushConfigRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("SaveDriverPushConfig error", ex);
        return super.onException(req, ex);
    }
}
