package com.ctrip.dcs.driver.domain.application.domain.rigths;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.DriverPointModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.value.rights.LevelObject;
import com.google.gson.annotations.Expose;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 */
public class LevelObjectImpl extends LevelObject {

    private final DomainBeanFactory domainBeanFactory;

    public LevelObjectImpl(DomainBeanFactory domainBeanFactory, long driverId, String monthIdx) {
        this.domainBeanFactory = domainBeanFactory;

        LevelModel model = domainBeanFactory.rightsRepoService().queryDriverLevel(driverId, monthIdx);
        if (Objects.isNull(model)) {
            return;
        }

        this.levelConfigId = model.getLevelConfigId();
        this.levelName = model.getLevelName();
        this.cityId = model.getCityId();
        this.monthIdx = model.getMonthIdx();
        this.drivId = model.getDrivId();
        this.drivLevel = LevelEnum.getByCode(model.getDrivLevel());
        this.drivPoint = model.getDrivPoint();
        this.drivActivity = model.getDrivActivity();
        this.drivRank = model.getDrivRank();
    }

    public LevelObjectImpl(DomainBeanFactory domainBeanFactory, long driverId, Long cityId) {
        this.domainBeanFactory = domainBeanFactory;
        LocalDateTime localCurrentTime = ObjectUtils.defaultIfNull(this.domainBeanFactory.geoGateway().getLocalCurrentTime(cityId), LocalDateTime.now());
        DriverPointModel model = this.domainBeanFactory.rightsRedisLogic().getDriverPointSnapshot(Arrays.asList(driverId), LocalDateTimeUtils.todayStr(localCurrentTime)).stream().findFirst().orElse(null);
        //当天分数没有 查询前一天分数
        if (Objects.isNull(model)) {
            model = this.domainBeanFactory.rightsRedisLogic().getDriverPointSnapshot(Arrays.asList(driverId), LocalDateTimeUtils.yesterdayStr(localCurrentTime)).stream().findFirst().orElse(null);
            if (Objects.isNull(model)) {
                return;
            }
        }
        this.levelConfigId = model.getLevelConfigId();
        this.levelName = model.getLevelName();
        this.drivId = model.getDrivId();
        this.drivLevel = LevelEnum.getByCode(model.getDrivLevel());
        this.drivPoint = model.getDrivPoint();
        this.drivActivity = model.getDrivActivity();
        this.driverSafePoint = model.getDriverSafePoint();
        this.drivRank = model.getDrivRank();
        this.drivTotalRank = model.getTotalRank();
        this.rankGap = model.getRankGap();
        this.drivPointLow = model.getDrivPointLow();
        this.drivActivityLow = model.getDrivActivityLow();
        this.driverSafePointLow = model.getDriverSafePointLow();
    }

    @Expose
    private long levelConfigId;
    @Expose
    private String levelName;
    @Expose
    private Long cityId;
    @Expose
    private String monthIdx;
    @Expose
    private Long drivId;
    @Expose
    private LevelEnum drivLevel;
    @Expose
    private BigDecimal drivPoint;
    @Expose
    private BigDecimal drivActivity;
    @Expose
    private BigDecimal driverSafePoint;
    @Expose
    private BigDecimal drivPointLow;
    @Expose
    private BigDecimal drivActivityLow;
    @Expose
    private BigDecimal driverSafePointLow;
    @Expose
    private Integer drivRank;
    @Expose
    private Integer drivTotalRank;
    @Expose
    private Integer rankGap;

    @Override
    public long levelConfigId() {
        return this.levelConfigId;
    }

    @Override
    public String levelName() {
        return this.levelName;
    }

    @Override
    public Long cityId() {
        return this.cityId;
    }

    @Override
    public String monthIdx() {
        return this.monthIdx;
    }

    @Override
    public Long drivId() {
        return this.drivId;
    }

    @Override
    public LevelEnum drivLevel() {
        return this.drivLevel;
    }

    @Override
    public BigDecimal drivPoint() {
        return this.drivPoint;
    }

    @Override
    public BigDecimal drivActivity() {
        return this.drivActivity;
    }

    @Override
    public Integer drivRank() {
        return this.drivRank;
    }

    @Override
    public Integer rankGap() {
        return this.rankGap;
    }

    @Override
    public BigDecimal drivPointLow() {
        return this.drivPointLow;
    }

    @Override
    public BigDecimal drivActivityLow() {
        return this.drivActivityLow;
    }

    @Override
    public Integer drivTotalRank() {
        return this.drivTotalRank;
    }

    @Override
    public BigDecimal driverSafePoint() {
        return this.driverSafePoint;
    }

    @Override
    public BigDecimal driverSafePointLow() {
        return this.driverSafePointLow;
    }
}
