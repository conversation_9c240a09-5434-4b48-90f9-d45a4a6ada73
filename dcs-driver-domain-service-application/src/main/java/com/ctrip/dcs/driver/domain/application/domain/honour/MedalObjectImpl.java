package com.ctrip.dcs.driver.domain.application.domain.honour;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.HonourEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormHonourWallDriverMedalInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.DynamicMedalInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import com.ctrip.dcs.driver.value.honour.MedalObject;
import com.google.gson.annotations.Expose;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;

import java.time.LocalDate;
import java.util.Optional;

/**
 * 勋章领域
 * */
public class MedalObjectImpl extends MedalObject {

  private final DomainBeanFactory domainBeanFactory;
  private final DynamicMedalInfoModel biDriverMedalInfo;
  private final MedalBasicInfoModel basicMedal;
  private final boolean isLight;
  private final boolean isNotice;
  private final String useMonth;
  private final int totalCount;

  MedalObjectImpl(DomainBeanFactory domainBeanFactory, DynamicMedalInfoModel biDriverMedalInfo,
                  MedalBasicInfoModel basicMedal, boolean isNew) {
    this.domainBeanFactory = domainBeanFactory;
    this.biDriverMedalInfo = biDriverMedalInfo;
    this.basicMedal = basicMedal;
    this.code = basicMedal.getMedalCode();
    this.type = basicMedal.getType();

    this.isLight = this.checkLight();
    this.icon = this.domainBeanFactory.honourImgConfig().getMedalImg(this.type.isHasLevel() ? this.code : this.type.getCode(), this.isLight, isNew);

    this.grade = basicMedal.getBimedalGrade().intValue();
    this.notStartRemind = false;
    this.inProgressRemind = 0;
    this.setMedalRemind();

    this.position = 0;
    this.total = 0;
    this.isNotice = this.isLight;

    this.useMonth = Strings.EMPTY;
    this.totalCount = 0;
  }

  MedalObjectImpl(DomainBeanFactory domainBeanFactory,
                  FormHonourWallDriverMedalInfo formMedal) {
    this.domainBeanFactory = domainBeanFactory;
    this.biDriverMedalInfo = null;
    this.basicMedal = null;
    this.code = formMedal.getCode();
    this.type = HonourEnum.MedalTypeEnum.HONOURWALL;
    this.isLight = formMedal.isLight();
    this.icon = formMedal.getIcon();
    this.grade = 0;
    this.notStartRemind = false;
    this.inProgressRemind = 0;
    this.position = formMedal.getDriverRank();
    this.total = formMedal.getTotalRank();
    this.isNotice = ObjectUtils.defaultIfNull(formMedal.getHonourTime(), LocalDate.MIN).equals(LocalDate.now());
    this.useMonth = Strings.EMPTY;
    this.totalCount = 0;
  }

  MedalObjectImpl(DomainBeanFactory domainBeanFactory,
                  FormHonourWallDriverMedalInfo formMedal,
                  GoldMedalDriverModel goldMedalDriverModel) {
    int goldMedalTotalCount = 0;
    String useMonth = Strings.EMPTY;
    if(goldMedalDriverModel != null){
      goldMedalTotalCount = Optional.of(goldMedalDriverModel.getTotalCount()).orElse(0);
      useMonth = Optional.ofNullable(goldMedalDriverModel.getMonthTime()).orElse(Strings.EMPTY);
    }
    this.domainBeanFactory = domainBeanFactory;
    this.biDriverMedalInfo = null;
    this.basicMedal = null;
    this.code = formMedal.getCode();
    this.type = HonourEnum.MedalTypeEnum.HONOURWALL;
    this.isLight = goldMedalTotalCount > 0;
    this.icon = formMedal.getIcon();
    this.grade = 0;
    this.notStartRemind = false;
    this.inProgressRemind = 0;
    this.position = 0;
    this.total = 0;
    this.useMonth = useMonth;
    this.totalCount = goldMedalTotalCount;
    this.isNotice = goldMedalTotalCount > 0 && StringUtils.isNotBlank(this.useMonth);
  }

  @Expose
  private String code;
  @Expose
  private HonourEnum.MedalTypeEnum type;
  @Expose
  private String icon;
  @Expose
  private int grade;
  @Expose
  private boolean notStartRemind;
  @Expose
  private int inProgressRemind;
  @Expose
  private int position;
  @Expose
  private int total;

  @Override
  public String code() {
    return this.code;
  }

  @Override
  public HonourEnum.MedalTypeEnum type() {
    return this.type;
  }

  @Override
  public String typeCode() {
    return this.type.getTypeCode();
  }

  @Override
  public int sortIndex() {
    return this.type.getIndex();
  }

  @Override
  public String icon() {
    return this.icon;
  }

  @Override
  public int grade() {
    return this.grade;
  }

  @Override
  public boolean notStartRemind(){
    return this.notStartRemind;
  }

  @Override
  public int inProgressRemind(){
    return this.inProgressRemind;
  }

  @Override
  public int position() {
    return this.position;
  }

  @Override
  public int total() {
    return this.total;
  }

  @Override
  public void updateNewMedalInfo(long position, long total) {
    this.position = (int)position;
    this.total = (int)total;
  }

  @Override
  public boolean isLight() {
    return this.isLight;
  }

  @Override
  public boolean isNotice() {
    return this.isNotice;
  }

  @Override
  public String useMonth(){
    return this.useMonth;
  }

  @Override
  public int totalCount(){
    return this.totalCount;
  }

  private boolean checkLight() {
    switch (this.type) {
      case SERVICEDAYS:
        return biDriverMedalInfo.getServiceDayGrade() >= this.basicMedal.getBimedalGrade();
      case FINISHORDERS:
        return biDriverMedalInfo.getOrderCountGrade() >= this.basicMedal.getBimedalGrade();
      case GOODCOMMENTS:
        return biDriverMedalInfo.getGoodCommentGrade() >= this.basicMedal.getBimedalGrade();
      case GOODMANNER:
        return biDriverMedalInfo.isGoodManner();
      case GOODCAR:
        return biDriverMedalInfo.isGoodCar();
      case GOODMAP:
        return biDriverMedalInfo.isGoodMap();
      case GOODLUGGAGE:
        return biDriverMedalInfo.isGoodLuggage();
      default:
        return false;
    }
  }

  /**
   * 勋章提示信息
   * */
  private void setMedalRemind() {
    switch (this.type) {
      case SERVICEDAYS:
        if(biDriverMedalInfo.getServiceDayGrade() == 0 && basicMedal.getBimedalGrade() == 1) {
          this.notStartRemind = true;
        } else if(biDriverMedalInfo.getServiceDayGrade() < basicMedal.getBimedalGrade()) {
          this.inProgressRemind = (int)Math.subtractExact(basicMedal.getBimedalGrade(),
                  biDriverMedalInfo.getServiceDay());
        }
        break;
      case FINISHORDERS:
        if(biDriverMedalInfo.getOrderCountGrade() == 0 && basicMedal.getBimedalGrade() == 1) {
          this.notStartRemind = true;
        } else if(biDriverMedalInfo.getOrderCountGrade() < basicMedal.getBimedalGrade()) {
          this.inProgressRemind = (int)Math.subtractExact(basicMedal.getBimedalGrade(),
                  biDriverMedalInfo.getOrderCount());
        }
        break;
      case GOODCOMMENTS:
        if(biDriverMedalInfo.getGoodCommentGrade() == 0 && basicMedal.getBimedalGrade() == 1) {
          this.notStartRemind = true;
        } else if(biDriverMedalInfo.getGoodCommentGrade() < basicMedal.getBimedalGrade()) {
          this.inProgressRemind = (int)Math.subtractExact(basicMedal.getBimedalGrade(),
                  biDriverMedalInfo.getGoodComment());
        }
        break;
      default:
        break;
    }
  }
}
