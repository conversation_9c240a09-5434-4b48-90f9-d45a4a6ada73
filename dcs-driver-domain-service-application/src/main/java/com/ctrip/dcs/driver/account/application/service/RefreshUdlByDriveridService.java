package com.ctrip.dcs.driver.account.application.service;
/*
作者：pl.yang
创建时间：2025/3/27-下午8:09-2025
*/


import cn.hutool.core.collection.CollUtil;
import com.ctrip.dcs.driver.account.application.condition.RefreshUdlByDriverIdCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.DriverApplyFreezeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.DriverFrozenBufferRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivLoginInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverDeviceInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsDrvLoginInformationDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.TmsVerifyEventDao;
import com.ctrip.dcs.driver.gateway.AccountRepository;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RefreshUdlService
 * @Package com.ctrip.dcs.driver.account.application.service
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/3/27 下午8:09
 */

@Component
public class RefreshUdlByDriveridService {

    Logger log = LoggerFactory.getLogger(RefreshUdlByDriveridService.class);

    @Resource
    AccountBaseInfoDao accountBaseInfoDao;
    @Resource
    DriverAccountConfig driverAccountConfig;
    @Autowired
    AccountRepository accountGateway;
    @Autowired
    TmsDrvLoginInformationDao tmsDrvLoginInformationDao;
    @Autowired
    DriverApplyFreezeRecordDao driverApplyFreezeRecordDao;
    @Autowired
    DriverFrozenBufferRecordDao driverFrozenBufferRecordDao;
    @Autowired
    DrivLoginInfoDao drivLoginInfoDao;
    @Autowired
    DriverDeviceInfoDao driverDeviceInfoDao;
    @Autowired
    TmsVerifyEventDao tmsVerifyEventDao;


    public void refreshAllUdlByDriverId(RefreshUdlByDriverIdCondition condition) {
        long startId = condition.getStartId();
        TaskMonitor taskMonitor = TaskHolder.getKeeper();

        log.info("refresh account udl start", "start id : " + startId);
        while (true) {
            if (taskMonitor.isStopped()) {
                log.error("refresh account udl job stop", "current id : " + startId);
                break;
            }
            log.info("refresh account udl", "current id : " + startId);
            List<AccountBaseInfoPO> accountBaseInfoPOList = accountBaseInfoDao.batchQueryByPage(startId, 100);
            if (CollectionUtils.isEmpty(accountBaseInfoPOList)) {
                break;
            }
            List<String> uidList = accountBaseInfoPOList.stream().map(AccountBaseInfoPO::getUid).collect(Collectors.toList());
            List<AccountUDLDo> accountUDLDos = accountGateway.batchQueryByUid(uidList);
            Map<String, String> udlMap = CollUtil.toMap(accountUDLDos, new HashMap<>(), AccountUDLDo::getSourceId, AccountUDLDo::getUdl);
            refreshUdlByDriverId(condition.getDbList(), udlMap);
            startId = accountBaseInfoPOList.get(accountBaseInfoPOList.size() - 1).getId();
        }
    }
    public void refreshUdlByDriverId(RefreshUdlByDriverIdCondition refreshUdlByDriverIdCondition) {
        List<String> driverIds = refreshUdlByDriverIdCondition.getDriverIds();
        if (CollUtil.isEmpty(driverIds)) {
            return;
        }
        for (List<String> driverList : Lists.partition(driverIds, 100)) {
            Map<String, String> udlMap = getUdlByDriver(driverList);
            refreshUdlByDriverId(refreshUdlByDriverIdCondition.getDbList(), udlMap);
        }

    }

    @Transactional
    private void updateUdlByDriverId(String driverId, String udl, List<String> dbLists) {
        if (CollUtil.isNotEmpty(dbLists)) {
            if (dbLists.contains("tms_drv_login_information")) {
                tmsDrvLoginInformationDao.updateUdlByDriverId(udl, driverId);
            }
            //只有境内车辆信息
//            if(dbLists.contains("tms_verify_event")){
//                tmsVerifyEventDao.updateUdlByDriverId(udl,driverId);
//            }
            if(dbLists.contains("driver_apply_freeze_record")){
                driverApplyFreezeRecordDao.updateUdlByDriverId(udl,driverId);
            }
            if(dbLists.contains("driver_frozen_buffer_record")){
                driverFrozenBufferRecordDao.updateUdlByDriverId(udl,driverId);
            }
            if(dbLists.contains("driv_login_info")){
                drivLoginInfoDao.updateUdlByDriverId(udl,driverId);
            }
            if(dbLists.contains("driver_device_info")){
                driverDeviceInfoDao.updateUdlByDriverId(udl,driverId);
            }

        }
    }
    private @Nullable Map<String/*司机id*/, String/*udl*/> getUdlByDriver(List<String> driverIdList) {

        List<AccountUDLDo> accountUDLDos = accountGateway.batchQueryBySourceId(driverIdList);

        return CollUtil.toMap(accountUDLDos, new HashMap<>(), AccountUDLDo::getSourceId, AccountUDLDo::getUdl);
    }


    private void refreshUdlByDriverId(List<String> dbList, Map<String, String> udlMap) {
        if (udlMap != null) {
            for (Map.Entry<String, String> entry : udlMap.entrySet()) {

                try {
                    String driverId = entry.getKey();
                    String udl = entry.getValue();
                    if (udl == null) continue;

                    if (StringUtils.isBlank(udl)) {
                        Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "NoUDL");
                        continue;
                    }
                    // 更新udl
                    updateUdlByDriverId(driverId, udl, dbList);
                    log.info("refresh account udl success", driverId);
                    Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "SUCCESS");
                    Thread.sleep(driverAccountConfig.getJobSleepMills());
                } catch (Throwable e) {
                    log.error(e);
                    Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "ERROR");
                }
            }
        }
    }


}
