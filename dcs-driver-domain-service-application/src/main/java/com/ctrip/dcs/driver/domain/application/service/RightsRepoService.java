package com.ctrip.dcs.driver.domain.application.service;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel;

import java.util.List;

public interface RightsRepoService {
    List<RightsModel> queryDriverRights(long driverId, List<Integer> rightsTypes, String monthIdx);

    List<RightsRecordModel> queryDriverRecords(long driverId, String useStartDate, String useEndDate, List<Integer> rightsTypes);

    LevelModel queryDriverLevel(long driverId, String monthIdx);

    void saveRightsRecords(DrivRightsRecordPO recordPO);

    void updateDriverRights(DrivRightsPO rightsPO);

    List<RightsRecordModel> queryDriverRecordsById(Long id);
}
