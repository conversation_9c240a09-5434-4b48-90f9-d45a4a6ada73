package com.ctrip.dcs.driver.trip.university.application.mq;


import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.NeedRetryException;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
　* @description: 司机信息推送到携程大学
　* <AUTHOR>
　* @date 2021/7/20 10:26
*/
@Component
public class PushDataToCtripUniversityListener {
    private static final Logger logger = LoggerFactory.getLogger(PushDataToCtripUniversityListener.class);

    @Autowired
    TripUniversityService tripUniversityService;

    @QmqConsumer(prefix = TripUniversityConstants.SUBJECT_PUSH_DATA_TRIP_UNIVERSITY , consumerGroup = "100038374-tripUniversity")
    public void pushDataToTripUniversityListener(Message message) {
        try {

            Long drvId = message.getLongProperty("drvId");
            String uid = message.getStringProperty("uid");
            if ( drvId <= 0 && uid == null) {
                return;
            }
            logger.info("pushDataToCtripUniversityListenerStart", "params:{}, {}", drvId, uid);

            TripUniversityDriverDTO driverDTO = new TripUniversityDriverDTO();
            driverDTO.setDrvId(drvId);
            driverDTO.setUid(uid);
            tripUniversityService.syncDriverInfoToTripUniversity(driverDTO);
            logger.info("pushDataToCtripUniversityListenerEnd", "params:{}, {}", drvId, uid);

        } catch (NeedRetryException nre) {
            logger.error("pushDataToCtripUniversityListener", nre);
            throw nre;
        }
    }
}
