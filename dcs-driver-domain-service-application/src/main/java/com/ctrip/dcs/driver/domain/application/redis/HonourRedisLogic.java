package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.honour.CustomerHonourMedalInfoDTO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalBasicInfoModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankCityDataModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.gson.reflect.TypeToken;
import credis.java.client.CacheProvider;
import credis.java.client.pipeline.CachePipeline;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class HonourRedisLogic {

  // 司机勋章数
  private static final String DRIVER_MEDAL_COUNT_REDIS_KEY = "medalcount:%s:%s";
  private static final String DRIVER_RANK_REDPOINT_REDIS_KEY = "rankredpoint:%s";
  private static final String DRIVER_GOLD_MEDAL_INFO_REDIS_KEY = "goldmedalinfo:%s";
  private static final String DRIVER_CUSTOMER_MEDAL_INFO_REDIS_KEY = "customermedalinfo:%s";

  // BI的勋章基础设置数据
  private static final String HONOUR_MEDAL_BASIC_DATA_REDIS_KEY = "bi_honour_medal_basic";
  private static final String HONOUR_MEDAL_BASIC_COUNT_REDIS_KEY = "bi_honour_medal_count";
  // BI排行榜数据批次
  private static final String HONOUR_RANK_DATA_UPDATE_REDIS_KEY = "bi_honour_rank_batch";

  // 排名列表（区分周榜月榜）ranklist:城市ID:周1/月2
  private static final String DRIVER_RANKING_DRIVER_REDIS_KEY = "rankdriver:%s:%s";
  private static final String DRIVER_RANKING_LIST_NEW_REDIS_KEY = "ranklist:%s:%s";
  // 点赞（司机点赞数、区分周榜月榜）rank:liking:城市ID:司机:周（2022-07-25）/月（2022-07）
  private static final String DRIVER_RANK_LIKE_REDIS_KEY = "rank:liking:%s:%s:%s";

  private static final DateTimeFormatter LOCAL_DATEHOUR_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHH");
  private static final long EXPIRE_TIME_ONE_HOUR = 3600L;  //1小时
  private static final long EXPIRE_TIME_TWO_DAY = 172800L;               //2天
  private static final long EXPIRE_TIME_ONE_DAY = 86400L;               //1天
  private static final long RANK_WEEK_LIST_EXPIRE_TIME = 691200L;       //8天
  private static final long RANK_MONTH_LIST_EXPIRE_TIME = 2764800L;     //32天

  @Autowired
  DirectorRedis directorRedis;

  /**
   * 保存城市排行榜数据
   * */
  public void updateCityRankData(long cityId, List<RankCityDataModel> rankList, boolean isWeek) {
    if (CollectionUtils.isNotEmpty(rankList)) {
      CacheProvider provider = directorRedis.getCacheProvider();
      CachePipeline pipeline = provider.getPipeline();
      rankList.forEach(rank -> {
        //司机id+isWeek 为key
        String key = String.format("%s_%s", "dcs_driver", String.format(DRIVER_RANKING_DRIVER_REDIS_KEY, rank.getDrvId(), isWeek ? 1 : 2));
        pipeline.set(key, JacksonUtil.serialize(rank));
        pipeline.expire(key, (int) EXPIRE_TIME_ONE_DAY);
      });
      //排行榜只前50名数据
      rankList = rankList.stream().filter(r -> r.getRanking() <= 50).collect(Collectors.toList());
      directorRedis.set(String.format(DRIVER_RANKING_LIST_NEW_REDIS_KEY, cityId, isWeek ? 1 : 2), rankList, EXPIRE_TIME_TWO_DAY);
      // pipeline提交
      pipeline.sync();
    }
  }

  /**
   * 查询司机排行数据
   * */
  public RankCityDataModel queryDriverRankData(long driverId, boolean isWeek) {
    String value = directorRedis.get(String.format(DRIVER_RANKING_DRIVER_REDIS_KEY, driverId, isWeek ? 1 : 2));
    return StringUtils.isNotBlank(value) ? JacksonUtil.deserialize(value, RankCityDataModel.class) : null;
  }

  /**
   * 保存司机排行数据
   * */
  public void saveDriverRankData(long driverId, boolean isWeek,RankCityDataModel rankCityDataModel) {
    if(Objects.nonNull(rankCityDataModel)){
      String key = String.format(DRIVER_RANKING_DRIVER_REDIS_KEY, driverId, isWeek ? 1 : 2);
      directorRedis.set(key, rankCityDataModel, EXPIRE_TIME_ONE_DAY);
    }
  }

  /**
   * 查询城市排行榜数据
   * */
  public List<RankCityDataModel> queryCityRankData(long cityId, boolean isWeek) {
    List<RankCityDataModel> rankCityDataModelList = queryCityRankDataNew(cityId,isWeek);
    if(CollectionUtils.isNotEmpty(rankCityDataModelList)){
      return rankCityDataModelList;
    }
    return Collections.emptyList();
  }

  private List<RankCityDataModel> queryCityRankDataNew(long cityId, boolean isWeek) {
    String value = directorRedis.get(String.format(DRIVER_RANKING_LIST_NEW_REDIS_KEY, cityId, isWeek ? 1 : 2));
    if(StringUtils.isNotBlank(value)) {
      return JacksonUtil.deserialize(value, new TypeToken<List<RankCityDataModel>>(){}.getType());
    }
    return Collections.emptyList();
  }

  private String getRankField(long cityId, boolean isWeek) {
   return String.format("%s:%s", cityId, isWeek ? 1 : 2);
  }

  /**
   * 查询勋章总数
   * */
  public long getDriverMedalCount(long driverId) {
    String key = String.format(DRIVER_MEDAL_COUNT_REDIS_KEY, driverId, LocalDateTime.now().format(LOCAL_DATEHOUR_FORMATTER));
    String result = directorRedis.get(key);
    return StringUtils.isEmpty(result) ? -1 : Long.parseLong(result);
  }

  /**
   * 保存勋章总数
   * */
  public void saveDriverMedalCount(long driverId, long medalCount) {
    String key = String.format(DRIVER_MEDAL_COUNT_REDIS_KEY, driverId, LocalDateTime.now().format(LOCAL_DATEHOUR_FORMATTER));
    directorRedis.set(key, medalCount, EXPIRE_TIME_ONE_HOUR);
  }

  /**
   * 查询最新的排行榜信息
   * */
  public RankOriginalDataModel getCurrentRankInfo() {
    return directorRedis.get(HONOUR_RANK_DATA_UPDATE_REDIS_KEY, RankOriginalDataModel.class);
  }

  /**
   * 保存最新的排行榜批次信息
   * */
  public void saveCurrentRankInfo(RankOriginalDataModel rankOriginalDataModel) {
    directorRedis.set(HONOUR_RANK_DATA_UPDATE_REDIS_KEY, rankOriginalDataModel, EXPIRE_TIME_TWO_DAY);
  }

  /**
   * 查询点赞结果
   * */
  public int getCurrentRankLikeCount(long driverId, long cityId, int subDataTime) {
    String key = String.format(DRIVER_RANK_LIKE_REDIS_KEY, cityId, driverId, subDataTime);
    String result = directorRedis.get(key);
    return StringUtils.isEmpty(result) ? 0 : Integer.parseInt(result);
  }

  /**
   * 更新点赞结果
   * */
  public void updateDriverRankLikeCount(long cityId, long driverId, int type, int subDataTime) {
    String key = String.format(DRIVER_RANK_LIKE_REDIS_KEY, cityId, driverId, subDataTime);
    directorRedis.incr(key);
    directorRedis.expire(key, type == 1 ?  RANK_WEEK_LIST_EXPIRE_TIME : RANK_MONTH_LIST_EXPIRE_TIME);
  }

  /**
   * 查询排行榜小红点提示
   * */
  public boolean isNoticeRankUpdate(long driverId) {
    return StringUtils.isNotBlank(directorRedis.get(String.format(DRIVER_RANK_REDPOINT_REDIS_KEY, driverId)));
  }

  /**
   * 设置排行榜小红点提示
   * */
  public void cleartNoticeRankUpdate(long driverId) {
    directorRedis.remove(String.format(DRIVER_RANK_REDPOINT_REDIS_KEY, driverId));
  }

  /**
   * 批量设置小红点
   * */
  public void batchNotieRankUpdate(List<Long> driverIdList) {
    CacheProvider provider = directorRedis.getCacheProvider();
    CachePipeline pipeline = provider.getPipeline();
    //分批设置小红点提醒（有效时间都是1周）
    driverIdList.forEach(d -> {
        String rankRedPointKey = String.format("%s_%s", "dcs_driver", String.format(DRIVER_RANK_REDPOINT_REDIS_KEY, d.toString()));
        pipeline.set(rankRedPointKey, "1");
        pipeline.expire(rankRedPointKey, (int)RANK_WEEK_LIST_EXPIRE_TIME);
    });

    // pipeline提交
    pipeline.sync();
  }

  /**
   * 查询最新的勋章基础信息
   * */
  public List<MedalBasicInfoModel> queryCurrentMedalBasicInfo() {
    String basicMedalInfo = directorRedis.get(HONOUR_MEDAL_BASIC_DATA_REDIS_KEY);
    if(StringUtils.isNotBlank(basicMedalInfo)) {
      return JacksonUtil.deserialize(basicMedalInfo, new TypeToken<List<MedalBasicInfoModel>>(){}.getType());
    }
    return Collections.emptyList();
  }

  /**
   * 查询最新的勋章基础量
   * */
  public int queryCurrentMedalCount() {
    String basicMedalInfoCount = directorRedis.get(HONOUR_MEDAL_BASIC_COUNT_REDIS_KEY);
    return Integer.parseInt(ObjectUtils.defaultIfNull(basicMedalInfoCount, "0"));
  }

  /**
   * 查询最新的勋章基础信息
   * */
  public void saveCurrentMedalBasicInfo(List<MedalBasicInfoModel> medalBasicInfoModels) {
    if(CollectionUtils.isNotEmpty(medalBasicInfoModels)) {
      directorRedis.set(HONOUR_MEDAL_BASIC_DATA_REDIS_KEY, medalBasicInfoModels, EXPIRE_TIME_TWO_DAY);
      directorRedis.set(HONOUR_MEDAL_BASIC_COUNT_REDIS_KEY, medalBasicInfoModels.size(), EXPIRE_TIME_TWO_DAY);
    } else {
      directorRedis.remove(HONOUR_MEDAL_BASIC_DATA_REDIS_KEY);
      directorRedis.remove(HONOUR_MEDAL_BASIC_COUNT_REDIS_KEY);
    }
  }

  /**
   * 查询金牌司机信息
   * */
  public GoldMedalDriverModel getDriverGoldMedalInfo(long driverId) {
    String key = String.format(DRIVER_GOLD_MEDAL_INFO_REDIS_KEY, driverId);
    String result = directorRedis.get(key);
    return StringUtils.isEmpty(result) ? null : JacksonUtil.deserialize(result, GoldMedalDriverModel.class);
  }

  /**
   * 保存金牌司机信息
   * */
  public void saveDriverGoldMedalInfo(long driverId, GoldMedalDriverModel goldMedalDriverInfo) {
    String key = String.format(DRIVER_GOLD_MEDAL_INFO_REDIS_KEY, driverId);
    directorRedis.set(key, JacksonUtil.serialize(goldMedalDriverInfo), EXPIRE_TIME_ONE_HOUR);
  }

  /**
   * 缓存司机对客勋章信息
   * */
  public Pair<Boolean, List<CustomerHonourMedalInfoDTO>> getDriverCustomerMedalInfo(long driverId) {
    String key = String.format(DRIVER_CUSTOMER_MEDAL_INFO_REDIS_KEY, driverId);
    String medalInfo = directorRedis.get(key);
    if(StringUtils.isNotBlank(medalInfo)) {
      if(medalInfo.equals("1")){
        return Pair.of(true, Collections.emptyList());
      } else {
        return Pair.of(true, JacksonUtil.deserialize(medalInfo, new TypeToken<List<CustomerHonourMedalInfoDTO>>(){}.getType()));
      }
    }
    return Pair.of(false, Collections.emptyList());
  }

  public void saveDriverCustomerMedalInfo(long driverId, List<CustomerHonourMedalInfoDTO> medalInfoList) {
    String key = String.format(DRIVER_CUSTOMER_MEDAL_INFO_REDIS_KEY, driverId);
    if(CollectionUtils.isNotEmpty(medalInfoList)) {
      directorRedis.set(key, JacksonUtil.serialize(medalInfoList), EXPIRE_TIME_ONE_HOUR);
    } else {
      directorRedis.set(key, "1", EXPIRE_TIME_ONE_HOUR);
    }
  }
}
