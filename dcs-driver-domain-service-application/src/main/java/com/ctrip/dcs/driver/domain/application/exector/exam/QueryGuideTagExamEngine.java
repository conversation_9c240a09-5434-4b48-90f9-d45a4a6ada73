package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.exam.GuideTagExamDTO;
import com.ctrip.dcs.driver.domain.exam.QueryGuideTagExamRequestType;
import com.ctrip.dcs.driver.domain.exam.QueryGuideTagExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ctrip.igt.framework.infrastructure.validator.Validator;

@Component
public class QueryGuideTagExamEngine extends
    ShoppingExecutor<QueryGuideTagExamRequestType, QueryGuideTagExamResponseType>
    implements Validator<QueryGuideTagExamRequestType> {

  @Autowired
  private DomainBeanFactory domainBeanFactory;

  @Autowired
  LockService lockService;

  @Override
  public QueryGuideTagExamResponseType execute(
      QueryGuideTagExamRequestType queryGuideTagExamRequestType) {
    QueryGuideTagExamResponseType response = new QueryGuideTagExamResponseType();
    Executor executor = new Executor(queryGuideTagExamRequestType, response, this);
    executor.buildResponse();
    return ServiceResponseUtils.success(response);
  }


  @Override
  public void validate(AbstractValidator<QueryGuideTagExamRequestType> validator) {
    validator.ruleFor("guideId").notNull().notEmpty().greaterThan(0L);
  }

  private static final class Executor extends ShoppingBaseExecutor {
    private final QueryGuideTagExamEngine owner;
    private final QueryGuideTagExamResponseType response;
    private final QueryGuideTagExamRequestType request;

    private Executor(QueryGuideTagExamRequestType request, QueryGuideTagExamResponseType response,
        QueryGuideTagExamEngine owner) {
      super(request);
      this.owner = owner;
      this.response = response;
      this.request = request;
    }

    @Override
    protected boolean validate() {
      return true;
    }

    @Override
    protected void buildResponse() {
      GuideTagExamDTO guideTagExamCache =
          this.owner.domainBeanFactory.examRedisLogic().getGuideTagExam(this.request.guideId);
      if(guideTagExamCache != null){
        this.response.setGuideTagExamDTO(guideTagExamCache);
        return;
      }

      // 加锁
      GuideTagExamDTO guideTagExamDTO =
          this.owner.lockService.executeInLock(String.format("dcs_guide_tag_exam:%s", this.request.guideId),
              1000, () -> this.owner.domainBeanFactory.guideTagExamService().queryAndSaveGuideTagExamDTO(this.request.guideId));
      this.response.setGuideTagExamDTO(guideTagExamDTO);
    }
  }
}
