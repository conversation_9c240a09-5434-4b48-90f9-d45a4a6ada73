package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceDriverObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceObjectImpl;
import com.ctrip.dcs.driver.domain.finance.ResetFinancePasswordRequestType;
import com.ctrip.dcs.driver.domain.finance.ResetFinancePasswordResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 重置密码
 */
@Component
public class ResetFinancePasswordEngine extends ShoppingExecutor<ResetFinancePasswordRequestType, ResetFinancePasswordResponseType>
        implements Validator<ResetFinancePasswordRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<ResetFinancePasswordRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
        validator.ruleFor("verificationCode").notNull().notEmpty();
        validator.ruleFor("identitycode").notNull().notEmpty();
        validator.ruleFor("password").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final ResetFinancePasswordEngine owner;
        private final ResetFinancePasswordResponseType response;
        private final ResetFinancePasswordRequestType request;
        private FinanceDriverObject financeDriverObject;

        private Executor(ResetFinancePasswordRequestType request, ResetFinancePasswordResponseType response, ResetFinancePasswordEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.responseResult.returnCode = FinanceResultEnum.OK.getCode();
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            financeDriverObject = new FinanceDriverObjectImpl(this.request.driverId, this.request.source, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            if(FinanceResultEnum.isValidError(this.financeDriverObject.validResult())) {
                response.responseResult.returnCode = this.financeDriverObject.validResult();
                return false;
            }
            return true;
        }

        @Override
        protected void buildResponse() {
            response.responseResult.returnCode = financeDriverObject.checkFinanceVerification(this.request.verificationCode);
            if(FinanceResultEnum.isValidError(response.responseResult.returnCode)){
                return;
            }

            FinanceObject financeObject = new FinanceObjectImpl(this.request.driverId,
                    false, this.owner.domainBeanFactory);

            response.responseResult.returnCode =
                    financeObject.resetPassword(this.request.password, this.request.identitycode);
        }
    }

    @Override
    public ResetFinancePasswordResponseType execute(ResetFinancePasswordRequestType request) {
        ResetFinancePasswordResponseType response = new ResetFinancePasswordResponseType();
        Executor executor = new Executor(request, response, this);
        if(executor.validate()){
            executor.buildResponse();
        }
        if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
            response.responseResult.returnMessage = this.domainBeanFactory.financeConvertService().getFinanceResult(response.responseResult.returnCode);
            return ServiceResponseUtils.fail(response, response.responseResult.returnCode, response.responseResult.returnMessage);
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public ResetFinancePasswordResponseType onException(ResetFinancePasswordRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("ResetFinancePassword error", ex);
        return super.onException(req, ex);
    }
}
