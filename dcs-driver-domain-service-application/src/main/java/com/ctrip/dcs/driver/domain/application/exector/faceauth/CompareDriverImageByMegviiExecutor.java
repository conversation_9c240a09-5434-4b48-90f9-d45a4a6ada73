package com.ctrip.dcs.driver.domain.application.exector.faceauth;

import com.ctrip.dcs.common.enums.DriverTypeEnum;
import com.ctrip.dcs.driver.account.domain.config.MegviiQConfig;
import com.ctrip.dcs.driver.domain.application.common.CommonCryptographicUtils;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyParamDTO;
import com.ctrip.dcs.driver.domain.application.dto.faceauth.megvii.V5VerifyResultDTO;
import com.ctrip.dcs.driver.domain.application.service.faceauth.megvii.FaceComparisonResultEnum;
import com.ctrip.dcs.driver.domain.application.service.faceauth.megvii.VerifyFaceService;
import com.ctrip.dcs.driver.domain.application.util.ImageFileUtil;
import com.ctrip.dcs.driver.domain.facerecognize.CompareDriverImageByMegvRequestType;
import com.ctrip.dcs.driver.domain.facerecognize.CompareDriverImageByMegvResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverOverseaFaceVerifyDetailDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverOverseaFaceVerifyRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverOverseaFaceVerifyDetailPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverOverseaFaceVerifyRecordPO;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.framework.ucs.client.ShardingKey;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.tour.driver.utility.log.LogHelper;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class CompareDriverImageByMegviiExecutor extends ShoppingExecutor<CompareDriverImageByMegvRequestType, CompareDriverImageByMegvResponseType> implements Validator<CompareDriverImageByMegvRequestType> {

  @Autowired
  private VerifyFaceService verifyFaceService;

  @Autowired
  private MegviiQConfig megviiQConfig;

  @Autowired
  private DriverOverseaFaceVerifyRecordDao driverOverseaFaceVerifyRecordDao;

  @Autowired
  DriverOverseaFaceVerifyDetailDao driverOverseaFaceVerifyDetailDao;

  @Autowired
  private CommonCryptographicUtils commonCryptographicUtils;

  @Override
  public CompareDriverImageByMegvResponseType execute(CompareDriverImageByMegvRequestType request) {
    CompareDriverImageByMegvResponseType response = new CompareDriverImageByMegvResponseType();

    Long driverId = request.getDriverId();

    Map<String, String> logTag = new HashMap<>();
    logTag.put("driverId", String.valueOf(driverId));

    LogHelper.info("megviiCmpImg", "start", logTag);

    String refImageUrl = commonCryptographicUtils.decryptByDES(request.getRef());
    String cmpImageUrl = commonCryptographicUtils.decryptByDES(request.getCmp());

    byte[] refImgBuf = ImageFileUtil.downloadThenCompressImage(refImageUrl);
    if (ArrayUtils.isEmpty(refImgBuf)) {
      LogHelper.info("megviiCmpImg", "get ref image failed", logTag);
      response.setResult(FaceComparisonResultEnum.REF_MISSING.getValue());
      return ServiceResponseUtils.success(response);
    }

    byte[] cmpImgBuf = ImageFileUtil.downloadThenCompressImage(cmpImageUrl);
    if (ArrayUtils.isEmpty(cmpImgBuf)) {
      LogHelper.info("megviiCmpImg", "get cmp image failed", logTag);
      response.setResult(FaceComparisonResultEnum.CMP_MISSING.getValue());
      return ServiceResponseUtils.success(response);
    }

    V5VerifyParamDTO params = new V5VerifyParamDTO();
    params.setBizNo(driverId + "_" + System.currentTimeMillis());
    params.setComparisonType("0");
    params.setDataType("1");
    params.setVerifyId(megviiQConfig.getVerifyId());
    params.setUuid(String.valueOf(driverId));
    params.setImageFileName(extractImageName(cmpImageUrl));
    params.setImage(cmpImgBuf);
    params.setImageRef1(refImgBuf);
    params.setImageRef1FileName(extractImageName(refImageUrl));
    V5VerifyResultDTO cmpResult = null;
    try {
      cmpResult = verifyFaceService.verify(params);
    } catch (Exception e) {
      LogHelper.warn("megviiCmpImg", e, logTag);
    }
    if (cmpResult == null) {
      return ServiceResponseUtils.error(response);
    }

    // 记录流水
    logRecord(driverId, cmpResult.getResultCode(), request.getRefType(), request.getCmpType());
    logDetail(driverId, cmpResult.getRes());

    if (Objects.equals(1000, cmpResult.getResultCode())) {
      response.setResult(FaceComparisonResultEnum.PASS.getValue());
    } else {
      response.setResult(FaceComparisonResultEnum.NOT_PASS.getValue());
    }

    return ServiceResponseUtils.success(response);
  }

  @Override
  public void validate(AbstractValidator<CompareDriverImageByMegvRequestType> validator) {
    validator.ruleFor("driverId").notNull().notEmpty();
    validator.ruleFor("ref").notNull().notEmpty();
    validator.ruleFor("cmp").notNull().notEmpty();
    validator.ruleFor("refType").notNull();
    validator.ruleFor("cmpType").notNull();
  }

  private String extractImageName(String url) {
    try {
      URL urlObj = new URL(url);
      String path = urlObj.getPath();
      return path.substring(path.lastIndexOf('/') + 1);
    } catch (Exception e) {
      LogHelper.warn("extractImageName error", e);
      return "";
    }
  }

  private void logRecord(Long driverId, Integer resultCode, Integer refType, Integer cmpType) {
    DriverOverseaFaceVerifyRecordPO recordPO = DriverOverseaFaceVerifyRecordPO.builder()
            .driverId(driverId)
            .driverType(DriverTypeEnum.DRIVER_GUIDE.getType())
            .verifyImgType(refType)
            .verifyImgStandbyType(cmpType)
            .verifyResultCode(String.valueOf(resultCode))
            .providerDataLocation(Cat.getOrCreateTraceContext().get(ShardingKey.UDL.getTraceContextKey()))
            .build();
    driverOverseaFaceVerifyRecordDao.insert(recordPO);
  }

  private void logDetail(Long driverId, String megvRes) {
    DriverOverseaFaceVerifyDetailPO detailPO = new DriverOverseaFaceVerifyDetailPO();
    detailPO.setDriverId(driverId.intValue());
    detailPO.setProviderDataLocation(Cat.getOrCreateTraceContext().get(ShardingKey.UDL.getTraceContextKey()));
    detailPO.setVerifyResultDetail(megvRes);
    driverOverseaFaceVerifyDetailDao.insert(detailPO);
  }


}
