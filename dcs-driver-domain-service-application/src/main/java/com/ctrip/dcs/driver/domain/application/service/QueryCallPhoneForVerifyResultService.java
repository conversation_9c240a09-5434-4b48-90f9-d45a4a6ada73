package com.ctrip.dcs.driver.domain.application.service;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.domain.application.condition.QueryCallPhoneForVerifyResultCondition;
import com.ctrip.dcs.driver.domain.application.dto.PhoneInfoDTO;
import com.google.common.collect.Lists;

@Service
public class QueryCallPhoneForVerifyResultService {

    @Resource
    private PhoneCheckTaskTable phoneCheckTaskTable;

    @Autowired
    private PhoneCheckTable phoneCheckTable;

    /**
     * 返回结果
     *
     * @param condition
     * @return
     */
    public String query(QueryCallPhoneForVerifyResultCondition condition) {
        if (Objects.nonNull(condition.getCallTaskId())) {
            PhoneCheckTaskEntity phoneCheckTaskEntity = phoneCheckTaskTable.queryByPk(condition.getCallTaskId());
            return phoneCheckTaskEntity.getCallHangupCode();
        }
        if (CollectionUtils.isEmpty(condition.getPhoneInfoDTOS())) {
            return null;
        }
        List<String> placeholders = Lists.newArrayList();
        List<Object> parameters = Lists.newArrayList(condition.getPhoneInfoDTOS().size());
        for (PhoneInfoDTO phoneInfoDTO : condition.getPhoneInfoDTOS()) {
            placeholders.add("(?, ?)");
            parameters.add(phoneInfoDTO.getCountryCode());
            parameters.add(phoneInfoDTO.getPhoneNumber());
        }
        List<PhoneCheckEntity> phoneCheckEntities = phoneCheckTable.queryMany("(phone_prefix,phone_number) in (" + StringUtils.join(placeholders, ",") + ")", parameters.toArray());

        if (CollectionUtils.isNotEmpty(phoneCheckEntities)) {

        }
    }
}
