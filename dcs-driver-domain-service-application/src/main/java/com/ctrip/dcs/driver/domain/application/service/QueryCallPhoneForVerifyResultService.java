package com.ctrip.dcs.driver.domain.application.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTable;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.table.PhoneCheckTaskTable;
import com.ctrip.dcs.driver.domain.application.condition.QueryCallPhoneForVerifyResultCondition;
import com.ctrip.dcs.driver.domain.application.dto.PhoneInfoDTO;
import com.ctrip.dcs.driver.domain.application.dto.PhoneResultDTO;
import com.google.common.collect.Lists;

@Service
public class QueryCallPhoneForVerifyResultService {

    @Resource
    private PhoneCheckTaskTable phoneCheckTaskTable;

    @Autowired
    private PhoneCheckTable phoneCheckTable;

    /**
     * 返回结果
     *
     * @param condition
     * @return
     */
    public Pair<String, List<PhoneResultDTO>> query(QueryCallPhoneForVerifyResultCondition condition) {
        if (Objects.nonNull(condition.getCallTaskId())) {
            PhoneCheckTaskEntity phoneCheckTaskEntity = phoneCheckTaskTable.queryByPk(condition.getCallTaskId());
            return Pair.of(phoneCheckTaskEntity.getCallHangupCode(), null);
        }
        if (CollectionUtils.isEmpty(condition.getPhoneInfoDTOS())) {
            return Pair.of(null, null);
        }
        List<String> placeholders = Lists.newArrayList();
        List<Object> parameters = Lists.newArrayList(condition.getPhoneInfoDTOS().size());
        for (PhoneInfoDTO phoneInfoDTO : condition.getPhoneInfoDTOS()) {
            placeholders.add("(?, ?)");
            parameters.add(phoneInfoDTO.getCountryCode());
            parameters.add(phoneInfoDTO.getPhoneNumber());
        }

        List<PhoneCheckEntity> phoneCheckEntities = phoneCheckTable.queryMany("(phone_prefix,phone_number) in (" + StringUtils.join(placeholders, ",") + ")", parameters.toArray());
        // 将phoneCheckEntities按照phonePrefix和phoneNumber字段拼接后进行分组
        Map<String, List<PhoneCheckEntity>> groupedPhoneCheckEntities = Optional.ofNullable(phoneCheckEntities).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.groupingBy(entity ->
                        (entity.getPhonePrefix() != null ? entity.getPhonePrefix() : "") +
                                (entity.getPhoneNumber() != null ? entity.getPhoneNumber() : "")
                ));


        List<PhoneCheckTaskEntity> phoneCheckTaskEntities = phoneCheckTaskTable.queryMany("(phone_prefix,phone_number) in (" + StringUtils.join(placeholders, ",") + ")", parameters.toArray());
        Map<String, List<PhoneCheckTaskEntity>> groupedPhoneCheckTskEntities = Optional.ofNullable(phoneCheckTaskEntities).orElse(Lists.newArrayList()).stream()
                .collect(Collectors.groupingBy(entity ->
                        (entity.getPhonePrefix() != null ? entity.getPhonePrefix() : "") +
                        (entity.getPhoneNumber() != null ? entity.getPhoneNumber() : "")
                ));


        for (PhoneInfoDTO phoneInfoDTO : condition.getPhoneInfoDTOS()) {
            PhoneResultDTO phoneResultDTO = new PhoneResultDTO();
            phoneResultDTO.setPhoneNumber(phoneInfoDTO.getPhoneNumber());
            phoneResultDTO.setCountryCode(phoneInfoDTO.getCountryCode());
            String key = phoneInfoDTO.getCountryCode() + phoneInfoDTO.getPhoneNumber();
            List<PhoneCheckEntity> phoneCheckEntityList = groupedPhoneCheckEntities.get(key);
            if (CollectionUtils.isNotEmpty(phoneCheckEntityList)) {
                phoneResultDTO.setIsInPhoneCheck(true);
            }
            List<PhoneCheckTaskEntity> phoneCheckTaskEntityList = groupedPhoneCheckTskEntities.get(key);
            if (CollectionUtils.isNotEmpty(phoneCheckTaskEntityList)) {
                phoneCheckTaskEntityList.stream().map(PhoneCheckTaskEntity::getCallHangupCode)
            }

        }



        // TODO: 根据业务需求处理分组后的数据并转换为PhoneResultDTO
        // 这里需要根据具体的业务逻辑来实现PhoneResultDTO的构建
        List<PhoneResultDTO> phoneResultDTOs = Lists.newArrayList();

        return Pair.of(null, phoneResultDTOs);
    }
}
