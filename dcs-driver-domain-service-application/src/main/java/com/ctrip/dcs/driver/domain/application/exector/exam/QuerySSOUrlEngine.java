package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.common.CommonExamSSOUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.exam.QuerySSOUrlRequestType;
import com.ctrip.dcs.driver.domain.exam.QuerySSOUrlResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class QuerySSOUrlEngine
    extends ShoppingExecutor<QuerySSOUrlRequestType, QuerySSOUrlResponseType>
    implements Validator<QuerySSOUrlRequestType> {
  @Autowired
  private DomainBeanFactory domainBeanFactory;

  @Override
  public QuerySSOUrlResponseType execute(QuerySSOUrlRequestType querySSOUrlRequestType) {
    QuerySSOUrlResponseType response = new QuerySSOUrlResponseType();
    CommonExamSSOUtils commonExamSSOUtils = this.domainBeanFactory.commonExamSSOUtils();
    String oauthDomainName = this.domainBeanFactory.examInterfaceConfig().getOauthDomainName();
    String oauthUrl = this.domainBeanFactory.examInterfaceConfig().getOauthUrl();
    String encryptStr = commonExamSSOUtils.encryptByAES(querySSOUrlRequestType.examAccountId);
    String encodeStr = commonExamSSOUtils.encode(encryptStr);
    String formatUrl = String.format(oauthUrl, encodeStr);
    response.setOauthUrl(oauthDomainName + formatUrl);
    return ServiceResponseUtils.success(response);
  }

  @Override
  public void validate(AbstractValidator<QuerySSOUrlRequestType> validator) {
    validator.ruleFor("examAccountId").notNull().notEmpty();
  }
}
