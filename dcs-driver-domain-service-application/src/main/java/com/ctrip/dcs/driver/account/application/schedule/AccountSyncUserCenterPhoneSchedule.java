package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.dcs.driver.account.application.helper.AccountChangeLogHelper;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLogType;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

import java.util.List;

@Component
public class AccountSyncUserCenterPhoneSchedule {
    Logger log = LoggerFactory.getLogger(AccountSyncUserCenterPhoneSchedule.class);

    @Autowired
    private AccountBaseInfoDao accountBaseInfoDao;
    @Autowired
    private UserCenterAccountGateway userCenterAccountGateway;
    @Autowired
    private AccountChangeLogHelper accountChangeLogHelper;
    @Autowired
    private DriverAccountConfig driverAccountConfig;

    @QSchedule("dcs.driver.account.sync.usercenter.phone.job")
    public void onExecute(Parameter parameter) {
        // 指定uid
        String uids = parameter.getString("uidList");
        String fixFlag = parameter.getString("fixFlag");
        if (StringUtils.isBlank(uids)) {
            return;
        }
        log.info("account sync user center phone start", "uids : " + uids);
        List<String> uidList = Lists.newArrayList(uids.split(","));
        if (CollectionUtils.isEmpty(uidList)) {
            return;
        }
        List<AccountBaseInfoPO> accountBaseInfoPOList = accountBaseInfoDao.batchQueryByUID(uidList);
        batchFix(accountBaseInfoPOList, StringUtils.defaultIfBlank(fixFlag, "0"));
    }

    private void batchFix(List<AccountBaseInfoPO> accountBaseInfoPOList, String fixFlag) {
        for (AccountBaseInfoPO accountBaseInfoPO : accountBaseInfoPOList) {
            if (driverAccountConfig.getJobSleepMills() > 0) {
                try {
                    Thread.sleep(driverAccountConfig.getJobSleepMills());
                } catch (Exception e) {
                    log.warn("sleep exception", e);
                }
            }
            AccountInfoResponseType account = userCenterAccountGateway.getAccountByUid(accountBaseInfoPO.getUid());
            if (account == null) {
                Cat.logEvent(CatEventType.JOB_SYNC_USER_CENTER_PHONE_RESULT, "NoAccount");
                log.warn("account sync user center phone no account", accountBaseInfoPO.getUid());
                continue;
            }
            if (ObjectUtils.notEqual(accountBaseInfoPO.getCountryCode(), account.getCountryCode()) || ObjectUtils.notEqual(accountBaseInfoPO.getPhoneNumber(), account.getPhoneNumber())) {
                Cat.logEvent(CatEventType.JOB_SYNC_USER_CENTER_PHONE_RESULT, "PhoneConflict");
                log.warn("account sync user center phone error phone conflict", String.format("uid : %s, table : %s-%s, account : %s-%s",
                        accountBaseInfoPO.getUid(), accountBaseInfoPO.getCountryCode(), accountBaseInfoPO.getPhoneNumber(), account.getCountryCode(), account.getPhoneNumber()));
                if ("1".equals(fixFlag)) {
                    accountBaseInfoPO.setCountryCode(account.getCountryCode());
                    accountBaseInfoPO.setPhoneNumber(account.getPhoneNumber());
                    accountBaseInfoPO.setModifyUser("System");
                    accountBaseInfoDao.update(accountBaseInfoPO);
                    accountChangeLogHelper.saveLog(new AccountChangeLog(AccountChangeLogType.SYNC_USER_CENTER_PHONE, "Job", accountBaseInfoPO.getUid(),
                            accountBaseInfoPO.getCountryCode() + "-" + accountBaseInfoPO.getPhoneNumber(), account.getCountryCode() + "-" + account.getPhoneNumber()));

                }
                continue;
            }
            Cat.logEvent(CatEventType.JOB_SYNC_USER_CENTER_PHONE_RESULT, "Success");
        }
    }

}
