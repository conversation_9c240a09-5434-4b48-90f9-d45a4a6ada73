package com.ctrip.dcs.driver.domain.application.domain.finance;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exector.FinanceResetPasswordExecutor;
import com.ctrip.dcs.driver.domain.application.domain.exector.FinanceWithdrawExecutor;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.BalanceRecordModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.BankCardModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.supply.driver.dto.Balance;
import com.ctrip.dcs.supply.driver.dto.BalanceRecordDTO;
import com.ctrip.dcs.supply.driver.dto.CardInfo;
import com.google.gson.annotations.Expose;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.owasp.csrfguard.util.Strings;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * 司机钱包
 * */
public class FinanceObjectImpl extends FinanceObject {
  private final DomainBeanFactory domainBeanFactory;
  private final long driverId;

  /**
   * 新司机
   * */
  public FinanceObjectImpl(long driverId, boolean isOldProcess, DomainBeanFactory domainBeanFactory){
    this.domainBeanFactory = domainBeanFactory;
    this.driverId = driverId;
    this.isOldProcess = isOldProcess;
  }

  /**
   * 查询银行卡列表
   * */
  private void searchBankcardList() {
    this.bankCardList = new ArrayList<>();
    List<CardInfo> cardInfoList = this.domainBeanFactory.financeConvertService().queryDriverCardList(this.driverId);
    if (CollectionUtils.isNotEmpty(cardInfoList)) {
      cardInfoList.forEach(c -> {
        BankCardModel bankCardModel = new BankCardModel();
        bankCardModel.setBankId(ObjectUtils.defaultIfNull(c.bankId, Strings.EMPTY));
        bankCardModel.setBankCard(ObjectUtils.defaultIfNull(c.bankCard, Strings.EMPTY));
        bankCardModel.setBankName(ObjectUtils.defaultIfNull(c.bankName, Strings.EMPTY));
        if(StringUtils.isNoneBlank(bankCardModel.getBankCard())) {
          this.bankCardList.add(bankCardModel);
        }
      });
    }
  }

  /**
   * 查询提现记录
   * */
  private void searchRecordList(String beginDate, String endDate) {
    this.balanceRecordList = new ArrayList<>();
    List<BalanceRecordDTO> cardInfoList = this.domainBeanFactory.financeConvertService().queryDriverBalanceRecord(this.driverId, beginDate, endDate);
    if (CollectionUtils.isNotEmpty(cardInfoList)) {
      cardInfoList.forEach(r -> {
        BalanceRecordModel record = new BalanceRecordModel();
        record.setRecordId(ObjectUtils.defaultIfNull(r.recordId, 0L));
        record.setAmount(ObjectUtils.defaultIfNull(r.amount, BigDecimal.ZERO));
        record.setBankString(ObjectUtils.defaultIfNull(r.bankString, Strings.EMPTY));
        record.setRecordTime(ObjectUtils.defaultIfNull(r.recordTime, Strings.EMPTY));
        record.setApplyStatus(ObjectUtils.defaultIfNull(r.applyStatus, 0));
        record.setApplyStatusDesc(ObjectUtils.defaultIfNull(r.applyStatusDesc, Strings.EMPTY));
        this.balanceRecordList.add(record);
      });
    }
  }

  /**
   * 查询余额
   * */
  private void searchBalance() {
    this.balanceCode = FinanceResultEnum.ERROR.getCode();
    this.balanceAmount = BigDecimal.ZERO;
    this.maxWithDrawAmount = BigDecimal.ZERO;
    this.minWithDrawAmount = BigDecimal.ZERO;
    this.withDrawLimitCount = 0;
    this.withDrawCount = 0;
    this.initTag = 0;
    Pair<String, Balance> balancePair = this.domainBeanFactory.financeConvertService()
            .queryDriverWithdrawBalance(this.driverId, this.isOldProcess);
    if(Objects.nonNull(balancePair)) {
      this.balanceCode = balancePair.getLeft();
      if(Objects.nonNull(balancePair.getRight())){
        this.balanceAmount = ObjectUtils.defaultIfNull(balancePair.getRight().balance, BigDecimal.ZERO);
        this.maxWithDrawAmount = ObjectUtils.defaultIfNull(balancePair.getRight().maxAmount, BigDecimal.ZERO);
        this.minWithDrawAmount = ObjectUtils.defaultIfNull(balancePair.getRight().minAmount, BigDecimal.ZERO);
        this.withDrawLimitCount = ObjectUtils.defaultIfNull(balancePair.getRight().withdrawLimit, 0);
        this.withDrawCount = ObjectUtils.defaultIfNull(balancePair.getRight().withdrawCount, 0);
        this.initTag = ObjectUtils.defaultIfNull(balancePair.getRight().initTag, 0);
      }
    }
  }

  @Expose
  private boolean isOldProcess = false;
  @Expose
  private String balanceCode;
  @Expose
  private BigDecimal balanceAmount;
  @Expose
  private BigDecimal maxWithDrawAmount;
  @Expose
  private BigDecimal minWithDrawAmount;
  @Expose
  private int withDrawCount;
  @Expose
  private int withDrawLimitCount;
  @Expose
  private int initTag;
  @Expose
  private List<BankCardModel> bankCardList;
  @Expose
  private List<BalanceRecordModel> balanceRecordList;

  @Override
  public String balanceCode(){
    return this.balanceCode;
  }

  @Override
  public BigDecimal balanceAmount(){
    return this.balanceAmount;
  }

  @Override
  public BigDecimal maxWithDrawAmount(){
    return this.maxWithDrawAmount;
  }

  @Override
  public BigDecimal minWithDrawAmount(){
    return this.minWithDrawAmount;
  }

  @Override
  public int withDrawCount(){
    return this.withDrawCount;
  }

  @Override
  public int withDrawLimitCount(){
    return this.withDrawLimitCount;
  }

  @Override
  public boolean isHasBankCard(){
    return CollectionUtils.isNotEmpty(this.bankCardList);
  }

  @Override
  public boolean isHasPassword(){
    return ObjectUtils.defaultIfNull(this.initTag, 0) == 1;
  }

  @Override
  public List<BankCardModel> bankCardList(){
    this.searchBankcardList();
    return this.bankCardList;
  }

  @Override
  public List<BalanceRecordModel> balanceRecordList(String beginDate, String endDate) {
    searchRecordList(beginDate, endDate);
    return this.balanceRecordList;
  }

  @Override
  public String withdraw(String password,
                         BigDecimal amount, String bankCard,
                         String bankCardInfo, String driverName, String bankCardId) {
    WithdrawModel withdrawModel = new WithdrawModel();
    withdrawModel.setDriverId(this.driverId);
    withdrawModel.setPassword(password);
    withdrawModel.setAmount(amount);
    withdrawModel.setBankCard(bankCard);
    withdrawModel.setBankCardInfo(bankCardInfo);
    withdrawModel.setDriverName(driverName);
    withdrawModel.setOldProcess(this.isOldProcess);
    withdrawModel.setBankCardId(bankCardId);
    return new FinanceWithdrawExecutor(this, this.domainBeanFactory).doWork(withdrawModel);
  }

  @Override
  public String resetPassword(String password, String identitycode) {
    WithdrawModel withdrawModel = new WithdrawModel();
    withdrawModel.setDriverId(this.driverId);
    withdrawModel.setPassword(password);
    withdrawModel.setIdentitycode(identitycode);
    withdrawModel.setOldProcess(this.isOldProcess);
    return new FinanceResetPasswordExecutor(this, this.domainBeanFactory).doWork(withdrawModel);
  }

  @Override
  public void queryBalance(){
    this.searchBalance();
  }
}
