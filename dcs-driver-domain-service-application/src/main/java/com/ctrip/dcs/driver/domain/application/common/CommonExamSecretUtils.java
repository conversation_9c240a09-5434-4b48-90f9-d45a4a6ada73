package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.driver.domain.infrastructure.qconfig.KmsTokenConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CommonExamSecretUtils {
  @Autowired
  private CommonLogger logger;

  @Autowired
  private KmsTokenConfig kmsTokenConfig;
  private String secret;

  public String secret() {
    return this.secret;
  }

  @Autowired
  public void initialize() throws KMSUtils.KmsException {

    String token = kmsTokenConfig.getExexmInterfaceAccessToken();
    if (StringUtils.isBlank(token)) {
      throw new KMSUtils.KmsException("CommonExamSecretUtils token empty");
    }
    this.secret = KMSUtils.key(token);
    if (StringUtils.isBlank(this.secret)) {
      throw new KMSUtils.KmsException("CommonExamSecretUtils initialize error");
    }
    this.logger.info("KMS", "CommonExamSecretUtils key: [" + this.secret + "]");
  }
}
