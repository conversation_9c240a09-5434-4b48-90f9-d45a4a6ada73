package com.ctrip.dcs.driver.domain.application.redis.obj;

/**
 * Created by <AUTHOR> on 2023/3/5 21:23
 */
public class NoticeIdDTO {
  private long noticeId;
  private int idType;
  private String productType;
  private String userType;
  private int driverStatus;
  private String countryIds;
  private String cityIds;
  private String vehicleTypes;
  private String startDate;
  private String endDate;

  public long getNoticeId() {
    return noticeId;
  }

  public void setNoticeId(long noticeId) {
    this.noticeId = noticeId;
  }

  public int getIdType() {
    return idType;
  }

  public void setIdType(int idType) {
    this.idType = idType;
  }

  public String getProductType() {
    return productType;
  }

  public void setProductType(String productType) {
    this.productType = productType;
  }

  public int getDriverStatus() {
    return driverStatus;
  }

  public void setDriverStatus(int driverStatus) {
    this.driverStatus = driverStatus;
  }

  public String getCountryIds() {
    return countryIds;
  }

  public void setCountryIds(String countryIds) {
    this.countryIds = countryIds;
  }

  public String getCityIds() {
    return cityIds;
  }

  public void setCityIds(String cityIds) {
    this.cityIds = cityIds;
  }

  public String getVehicleTypes() {
    return vehicleTypes;
  }

  public void setVehicleTypes(String vehicleTypes) {
    this.vehicleTypes = vehicleTypes;
  }

  public String getStartDate() {
    return startDate;
  }

  public void setStartDate(String startDate) {
    this.startDate = startDate;
  }

  public String getEndDate() {
    return endDate;
  }

  public void setEndDate(String endDate) {
    this.endDate = endDate;
  }

  public String getUserType() {
    return userType;
  }

  public void setUserType(String userType) {
    this.userType = userType;
  }
}
