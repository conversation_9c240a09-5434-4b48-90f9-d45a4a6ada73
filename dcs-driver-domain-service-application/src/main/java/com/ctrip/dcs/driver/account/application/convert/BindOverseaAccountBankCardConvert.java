package com.ctrip.dcs.driver.account.application.convert;
/*
作者：pl.yang
创建时间：2025/4/28-上午11:20-2025
*/


import cn.hutool.core.text.CharSequenceUtil;
import com.ctrip.dcs.driver.account.domain.condition.BindOverseaBankCardCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.infrastructure.constant.IdCardTypeEnum;
import com.ctrip.dcs.driver.domain.account.BankCardInfoDTO;
import com.ctrip.dcs.driver.domain.account.BindOverseaAccountBankCardRequestType;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BindOverseaAccountBankCardConvert
 * @Package com.ctrip.dcs.driver.account.application.convert
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 上午11:20
 */

@Component
public class BindOverseaAccountBankCardConvert {
    @Autowired
    ArchCoreInfoRepository archCoreInfoService;
    public BindOverseaBankCardCondition convert(BindOverseaAccountBankCardRequestType requestType) {
        BindOverseaBankCardCondition.BindOverseaBankCardConditionBuilder builder = BindOverseaBankCardCondition.builder();
        builder.driverId(requestType.getDriverId());
        builder.uid(requestType.getUid());
        builder.bankCardInfo(convertBandCardInfo(requestType,requestType.getBankCardInfo()));
        return builder.build();


    }

    private AccountBankCardDo convertBandCardInfo(BindOverseaAccountBankCardRequestType cardRequestType, BankCardInfoDTO bankCardInfoList) {
        AccountBankCardDo.AccountBankCardDoBuilder builder = AccountBankCardDo.builder();
        builder.uid(cardRequestType.getUid());
//        //todo 密码对齐下
//        String idCardNoDe = archCoreInfoService.encryptByType(KeyType.Identity_Card, bankCardInfoList.getIdNumber());

        builder
                //持卡人 名字 如果境外的卡片，请输入英文
                .accountName(bankCardInfoList.getAccountName())
                .accountCurrency(bankCardInfoList.getAccountCurrency())
                //condition.isMainland() ? "DOMESTIC_SUPPLIER" : "OVERSEAS_SUPPLIER"; //境内境外卡
                .isOverseaBankCard(CharSequenceUtil.equals("OVERSEAS_SUPPLIER", bankCardInfoList.getType()))
                .address(bankCardInfoList.getAddress())
                .cardNo(bankCardInfoList.getCardNo())

                .bankNameCode(bankCardInfoList.getBankNameCode())
                .bankName(bankCardInfoList.getBankName())
                .bankAddress(bankCardInfoList.getBankAddress())
                .bankCode(bankCardInfoList.getBankCode())
                .branchCode(bankCardInfoList.getBranchCode())

                .provinceCode(bankCardInfoList.getProvinceCode())
                .province(bankCardInfoList.getProvince())

                .idType(IdCardTypeEnum.getByIdString(bankCardInfoList.getIdType()))
                .idNumber(bankCardInfoList.getIdNumber())


                .fedwireNumber(bankCardInfoList.getFedwireNumber())
                .sortCode(bankCardInfoList.getSortCode())
                .iban(bankCardInfoList.getIban())
                .postCode(bankCardInfoList.getPostCode())
                .abaNumber(bankCardInfoList.getAbaNumber())
                .email(bankCardInfoList.getEmail())

                .phoneCountryCode(bankCardInfoList.getPhoneCountryCode())
                .phoneNo(bankCardInfoList.getPhoneNo())
                .routingNumber(bankCardInfoList.getRoutingNumber())
                .ifsCode(bankCardInfoList.getIfsCode())
                .swiftCode(bankCardInfoList.getSwiftCode())

                .cityId(bankCardInfoList.getCityId())
                .countryId(bankCardInfoList.getCountryId())
                .uid(cardRequestType.getUid())
                .driverId(cardRequestType.getDriverId())
                .firstName(bankCardInfoList.getFirstName())
                .middleName(bankCardInfoList.getMiddleName())
                .lastName(bankCardInfoList.getLastName())
                .yeePayCountryThreeCode(bankCardInfoList.getYeePayCountryThreeCode())
                .yeePayCityName(bankCardInfoList.getYeePayCityName())
                .branchName(bankCardInfoList.getBranchName())
                .build();
        return  builder.build();
    }
}
