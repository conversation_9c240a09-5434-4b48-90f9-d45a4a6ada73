package com.ctrip.dcs.driver.domain.application.http.constant;

public enum ExamRequestEnum {
  QUERY_DEPT_INFO(1,"queryDeptInfo"),

  CHANGE_USER_INFO(2,"changeUserInfo"),

  QUERY_USER_INFO(3,"queryUserInfo"),

  QUERY_USER_EXAM_INFO(4,"queryUserExamInfo"),

  OPEN_AUTH_INTERFACE(5,"openAuthInterface")
  ;

  private int code;
  private String name;

  ExamRequestEnum(int code, String name) {
    this.code = code;
    this.name = name;
  }

  public static ExamRequestEnum getByCode(Integer code) {
    for (ExamRequestEnum examRequestEnum : ExamRequestEnum.values()) {
      if (examRequestEnum.code == code) {
        return examRequestEnum;
      }
    }
    return QUERY_DEPT_INFO;
  }

  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }
}
