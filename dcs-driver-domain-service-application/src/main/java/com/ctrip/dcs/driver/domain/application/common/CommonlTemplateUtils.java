package com.ctrip.dcs.driver.domain.application.common;

import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.springframework.stereotype.Component;

import java.io.StringWriter;
import java.util.Map;

@Component
public final class CommonlTemplateUtils {

  static {
    Velocity.init();
  }

  public CommonlTemplateUtils() {}

  public String evaluate(String template, Map<String, Object> parameters) {
    if (MapUtils.isEmpty(parameters) || StringUtils.isBlank(template)) {
      return ObjectUtils.defaultIfNull(template, StringUtils.EMPTY);
    }

    return ShoppingFuncUtils.tryFunc(() -> {
      VelocityContext context = new VelocityContext(parameters);
      StringWriter w = new StringWriter();
      Velocity.evaluate(context, w, this.getClass().getSimpleName(), template);
      return w.toString();
    }, template);
  }

}
