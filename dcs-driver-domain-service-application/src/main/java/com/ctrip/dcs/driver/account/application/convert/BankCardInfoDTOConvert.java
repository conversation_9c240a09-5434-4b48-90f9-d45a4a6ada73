package com.ctrip.dcs.driver.account.application.convert;
/*
作者：pl.yang
创建时间：2025/5/8-上午11:32-2025
*/


import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.infrastructure.constant.IdCardTypeEnum;
import com.ctrip.dcs.driver.domain.account.BankCardInfoDTO;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BankCardInfoDTOConvert
 * @Package com.ctrip.dcs.driver.account.application.convert
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 上午11:32
 */

@Component
public class BankCardInfoDTOConvert {
    public BankCardInfoDTO convertBankCardInfo(AccountBankCardDo bankCardInfoDo) {
        if(bankCardInfoDo==null){
            return null;
        }
        BankCardInfoDTO bankCardInfoDTO = new BankCardInfoDTO();
        bankCardInfoDTO.setCountryId( bankCardInfoDo.getCountryId());
        bankCardInfoDTO.setCityId (bankCardInfoDo.getCityId());
        bankCardInfoDTO.setAccountName(bankCardInfoDo.getAccountName());
        bankCardInfoDTO.setAccountCurrency(bankCardInfoDo.getAccountCurrency());
        bankCardInfoDTO.setType(bankCardInfoDo.getType());
        bankCardInfoDTO.setAddress(bankCardInfoDo.getAddress());
        bankCardInfoDTO.setCardNo(bankCardInfoDo.getCardNo());
        bankCardInfoDTO.setBankNameCode(bankCardInfoDo.getBankNameCode());
        bankCardInfoDTO.setBankName(bankCardInfoDo.getBankName());
        bankCardInfoDTO.setBankAddress(bankCardInfoDo.getBankAddress());
        bankCardInfoDTO.setBankCode(bankCardInfoDo.getBankCode());
        bankCardInfoDTO.setBranchCode(bankCardInfoDo.getBranchCode());
        bankCardInfoDTO.setProvinceCode(bankCardInfoDo.getProvinceCode());
        bankCardInfoDTO.setProvince(bankCardInfoDo.getProvince());
        IdCardTypeEnum idType = bankCardInfoDo.getIdType();
        if(idType!=null){
            bankCardInfoDTO.setIdType(idType.getCode()+"");
        }
        bankCardInfoDTO.setIdNumber(bankCardInfoDo.getIdNumber());
        bankCardInfoDTO.setFedwireNumber(bankCardInfoDo.getFedwireNumber());
        bankCardInfoDTO.setSortCode(bankCardInfoDo.getSortCode());
        bankCardInfoDTO.setIban(bankCardInfoDo.getIban());
        bankCardInfoDTO.setPostCode(bankCardInfoDo.getPostCode());
        bankCardInfoDTO.setAbaNumber(bankCardInfoDo.getAbaNumber());
        bankCardInfoDTO.setEmail(bankCardInfoDo.getEmail());
        bankCardInfoDTO.setPhoneCountryCode(bankCardInfoDo.getPhoneCountryCode());
        bankCardInfoDTO.setPhoneNo(bankCardInfoDo.getPhoneNo());
        bankCardInfoDTO.setRoutingNumber(bankCardInfoDo.getRoutingNumber());
        bankCardInfoDTO.setIfsCode(bankCardInfoDo.getIfsCode());
        bankCardInfoDTO.setSwiftCode(bankCardInfoDo.getSwiftCode());
        bankCardInfoDTO.setFirstName(bankCardInfoDo.getFirstName());
        bankCardInfoDTO.setMiddleName(bankCardInfoDo.getMiddleName());
        bankCardInfoDTO.setLastName(bankCardInfoDo.getLastName());
        bankCardInfoDTO.setYeePayCountryThreeCode(bankCardInfoDo.getYeePayCountryThreeCode());
        bankCardInfoDTO.setYeePayCityName(bankCardInfoDo.getYeePayCityName());
        bankCardInfoDTO.setBranchName(bankCardInfoDo.getBranchName());
        return bankCardInfoDTO;

    }
}
