package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.redis.AppControlRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.DriverAppControlService;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverAppGuidanceControlDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverAppGuidanceControlPO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DriverAppControlServiceImpl implements DriverAppControlService {
    @Autowired
    private DriverAppGuidanceControlDao dao;
    @Autowired
    private AppControlRedisLogic cache;

    @Override
    public Map<String, Boolean> queryDriverAppGuidance(String uid, List<String> guidanceTypes) {
        Map<String, Boolean> result = guidanceTypes.stream().collect(Collectors.toMap(e -> e, e -> false));
        // cache
        Map<String, String> cacheMap = cache.getGuidance(uid);
        cacheMap.forEach((key, value) -> result.put(key, Boolean.valueOf(value)));

        // db
        List<String> noCacheTypes = guidanceTypes.stream().filter(m -> !cacheMap.containsKey(m)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noCacheTypes)) {
            List<DriverAppGuidanceControlPO> guidanceList = dao.findMany(uid, noCacheTypes);
            if (CollectionUtils.isNotEmpty(guidanceList)) {
                guidanceList.forEach(o -> result.put(o.getGuidanceType(), true));
                // reset cache
                List<String> needCacheTypes = guidanceList.stream().map(DriverAppGuidanceControlPO::getGuidanceType).collect(Collectors.toList());
                cache.cacheGuidance(uid, needCacheTypes);
            }
        }
        return result;
    }

    @Override
    public void saveDriverAppGuidance(String uid, String guidanceType) {
        DriverAppGuidanceControlPO value = new DriverAppGuidanceControlPO();
        value.setUid(uid);
        value.setGuidanceType(guidanceType);
        value.setClickTime(LocalDateTime.now());
        dao.insert(value);
    }
}
