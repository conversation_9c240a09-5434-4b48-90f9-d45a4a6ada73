package com.ctrip.dcs.driver.account.application.exector;
/*
作者：pl.yang
创建时间：2025/5/22-下午5:32-2025
*/


import com.ctrip.dcs.driver.domain.infrastructure.utils.RegionContextUtil;
import com.ctrip.igt.HasIGTRequestHeader;
import com.ctrip.igt.HasIGTResponseResult;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AbDriverDomainShoppingExecutor
 * @Package com.ctrip.dcs.driver.account.application.exector
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/22 下午5:32
 */


public abstract class AbDriverDomainShoppingExecutor<Request extends HasIGTRequestHeader, Response extends HasIGTResponseResult> extends AbstractRpcExecutor<Request, Response> {
    @Override
    public Response onException(Request req, Exception ex) {
        if (RegionContextUtil.isLocalReginContext()||RegionContextUtil.isFatReginContext()) {
            if (ex != null) {
                ex.printStackTrace();
            }
        }

        return super.onException(req, ex);
    }
}
