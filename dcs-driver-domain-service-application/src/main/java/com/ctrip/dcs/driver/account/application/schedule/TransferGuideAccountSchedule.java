package com.ctrip.dcs.driver.account.application.schedule;

import com.ctrip.dcs.driver.account.application.exector.SyncAccountEngine;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.account.infrastructure.gateway.EmailServiceGateway;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.EmailNotifyConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.account.infrastructure.value.SendEmailCondition;
import com.ctrip.dcs.driver.account.infrastructure.value.emil.EmailConfigInfo;
import com.ctrip.dcs.driver.domain.account.SyncAccountRequestType;
import com.ctrip.dcs.driver.domain.account.SyncAccountResponseType;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.service.GmsTransportDomainProxyService;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideAllInfoDTO;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class TransferGuideAccountSchedule {
    Log log = Log.getInstance(TransferGuideAccountSchedule.class);

    @Autowired
    private AccountMapperDao accountMapperDao;
    @Autowired
    private GmsTransportDomainProxyService gmsTransportDomainProxyService;
    @Autowired
    private SyncAccountEngine syncAccountEngine;
    @Autowired
    private CityRepository cityRepository;
    @Autowired
    private DirectorRedis directorRedis;
    @Autowired
    private EmailServiceGateway emailServiceGateway;
    @Autowired
    private EmailNotifyConfig emailNotifyConfig;
    @Autowired
    private DriverAccountConfig driverAccountConfig;

    private static final String PROGRESS_KEY = "dcs.driver.account.transfer.guide.job.progress";

    private static final long PROCESS_KEY_EXPIRE_SECONDS = 30 * 24 * 3600;

    @QSchedule("dcs.driver.account.transfer.guide.job")
    public void onExecute(Parameter parameter) {
        List<Pair<String, String>> failedGuideList = Lists.newArrayList();

        // 指定uid
        String uids = parameter.getString("uidList");
        if (StringUtils.isNotBlank(uids)) {
            log.info("sync guide account job start", "uidList : " + uids);
            List<String> uidList = Lists.newArrayList(uids.split(","));
            if (CollectionUtils.isNotEmpty(uidList)) {
                List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryByUid(uidList);
                accountMapperPOList = accountMapperPOList.stream()
                        .filter(o -> AccountSouceEnum.isGuide(o.getSource()))
                        .sorted(Comparator.comparing(o -> Long.valueOf(o.getSourceId())))
                        .collect(Collectors.toList());
                batchSync(accountMapperPOList, failedGuideList);
            }
            sendSyncEmail(failedGuideList);
            return;
        }

        TaskMonitor taskMonitor = TaskHolder.getKeeper();

        Long startId = getStartId(parameter);
        log.info("sync guide account job start", "startId : " + startId);
        while (true) {
            if (taskMonitor.isStopped()) {
                log.error("sync guide account job stop", "current id : " + startId);
                break;
            }
            log.info("sync guide account", "current id : " + startId);
            List<AccountMapperPO> accountMapperPOList = accountMapperDao.batchQueryByPage(startId, 100);
            if (CollectionUtils.isEmpty(accountMapperPOList)) {
                break;
            }
            batchSync(accountMapperPOList, failedGuideList);
            startId = accountMapperPOList.get(accountMapperPOList.size() - 1).getId();
            directorRedis.set(PROGRESS_KEY, startId, PROCESS_KEY_EXPIRE_SECONDS);
        }
        sendSyncEmail(failedGuideList);
        log.info("sync guide account finish", "last id : " + startId);
    }

    private Long getStartId(Parameter parameter) {
        String startId = parameter.getString("startId");
        if (StringUtils.isNotBlank(startId)) {
            log.info("bind guide mobile phone get start id from param", startId);
            return Long.valueOf(startId);
        }
        String redisStartId = directorRedis.get(PROGRESS_KEY);
        if (StringUtils.isNotBlank(redisStartId)) {
            log.info("bind guide mobile phone get start id from redis", redisStartId);
            return Long.valueOf(redisStartId);
        }
        log.info("bind guide mobile phone get start id from 0", "0");
        return 0L;
    }

    private void sendSyncEmail(List<Pair<String, String>> failedGuideList) {
        EmailConfigInfo emailConfig = emailNotifyConfig.getSyncGuideAccountResultConfig();
        if (CollectionUtils.isEmpty(failedGuideList) || emailConfig == null) {
            return;
        }
        String guideList = failedGuideList.stream().map(o -> String.format("guide id: %s, reason : %s", o.getLeft(), o.getRight()))
                .collect(Collectors.joining("<br/>"));
        SendEmailCondition sendEmailCondition = new SendEmailCondition();
        sendEmailCondition.setSendCode(emailConfig.getSendCode());
        sendEmailCondition.setSender(emailConfig.getSenderEmail());
        sendEmailCondition.setReceiver(Lists.newArrayList(emailConfig.getReceiverEmails().split(",")));
        sendEmailCondition.setSubject(emailConfig.getSubjectTemplate());
        sendEmailCondition.setBodyContent(String.format(emailConfig.getContentTemplate(), "sync account", guideList));
        sendEmailCondition.setBodyHtml(true);
        emailServiceGateway.sendEmail(sendEmailCondition);
    }

    private void batchSync(List<AccountMapperPO> accountMapperPOList, List<Pair<String, String>> failedGuideList) {
        for (AccountMapperPO accountMapperPO : accountMapperPOList) {
            // 筛选向导身份
            if (!AccountSouceEnum.isGuide(accountMapperPO.getSource())) {
                continue;
            }
            try {
                GuideAllInfoDTO guideAllInfoDTO = gmsTransportDomainProxyService.queryGuideAllInfo(Long.parseLong(accountMapperPO.getSourceId()));
                if (guideAllInfoDTO == null) {
                    Cat.logEvent(CatEventType.SYNC_GUIDE_ACCOUNT, "no_guide");
                    failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), "no_guide"));
                    log.warn("sync guide account failed", "guide detail info is null, guide id: " + accountMapperPO.getSourceId());
                    continue;
                }
                if (StringUtils.isBlank(guideAllInfoDTO.getTelAreaCode()) || StringUtils.isBlank(guideAllInfoDTO.getTelephoneNum())) {
                    Cat.logEvent(CatEventType.SYNC_GUIDE_ACCOUNT, "no_phone");
                    failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), "no_phone"));
                    log.warn("sync guide account failed", "guide phone is null, guide id: " + accountMapperPO.getSourceId());
                    continue;
                }
                SyncAccountRequestType request = new SyncAccountRequestType();
                request.setSource(AccountSouceEnum.GUIDE_SOURCE.getName());
                request.setSourceId(guideAllInfoDTO.getGuideId().toString());
                request.setCountryCode(guideAllInfoDTO.getTelAreaCode());
                request.setPhoneNumber(guideAllInfoDTO.getTelephoneNum());
                request.setName(guideAllInfoDTO.getName());
                request.setIdCardNo(guideAllInfoDTO.getIdentityCardNum());
                request.setEmail(guideAllInfoDTO.getEmail());
                request.setIsValid(true);
                if (StringUtils.isNotBlank(guideAllInfoDTO.getBuid())) {
                    request.setUid(guideAllInfoDTO.getBuid());
                }
                City city = cityRepository.findOne(Long.valueOf(guideAllInfoDTO.getCity().getKey()));
                if (city != null) {
                    request.setIsOversea(BooleanUtils.isFalse(city.isChineseMainland()));
                }

                SyncAccountResponseType resp = syncAccountEngine.execute(request);
                if (resp != null && StringUtils.isNotBlank(resp.getUid())) {
                    Cat.logEvent(CatEventType.SYNC_GUIDE_ACCOUNT, "success");
                    log.info("sync guide account success", String.format("guideId: %s, uid: %s", guideAllInfoDTO.getGuideId(), resp.getUid()));
                } else {
                    Cat.logEvent(CatEventType.SYNC_GUIDE_ACCOUNT, "failed");
                    failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), resp == null ? "failed" : resp.getResponseResult().getReturnMessage()));
                    log.warn("sync guide account failed", String.format("guideId: %s, resp: %s", guideAllInfoDTO.getGuideId(), JsonUtil.toString(resp)));
                }
                if (driverAccountConfig.getJobSleepMills() > 0) {
                    try {
                        Thread.sleep(driverAccountConfig.getJobSleepMills());
                    } catch (Exception e) {
                        log.warn("sleep exception", e);
                    }
                }
            } catch (Exception e) {
                Cat.logEvent(CatEventType.SYNC_GUIDE_ACCOUNT, "exception");
                failedGuideList.add(Pair.of(accountMapperPO.getSourceId(), "exception"));
                log.warn("sync guide account exception", String.format("guideId: %s", accountMapperPO.getSourceId()), e, Maps.newHashMap());
            }
        }
    }
}
