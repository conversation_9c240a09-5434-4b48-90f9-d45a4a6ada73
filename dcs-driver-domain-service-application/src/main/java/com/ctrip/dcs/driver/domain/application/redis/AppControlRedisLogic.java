
package com.ctrip.dcs.driver.domain.application.redis;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AppControlRedisLogic {
    private static final String DRIVER_APP_GUIDANCE_UID = "app:guidance:%s";

    @Autowired
    private DirectorRedis directorRedis;

    public Map<String, String> getGuidance(String uid) {
        Map<String, String> result = directorRedis.hgetAll(String.format(DRIVER_APP_GUIDANCE_UID, uid));
        return result == null ? Collections.emptyMap() : result;
    }

    public boolean cacheGuidance(String uid, List<String> guidanceTypes) {
        Map<String, String> fields = new HashMap<>();
        guidanceTypes.forEach(g -> fields.put(g, "true"));
        return directorRedis.hmset(String.format(DRIVER_APP_GUIDANCE_UID, uid), fields, 3600 * 6);
    }
}
