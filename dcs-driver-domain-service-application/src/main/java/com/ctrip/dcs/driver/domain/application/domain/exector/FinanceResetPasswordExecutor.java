package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.driver.value.finance.FinanceObject;

public class FinanceResetPasswordExecutor extends DomainExecutor<FinanceObject, WithdrawModel, String> {

  public FinanceResetPasswordExecutor(FinanceObject owner, DomainBeanFactory domainBeanFactory) {
    super(owner, domainBeanFactory);
  }

  @Override
  public String doWork(WithdrawModel withdrawModel) {
      return this.domainBeanFactory.financeConvertService().driverResetPsw(withdrawModel);
  }
}
