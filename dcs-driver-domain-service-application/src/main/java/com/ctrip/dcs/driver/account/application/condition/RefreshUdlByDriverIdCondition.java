package com.ctrip.dcs.driver.account.application.condition;
/*
作者：pl.yang
创建时间：2025/4/25-下午7:03-2025
*/


import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RefreshUdlByDriverIdCondition
 * @Package com.ctrip.dcs.driver.account.application.condition
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/25 下午7:03
 */

@Data
public class RefreshUdlByDriverIdCondition {
    private List<String> driverIds;
    private boolean refreshALLUdl;
    private long startId;
    private List<String> dbList;


}
