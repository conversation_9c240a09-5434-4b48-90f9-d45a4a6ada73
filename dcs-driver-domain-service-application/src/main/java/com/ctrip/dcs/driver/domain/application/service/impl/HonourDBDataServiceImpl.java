package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.HonourDBDataService;
import com.ctrip.dcs.driver.domain.infrastructure.constant.HonourEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi.AdmPrdTrhDriverHonorInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi.DimPrdTrhDriverMedalInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi.DimPrdTrhDriverMedalRankingDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivHonourMedalNoticeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DrivHonourRankLikeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalRankingPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourMedalNoticeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourRankLikeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import org.apache.commons.collections.CollectionUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.*;

@Component
public class HonourDBDataServiceImpl implements HonourDBDataService {

    @Autowired
    private DrivHonourMedalNoticeRecordDao drivHonourMedalNoticeRecordDao;

    @Autowired
    private DrivHonourRankLikeRecordDao drivHonourRankLikeRecordDao;

    @Autowired
    private AdmPrdTrhDriverHonorInfoDao admPrdTrhDriverHonorInfoDao;

    @Autowired
    private DimPrdTrhDriverMedalInfoDao dimPrdTrhDriverMedalInfoDao;

    @Autowired
    private DimPrdTrhDriverMedalRankingDao dimPrdTrhDriverMedalRankingDao;

    /**
     * 通用 查询最新数据批次
     * */
    @Override
    public AdmPrdTrhDriverHonorInfoPO queryMaxBatchDataInfo(){
        Long maxBatch = admPrdTrhDriverHonorInfoDao.findMaxBatch();
        if(Objects.isNull(maxBatch)){
            return null;
        }
        return admPrdTrhDriverHonorInfoDao.findMaxBatchInfo(maxBatch.longValue());
    }
    /**
     * 通用 查询最新数据
     * */
    @Override
    public AdmPrdTrhDriverHonorInfoPO queryMaxBatchDataDetail(long driverId){
        try {
            return admPrdTrhDriverHonorInfoDao.findDriverInfo(driverId);
        } catch(Exception e){
            return null;
        }
    }

    /**
     * 勋章相关 勋章总数
     * */
    @Override
    public int queryDriverMedalCount(long driverId){
        try {
            AdmPrdTrhDriverHonorInfoPO admPrdTrhDriverHonorInfoPO = this.queryMaxBatchDataDetail(driverId);
            if (Objects.nonNull(admPrdTrhDriverHonorInfoPO)) {
                return admPrdTrhDriverHonorInfoPO.getMedalGetCnt().intValue();
            }
        } catch(Exception e){
            return 0;
        }
        return 0;
    }

    /**
     * 勋章相关 提醒过的勋章信息
     * */
    @Override
    public DrivHonourMedalNoticeRecordPO queryNoticedMedalInfo(long driverId){
        try{
            return drivHonourMedalNoticeRecordDao.findOne(driverId);
        } catch(Exception e){
            return null;
        }
    }
    /**
     * 勋章相关 提醒过的勋章列表
     * */
    @Override
    public List<String> queryNoticedMedalList(long driverId){
        List<String> medalList = new ArrayList<>();
        DrivHonourMedalNoticeRecordPO noticeRecord = this.queryNoticedMedalInfo(driverId);
        if(Objects.nonNull(noticeRecord)) {
            medalList.addAll(ShoppingCollectionUtils.toList(noticeRecord.getCommonMedalNotice().split(",")));
            List<String> activeList = ShoppingCollectionUtils.toList(noticeRecord.getActiveMedalNotice().split(","));
            if(CollectionUtils.isNotEmpty(activeList)){
                String thisYear = String.valueOf(LocalDate.now().getYear());
                activeList.forEach(a -> {
                    if(a.contains(thisYear)){
                        String[] medalInfo = a.split("\\^");
                        medalList.add(medalInfo[0]);
                    }
                });
            }
            medalList.addAll(ShoppingCollectionUtils.toList(noticeRecord.getFormMedalNotice().split(",")));
        }
        return medalList;
    }
    /**
     * 勋章相关 插入提醒过的勋章信息
     * */
    @Override
    public int insertNoticedMedalInfo(DrivHonourMedalNoticeRecordPO drivHonourMedalNoticeRecordPO){
        try {
            return drivHonourMedalNoticeRecordDao.insert(drivHonourMedalNoticeRecordPO);
        } catch(Exception e){
            return 0;
        }
    }
    /**
     * 勋章相关 更新提醒过的勋章信息
     * */
    @Override
    public int updateNoticedMedalInfo(DrivHonourMedalNoticeRecordPO drivHonourMedalNoticeRecordPO){
        try {
            return drivHonourMedalNoticeRecordDao.update(drivHonourMedalNoticeRecordPO);
        } catch(Exception e){
            return 0;
        }
    }

    /**
     * 勋章相关 查询勋章基础配置
     * */
    @Override
    public List<DimPrdTrhDriverMedalInfoPO> queryBasicMedalInfo(){
        try {
            return dimPrdTrhDriverMedalInfoDao.queryAll();
        } catch(Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 勋章相关 查询新勋章的排行信息
     * */
    @Override
    public Map<String, DimPrdTrhDriverMedalRankingPO> queryDriverMedalRankInfo(long driverId){
        List<DimPrdTrhDriverMedalRankingPO> driverMedalRankingPOS;
        try{
            driverMedalRankingPOS = dimPrdTrhDriverMedalRankingDao.findDriverRankInfo(driverId);
        } catch(Exception e) {
            return new HashMap<>();
        }
        if(CollectionUtils.isNotEmpty(driverMedalRankingPOS)){
            Map<String, DimPrdTrhDriverMedalRankingPO> medalRankingPOHashMap = new HashMap<>();
            driverMedalRankingPOS.forEach(a -> {
                String medalCode = Strings.EMPTY;
                HonourEnum.MedalTypeEnum mathMedalType = HonourEnum.mathMedalType(a.getMedalType());
                if(mathMedalType != null) {
                    medalCode = HonourEnum.bulidMedalCode(mathMedalType, a.getMedalGrade());

                    medalRankingPOHashMap.put(medalCode, a);
                }
            });
            return medalRankingPOHashMap;
        }
        return new HashMap<>();
    }

    /**
     * 排行榜相关 查询城市排行榜
     * */
    @Override
    public List<RankOriginalDataModel> queryCityRankList(long batchId, long cityId, boolean isWeek){
        List<RankOriginalDataModel> rankList = new ArrayList<>();
        List<AdmPrdTrhDriverHonorInfoPO> admPrdTrhDriverHonorInfoPOS;
        try{
            if(isWeek) {
                admPrdTrhDriverHonorInfoPOS = admPrdTrhDriverHonorInfoDao.findCityWeekRankList(batchId, cityId);
                if(CollectionUtils.isNotEmpty(admPrdTrhDriverHonorInfoPOS)){
                    admPrdTrhDriverHonorInfoPOS.sort(Comparator.comparing(AdmPrdTrhDriverHonorInfoPO::getWeekOrderCnt).reversed()
                            .thenComparing(AdmPrdTrhDriverHonorInfoPO::getDrvId));
                }
            } else {
                admPrdTrhDriverHonorInfoPOS = admPrdTrhDriverHonorInfoDao.findCityMonthRankList(batchId, cityId);
                if(CollectionUtils.isNotEmpty(admPrdTrhDriverHonorInfoPOS)){
                    admPrdTrhDriverHonorInfoPOS.sort(Comparator.comparing(AdmPrdTrhDriverHonorInfoPO::getMonthOrderCnt).reversed()
                            .thenComparing(AdmPrdTrhDriverHonorInfoPO::getDrvId));
                }
            }
        } catch(Exception e) {
            return Collections.emptyList();
        }

        if(CollectionUtils.isNotEmpty(admPrdTrhDriverHonorInfoPOS)){
            admPrdTrhDriverHonorInfoPOS.forEach(m -> {
                rankList.add(convertAdmPrdTrhDriverHonorInfoPO(m));
            });
        }
        return rankList;
    }

    /**
     * 点赞相关 查询点赞信息
     * */
    @Override
    public DrivHonourRankLikeRecordPO searchHonourRankLikeInfo(int rankRefs, long driverId){
        try{
            return drivHonourRankLikeRecordDao.findOne(rankRefs, driverId);
        } catch(Exception e) {
            return null;
        }
    }
    /**
     * 点赞相关 插入点赞信息
     * */
    @Override
    public int insertHonourRankLikeInfo(DrivHonourRankLikeRecordPO drivHonourRankLikeRecordPO){
        try{
            return drivHonourRankLikeRecordDao.insert(drivHonourRankLikeRecordPO);
        } catch(Exception e) {
            return 0;
        }
    }
    /**
     * 点赞相关 更新点赞信息
     * */
    @Override
    public int updateHonourRankLikeInfo(DrivHonourRankLikeRecordPO drivHonourRankLikeRecordPO){
        try{
            return drivHonourRankLikeRecordDao.update(drivHonourRankLikeRecordPO);
        } catch(Exception e) {
            return 0;
        }
    }

    /**
     * BI数据转换
     * */
    private RankOriginalDataModel convertAdmPrdTrhDriverHonorInfoPO(AdmPrdTrhDriverHonorInfoPO honorInfoPO){
        RankOriginalDataModel rankInfoModel = new RankOriginalDataModel();
        rankInfoModel.setBatchTime(honorInfoPO.getBatchTime());
        rankInfoModel.setCityId(honorInfoPO.getCityId());
        rankInfoModel.setDataTime(honorInfoPO.getDataTime());
        rankInfoModel.setDataTimeMonth(honorInfoPO.getDataTimeMonth());
        rankInfoModel.setDataTimeWeek(honorInfoPO.getDataTimeWeek());
        rankInfoModel.setDrvId(honorInfoPO.getDrvId());
        rankInfoModel.setDrvName(honorInfoPO.getDrvName());
        rankInfoModel.setMonthOrderCnt(honorInfoPO.getMonthOrderCnt());
        rankInfoModel.setMonthRanking(honorInfoPO.getMonthRanking());
        rankInfoModel.setWeekOrderCnt(honorInfoPO.getWeekOrderCnt());
        rankInfoModel.setWeekRanking(honorInfoPO.getWeekRanking());
        return rankInfoModel;
    }
}
