package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.MesTypeEnum;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import org.apache.commons.lang3.StringUtils;

public class SendVerificationExecutor extends DomainExecutor<FinanceDriverObject, String, String> {

  public SendVerificationExecutor(FinanceDriverObject owner, DomainBeanFactory domainBeanFactory) {
    super(owner, domainBeanFactory);
  }

  @Override
  public String doWork(String driverPhone) {
      if(StringUtils.isBlank(driverPhone)) {
          return FinanceResultEnum.SEND_VERIFYCODE_FAIL.getCode();
      }

      // 发送验证码
      return this.domainBeanFactory.infrastructureServiceProxy().
              sendMessageByPhone(driverPhone, MesTypeEnum.MODIFY_WITHDRAW_PASSWORD);
  }
}
