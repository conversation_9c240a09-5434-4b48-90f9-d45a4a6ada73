package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.base.Function;
import qunar.tc.qmq.Message;

import javax.annotation.Nullable;
import java.util.UUID;

/**
 * Created by <AUTHOR> on 2022/5/24 10:31
 */
public class IdempotentCheckFunction implements Function<Message, String> {

  private static final Logger LOGGER = LoggerFactory.getLogger(IdempotentCheckFunction.class);

  @Nullable
  @Override
  public String apply(@Nullable Message message) {
    try {
      assert message != null;
      return message.getMessageId();
    } catch (Throwable e) {
      LOGGER.error(e);
      return UUID.randomUUID().toString();
    }
  }
}
