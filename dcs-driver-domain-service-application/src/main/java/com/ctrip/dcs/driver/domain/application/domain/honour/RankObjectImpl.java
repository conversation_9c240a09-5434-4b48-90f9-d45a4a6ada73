package com.ctrip.dcs.driver.domain.application.domain.honour;

import com.ctrip.dcs.driver.domain.application.common.CommonStringUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exector.SaveRankLikeExecutor;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankCityDataModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.domain.infrastructure.utils.MaskHelperUtils;
import com.ctrip.dcs.driver.value.honour.DriverRankObject;
import com.ctrip.dcs.driver.value.honour.RankObject;
import com.ctrip.dcs.shopping.utils.ShoppingArrayUtils;
import com.google.gson.annotations.Expose;
import org.apache.commons.collections.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 勋章领域
 * */
public class RankObjectImpl extends RankObject {

  private final DomainBeanFactory domainBeanFactory;

  private static final String RANK_REFS_FORMAT = "%s_%s_%s_%s_%s";
  private static final String RANK_REFS_SPLIT = "_";
  private boolean isWeek;
  private boolean isLoadData;
  private long searchDriverId;
  private List<RankCityDataModel> rankCityDataModelList = new ArrayList<>();

  @Expose
  private String rankRefs;
  @Expose
  private long batchTime;
  @Expose
  private LocalDateTime batchDataTime;
  @Expose
  private int subDataTime;
  @Expose
  private long cityId;
  @Expose
  private int totalCount;

  public RankObjectImpl(DomainBeanFactory domainBeanFactory, long driverId, String requestRankRefs) {
    this.domainBeanFactory = domainBeanFactory;
    this.searchDriverId = driverId;
    this.rankRefs = requestRankRefs;

    String[] arrayRankRefs = this.splitRankRefs(this.rankRefs);
    if(arrayRankRefs.length < 5){
      return;
    }
    this.cityId = Integer.parseInt(arrayRankRefs[0]);
    this.isWeek = Integer.parseInt(arrayRankRefs[1]) == 1;
    this.batchTime = Long.parseLong(arrayRankRefs[2]);
    this.batchDataTime = LocalDateTimeUtils.localDateTime(arrayRankRefs[3]);
    this.subDataTime = CommonStringUtils.convertTimeToInt(arrayRankRefs[4]);
    this.totalCount = 0;
  }

  public RankObjectImpl(DomainBeanFactory domainBeanFactory, long cityId, int rankType, long searchDriverId) {
    this.domainBeanFactory = domainBeanFactory;
    this.isWeek = rankType == 1;
    this.searchDriverId = searchDriverId;

    // 查询缓存的城市信息
    RankOriginalDataModel rankOriginalDataModel = this.domainBeanFactory.honourRedisLogic().getCurrentRankInfo();
    if(rankOriginalDataModel == null){
      return;
    }

    this.cityId = cityId;
    this.rankRefs = this.bulidRankRefs(rankOriginalDataModel, rankType);
    this.batchTime = rankOriginalDataModel.getBatchTime();
    this.batchDataTime = LocalDateTimeUtils.localDateTime(rankOriginalDataModel.getDataTime());
    this.subDataTime = this.isWeek ?
            CommonStringUtils.convertTimeToInt(rankOriginalDataModel.getDataTimeWeek()):
            CommonStringUtils.convertTimeToInt(rankOriginalDataModel.getDataTimeMonth());
    this.totalCount = 0;
    this.prepareRankData();
  }

  @Override
  public String rankRefs() {
    return this.rankRefs;
  }

  @Override
  public boolean isWeek() {
    return this.isWeek;
  }

  @Override
  public long batchTime() {
    return this.batchTime;
  }

  @Override
  public LocalDateTime batchDataTime() {
    return this.batchDataTime;
  }

  @Override
  public int subDataTime() {
    return this.subDataTime;
  }

  @Override
  public long cityId() {
    return this.cityId;
  }

  @Override
  public int totalCount(){
    return this.totalCount;
  }

  @Override
  public DriverRankObject queryCurrentDriverRankInfo() {
    RankCityDataModel rankCityDataModel = this.domainBeanFactory.honourRedisLogic().queryDriverRankData(this.searchDriverId, this.isWeek());
    // 过滤订单数为0的数据
    if (Objects.nonNull(rankCityDataModel)) {
      return rankCityDataModel.getOrderCnt() == 0 ? null : new DriverRankObjectImpl(this.domainBeanFactory, rankCityDataModel, this);
    }
    //查询db
    AdmPrdTrhDriverHonorInfoPO biMedalInfo = domainBeanFactory.honourDBDataService().queryMaxBatchDataDetail(this.searchDriverId);
    rankCityDataModel = convert2Model(biMedalInfo,isWeek);
    //增加缓存
    domainBeanFactory.honourRedisLogic().saveDriverRankData(this.searchDriverId, this.isWeek, rankCityDataModel);
    if (Objects.isNull(rankCityDataModel) || rankCityDataModel.getOrderCnt() == 0) {
      return null;
    }
    return new DriverRankObjectImpl(this.domainBeanFactory, rankCityDataModel, this);
  }

  private RankCityDataModel convert2Model(AdmPrdTrhDriverHonorInfoPO biMedalInfo, boolean isWeek) {
    if (Objects.isNull(biMedalInfo)) {
      return null;
    }
    RankCityDataModel model = new RankCityDataModel();
    model.setDrvId(biMedalInfo.getDrvId());
    model.setDrvName(MaskHelperUtils.maskName(biMedalInfo.getDrvName()));
    model.setOrderCnt((isWeek ? biMedalInfo.getWeekOrderCnt().intValue() : biMedalInfo.getMonthOrderCnt().intValue()));
    model.setRanking(isWeek ? biMedalInfo.getWeekRanking().intValue() : biMedalInfo.getMonthRanking().intValue());
    return model;
  }

  @Override
  public DriverRankObject[] queryRankList(int startIndex, int endIndex) {
    List<RankCityDataModel> pageRankDataList = this.rankCityDataModelList.subList(startIndex, endIndex);
    if(CollectionUtils.isEmpty(pageRankDataList)){
      return new DriverRankObject[0];
    }

    List<DriverRankObject> objects = new ArrayList<>();
    pageRankDataList.forEach(r -> {
      objects.add(new DriverRankObjectImpl(this.domainBeanFactory, r, this));
    });
    return ShoppingArrayUtils.toArray(objects, DriverRankObject.class);
  }

  @Override
  public void saveRankLike(long likedDriverId) {
    new SaveRankLikeExecutor(this, this.domainBeanFactory).doWork(likedDriverId);
  }

  /**
   * 加载排行榜的详细信息
   * */
  private void prepareRankData(){
      List<RankCityDataModel> cacheRankList = this.domainBeanFactory.honourRedisLogic()
              .queryCityRankData(this.cityId, this.isWeek());

      //补偿查询
      if(CollectionUtils.isEmpty(cacheRankList)){
        this.domainBeanFactory.rankInfoService().updateRankListForManual(Collections.singletonList(this.cityId));
        cacheRankList = this.domainBeanFactory.honourRedisLogic()
                .queryCityRankData(this.cityId, this.isWeek());
      }

      if(CollectionUtils.isNotEmpty(cacheRankList)) {
//      this.totalCount = cacheRankList.size();
        //https://idev.ctripcorp.com?1831668 排行榜只展示前50名的数据
        this.totalCount = (int) cacheRankList.stream().filter(r -> r.getRanking() <= 50).count();
        this.rankCityDataModelList = cacheRankList;
      }
  }

  /**
   * 榜单refs
   * 城市ID_类型周榜1月榜2_榜单时间戳_榜单数据时间yyyy-MM-dd HH:ss_榜单数据时间所在周yyyy-MM-dd/数据时间所在月yyyy-MM
   * */
  private String bulidRankRefs(RankOriginalDataModel rankOriginalDataModel, int rankType) {
    String strRefs = String.format(RANK_REFS_FORMAT,
            this.cityId,
            rankType,
            rankOriginalDataModel.getBatchTime(),
            rankOriginalDataModel.getDataTime(),
            this.isWeek ? rankOriginalDataModel.getDataTimeWeek() : rankOriginalDataModel.getDataTimeMonth());
    return domainBeanFactory.commonCryptographicUtils().encryptByDES(strRefs);
  }

  /**
   * 榜单refs
   * */
  private String[] splitRankRefs(String refs) {
    String strRefs = domainBeanFactory.commonCryptographicUtils().decryptByDES(refs);
    if(strRefs.indexOf(RANK_REFS_SPLIT) <= 0) {
      return new String[0];
    }
    return strRefs.split(RANK_REFS_SPLIT);
  }
}
