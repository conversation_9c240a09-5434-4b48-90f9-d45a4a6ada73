package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverCarInspectionRecordDO;
import com.ctrip.dcs.driver.domain.task.CarInspectionInfoDTO;
import com.ctrip.dcs.driver.domain.task.QueryCarInspectionInfoRequestType;
import com.ctrip.dcs.driver.domain.task.QueryCarInspectionInfoResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueryCarInspectionInfoExector extends ShoppingExecutor<QueryCarInspectionInfoRequestType, QueryCarInspectionInfoResponseType>
        implements Validator<QueryCarInspectionInfoRequestType> {

    @Autowired
    DriverTaskAdapter driverTaskAdapter;

    @Override
    public QueryCarInspectionInfoResponseType execute(QueryCarInspectionInfoRequestType request) {
        QueryCarInspectionInfoResponseType responseType = new QueryCarInspectionInfoResponseType();
        responseType.carInspectionList = new ArrayList<>();
        List<DriverCarInspectionRecordDO> carInspectionRecordList = driverTaskAdapter.queryCarInspectionInfoList(request.taskId);
        if(CollectionUtils.isNotEmpty(carInspectionRecordList)){
            carInspectionRecordList.forEach(r ->{
                CarInspectionInfoDTO carInspectionInfo = new CarInspectionInfoDTO();
                carInspectionInfo.taskStepKey = r.getTaskStepKey();
                carInspectionInfo.taskStepValue = r.getTaskStepValue();
                responseType.carInspectionList.add(carInspectionInfo);
            });
        }

        return ServiceResponseUtils.success(responseType);
    }

    @Override
    public void onExecuted(QueryCarInspectionInfoRequestType req, QueryCarInspectionInfoResponseType resp) {
        super.onExecuted(req, resp);
        Cat.logEvent(CatEventType.DRIVER_CARINSPECTION_QUERY, resp.getResponseResult().getReturnCode());
    }

    @Override
    public void validate(AbstractValidator<QueryCarInspectionInfoRequestType> validator) {
        validator.ruleFor("driverId").notNull().greaterThan(0L);
        validator.ruleFor("taskId").notNull().notEmpty();
    }

}
