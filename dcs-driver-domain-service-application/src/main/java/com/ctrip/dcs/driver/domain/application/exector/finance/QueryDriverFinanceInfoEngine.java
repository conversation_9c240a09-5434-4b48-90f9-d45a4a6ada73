package com.ctrip.dcs.driver.domain.application.exector.finance;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceDriverObjectImpl;
import com.ctrip.dcs.driver.domain.application.domain.finance.FinanceObjectImpl;
import com.ctrip.dcs.driver.domain.finance.QueryDriverFinanceInfoRequestType;
import com.ctrip.dcs.driver.domain.finance.QueryDriverFinanceInfoResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import com.ctrip.dcs.driver.value.finance.FinanceObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.ResponseResult;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 查询司机提现信息
 */
@Component
public class QueryDriverFinanceInfoEngine extends ShoppingExecutor<QueryDriverFinanceInfoRequestType, QueryDriverFinanceInfoResponseType>
        implements Validator<QueryDriverFinanceInfoRequestType> {

    @Autowired
    private DomainBeanFactory domainBeanFactory;

    @Override
    public void validate(AbstractValidator<QueryDriverFinanceInfoRequestType> validator) {
        validator.ruleFor("driverId").notNull().notEmpty().greaterThan(0L);
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverFinanceInfoEngine owner;
        private final QueryDriverFinanceInfoResponseType response;
        private final QueryDriverFinanceInfoRequestType request;
        private FinanceDriverObject financeDriverObject;
        private FinanceObject financeObject;

        private Executor(QueryDriverFinanceInfoRequestType request, QueryDriverFinanceInfoResponseType response, QueryDriverFinanceInfoEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.response.responseResult = new ResponseResult();
            this.response.responseResult.returnCode = FinanceResultEnum.OK.getCode();
            this.request = request;

            this.invokeService();
        }

        private void invokeService() {
            financeDriverObject = new FinanceDriverObjectImpl(this.request.driverId, this.request.source, this.owner.domainBeanFactory);
            financeObject = new FinanceObjectImpl(this.request.driverId,
                    false, this.owner.domainBeanFactory);
        }

        @Override
        protected boolean validate() {
            if(FinanceResultEnum.isValidError(this.financeDriverObject.validResult())) {
                response.responseResult.returnCode = this.financeDriverObject.validResult();
                return false;
            }
            if(Objects.isNull(financeObject)) {
                return false;
            }

            try{
                final CountDownLatch latch = new CountDownLatch(2);
                this.owner.domainBeanFactory.executorService().submit(() -> {
                    this.financeObject.queryBalance();
                    latch.countDown();});
                this.owner.domainBeanFactory.executorService().submit(() -> {
                    this.financeObject.bankCardList();
                    latch.countDown();});
                // 设置等待时间
                latch.await(this.owner.domainBeanFactory.financeConfig().getThreadWaitTime(), TimeUnit.MILLISECONDS);
            } catch (Exception ex) {
                CommonLogger.INSTANCE.warn("QueryDriverFinanceInfo error", ex);
            }

            if (FinanceResultEnum.isValidError(financeObject.balanceCode())) {
                response.responseResult.returnCode = financeObject.balanceCode();
                return false;
            }
            return true;
        }

        @Override
        protected void buildResponse() {
            response.driverName = financeDriverObject.driverName();
            response.driverPhone = financeDriverObject.driverPhone();
            response.balanceAmount = financeObject.balanceAmount();
            response.hasPassword = financeObject.isHasPassword();
            response.hasBankCard = financeObject.isHasBankCard();
            response.maxWithdrawAmount = financeObject.maxWithDrawAmount();
            response.minWithdrawAmount = financeObject.minWithDrawAmount();
            response.maxWithdrawCount = financeObject.withDrawLimitCount();
            response.todayWithdrawCount = financeObject.withDrawCount();
        }
    }

    @Override
    public QueryDriverFinanceInfoResponseType execute(QueryDriverFinanceInfoRequestType request) {
        QueryDriverFinanceInfoResponseType response = new QueryDriverFinanceInfoResponseType();
        Executor executor = new Executor(request, response, this);
        if(executor.validate()){
            executor.buildResponse();
        }
        if(FinanceResultEnum.isValidError(response.responseResult.returnCode)) {
            response.responseResult.returnMessage = this.domainBeanFactory.financeConvertService().getFinanceResult(response.responseResult.returnCode);
            return ServiceResponseUtils.fail(response, response.responseResult.returnCode, response.responseResult.returnMessage);
        }
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverFinanceInfoResponseType onException(QueryDriverFinanceInfoRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverFinanceInfo error", ex);
        return super.onException(req, ex);
    }
}
