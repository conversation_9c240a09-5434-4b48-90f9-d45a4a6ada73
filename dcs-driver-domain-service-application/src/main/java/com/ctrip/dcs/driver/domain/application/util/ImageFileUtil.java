package com.ctrip.dcs.driver.domain.application.util;

import com.ctrip.tour.driver.utility.log.LogHelper;
import com.dianping.cat.Cat;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.hc.QunarAsyncClient;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Objects;

public class ImageFileUtil {

    private static final QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    /**
     * 图片最大500K: 500 * 1024 = 512,000
     */
    private static final BigDecimal MAX_COMPRESS_SIZE = BigDecimal.valueOf(500000);

    private static final String JPG_HEADER_HEX_VALUE = "FFD8FF";
    private static final String PNG_HEADER_HEX_VALUE = "89504E47";
    private static final String JPG_FILE_TYPE = "jpg";
    private static final String PNG_FILE_TYPE = "png";


    /**
     * 先下载然后压缩图片
     * TODO 注意，图片类型只支持jpg/png(30024 ImagFileUtils历史逻辑)
     */
    public static byte[] downloadThenCompressImage(String fileName) {
        try {
            byte[] imgBuf = downloadImage(fileName);
            if (ArrayUtils.isEmpty(imgBuf)) {
                return null;
            }
            String type = getImageTypeFromBuf(Arrays.copyOf(imgBuf, 4));
            if (StringUtils.isBlank(type)) {
                return null;
            }
            return compressImage(imgBuf, type, MAX_COMPRESS_SIZE);
        } catch (Exception e) {
            LogHelper.error(e);
        }
        return null;
    }

    public static byte[] downloadImage(String fileName) throws Exception {
        String urlChange = "0";
        if (fileName.startsWith("https://dimg04.c-ctrip.com/images/")) {
            fileName = "https://dimg04.fx.ctripcorp.com/images/" + fileName.split("https://dimg04.c-ctrip.com/images/")[1];
            urlChange = "1";
        }
        Cat.logEvent("ImagFileUtils.GetImage.Url.Change", urlChange);
        LogHelper.info("TempFileUtil.getImageStr.fileName", fileName);
        ListenableFuture<Response> responseFuture = qunarAsyncClient.get(fileName);
        Response response = responseFuture.get();
        if(Objects.isNull(response)) {
            return null;
        }
        return response.getResponseBodyAsBytes();
    }

    /**
     * 判断大小并压缩
     * */
    public static byte[] compressImage(byte[] data, String fileType, BigDecimal maxCompressSize) throws Exception {
        BigDecimal dataLength = BigDecimal.valueOf(data.length);
        if (dataLength.compareTo(maxCompressSize) <= 0) {
            return data;
        }
        ByteArrayInputStream in = new ByteArrayInputStream(data);
        BufferedImage image = ImageIO.read(in);
        BigDecimal scale = maxCompressSize.divide(dataLength, 1, RoundingMode.HALF_UP);
        BufferedImage output = Thumbnails.of(image).scale(scale.doubleValue()).asBufferedImage();
        return bufferedImageToByte(output, fileType);
    }

    public static byte[] bufferedImageToByte(BufferedImage image, String type) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, type, baos);
        return baos.toByteArray();
    }

    /**
     * 根据文件流判断图片类型
     * 读取文件的前几个字节来判断图片格式
     */
    public static String getImageTypeFromBuf(byte[] bytes) {
        String hexType = new BigInteger(1, bytes).toString(16).toUpperCase();
        if (hexType.contains(JPG_HEADER_HEX_VALUE)){
            return JPG_FILE_TYPE;
        } else if(hexType.contains(PNG_HEADER_HEX_VALUE)){
            return PNG_FILE_TYPE;
        }
        return null;
    }

}
