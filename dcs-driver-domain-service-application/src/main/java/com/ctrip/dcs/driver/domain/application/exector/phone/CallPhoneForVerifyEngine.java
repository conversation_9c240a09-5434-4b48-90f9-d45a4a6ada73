package com.ctrip.dcs.driver.domain.application.exector.phone;

import com.ctrip.dcs.driver.domain.application.condition.CallPhoneForVerifyCondition;
import com.ctrip.dcs.driver.domain.application.covert.CallPhoneForVerifyConvert;
import com.ctrip.dcs.driver.domain.application.service.CallPhoneForVerifyService;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.CallPhoneForVerifyRequestType;
import com.ctrip.model.CallPhoneForVerifyResponseType;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CallPhoneForVerifyEngine extends ShoppingExecutor<CallPhoneForVerifyRequestType, CallPhoneForVerifyResponseType>
        implements Validator<CallPhoneForVerifyRequestType> {
    @Resource
    private CallPhoneForVerifyConvert convert;

    @Resource
    private CallPhoneForVerifyService service;

    /**
     * 拨打提供的手机号，看该手机号是否有效。
     * 这里仅仅是发起一个异步拨打任务，查看结果需要根据该接口返回的callTaskId，请求{@link QueryCallPhoneForVerifyResultEngine}
     * 拿到结果
     *
     * V1:https://idev.ctripcorp.com?5608831
     *
     * @param requestType
     * @return
     */
    @Override
    public CallPhoneForVerifyResponseType execute(CallPhoneForVerifyRequestType requestType) {
        CallPhoneForVerifyCondition condition = convert.buildCondition(requestType);
        Long callTaskId = service.call(condition);
        return ServiceResponseUtils.success(convert.buildResponse(callTaskId));
    }

    @Override
    public void validate(AbstractValidator<CallPhoneForVerifyRequestType> validator) {
        validator.ruleFor("locale").notNull().notEmpty();
        validator.ruleFor("countryCode").notNull().notEmpty();
        validator.ruleFor("phoneNumber").notNull().notEmpty();
        validator.ruleFor("channel").notNull().notEmpty();
    }
}
