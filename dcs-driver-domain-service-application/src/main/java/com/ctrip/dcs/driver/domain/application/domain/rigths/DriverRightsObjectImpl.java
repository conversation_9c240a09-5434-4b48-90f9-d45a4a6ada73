package com.ctrip.dcs.driver.domain.application.domain.rigths;

import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exector.CheckRightsCanUseExecutor;
import com.ctrip.dcs.driver.domain.application.domain.exector.UseRightsExecutor;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.ProbabilityDto;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.VacationExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.WelfareExtendDto;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LotteryUtils;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.dcs.driver.value.rights.LevelObject;
import com.ctrip.dcs.driver.value.rights.RightsObject;
import com.ctrip.dcs.driver.value.rights.RightsRecordObject;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.google.gson.annotations.Expose;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
public class DriverRightsObjectImpl extends DriverRightsObject {

    private final DomainBeanFactory domainBeanFactory;

    private String extend;

    public DriverRightsObjectImpl(DomainBeanFactory domainBeanFactory, long driverId, String monthIdx) {
        this.domainBeanFactory = domainBeanFactory;
        this.drivId = driverId;
        this.monthIdx = monthIdx;
    }

    public DriverRightsObjectImpl(DomainBeanFactory domainBeanFactory, long driverId, long cityId) {
        this.domainBeanFactory = domainBeanFactory;
        this.drivId = driverId;
        this.cityId = cityId;
    }

    @Expose
    private Long drivId;
    @Expose
    private String monthIdx;
    @Expose
    private Long cityId;
    @Expose
    private Long id;

    @Override
    public Long drivId() {
        return this.drivId;
    }

    @Override
    public Long cityId() {
        return this.cityId;
    }

    @Override
    public String monthIdx() {
        return this.monthIdx;
    }

    @Override
    public boolean checkRightsCanUse(int righstType) {
        //校验权益共同规则
        List<RightsModel> rightsModels = this.domainBeanFactory.rightsRepoService().queryDriverRights(this.drivId, Collections.singletonList(righstType), this.monthIdx);
        RightsModel rightsModel = rightsModels.stream().filter(model -> (model.getRightsStatus().equals(RightsStatusEnum.RIGHTS_STATUS_ISSUED.getStatus()) || model.getRightsStatus().equals(RightsStatusEnum.RIGHTS_STATUS_USED.getStatus()))
                && (LocalDateTime.now().isAfter(LocalDateTimeUtils.localDateTime(model.getRightsStratTime())) && LocalDateTime.now().isBefore(LocalDateTimeUtils.localDateTime(model.getRightsEndTime())))
                && (model.getUseLimit() == 0 || (model.getUseLimit() - model.getUseCount()) > 0)).findFirst().orElse(null);
        if (Objects.isNull(rightsModel)) {
            return false;
        }

        this.extend = rightsModel.getExtend();
        this.id =rightsModel.getId();
        //校验私有规则
        return new CheckRightsCanUseExecutor(this, this.domainBeanFactory).doWork(rightsModel);
    }

    @Override
    public boolean useRights(UseRightsCondition condition) {
        return new UseRightsExecutor(this, this.domainBeanFactory).doWork(condition);
    }

    @Override
    public BigDecimal computeWelfare() {
        WelfareExtendDto welfareExtendDto = JacksonUtil.deserialize(this.extend, WelfareExtendDto.class);
        if (Objects.isNull(welfareExtendDto) || Objects.isNull(welfareExtendDto.getWelfareLimit())) {
            return BigDecimal.ZERO;
        }
        BigDecimal welafreUsed = Optional.ofNullable(welfareExtendDto.getWelfareUse()).orElse(BigDecimal.ZERO);
        BigDecimal welafreLimit = welfareExtendDto.getWelfareLimit();
        BigDecimal welafreFreeze = Optional.ofNullable(welfareExtendDto.getWelfareFreeze()).orElse(BigDecimal.ZERO);

        LevelObject levelObject = this.queryDriverLevel();
        if (Objects.isNull(levelObject) || Objects.isNull(levelObject.drivLevel())) {
            return BigDecimal.ZERO;
        }
        List<ProbabilityDto<BigDecimal>> probabilityList = this.domainBeanFactory.welfareConfig().getProbabilityList(levelObject.drivLevel());
        if (CollectionUtils.isEmpty(probabilityList)) {
            return BigDecimal.ZERO;
        }
        // 福利金上限-已发福利金-已冻结 与0取 max
        // 福利金 与 结果 取min
        BigDecimal welfare = LotteryUtils.probabilityLottery(probabilityList, probabilityList.get(0)).getEntity();
        BigDecimal remain = welafreLimit.subtract(welafreUsed).subtract(welafreFreeze).setScale(2, RoundingMode.HALF_UP);
        BigDecimal max = remain.compareTo(BigDecimal.ZERO) > 0 ? remain : BigDecimal.ZERO;
        BigDecimal money = welfare.compareTo(max) < 0 ? welfare : max;
        //冻结金额落db
        DrivRightsPO po = new DrivRightsPO();
        po.setId(this.id);
        po.setExtend(CommonRightsExtendUtils.buildExtendForWelfareFreeze(this.extend, money));
        this.domainBeanFactory.rightsRepoService().updateDriverRights(po);
        return money;
    }

    @Override
    public LevelObject queryDriverLevel() {
        return new LevelObjectImpl(this.domainBeanFactory, this.drivId, this.monthIdx);
    }

    @Override
    public LevelObject estimateLevel() {
        return new LevelObjectImpl(this.domainBeanFactory, this.drivId, this.cityId);
    }

    @Override
    public List<RightsObject> queryDriverRights(List<Integer> rightsTypes) {
        List<RightsModel> rightsModels = this.domainBeanFactory.rightsRepoService().queryDriverRights(this.drivId, rightsTypes, this.monthIdx);
        if (CollectionUtils.isEmpty(rightsModels)) {
            return Collections.emptyList();
        }
        return rightsModels.stream().map(model -> new RightsObjectImpl(this.domainBeanFactory, model)).collect(Collectors.toList());
    }

    @Override
    public List<RightsRecordObject> queryDriverRightsRecord(String useStartDate, String useEndDate, List<Integer> rightsTypes, Long id) {
        List<RightsRecordModel> rightsRecordModels;
        if (Objects.isNull(id) || id <= 0) {
            rightsRecordModels = this.domainBeanFactory.rightsRepoService().queryDriverRecords(this.drivId, useStartDate, useEndDate, rightsTypes);
        } else {
            rightsRecordModels = this.domainBeanFactory.rightsRepoService().queryDriverRecordsById(id);
        }
        if (CollectionUtils.isEmpty(rightsRecordModels)) {
            return Collections.emptyList();
        }
        return rightsRecordModels.stream().map(model -> new RightsRecordObjectImpl(this.domainBeanFactory, model)).collect(Collectors.toList());
    }

    @Override
    public List<RightsRecordObject> queryDriverRightsRecordByDriver(Integer rightsType, Long id) {
        List<RightsRecordModel> rightsRecordModels=this.domainBeanFactory.rightsDBDataService().querDriverRecordsByDriver(rightsType,id);
        if (CollectionUtils.isEmpty(rightsRecordModels)) {
            return Collections.emptyList();
        }
        return rightsRecordModels.stream().map(model -> new RightsRecordObjectImpl(this.domainBeanFactory, model)).collect(Collectors.toList());
    }

    @Override
    public int vacationLimit() {
        VacationExtendDto vacationExtendDto = JacksonUtil.deserialize(this.extend, VacationExtendDto.class);
        return vacationExtendDto.getVacationLimit();
    }
}
