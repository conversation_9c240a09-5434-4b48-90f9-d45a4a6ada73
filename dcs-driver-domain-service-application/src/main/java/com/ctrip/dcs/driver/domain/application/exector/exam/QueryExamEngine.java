package com.ctrip.dcs.driver.domain.application.exector.exam;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.application.domain.exam.GuideExamObjectImpl;
import com.ctrip.dcs.driver.domain.exam.GuideApplyExamDTO;
import com.ctrip.dcs.driver.domain.exam.QueryExamRequestType;
import com.ctrip.dcs.driver.domain.exam.QueryExamResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.ExamIsPassedEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.value.exam.ApplyExamObject;
import com.ctrip.dcs.driver.value.exam.GuideExamObject;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class QueryExamEngine extends ShoppingExecutor<QueryExamRequestType, QueryExamResponseType>
    implements Validator<QueryExamRequestType> {

  @Autowired
  private DomainBeanFactory domainBeanFactory;

  @Override
  public QueryExamResponseType execute(QueryExamRequestType request) {
    QueryExamResponseType response = new QueryExamResponseType();
    Executor executor = new Executor(request, response, this);
    executor.buildResponse();
    return ServiceResponseUtils.success(response);
  }

  @Override
  public void validate(AbstractValidator<QueryExamRequestType> validator) {
    validator.ruleFor("guideId").notNull().notEmpty().greaterThan(0L);
    validator.ruleFor("examAccountId").notNull().notEmpty();
  }

  private static final class Executor extends ShoppingBaseExecutor {
    private final QueryExamEngine owner;
    private final QueryExamResponseType response;
    private final QueryExamRequestType request;

    private GuideExamObject guideExamObject;

    private List<ApplyExamObject> applyExamObjectList;

    private Executor(QueryExamRequestType request, QueryExamResponseType response,
        QueryExamEngine owner) {
      super(request);
      this.owner = owner;
      this.response = response;
      this.request = request;
      guideExamObject =
          new GuideExamObjectImpl(this.owner.domainBeanFactory, request.examAccountId);
    }

    @Override
    protected boolean validate() {
      return true;
    }

    @Override
    protected void buildResponse() {
      List<ApplyExamObject> applyExamObjects = guideExamObject.queryApplyExamRecords();
      List<FromGuideExamInfo> validityExamList =
          this.owner.domainBeanFactory.guideExamConfig().queryValidityExam();
      if (CollectionUtils.isEmpty(applyExamObjects)) {
        GuideApplyExamDTO guideApplyExamDTO = new GuideApplyExamDTO();
        guideApplyExamDTO.setApplyRecord(false);
        guideApplyExamDTO.setExamRecord(false);
        guideApplyExamDTO.setMoreExam(CollectionUtils.isNotEmpty(validityExamList));
        this.response.setGuideApplyExamDTO(guideApplyExamDTO);
        return;
      }
      ApplyExamObject latestApplyExam = applyExamObjects.get(0);
      FromGuideExamInfo examInfo = this.owner.domainBeanFactory.guideExamConfig()
          .getExamInfoByDeptId(latestApplyExam.applySubject());
      //最新报考的科目在有效期内
      if (LocalDateTimeUtils.isNowInDate(examInfo.getStartTime(), examInfo.getEndTime())) {
        GuideApplyExamDTO guideApplyExamDTO = buildGuideApplyExamDTO(latestApplyExam);
        Long countExamRecords = this.owner.domainBeanFactory.guideExamScoreDBDataService()
            .countByExamAccountIdApplySubject(latestApplyExam.examAccountId(), latestApplyExam.applySubject());
        guideApplyExamDTO.setExamRecord(countExamRecords > 0);
        this.response.setGuideApplyExamDTO(guideApplyExamDTO);
        //获取未通过的考试
        List<FromGuideExamInfo> unPassedExamList =
            getUnPassedExamList(applyExamObjects, validityExamList);
        guideApplyExamDTO.setMoreExam(CollectionUtils.isNotEmpty(unPassedExamList));
      }else {
        //没有在有效期内的报考记录
        GuideApplyExamDTO guideApplyExamDTO = new GuideApplyExamDTO();
        guideApplyExamDTO.setApplyRecord(false);
        guideApplyExamDTO.setExamRecord(false);
        this.response.setGuideApplyExamDTO(guideApplyExamDTO);
        //获取未通过的考试
        List<FromGuideExamInfo> unPassedExamList =
            getUnPassedExamList(applyExamObjects, validityExamList);
        guideApplyExamDTO.setMoreExam(CollectionUtils.isNotEmpty(unPassedExamList));
      }
    }

    private List<FromGuideExamInfo> getUnPassedExamList(List<ApplyExamObject> applyExamObjects,
        List<FromGuideExamInfo> validityExamList) {
      //已通过的考试
      Map<String, String> passedApplyExamMap =
          applyExamObjects.stream().filter(o -> o.examIsPassed().equals(ExamIsPassedEnum.PASSED))
              .map(o -> o.parentApplySubject())
              .collect(Collectors.toMap(o -> o, o -> o));
      //未通过的考试
      List<FromGuideExamInfo> unPassedExamList = new ArrayList<>();
      for(FromGuideExamInfo fromGuideExamInfo: validityExamList){
        if(passedApplyExamMap.containsKey(fromGuideExamInfo.getParentDeptId())){
          continue;
        }
        unPassedExamList.add(fromGuideExamInfo);
      }
      return unPassedExamList;
    }

    private GuideApplyExamDTO buildGuideApplyExamDTO(ApplyExamObject applyExamObject) {
      GuideApplyExamDTO guideApplyExamDTO = new GuideApplyExamDTO();
      guideApplyExamDTO.setApplyResult(applyExamObject.applyResult().getCode() > 0);
      guideApplyExamDTO.setExamIsPassed(applyExamObject.examIsPassed().getCode() > 0);
      guideApplyExamDTO.setApplyRecord(true);
      return guideApplyExamDTO;
    }
  }
}
