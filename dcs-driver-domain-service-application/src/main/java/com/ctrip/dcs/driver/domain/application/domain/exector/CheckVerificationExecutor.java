package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.MesTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.PhoneAndVerifyCodeModel;
import com.ctrip.dcs.driver.value.finance.FinanceDriverObject;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class CheckVerificationExecutor extends DomainExecutor<FinanceDriverObject, PhoneAndVerifyCodeModel, String> {

  public CheckVerificationExecutor(FinanceDriverObject owner, DomainBeanFactory domainBeanFactory) {
    super(owner, domainBeanFactory);
  }

  @Override
  public String doWork(PhoneAndVerifyCodeModel phoneAndVerifyCodeModel) {
      if(Objects.isNull(phoneAndVerifyCodeModel) || StringUtils.isBlank(phoneAndVerifyCodeModel.getDriverPhone()) || StringUtils.isBlank(phoneAndVerifyCodeModel.getCode())) {
          return FinanceResultEnum.VERIFYCODE_FAIL.getCode();
      }

      boolean result =
              this.domainBeanFactory.infrastructureServiceProxy().checkPhoneCode(
                      phoneAndVerifyCodeModel.getDriverPhone(), phoneAndVerifyCodeModel.getCode(), MesTypeEnum.MODIFY_WITHDRAW_PASSWORD);
      if(result) {
          return FinanceResultEnum.OK.getCode();
      }
      return FinanceResultEnum.VERIFYCODE_FAIL.getCode();
  }
}
