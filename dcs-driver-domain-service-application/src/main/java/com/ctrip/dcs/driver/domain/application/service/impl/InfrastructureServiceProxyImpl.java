package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.infrastructure.adapter.infrastructure.InfrastructureServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.constant.FinanceResultEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.MesTypeEnum;
import com.ctrip.igt.infrastructureservice.executor.contract.CheckMobilePhoneCodeRequestType;
import com.ctrip.igt.infrastructureservice.executor.contract.CheckMobilePhoneCodeResponseType;
import com.ctrip.igt.infrastructureservice.executor.contract.SendMesByPhoneRequestType;
import com.ctrip.igt.infrastructureservice.executor.contract.SendMesByPhoneResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class InfrastructureServiceProxyImpl {

  @Autowired
  private InfrastructureServiceProxy infrastructureServiceProxy;

  private static final String SERVICE_NAME = "DRIVER_DOMAIN_SERVICE";

  /**
   * 发送验证码
   * */
  public String sendMessageByPhone(String mobilePhone, MesTypeEnum messageTypeEnum) {
    SendMesByPhoneRequestType request = new SendMesByPhoneRequestType();
    request.setSf(SERVICE_NAME);
    request.setChannel(messageTypeEnum.getChannel());
    request.setCountryCode("86");
    request.setMessageCode(messageTypeEnum.getMesCode());
    request.setMobilePhone(mobilePhone);
    SendMesByPhoneResponseType responseType = null;
    try {
      responseType = infrastructureServiceProxy.sendMessageByPhone(request);
    } catch (Exception e) {
      return FinanceResultEnum.SEND_VERIFYCODE_FAIL.getCode();
    }
    if(Objects.isNull(responseType) || Objects.isNull(responseType.responseResult)){
      return FinanceResultEnum.SEND_VERIFYCODE_FAIL.getCode();
    }
    if("200".contentEquals(responseType.getResponseResult().getReturnCode())) {
      return FinanceResultEnum.OK.getCode();
    }
    if("401".contentEquals(responseType.getResponseResult().getReturnCode())) {
      return FinanceResultEnum.SEND_VERIFYCODE_FREQUENTLY.getCode();
    }
    if("402".contentEquals(responseType.getResponseResult().getReturnCode())) {
      return FinanceResultEnum.SEND_VERIFYCODE_OVERLIMIT.getCode();
    }
    return FinanceResultEnum.SEND_VERIFYCODE_FAIL.getCode();
  }

  /**
   * 校验验证码
   * */
  public boolean checkPhoneCode(String mobilePhone, String code, MesTypeEnum messageTypeEnum) {
    CheckMobilePhoneCodeRequestType request = new CheckMobilePhoneCodeRequestType();
    request.setSf(SERVICE_NAME);
    request.setChannel(messageTypeEnum.getChannel());
    request.setCountryCode("86");
    request.setMessageCode(messageTypeEnum.getMesCode());
    request.setCode(code);
    request.setMobilePhone(mobilePhone);
    CheckMobilePhoneCodeResponseType responseType = null;
    try {
      responseType = infrastructureServiceProxy.checkPhoneCode(request);
    } catch (Exception e) {
      return false;
    }
    if(Objects.isNull(responseType) || Objects.isNull(responseType.responseResult)){
      return false;
    }
    return FinanceResultEnum.isValidOK(responseType.responseResult.returnCode);
  }
}
