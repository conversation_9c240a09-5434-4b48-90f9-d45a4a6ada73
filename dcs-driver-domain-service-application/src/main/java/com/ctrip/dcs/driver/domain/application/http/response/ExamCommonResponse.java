package com.ctrip.dcs.driver.domain.application.http.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description
 */

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ExamCommonResponse<T> {
    private int code;

    private String msg;

    private T data;

    private boolean success;
}
