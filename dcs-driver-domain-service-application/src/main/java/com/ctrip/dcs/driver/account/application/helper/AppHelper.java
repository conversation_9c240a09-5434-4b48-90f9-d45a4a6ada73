package com.ctrip.dcs.driver.account.application.helper;

import qunar.tc.schedule.Parameter;

import java.util.Optional;
import java.util.function.Function;

public class AppHelper {

	public static Optional<String> getString(Parameter parameter, String key) {
		return Optional.ofNullable(parameter).map(e -> e.getString(key));
	}

	public static Function<Integer, Boolean> greaterThan(Integer n) {
		return e -> e > n;
	}

}