package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dto.condition.UseRightsCondition;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsRecordModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.value.rights.DriverRightsObject;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


public class UseRightsExecutor extends DomainExecutor<DriverRightsObject, UseRightsCondition, Boolean> {

    public static final Logger LOGGER = LoggerFactory.getLogger(UseRightsExecutor.class);

    public UseRightsExecutor(DriverRightsObject owner, DomainBeanFactory domainBeanFactory) {
        super(owner, domainBeanFactory);
    }

    @Override
    public Boolean doWork(UseRightsCondition condition) {
        //幂等校验 根据司机id，权益类型，司机单号
        List<RightsRecordModel> rightsRecordModels = this.domainBeanFactory.rightsDBDataService().queryDriverRecordsBySupplyOrderId(condition.getDriverId(), condition.getRightsType(), condition.getSupplyOrderId());
        if (CollectionUtils.isNotEmpty(rightsRecordModels)) {
            LOGGER.info("dcsdriverdomainservice::userights",String.format("this rights has been used condition:%s", JacksonUtil.serialize(condition)));
            return false;
        }

        //check rights
        RightsModel rightsModel = this.domainBeanFactory.rightsDBDataService().queryDriverRights(condition.getDriverId(), LocalDateTimeUtils.monthIndexStr()).stream()
                .filter(o -> o.getRightsStatus() != RightsStatusEnum.RIGHTS_STATUS_CANCELLED.getStatus())
                .filter(o -> o.getRightsType().equals(condition.getRightsType()) && (o.getUseLimit() == 0 || (o.getUseLimit() - o.getUseCount()) > 0)).findFirst().orElse(null);

        if (Objects.isNull(rightsModel)) {
            LOGGER.error(String.format("driverId:%s,rightType:%s rights is unavailable,the driver does not have this rights or used", condition.getDriverId(), condition.getRightsType()));
            throw new BizException(String.format("driverId:%s,rightType:%s rights is unavailable,the driver does not have this rights or used", condition.getDriverId(), condition.getRightsType()));
        }

        //save
        try {
            saveAndUpdateRights(buildDrivRightsRecordPO(condition, rightsModel), buildDrivRightsPO(condition, rightsModel));
        } catch (Exception e) {
            LOGGER.error(String.format("failed to saveAndUpdateRights driverId:%s,rightType:%s", condition.getDriverId(), condition.getRightsType()), e);
            throw new BizException(String.format("failed to saveAndUpdateRights driverId:%s,rightType:%s", condition.getDriverId(), condition.getRightsType()));
        }
        return true;
    }

    private DrivRightsPO buildDrivRightsPO(UseRightsCondition condition, RightsModel rightsModel) {
        condition.setRightsName(rightsModel.getRightsName());
        DrivRightsPO drivRightsPO = new DrivRightsPO();
        drivRightsPO.setId(rightsModel.getId());
        drivRightsPO.setUseCount(rightsModel.getUseCount() + 1);
        drivRightsPO.setRightsStatus(RightsStatusEnum.RIGHTS_STATUS_USED.getStatus());
        drivRightsPO.setExtend(CommonRightsExtendUtils.buildExtendForUpdate(rightsModel, condition.getMoney(), 0));
        return drivRightsPO;

    }

    private DrivRightsRecordPO buildDrivRightsRecordPO(UseRightsCondition condition, RightsModel rightsModel) {
        LevelModel levelModel = this.domainBeanFactory.rightsRepoService().queryDriverLevel(condition.getDriverId(), LocalDateTimeUtils.monthIndexStr());
        if (Objects.isNull(levelModel)) {
            LOGGER.error(String.format("driverId:%s,rightType:%s driver is null", condition.getDriverId(), condition.getRightsType()));
            throw new BizException(String.format("driverId:%s,rightType:%s driver is null", condition.getDriverId(), condition.getRightsType()));
        }
        DrivRightsRecordPO drivRightsRecordPO = new DrivRightsRecordPO();
        drivRightsRecordPO.setRightsId(rightsModel.getId());
        drivRightsRecordPO.setDrivId(condition.getDriverId());
        drivRightsRecordPO.setRightsType(condition.getRightsType());
        drivRightsRecordPO.setUseLevel(levelModel.getDrivLevel());
        drivRightsRecordPO.setRightsName(rightsModel.getRightsName());
        drivRightsRecordPO.setLevelName(levelModel.getLevelName());
        drivRightsRecordPO.setUserOrderId(condition.getUserOrderId());
        drivRightsRecordPO.setPurchaseOrderId(condition.getPurchaseOrderId());
        drivRightsRecordPO.setSupplyOrderId(condition.getSupplyOrderId());
        drivRightsRecordPO.setPunishOrderId(condition.getPunishOrderId());
        drivRightsRecordPO.setMoney(Optional.ofNullable(condition.getMoney()).orElse(BigDecimal.ZERO).toString());
        return drivRightsRecordPO;
    }

    @Transactional
    public void saveAndUpdateRights(DrivRightsRecordPO recordPO, DrivRightsPO rightsPO) {
        this.domainBeanFactory.rightsRepoService().saveRightsRecords(recordPO);
        this.domainBeanFactory.rightsRepoService().updateDriverRights(rightsPO);
    }

}
