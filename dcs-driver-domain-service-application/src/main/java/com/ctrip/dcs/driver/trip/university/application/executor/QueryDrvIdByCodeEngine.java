package com.ctrip.dcs.driver.trip.university.application.executor;

import com.ctrip.dcs.driver.domain.trip.university.QueryDrvIdByCodeRequestType;
import com.ctrip.dcs.driver.domain.trip.university.QueryDrvIdByCodeResponseType;
import com.ctrip.dcs.driver.trip.university.application.service.TripUniversityService;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 生成一个随机code码,与司机ID对应，保证一对一
 * @Date 9:49 2020/8/13
 * @Param
 * @return
 **/
@Component
public class QueryDrvIdByCodeEngine extends ShoppingExecutor<QueryDrvIdByCodeRequestType, QueryDrvIdByCodeResponseType>
  implements Validator<QueryDrvIdByCodeRequestType> {

    @Autowired
    TripUniversityService tripUniversityService;

    @Override
    public QueryDrvIdByCodeResponseType execute(QueryDrvIdByCodeRequestType requestType) {
        QueryDrvIdByCodeResponseType responseType = new QueryDrvIdByCodeResponseType();
        Result<String> result = tripUniversityService.queryDrvIdByCode(requestType.getDrvCode());
        if (result.isSuccess()) {
            responseType.setData(result.getData());
            return ServiceResponseUtils.success(responseType);
        }
        return ServiceResponseUtils.fail(responseType);
    }

    @Override
    public void validate(AbstractValidator<QueryDrvIdByCodeRequestType> validator) {
        validator.ruleFor("drvCode").notNull();
    }
}
