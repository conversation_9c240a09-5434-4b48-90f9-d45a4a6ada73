package com.ctrip.dcs.driver.account.application.helper;

import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.value.AccountChangeLog;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AccountChangeLogHelper {
    Logger log = LoggerFactory.getLogger(AccountChangeLogHelper.class);

    public boolean saveLog(AccountChangeLog log) {
        return saveLog(Lists.newArrayList(log));
    }

    public boolean saveLog(List<AccountChangeLog> logList) {
        if (CollectionUtils.isEmpty(logList)) {
            return true;
        }
        try {
            for (AccountChangeLog accountChangeLog : logList) {
                String content = JsonUtil.toString(accountChangeLog);
                Map<String, Object> properties = new HashMap<>();
                properties.put("content", content);
                QmqProducerProvider.sendMessage(Constants.ACCOUNT_CHANGE_LOG_TOPIC, properties);
            }
        } catch (Exception e) {
            log.warn("send account change log qmq message error", JsonUtil.toString(logList), e, Maps.newConcurrentMap());
            return false;
        }
        return true;
    }
}
