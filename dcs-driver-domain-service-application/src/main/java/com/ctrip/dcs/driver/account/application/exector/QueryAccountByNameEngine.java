package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.convert.AccountDetailConvert;
import com.ctrip.dcs.driver.account.application.util.DriverMetricUtil;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByNameRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByNameResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@ServiceLogTag(tagKeys= {"name"})
public class QueryAccountByNameEngine extends ShoppingExecutor<QueryAccountByNameRequestType, QueryAccountByNameResponseType> implements Validator<QueryAccountByNameRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public QueryAccountByNameResponseType execute(QueryAccountByNameRequestType request) {


        QueryAccountByNameResponseType resp = new QueryAccountByNameResponseType();
        resp.setAccountDetailList(Lists.newArrayList());
        List<AccountInfoDTO> accountList = accountService.getAccountInfoByName(request.getName());
        if (CollectionUtils.isNotEmpty(accountList)) {
            resp.setAccountDetailList(accountList.stream().map(AccountDetailConvert::convert).collect(Collectors.toList()));
        }
        Cat.logEvent(CatEventType.QUERY_BY_NAME_RESULT, CollectionUtils.isNotEmpty(accountList) ? "1" : "0");
        return ServiceResponseUtils.success(resp);
    }
    @Override
    public void onFinally(QueryAccountByNameRequestType req, QueryAccountByNameResponseType resp, Exception ex) {
        DriverMetricUtil.batchMetricUdlIsValid(resp.getAccountDetailList());
    }

    @Override
    public Boolean isEmptyResult(QueryAccountByNameResponseType resp) {
        return resp!=null&&CollectionUtils.isNotEmpty(resp.getAccountDetailList());
    }
}
