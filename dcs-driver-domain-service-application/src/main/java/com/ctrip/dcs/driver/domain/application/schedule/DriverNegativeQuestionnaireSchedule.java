package com.ctrip.dcs.driver.domain.application.schedule;

import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.driver.domain.application.mq.DriverNegativeQuestionnaireListener;

import qunar.tc.qschedule.config.QSchedule;
import qunar.tc.schedule.Parameter;

@Component
public class DriverNegativeQuestionnaireSchedule {
    @Autowired
    private DriverNegativeQuestionnaireListener listener;

    @QSchedule("dcs.driver.lost.contact")
    public void process(Parameter parameter) {

        String orderListStr = parameter.getString("orderList");
        String[] orderList = orderListStr.split(",");
        if (ArrayUtils.isEmpty(orderList)) {
            return;
        }
        for (String order : orderList) {
            listener.doProcessOrder(order);
        }
    }
}
