package com.ctrip.dcs.driver.account.application.service;
/*
作者：pl.yang
创建时间：2025/3/27-下午8:09-2025
*/


import com.ctrip.basebiz.account.service.soa.AccountInfoResponseType;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountChangeLogDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.service.UserCenterAccountGateway;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverDeviceInfoDao;
import com.ctrip.dcs.driver.gateway.AccountRepository;
import com.ctrip.igt.framework.common.base.GsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.schedule.Parameter;
import qunar.tc.schedule.TaskHolder;
import qunar.tc.schedule.TaskMonitor;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RefreshUdlService
 * @Package com.ctrip.dcs.driver.account.application.service
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/3/27 下午8:09
 */

@Component
public class RefreshUdlService {
    private static final String PROGRESS_KEY = "dcs.driver.account.refresh.udl.job.progress";

    private static final long PROCESS_KEY_EXPIRE_SECONDS = 30 * 24 * 3600;
    public static final Logger log = LoggerFactory.getLogger(RefreshUdlService.class);

    @Resource
    AccountBaseInfoDao accountBaseInfoDao;
    @Resource
    AccountMapperDao accountMapperDao;
    @Resource
    AccountChangeLogDao accountChangeLogDao;
    @Resource
    DriverDeviceInfoDao driverDeviceInfoDao;
    @Resource
    UserCenterAccountGateway userCenterAccountGateway;
    @Resource
    DirectorRedis directorRedis;
    @Resource
    DriverAccountConfig driverAccountConfig;
    @Autowired
    AccountRepository accountGateway;

    public void refreshUdlByUid(Parameter parameter, String uids) {
        log.info("refresh account udl start", "uids : " + uids);
        List<String> uidList = Lists.newArrayList(uids.split(","));
        if (CollectionUtils.isNotEmpty(uidList)) {
            // 指定uid不用校验是否则base表存在，是否境外，全刷
            batchRefreshByUid(parameter, uidList);
        }
    }

    public void refreshAllUdl(Parameter parameter) {
        long startId = getStartId(parameter);
        TaskMonitor taskMonitor = TaskHolder.getKeeper();

        log.info("refresh account udl start", "start id : " + startId);
        while (true) {
            if (taskMonitor.isStopped()) {
                log.error("refresh account udl job stop", "current id : " + startId);
                break;
            }
            log.info("refresh account udl", "current id : " + startId);
            List<AccountBaseInfoPO> accountBaseInfoPOList = accountBaseInfoDao.batchQueryByPage(startId, 100);
            if (CollectionUtils.isEmpty(accountBaseInfoPOList)) {
                break;
            }
            batchRefresh(parameter,accountBaseInfoPOList);
            startId = accountBaseInfoPOList.get(accountBaseInfoPOList.size() - 1).getId();
            directorRedis.set(PROGRESS_KEY, startId, PROCESS_KEY_EXPIRE_SECONDS);
        }
    }

    private Long getStartId(Parameter parameter) {
        String startId = parameter.getString("startId");
        if (StringUtils.isNotBlank(startId)) {
            log.info("refresh account udl get start id from param", startId);
            return Long.valueOf(startId);
        }
        String redisStartId = directorRedis.get(PROGRESS_KEY);
        if (StringUtils.isNotBlank(redisStartId)) {
            log.info("refresh account udl get start id from redis", redisStartId);
            return Long.valueOf(redisStartId);
        }
        log.info("refresh account udl get start id from 0", "0");
        return 0L;
    }


    private void batchRefresh(Parameter parameter,List<AccountBaseInfoPO> accountBaseInfoPOList) {
        List<String> updateUdluidList=new ArrayList<>();
        for (AccountBaseInfoPO accountBaseInfoPO : accountBaseInfoPOList) {
            try {
                Thread.sleep(driverAccountConfig.getJobSleepMills());//不要把下游接口弄挂了
                String udl = getUdlByUid(accountBaseInfoPO, accountBaseInfoPO.getUid(), parameter.getString("isMockUdl"));
                if (StringUtils.isBlank(udl)) {
                    Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "NoUDL");
                    continue;
                }
                // 更新udl
                log.info("refresh account udl success-batchRefresh", accountBaseInfoPO.getUid());
                updateUdl(parameter, accountBaseInfoPO.getUid(), udl);
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "SUCCESS");
                updateUdluidList.add(accountBaseInfoPO.getUid());
            } catch (Exception e) {
                log.error(e);
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "ERROR");
            }
        }
        log.info("refresh account udl success", GsonUtil.toJson(updateUdluidList));
    }

    private void batchRefreshByUid(Parameter parameter, List<String> uidList) {
        for (String uid : uidList) {
            try {
                String udl = "";
                udl = getUdlByUid(null, uid, parameter.getString("isMockUdl"));
                if (udl == null) continue;

                if (StringUtils.isBlank(udl)) {
                    Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "NoUDL");
                    continue;
                }
                // 更新udl
                updateUdl(parameter, uid, udl);
                log.info("refresh account udl success", uid);
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "SUCCESS");
                Thread.sleep(driverAccountConfig.getJobSleepMills());
            } catch (Throwable  e) {
                log.error(e);
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "ERROR");
            }
        }
    }

    private @Nullable String getUdlByUid(AccountBaseInfoPO accountBaseInfoPO, String uid, String isMockUdlStr) {
        String udl = "";
        if (Boolean.TRUE.toString().equals(isMockUdlStr)) {
            if (accountBaseInfoPO == null) {
                accountBaseInfoPO = accountBaseInfoDao.queryByUID(uid);
            }
            if (accountBaseInfoPO == null) {
                log.warn("refresh account udl no account", uid);
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "NoAccount");
                return null;
            }
            //境内 :  CN_CSPD,境外: US_SPD
            udl = accountBaseInfoPO.getIsOversea() == 1 ? Constants.UDL_US_SPD : Constants.UDL_CN_CSPD;
        } else {
            AccountInfoResponseType account = userCenterAccountGateway.getAccountByUid(uid);
            if (account == null) {
                log.warn("refresh account udl no account", uid);
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "NoAccount");
                return null;
            }
            udl = account.getUdl();
            checkUdlValid(udl, uid);
        }
        return udl;
    }



    private void checkUdlValid(String udl, String uid) {
        if (StringUtils.isBlank(udl)) {
            log.warn("refresh account udl no udl", uid);
        }
        AccountBaseInfoPO accountBaseInfoPO = accountBaseInfoDao.queryByUID(udl);
        if (accountBaseInfoPO != null) {
            String db_udl = accountBaseInfoPO.getIsOversea() == 1 ? Constants.UDL_US_SPD : Constants.UDL_CN_CSPD;
            if (!db_udl.equals(udl)) {
                log.warn("refresh account udl udl not match", uid);
                // udl不匹配
                Cat.logEvent(CatEventType.JOB_REFRESH_ACCOUNT_UDL_RESULT, "UdlNotMatch");
            }
        }
    }

    //加上事务
    private void updateUdl(Parameter parameter, String uid, String udl) {
        String dbLists = parameter.getString("dbList");
        List<String> dbList = Arrays.stream(dbLists.split(",")).collect(Collectors.toList());
        updateUdl(uid, udl, dbList);
    }

    @Transactional
    private void updateUdl(String uid, String udl, List<String> dbLists) {
        if (CollectionUtils.isNotEmpty(dbLists)) {

            if (dbLists.contains("account_base_info")) {
                accountBaseInfoDao.updateUdl(udl, uid);
                accountMapperDao.updateUdl(udl, uid);
                accountChangeLogDao.updateUdl(udl, uid);
            }
        }
    }
}
