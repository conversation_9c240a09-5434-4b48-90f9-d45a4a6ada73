package com.ctrip.dcs.driver.domain.application.exector.account;

import com.ctrip.dcs.driver.domain.account.QueryAccountProcessRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountProcessResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.DriverAccountParamConfig;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Description
 */
@Component
public class QueryAccountProcessEngine extends ShoppingExecutor<QueryAccountProcessRequestType, QueryAccountProcessResponseType>
        implements Validator<QueryAccountProcessRequestType> {

    @Autowired
    private DriverAccountParamConfig driverAccountParamConfig;

    @Override
    public QueryAccountProcessResponseType execute(QueryAccountProcessRequestType request) {
        QueryAccountProcessResponseType response = new QueryAccountProcessResponseType();
        response.setIsNewAccount(driverAccountParamConfig.isGrey(request.driverId,request.cityId));
        return ServiceResponseUtils.success(response);
    }

    @Override
    public void validate(AbstractValidator<QueryAccountProcessRequestType> validator) {
        validator.ruleFor("cityId").notNull().notEmpty();
        validator.ruleFor("driverId").notNull().notEmpty();
    }
}
