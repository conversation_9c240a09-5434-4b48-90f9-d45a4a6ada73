package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.model.finance.WithdrawModel;
import com.ctrip.dcs.driver.value.finance.FinanceObject;

public class FinanceWithdrawExecutor extends DomainExecutor<FinanceObject, WithdrawModel, String> {

  public FinanceWithdrawExecutor(FinanceObject owner, DomainBeanFactory domainBeanFactory) {
    super(owner, domainBeanFactory);
  }

  @Override
  public String doWork(WithdrawModel withdrawModel) {
      return this.domainBeanFactory.financeConvertService().driverWithdraw(withdrawModel);
  }
}
