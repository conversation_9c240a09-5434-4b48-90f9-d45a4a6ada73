package com.ctrip.dcs.driver.domain.application.exector.control;


import com.ctrip.dcs.common.enums.AppGuidanceTypeEnum;
import com.ctrip.dcs.driver.domain.application.service.DriverAppControlService;
import com.ctrip.dcs.driver.domain.guidance.SaveDriverAppGuidanceRequestType;
import com.ctrip.dcs.driver.domain.guidance.SaveDriverAppGuidanceResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SaveDriverAppGuidanceEngine extends ShoppingExecutor<SaveDriverAppGuidanceRequestType, SaveDriverAppGuidanceResponseType> implements Validator<SaveDriverAppGuidanceRequestType> {
    @Autowired
    private DriverAppControlService appControlService;

    @Override
    public void validate(AbstractValidator<SaveDriverAppGuidanceRequestType> validator) {
        validator.ruleFor("uid").notNull().notEmpty();
        validator.ruleFor("guidanceType").notNull().notEmpty();
    }

    @Override
    public SaveDriverAppGuidanceResponseType execute(SaveDriverAppGuidanceRequestType request) {
        SaveDriverAppGuidanceResponseType responseType = new SaveDriverAppGuidanceResponseType();
        // 校验类型是否支持
        if (!AppGuidanceTypeEnum.isSupport(request.getGuidanceType())) {
            return ServiceResponseUtils.fail(responseType, "400", "not support type:" + request.getGuidanceType());
        }
        appControlService.saveDriverAppGuidance(request.getUid(), request.getGuidanceType());
        return ServiceResponseUtils.success(responseType);
    }

}
