package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.application.common.DriverLevelHelper;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.DriverPointModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.LevelModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.rights.RightsModel;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class RightsRedisLogic {
    private static final String DRIV_RIGHTS_REDIS_KEY = "drivrights:%s_%s";
    private static final String DRIV_LEVEL_REDIS_KEY = "drivlevel:%s_%s";
    private static final String DRIV_POINT_REDIS_KEY = "dcs_driver_drivpoint:%s_%s";

    private static final int RANK_MONTH_LIST_EXPIRE_TIME = 2764800;     //32天


    private final Gson gson = new Gson();


    @Autowired
    DirectorRedis directorRedis;
    @Autowired
    DriverLevelHelper driverLevelHelper;

    /**
     * 查询司机权益
     */
    public List<RightsModel> getDrivRights(long drivId, String monthIdx) {
        String rightStr = directorRedis.get(String.format(DRIV_RIGHTS_REDIS_KEY, drivId, monthIdx));
        if (Strings.isBlank(rightStr)) {
            return Collections.emptyList();
        }
        return gson.fromJson(rightStr, new TypeToken<List<RightsModel>>() {
        }.getType());
    }

    /**
     * 保存司机权益
     */
    public void saveDrivRights(List<RightsModel> rightsModels, long drivId, String monthIdx) {
        directorRedis.set(String.format(DRIV_RIGHTS_REDIS_KEY, drivId, monthIdx), rightsModels, getExpireTime(LocalDateTimeUtils.lastDayOfMonth()));
    }

    /**
     * 删除司机权益
     */
    public void delDrivRights(long drivId, String monthIdx) {
        directorRedis.remove(String.format(DRIV_RIGHTS_REDIS_KEY, drivId, monthIdx));
    }

    /**
     * 查询司机等级
     */
    public LevelModel getDrivLevel(long drivId, String monthIdx) {
        return directorRedis.get(String.format(DRIV_LEVEL_REDIS_KEY, drivId, monthIdx), LevelModel.class);
    }

    /**
     * 保存司机等级
     */
    public void saveDrivLevel(LevelModel levelModel, long drivId, String monthIdx) {
        directorRedis.set(String.format(DRIV_LEVEL_REDIS_KEY, drivId, monthIdx), levelModel, getExpireTime(LocalDateTimeUtils.lastDayOfMonth()));
    }

    /**
     * 删除司机等级
     */
    public void delDrivLevel(long drivId, String monthIdx) {
        directorRedis.remove(String.format(DRIV_LEVEL_REDIS_KEY, drivId, monthIdx));
    }

    /**
     * 过期时间
     */
    private Long getExpireTime(LocalDateTime time) {
        return (Timestamp.valueOf(time).getTime() - Timestamp.valueOf(LocalDateTime.now()).getTime()) / 1000;
    }

    /**
     * 快照司机分数
     */
    public void driverPointSnapshot(List<DriverPointModel> modelList, String todayStr) {
        directorRedis.mset(RANK_MONTH_LIST_EXPIRE_TIME, modelList.stream().flatMap(model ->
            Stream.of( String.format(DRIV_POINT_REDIS_KEY, model.getDrivId(), todayStr),gson.toJson(model))
        ).toArray(String[]::new));
    }

    /**
     * 获取司机分数
     */
    public List<DriverPointModel> getDriverPointSnapshot(List<Long> driverIds,String date) {
        List<String> strs=directorRedis.mget(driverIds.stream().map(id->String.format(DRIV_POINT_REDIS_KEY, id.toString(), date)).toArray(String[]::new));
        List<DriverPointModel> list = strs.stream().filter(o -> !Strings.isBlank(o)).map(str -> gson.fromJson(str, DriverPointModel.class)).collect(Collectors.toList());
        list.forEach(o -> o.setLevelName(driverLevelHelper.getDriverLevelName(o.getDrivLevel(), o.getLevelName())));
        return list;
    }
}
