package com.ctrip.dcs.driver.domain.application.exector.phone;

import com.ctrip.dcs.driver.domain.application.condition.CreatePhoneCheckCondition;
import com.ctrip.dcs.driver.domain.application.covert.CreatePhoneCheckConvert;
import com.ctrip.dcs.driver.domain.application.service.CreatePhoneCheckService;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.CreatePhoneCheckRequestType;
import com.ctrip.model.CreatePhoneCheckResponseType;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class CreatePhoneCheckEngine extends ShoppingExecutor<CreatePhoneCheckRequestType, CreatePhoneCheckResponseType>
        implements Validator<CreatePhoneCheckRequestType> {

    @Resource
    private CreatePhoneCheckConvert convert;

    @Resource
    private CreatePhoneCheckService service;

    /**
     * 在识别手机号是有效的后。直接在phone_check表写入数据
     * 这样统一收集用车履约司机这一块的手机号有效性信息
     *
     * @param requestType
     * @return
     */
    @Override
    public CreatePhoneCheckResponseType execute(CreatePhoneCheckRequestType requestType) {
        CreatePhoneCheckCondition condition = convert.buildCondition(requestType);
        boolean result = service.create(condition);
        return ServiceResponseUtils.success(convert.buildResponse(result));
    }

    @Override
    public void validate(AbstractValidator<CreatePhoneCheckRequestType> validator) {
        validator.ruleFor("countryCode").notNull().notEmpty();
        validator.ruleFor("phoneNumber").notNull().notEmpty();
        validator.ruleFor("checkType").notNull();
        validator.ruleFor("checkState").notNull();
    }
}
