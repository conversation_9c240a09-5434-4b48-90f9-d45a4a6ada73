package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO;
import com.ctrip.dcs.driver.domain.task.DriverTaskInfoDTO;
import com.ctrip.dcs.driver.domain.task.DriverTaskStatus;
import com.ctrip.dcs.driver.domain.task.QueryDriverTaskInfoRequestType;
import com.ctrip.dcs.driver.domain.task.QueryDriverTaskInfoResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Component
public class QueryDriverTaskInfoExector extends ShoppingExecutor<QueryDriverTaskInfoRequestType, QueryDriverTaskInfoResponseType>
        implements Validator<QueryDriverTaskInfoRequestType> {

    @Autowired
    DriverTaskAdapter driverTaskAdapter;

    private static final long ONE_DAY_HOURS = 24L;

    @Override
    public QueryDriverTaskInfoResponseType execute(QueryDriverTaskInfoRequestType request) {
        QueryDriverTaskInfoResponseType responseType = new QueryDriverTaskInfoResponseType();

        DriverTaskRecordDO driverTaskRecord;
        if(StringUtils.isNotBlank(request.driverOrderId)) {
            driverTaskRecord = driverTaskAdapter.queryDriverTaskByDriverOrderId(request.driverId, request.driverOrderId);
            if(Objects.isNull(driverTaskRecord)){
                //查询最近的任务，如果最近的任务是当天，也返回
                driverTaskRecord = driverTaskAdapter.queryDriverTaskByTaskId(request.driverId, StringUtils.EMPTY);
                if(Objects.nonNull(driverTaskRecord) && Objects.nonNull(driverTaskRecord.getDataChangeCreateTime())){
                    if(!LocalDate.now().equals(driverTaskRecord.getDataChangeCreateTime().toLocalDate())){
                        driverTaskRecord = null;
                    }
                }
            }
        } else {
            driverTaskRecord = driverTaskAdapter.queryDriverTaskByTaskId(request.driverId, request.taskId);
        }
        if(this.checkTaskValid(driverTaskRecord)) {
            responseType.task = this.convertToTaskInfoDTO(driverTaskRecord);
        }

        return ServiceResponseUtils.success(responseType);
    }

    /**
     * 检查task的有效性
     * 1、task的的状态不是生成，无效
     * 2、task的生成时间是24小时以前，认为过期
     * */
    private boolean checkTaskValid(DriverTaskRecordDO driverTaskRecord) {
        if(Objects.isNull(driverTaskRecord)){
            return false;
        }
//        if(BooleanUtils.isFalse(driverTaskRecord.getTaskStatus().isActive())){
//            return false;
//        }
        LocalDateTime localDateTime = LocalDateTime.now();
        if(localDateTime.minusHours(ONE_DAY_HOURS).isAfter(driverTaskRecord.getDataChangeCreateTime())){
            return false;
        }
        return true;
    }

    private DriverTaskInfoDTO convertToTaskInfoDTO(DriverTaskRecordDO driverTaskRecord){
        DriverTaskInfoDTO taskInfo = new DriverTaskInfoDTO();
        taskInfo.setTaskId(driverTaskRecord.getTaskId());
        taskInfo.taskStatus = DriverTaskStatus.findByValue(driverTaskRecord.getTaskStatus().getValue());
        taskInfo.taskPeriodWorkTime = driverTaskRecord.getTaskPeriodWorkTime();
        if (Objects.nonNull(driverTaskRecord.getTaskValidEndTime())) {
            taskInfo.taskValidEndTime = LocalDateTimeUtils.format(driverTaskRecord.getTaskValidEndTime());
        }
        taskInfo.customerOrderId = driverTaskRecord.getCustomerOrderId();
        taskInfo.driverOrderId = driverTaskRecord.getDriverOrderId();
        taskInfo.carId = driverTaskRecord.getCustomerOrderCarId();
        return taskInfo;
    }

    @Override
    public void onExecuted(QueryDriverTaskInfoRequestType req, QueryDriverTaskInfoResponseType resp) {
        super.onExecuted(req, resp);
        Cat.logEvent(CatEventType.DRIVER_TASK_QUERY, resp.getResponseResult().getReturnCode());
    }

    @Override
    public void validate(AbstractValidator<QueryDriverTaskInfoRequestType> validator) {
        validator.ruleFor("driverId").notNull().greaterThan(0L);
    }

}
