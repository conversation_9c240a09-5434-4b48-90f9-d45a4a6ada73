package com.ctrip.dcs.driver.domain.application.redis;

import com.ctrip.dcs.driver.domain.application.util.LanguageUtil;
import com.ctrip.dcs.driver.domain.application.util.PushInfoUtil;
import com.ctrip.dcs.driver.domain.setting.DriverPushConfigInfoDTO;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class PushConfigRedisLogic {

    @Autowired
    DirectorRedis directorRedis;

    /**
     * 保存司机推送信息
     * */
    public void saveDriverPushConfig(long driverId, DriverPushConfigInfoDTO driverPushConfigInfo) {
        if(Objects.nonNull(driverPushConfigInfo)) {
            directorRedis.set(this.getPushConfigKey(driverId), driverPushConfigInfo,
                    PushInfoUtil.DRIVER_PUSH_CONFIG_REDIS_EXPIRE_TIME);
        }
    }

    /**
     * 查询司机推送信息
     * */
    public DriverPushConfigInfoDTO queryDriverPushConfig(long driverId) {
        try {
            String redisInfo = directorRedis.get(this.getPushConfigKey(driverId));
            if(StringUtils.isNotBlank(redisInfo)) {
                return JacksonUtil.deserialize(redisInfo, DriverPushConfigInfoDTO.class);
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    /**
     * 配置KEY
     * */
    private String getPushConfigKey(long driverId) {
        return String.format(PushInfoUtil.DRIVER_PUSH_CONFIG_REDIS_KEY, driverId);
    }

    /**
     * 更新司机语言
     * */
    public void updateDriverLanguage(long driverId, String locale) {
        String driverLanguageKey = String.format(PushInfoUtil.DRIVER_LANGUAGE_REDIS_KEY, driverId);
        if(LanguageUtil.isMultiLanguage(locale)) {
            directorRedis.set(driverLanguageKey, locale, -1);
        } else {
            directorRedis.remove(driverLanguageKey);
        }
    }

    /**
     * 获取司机语言
     * */
    public String getDriverLanguage(long driverId) {
        String driverLanguageKey = String.format(PushInfoUtil.DRIVER_LANGUAGE_REDIS_KEY, driverId);
        return directorRedis.get(driverLanguageKey);
    }
}
