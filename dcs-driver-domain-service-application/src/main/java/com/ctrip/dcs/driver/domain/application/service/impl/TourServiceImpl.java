package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.service.TourService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.tour.ProductBasicServiceProxy;
import com.ctrip.frt.product.soa.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.language.LanguageContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class TourServiceImpl implements TourService {
    private static final Logger logger = LoggerFactory.getLogger(TourService.class);
    @Autowired
    ProductBasicServiceProxy productBasicServiceProxy;

    @Override
    public DriverBasicInfoType getDriverInfo(long driverId) {
        try {
            DriverInfoRequestType requestType = new DriverInfoRequestType();
            requestType.setLocale(LanguageContext.getLanguage().getLocaleCode());
            List<DriverKeyType> keyList = new ArrayList<>();
            DriverKeyType driverKeyType = new DriverKeyType();
            driverKeyType.setDriverId(driverId);
            keyList.add(driverKeyType);
            requestType.setKeys(keyList);
            DriverInfoReturnOptionType returnOptionType = new DriverInfoReturnOptionType();
            returnOptionType.setNeedDriverBasicInfo(true);
            requestType.setReturnOption(returnOptionType);
            DriverInfoResponseType responseType = productBasicServiceProxy.getDriverInfo(requestType);
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getDriverInfoList())) {
                return responseType.getDriverInfoList().get(0).getBasicInfo();
            }
        } catch (Exception e) {
            logger.error("productBasicServiceProxy.getDriverInfo", e);
        }
        return null;
    }
}
