package com.ctrip.dcs.driver.domain.application.domain.exector;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourRankLikeRecordPO;
import com.ctrip.dcs.driver.value.honour.RankObject;
import org.owasp.csrfguard.util.Strings;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;

public class SaveRankLikeExecutor extends DomainExecutor<RankObject, Long, String> {

  public SaveRankLikeExecutor(RankObject owner, DomainBeanFactory domainBeanFactory) {
    super(owner, domainBeanFactory);
  }

  @Override
  public String doWork(Long likedDriverId) {
      // 保存redis
      this.domainBeanFactory.honourRedisLogic().updateDriverRankLikeCount(
              this.owner.cityId(),
              likedDriverId,
              this.owner.isWeek() ? 1 : 2,
              this.owner.subDataTime());

      this.domainBeanFactory.executorService().submit(() -> {
          saveRankLikeRecord(likedDriverId);
      });
        return Strings.EMPTY;
  }

  protected void saveRankLikeRecord(long likedDriverId) {
      // 保存数据库 +1
      DrivHonourRankLikeRecordPO rankLikeRecordPO =
              this.domainBeanFactory.honourDBDataService().searchHonourRankLikeInfo(this.owner.subDataTime(), likedDriverId);
      boolean isUpdate = false;
      if(Objects.isNull(rankLikeRecordPO)){
          rankLikeRecordPO = new DrivHonourRankLikeRecordPO();
          rankLikeRecordPO.setLikeCount(1);
      } else {
          rankLikeRecordPO.setLikeCount(Math.addExact(rankLikeRecordPO.getLikeCount(), 1));
          isUpdate = true;
      }
      rankLikeRecordPO.setType(this.owner.isWeek() ? 1 : 2);
      rankLikeRecordPO.setRankRefs(this.owner.subDataTime());
      rankLikeRecordPO.setDriverId(likedDriverId);
      rankLikeRecordPO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
      int value = 0;
      if(isUpdate) {
          value = this.domainBeanFactory.honourDBDataService().updateHonourRankLikeInfo(rankLikeRecordPO);
      } else {
          value = this.domainBeanFactory.honourDBDataService().insertHonourRankLikeInfo(rankLikeRecordPO);
      }
      CommonLogger.INSTANCE.info(this.getClass().getSimpleName(),
              String.format("DrivHonourRankLikeRecord result : [%s] [%s]", likedDriverId, value));
  }
}
