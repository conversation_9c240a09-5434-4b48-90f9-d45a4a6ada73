package com.ctrip.dcs.driver.account.application.mq;
/*
作者：pl.yang
创建时间：2025/5/8-下午8:37-2025
*/


import com.ctrip.dcs.driver.account.application.service.AccountBankCardService;
import com.ctrip.dcs.driver.account.domain.condition.DriverBankCardBindResultCondition;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DriverAccountBingBankCardResultListener
 * @Package com.ctrip.dcs.driver.account.application.mq
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 下午8:37
 */

@Component
public class DriverAccountBingBankCardResultListener {
    public static final Logger logger = LoggerFactory.getLogger(DriverAccountBingBankCardResultListener.class);
    @Autowired
    AccountBankCardService accountBankCardService;

    @QmqConsumer(prefix = Constants.DRIVER_BANK_CARD_REGISTRATION_RESULT, consumerGroup = "*********")
    public void onMessage(Message message) {
        DriverBankCardBindResultCondition condition = convertDriverBankCardBindResultCondition(message);
        logger.info(condition.getRequestId());
        accountBankCardService.driverBankCardBindResultHandle(condition);
    }

    private DriverBankCardBindResultCondition convertDriverBankCardBindResultCondition(Message message) {
        String data = message.getStringProperty("data");
        return JacksonUtil.deserialize(data, DriverBankCardBindResultCondition.class);
    }


}
