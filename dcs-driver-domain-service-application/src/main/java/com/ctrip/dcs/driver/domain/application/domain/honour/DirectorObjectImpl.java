package com.ctrip.dcs.driver.domain.application.domain.honour;

import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.utils.GuideUtils;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.driver.value.honour.DirectorObject;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.dto.GuideBaseInfoDTO;
import com.ctrip.dcs.tms.transport.api.model.DriverInfo;
import com.google.gson.annotations.Expose;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 司导领域
 * */
public final class DirectorObjectImpl extends DirectorObject {

  public static final class Builder {
    private final DomainBeanFactory domainBeanFactory;
    private final long directorId;
    private final int directorType;
    private DirectorObjectImpl director;

    public Builder(long directorId, int directorType, DomainBeanFactory domainBeanFactory) {
      this.directorId = directorId;
      this.directorType = directorType;
      this.domainBeanFactory = domainBeanFactory;
    }

    public DirectorObject build() {
      if(directorId == 0) {
        return null;
      }

      this.director = new DirectorObjectImpl();
      if(GuideUtils.isGuide(this.directorId, this.directorType)) {
        GuideBaseInfoDTO baseInfoDTO = domainBeanFactory.gmsTransportDomainServiceProxy()
                .queryGuideBaseInfoById(directorId);
        if(Objects.isNull(baseInfoDTO) || Objects.isNull(baseInfoDTO.guideId)){
          return null;
        }
        this.bulidGuideInfo(baseInfoDTO);
      } else {
        DriverInfo driverInfo = domainBeanFactory.tmsTransportServiceProxy().queryDriver(directorId);
        if(Objects.isNull(driverInfo) || Objects.isNull(driverInfo.driverId)){
          return null;
        }
        this.bulidDriverInfo(driverInfo);
      }
      return this.director;
    }

    /**
     * 向导
     * */
    private void bulidGuideInfo(GuideBaseInfoDTO baseInfoDTO){
      this.director.isDriver = false;
      this.director.id = baseInfoDTO.guideId;
      this.director.name = baseInfoDTO.name;
      this.director.identityCardId = baseInfoDTO.identityCardNum;
      this.director.onlineDate = LocalDate.MIN;
      if(StringUtils.isNotBlank(baseInfoDTO.onlineTime)) {
        this.director.onlineDate = LocalDateTimeUtils.localDateTime(baseInfoDTO.onlineTime).toLocalDate();
      }
      this.director.cityId = ObjectUtils.defaultIfNull(baseInfoDTO.cityId, 0L);
      this.director.isMainLand = ObjectUtils.defaultIfNull(baseInfoDTO.areaScope, 0) == 0;
      this.director.isFixedLocationDriver = false;
      this.director.isCharterDriver = false;
      this.director.driverHeadImg = baseInfoDTO.headImageUrl;
    }

    /**
     * 司机
     * */
    private void bulidDriverInfo(DriverInfo driverInfo){
      this.director.isDriver = true;
      this.director.id = driverInfo.driverId;
      this.director.name = driverInfo.driverName;
      this.director.identityCardId = driverInfo.drvIdcard;
      this.director.onlineDate = LocalDate.MIN;
      this.director.onlineDate = LocalDateTimeUtils.localDateTime(driverInfo.onlineTime).toLocalDate();
      this.director.cityId = ObjectUtils.defaultIfNull(driverInfo.cityId, 0L);
      this.director.isMainLand = ObjectUtils.defaultIfNull(driverInfo.internalScope, 0) == 0;
      this.director.driverHeadImg = driverInfo.realPicUrl;  //picurl可能为卡通头像
      this.director.isFixedLocationDriver = CollectionUtils.isNotEmpty(driverInfo.drvProductionLineCodeList) &&
              driverInfo.drvProductionLineCodeList.contains(1);
      this.director.pointToPoint = CollectionUtils.isNotEmpty(driverInfo.drvProductionLineCodeList) &&
              driverInfo.drvProductionLineCodeList.contains(22);
      this.director.isCharterDriver = CollectionUtils.isNotEmpty(driverInfo.drvProductionLineCodeList) &&
              driverInfo.drvProductionLineCodeList.contains(3);
    }
  }

  public DirectorObjectImpl() {

  }

  private boolean isDriver;
  private boolean isFixedLocationDriver;
  private boolean pointToPoint;
  private boolean isCharterDriver;
  private boolean isMainLand;

  @Expose
  private long id;
  @Expose
  private String name;
  @Expose
  private String identityCardId;
  @Expose
  private LocalDate onlineDate;
  @Expose
  private long cityId;
  @Expose
  private String driverHeadImg;

  @Override
  public long id() {
    return this.id;
  }

  @Override
  public String name() {
    return this.name;
  }

  @Override
  public String identityCardId() {
    return this.identityCardId;
  }

  @Override
  public LocalDate onlineDate() {
    return this.onlineDate;
  }

  @Override
  public long cityId() {
    return this.cityId;
  }

  @Override
  public String driverHeadImg() {
    return this.driverHeadImg;
  }

  @Override
  public boolean isSupportHonourMedal(){
    return this.isDriver
            && this.isMainLand
            && (this.isFixedLocationDriver || this.pointToPoint);// 同时支持接送机和点对点
  }

  // 增加产险过滤
  @Override
  public boolean isSupportHonourRank(List<Long> rankOpenCityList){
    return this.isDriver
            && this.isMainLand
            && (this.isFixedLocationDriver || this.pointToPoint)// 同时支持接送机和点对点
            && CollectionUtils.isNotEmpty(rankOpenCityList)
            && rankOpenCityList.contains(this.cityId);
  }

  @Override
  public boolean isSupportCommemoration(){
    return this.isMainLand;
  }

}
