package com.ctrip.dcs.driver.domain.application.exector.setting;

import com.ctrip.dcs.driver.domain.application.common.CommonLogger;
import com.ctrip.dcs.driver.domain.application.redis.DirectorRedis;
import com.ctrip.dcs.driver.domain.application.redis.PushConfigRedisLogic;
import com.ctrip.dcs.driver.domain.application.util.PushInfoUtil;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverOrderPushConfigDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import com.ctrip.dcs.driver.domain.setting.DriverPushConfigInfoDTO;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPushConfigRequestType;
import com.ctrip.dcs.driver.domain.setting.QueryDriverPushConfigResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingBaseExecutor;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 查询司机推送配置
 */
@Component
public class QueryDriverPushConfigEngine extends ShoppingExecutor<QueryDriverPushConfigRequestType, QueryDriverPushConfigResponseType>
        implements Validator<QueryDriverPushConfigRequestType> {

    @Autowired
    private DriverOrderPushConfigDao driverOrderPushConfigDao;

    @Autowired
    private PushConfigRedisLogic pushConfigRedisLogic;

    @Autowired
    DirectorRedis directorRedis;


    @Override
    public void validate(AbstractValidator<QueryDriverPushConfigRequestType> validator) {
        validator.ruleFor("driverIdList").notNull().notEmpty();
    }

    private static final class Executor extends ShoppingBaseExecutor {
        private final QueryDriverPushConfigEngine owner;
        private final QueryDriverPushConfigResponseType response;
        private final QueryDriverPushConfigRequestType request;

        private Executor(QueryDriverPushConfigRequestType request, QueryDriverPushConfigResponseType response, QueryDriverPushConfigEngine owner) {
            super(request);
            this.owner = owner;
            this.response= response;
            this.request = request;
        }

        /**
         * 查询 drv_driver_order_push_config 数据
         * */
        private void QueryDriverPushConfigInfo() {
            this.response.pushInfoListList = new ArrayList<>();
            if(CollectionUtils.isEmpty(this.request.driverIdList)) {
                return;
            }
            if(this.request.driverIdList.size() ==1){
                long driverId = this.request.driverIdList.get(0);
                //缓存
                DriverPushConfigInfoDTO driverPushConfigInfo = this.owner.pushConfigRedisLogic.queryDriverPushConfig(driverId);
                if(Objects.nonNull(driverPushConfigInfo)) {
                    this.response.pushInfoListList = Collections.singletonList(driverPushConfigInfo);
                    return;
                }

                // 查DB
                DriverOrderPushConfigPO driverOrderPushConfig = this.owner.driverOrderPushConfigDao.findOne(driverId);
                if(Objects.nonNull(driverOrderPushConfig)) {
                    driverPushConfigInfo = PushInfoUtil.convertToPushInfo(driverOrderPushConfig);
                    this.response.pushInfoListList = Collections.singletonList(driverPushConfigInfo);
                    this.owner.pushConfigRedisLogic.saveDriverPushConfig(driverId, driverPushConfigInfo);
                }
            } else {
                List<String> pushResult = this.owner.directorRedis.mget(this.request.driverIdList.stream().map(PushInfoUtil::getPushConfigKey).toArray(String[]::new));
                if(CollectionUtils.isNotEmpty(pushResult)) {
                    pushResult.forEach(p -> {
                        try {
                            if (StringUtils.isNotBlank(p)) {
                                DriverPushConfigInfoDTO driverPushConfigInfo =
                                        JacksonUtil.deserialize(p.trim(), DriverPushConfigInfoDTO.class);
                                if (driverPushConfigInfo != null) {
                                    this.response.pushInfoListList.add(driverPushConfigInfo);
                                }
                            }
                        } catch (Exception e) {
                            CommonLogger.INSTANCE.warn("driverPushConfigInfo failed [%s]", p);
                        }
                    });
                }
            }
        }

        @Override
        protected boolean validate() {
            return true;
        }

        @Override
        protected void buildResponse() {
            if(!this.validate()){
                return;
            }

            this.QueryDriverPushConfigInfo();
        }
    }

    @Override
    public QueryDriverPushConfigResponseType execute(QueryDriverPushConfigRequestType request) {
        QueryDriverPushConfigResponseType response = new QueryDriverPushConfigResponseType();
        Executor executor = new Executor(request, response, this);
        executor.buildResponse();
        return ServiceResponseUtils.success(response);
    }

    @Override
    public QueryDriverPushConfigResponseType onException(QueryDriverPushConfigRequestType req, Exception ex) {
        CommonLogger.INSTANCE.warn("QueryDriverPushConfig error", ex);
        return super.onException(req, ex);
    }
}
