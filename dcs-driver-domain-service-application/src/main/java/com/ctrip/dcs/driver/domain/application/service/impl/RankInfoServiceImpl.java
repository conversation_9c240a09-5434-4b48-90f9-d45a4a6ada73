package com.ctrip.dcs.driver.domain.application.service.impl;

import com.ctrip.dcs.driver.domain.application.redis.HonourRedisLogic;
import com.ctrip.dcs.driver.domain.application.service.HonourDBDataService;
import com.ctrip.dcs.driver.domain.application.service.RankInfoService;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.message.DriverMessageServiceProxy;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankCityDataModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.RankOriginalDataModel;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.HonourFormConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.MaskHelperUtils;
import com.ctrip.dcs.driver.message.api.PushMessageRequestType;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 聚合排行榜有关的查询、更新
 * */
@Component
public class RankInfoServiceImpl implements RankInfoService {

    @Autowired
    private DriverMessageServiceProxy driverMessageServiceProxy;

    @Autowired
    private HonourFormConfig honourFormConfig;

    @Autowired
    private HonourRedisLogic honourRedisLogic;

    @Autowired
    private TmsTransportServiceProxyImpl tmsTransportServiceProxy;

    @Autowired
    private HonourDBDataService honourDBDataService;

    @Autowired
    @Qualifier("CommonThreadPool")
    private ExecutorService executorService;

    private static final Logger LOGGER = LoggerFactory.getLogger(RankInfoService.class);
    private static final String WEEK_RANK_NOTICE_TEMPLATEID = "4018";
    private static final String MONTH_RANK_NOTICE_TEMPLATEID = "4019";

    @Override
    public void updateRankListForSchedule(){
        this.updateRankData(honourFormConfig.getRankOpenCity());
    }

    @Override
    public void updateRankListForManual(List<Long> cityIdList){
        List<Long> openCityList = honourFormConfig.getRankOpenCity();
        //取交集
        cityIdList.retainAll(openCityList);
        if(CollectionUtils.isNotEmpty(cityIdList)) {
            this.updateRankData(cityIdList);
        }
    }

    @Override
    public void sendRankUpdateNotice(boolean isWeek) {
        List<Long> rankCityList = honourFormConfig.getRankOpenCity();
        if(CollectionUtils.isNotEmpty(rankCityList)) {
            rankCityList.forEach(c -> {
                getPartitionDriverList(c, isWeek);
            });
        }
    }

    /**
     * 获取最大批次时间
     * */
    private long getMaxRankRefs() {
        AdmPrdTrhDriverHonorInfoPO honorInfoPO = honourDBDataService.queryMaxBatchDataInfo();
        if(Objects.isNull(honorInfoPO)){
            return 0;
        }

        //保存最新的rank基础信息
        RankOriginalDataModel rankInfoModel = new RankOriginalDataModel();
        rankInfoModel.setBatchTime(honorInfoPO.getBatchTime());
        rankInfoModel.setDataTime(honorInfoPO.getDataTime());
        rankInfoModel.setDataTimeMonth(honorInfoPO.getDataTimeMonth());
        rankInfoModel.setDataTimeWeek(honorInfoPO.getDataTimeWeek());
        this.honourRedisLogic.saveCurrentRankInfo(rankInfoModel);

        return honorInfoPO.getBatchTime();
    }

    /**
     * 更新排行榜
     */
    private void updateRankData(List<Long> rankCityList) {
        if(CollectionUtils.isNotEmpty(rankCityList)) {
            long batchId = this.getMaxRankRefs();
            rankCityList.forEach(c -> {
                this.updateCityRankData(batchId, c);
            });
        }
    }

    /**
     * 分别更新周榜和月榜
     * */
    private void updateCityRankData(long batchId, long cityId){
        // 周榜
        List<RankOriginalDataModel> rankWeekDataList = honourDBDataService.queryCityRankList(batchId, cityId, true);
        if(CollectionUtils.isNotEmpty(rankWeekDataList)) {
            List<RankOriginalDataModel> weekRankList = rankWeekDataList.stream()
                    .filter(r -> r.getWeekOrderCnt() > 0)
                    .sorted(Comparator.comparing(RankOriginalDataModel::getWeekOrderCnt).reversed()
                            .thenComparing(RankOriginalDataModel::getDrvId))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(weekRankList)) {
                List<RankCityDataModel> weekRedisRankList = resetRank(weekRankList, true);
                honourRedisLogic.updateCityRankData(cityId, weekRedisRankList, true);
            }
        }
        // 月榜
        List<RankOriginalDataModel> rankMonthDataList = honourDBDataService.queryCityRankList(batchId, cityId, false);
        if(CollectionUtils.isNotEmpty(rankMonthDataList)) {
            List<RankOriginalDataModel> monthRankList = rankMonthDataList.stream()
                    .filter(r -> r.getMonthOrderCnt() > 0)
                    .sorted(Comparator.comparing(RankOriginalDataModel::getMonthOrderCnt).reversed()
                                      .thenComparing(RankOriginalDataModel::getDrvId))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(monthRankList)){
                List<RankCityDataModel> monthRedisRankList = resetRank(monthRankList,false);
                honourRedisLogic.updateCityRankData(cityId, monthRedisRankList, false);
            }
        }
    }

    /**
     * 重新排名
     * */
    private List<RankCityDataModel> resetRank(List<RankOriginalDataModel> rankList, boolean isWeek) {
        List<RankCityDataModel> resetRankList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger();          // 游标
        int rank = 1;                                       // 名次
        long lastOrderCnt = 0;
        for(RankOriginalDataModel r : rankList) {
            index.getAndIncrement();
            RankCityDataModel model = new RankCityDataModel();
            model.setDrvId(r.getDrvId());
            model.setDrvName(MaskHelperUtils.maskName(r.getDrvName()));
            model.setOrderCnt((int)(isWeek ? r.getWeekOrderCnt() : r.getMonthOrderCnt()));
            // 排名重置为游标
            if(model.getOrderCnt() < lastOrderCnt) {
                rank = index.get();
            }
            model.setRanking(rank);
            resetRankList.add(model);
            lastOrderCnt = model.getOrderCnt();
        }
        return resetRankList;
    }

    /**
     * 分批次发发送通知
     * */
    private void getPartitionDriverList(long cityId, boolean isWeek) {
        List<Long> driverIdList = tmsTransportServiceProxy.queryDriverList(cityId);
        if (CollectionUtils.isNotEmpty(driverIdList)) {
            List<List<Long>> driverIdsByPartition = Lists.partition(driverIdList, 200);
            for (List<Long> partitionDriverList : driverIdsByPartition) {
                if(CollectionUtils.isNotEmpty(partitionDriverList)) {
                    this.pushRankNoticeMessage(partitionDriverList,
                            isWeek ? WEEK_RANK_NOTICE_TEMPLATEID : MONTH_RANK_NOTICE_TEMPLATEID);
                }

                // 小红点提醒
                honourRedisLogic.batchNotieRankUpdate(partitionDriverList);

                // 间隔调用
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    LOGGER.info("getPartitionDriverList sleep");
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 发送通知
     * */
    private void pushRankNoticeMessage(List<Long> driverIdList, String templateId) {
        executorService.submit(() -> {
            PushMessageRequestType pushMessage = new PushMessageRequestType();
            pushMessage.setDriverIds(driverIdList);
            pushMessage.setTemplateId(templateId);
            Map<String, String> sharkMap = new HashMap<>();
            pushMessage.setSharkValues(sharkMap);

            Map<String, String> dataMap = new HashMap<>();
            pushMessage.setData(dataMap);
            driverMessageServiceProxy.pushMessage(pushMessage);
        });
    }
}
