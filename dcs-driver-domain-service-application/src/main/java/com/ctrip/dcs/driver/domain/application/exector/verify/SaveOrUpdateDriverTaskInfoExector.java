package com.ctrip.dcs.driver.domain.application.exector.verify;

import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapter;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO;
import com.ctrip.dcs.driver.domain.task.SaveOrUpdateDriverTaskInfoRequestType;
import com.ctrip.dcs.driver.domain.task.SaveOrUpdateDriverTaskInfoResponseType;
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SaveOrUpdateDriverTaskInfoExector extends ShoppingExecutor<SaveOrUpdateDriverTaskInfoRequestType, SaveOrUpdateDriverTaskInfoResponseType>
        implements Validator<SaveOrUpdateDriverTaskInfoRequestType> {

    @Autowired
    DriverTaskAdapter driverTaskAdapter;

    @Override
    public SaveOrUpdateDriverTaskInfoResponseType execute(SaveOrUpdateDriverTaskInfoRequestType request) {
        SaveOrUpdateDriverTaskInfoResponseType responseType = new SaveOrUpdateDriverTaskInfoResponseType();

        DriverTaskStatusEnum taskStatusEnum = DriverTaskStatusEnum.ofValue(request.task.taskStatus);
        switch (taskStatusEnum){
            case CREATE:
                DriverTaskRecordDO driverTaskRecord = convertToDriverTaskRecord(request);
                int result = driverTaskAdapter.insertTaskRecord(driverTaskRecord);
                if(result > 0){
                    return ServiceResponseUtils.success(responseType);
                } else {
                    return ServiceResponseUtils.fail(responseType);
                }
            case FINISHED:
                driverTaskAdapter.updateTaskStatus(request.task.taskId, DriverTaskStatusEnum.FINISHED);
                break;
            default:
                break;
        }
        return ServiceResponseUtils.success(responseType);
    }

    private DriverTaskRecordDO convertToDriverTaskRecord(SaveOrUpdateDriverTaskInfoRequestType request) {
        DriverTaskRecordDO driverTaskRecord = new DriverTaskRecordDO();
        driverTaskRecord.setDriverId(request.driverId);
        driverTaskRecord.setTaskId(request.task.taskId);
        driverTaskRecord.setTaskStatus(DriverTaskStatusEnum.CREATE);
        driverTaskRecord.setTaskPeriodWorkTime(request.task.taskId);
        driverTaskRecord.setCustomerOrderId(request.task.customerOrderId);
        driverTaskRecord.setDriverOrderId(request.task.driverOrderId);
        driverTaskRecord.setCustomerOrderCarId(ObjectUtils.defaultIfNull(request.task.carId, 0L));
        return driverTaskRecord;
    }

    @Override
    public void onExecuted(SaveOrUpdateDriverTaskInfoRequestType req, SaveOrUpdateDriverTaskInfoResponseType resp) {
        super.onExecuted(req, resp);
        Cat.logEvent(CatEventType.DRIVER_TASK_SAVE, resp.getResponseResult().getReturnCode());
    }

    @Override
    public void validate(AbstractValidator<SaveOrUpdateDriverTaskInfoRequestType> validator) {
        validator.ruleFor("driverId").notNull().greaterThan(0L);
        validator.ruleFor("task").notNull();
    }

}
