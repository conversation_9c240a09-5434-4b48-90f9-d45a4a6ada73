package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.domain.account.UpdateAccountWithdrawalStatusByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.UpdateAccountWithdrawalStatusByDriverIdResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctriposs.baiji.rpc.server.validation.AbstractValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Objects;

@Component
public class UpdateAccountWithdrawalStatusByDriverIdEngine extends ShoppingExecutor<UpdateAccountWithdrawalStatusByDriverIdRequestType, UpdateAccountWithdrawalStatusByDriverIdResponseType> implements Validator<UpdateAccountWithdrawalStatusByDriverIdRequestType> {

    @Autowired
    AccountBaseInfoDao accountBaseInfoDao;

    @Autowired
    AccountService accountService;

    @Override
    public UpdateAccountWithdrawalStatusByDriverIdResponseType execute(UpdateAccountWithdrawalStatusByDriverIdRequestType request) {
        UpdateAccountWithdrawalStatusByDriverIdResponseType resp = new UpdateAccountWithdrawalStatusByDriverIdResponseType();
        long driverId = request.driverId;
        //反查uid
        AccountInfoDTO accountInfo = accountService.getAccountByDriverId(driverId);
        if (Objects.isNull(accountInfo) || StringUtils.isBlank(accountInfo.getUid())){
            return ServiceResponseUtils.fail(resp);
        }

        AccountBaseInfoPO accountBaseInfo = accountBaseInfoDao.queryByUID(accountInfo.getUid());
        if (Objects.isNull(accountBaseInfo)) {
            return ServiceResponseUtils.fail(resp);
        }

        accountBaseInfo.setWithdrawStatus(request.withdrawalStatus);
        accountBaseInfo.setModifyUser(Objects.equals(request.getUpdateSource(), 1) ? request.getModifyUser() : "System");
        accountBaseInfo.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()).toLocalDateTime());
        int result = accountBaseInfoDao.update(accountBaseInfo);
        if (result > 0) {
            //刷新缓存
            accountService.refreshAccountCache(accountInfo.getUid());
            return ServiceResponseUtils.success(resp);
        }
        return ServiceResponseUtils.fail(resp);
    }

    @Override
    public void validate(AbstractValidator<UpdateAccountWithdrawalStatusByDriverIdRequestType> validator) {
        validator.ruleFor("driverId").notNull().greaterThan(0L);
        validator.ruleFor("withdrawalStatus").notNull().greaterThanOrEqualTo(0).lessThanOrEqualTo(1);
        validator.ruleFor("updateSource").notNull().greaterThan(0);
    }
}
