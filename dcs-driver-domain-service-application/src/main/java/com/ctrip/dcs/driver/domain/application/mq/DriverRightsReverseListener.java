package com.ctrip.dcs.driver.domain.application.mq;

import com.ctrip.dcs.driver.domain.application.common.CommonRightsExtendUtils;
import com.ctrip.dcs.driver.domain.application.domain.DomainBeanFactory;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsStatusEnum;
import com.ctrip.dcs.driver.domain.infrastructure.constant.RightsTypeEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights.DrivRightsRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.WorkBenchLogMessage;
import com.ctrip.dcs.driver.domain.infrastructure.helper.LockService;
import com.ctrip.dcs.driver.domain.rights.mq.UseRightsMessage;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.agile.Strings;
import qunar.tc.qmq.Message;
import qunar.tc.qmq.MessageListener;
import qunar.tc.qmq.consumer.annotation.QmqConsumer;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class DriverRightsReverseListener implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(DriverRightsReverseListener.class);

    public static final String CONSUMER_GROUP = "100038374";

    @Autowired
    DomainBeanFactory domainBeanFactory;
    @Autowired
    LockService lockService;
    @Autowired
    DrivRightsDao drivRightsDao;
    @Autowired
    DrivRightsRecordDao rightsRecordDao;

    @Override
    @QmqConsumer(prefix = "dcs.driver.rights.reverse", consumerGroup = CONSUMER_GROUP)
    public void onMessage(Message message) {
        String data = message.getStringProperty("content");
        if (Strings.isBlank(data)) {
            return;
        }
        UseRightsMessage useRightsMessage = JacksonUtil.deserialize(data, UseRightsMessage.class);
        if (Objects.isNull(useRightsMessage)) {
            return;
        }

        Boolean isSuccess = lockService.executeInLock(String.format("dcs_driver_use_rights:%s_%s", useRightsMessage.getDriverId(), useRightsMessage.getRightsType()), 1000, () -> handleReverse(useRightsMessage));
        if(isSuccess){
            // 发消息推送给客服
            try {
                WorkBenchLogProducer.sendMessage(buildWorkBenchLogMessage(useRightsMessage));
            } catch (Exception e) {
                LOGGER.error(e);
            }
        }
    }

    private WorkBenchLogMessage buildWorkBenchLogMessage(UseRightsMessage useRightsMessage) {
        WorkBenchLogMessage workBenchLogMessage = new WorkBenchLogMessage();
        workBenchLogMessage.setOrderId(Long.valueOf(useRightsMessage.getUserOrderId()));
        workBenchLogMessage.setLogType(0);
        workBenchLogMessage.setSourceType(3);
        workBenchLogMessage.setContentType(0);
        workBenchLogMessage.setTitleKey("op.operationlog.title.Cancellationofbonuspayments");
        workBenchLogMessage.setContentKey("op.operationlog.content.Cancellationofbonuspayments");
        Map<String, String> contentParams = new HashMap<>();
        contentParams.put("DriverId", useRightsMessage.getDriverId().toString());
        contentParams.put("Welfare", Optional.ofNullable(useRightsMessage.getMoney()).orElse(BigDecimal.ZERO).toString());
        workBenchLogMessage.setContentParams(JacksonUtil.serialize(contentParams));
        workBenchLogMessage.setLogTime(System.currentTimeMillis());
        workBenchLogMessage.setOperator("SYSTEM");
        workBenchLogMessage.setAppId(Foundation.app().getAppId());
        return workBenchLogMessage;

    }

    private Boolean handleReverse(UseRightsMessage useRightsMessage) {
        DrivRightsPO drivRightsPO = buildDrivRightsPO(useRightsMessage);
        DrivRightsRecordPO drivRightsRecordPO = buildDrivRightsRecordPO(useRightsMessage);
        if (Objects.isNull(drivRightsPO) || Objects.isNull(drivRightsRecordPO)) {
            return false;
        }
        try{
            reverseAndUpdateRights(drivRightsRecordPO, drivRightsPO);
        }catch (Exception e){
            LOGGER.warn("failed to reverseAndUpdateRights");
            return false;
        }
        return true;
    }

    @Transactional
    public void reverseAndUpdateRights(DrivRightsRecordPO recordPO, DrivRightsPO rightsPO) {
        rightsRecordDao.update(recordPO);
        drivRightsDao.update(rightsPO);
    }

    private DrivRightsPO buildDrivRightsPO(UseRightsMessage useRightsMessage) {
        DrivRightsPO drivRightsPO = drivRightsDao.queryDriverRights(useRightsMessage.getDriverId(), useRightsMessage.getDate()).stream()
                .filter(po -> (!po.getRightsStatus().equals(RightsStatusEnum.RIGHTS_STATUS_CANCELLED.getStatus())))
                .filter(po -> useRightsMessage.getRightsType().equals(po.getRightsType()))
                .findFirst().orElse(null);
        if (Objects.isNull(drivRightsPO)) {
            LOGGER.info("DriverRightsReverse","rights is null");
            return null;
        }
        if (drivRightsPO.getUseCount() == 1) {
            drivRightsPO.setUseCount(0);
            drivRightsPO.setRightsStatus(RightsStatusEnum.RIGHTS_STATUS_ISSUED.getStatus());
        } else {
            drivRightsPO.setUseCount(drivRightsPO.getUseCount() - 1);
        }
        if (useRightsMessage.getRightsType().equals(RightsTypeEnum.RIGHTS_TYPE_WELFARE.getCode())) {
            drivRightsPO.setExtend(CommonRightsExtendUtils.buildExtendForReverse(drivRightsPO.getExtend(), useRightsMessage.getMoney()));
        }
        return drivRightsPO;
    }

    private DrivRightsRecordPO buildDrivRightsRecordPO(UseRightsMessage useRightsMessage) {
        DrivRightsRecordPO drivRightsRecordPO = rightsRecordDao.queryDriverRecordsBySupplyOrderId(useRightsMessage.getDriverId(), useRightsMessage.getRightsType(), useRightsMessage.getSupplyOrderId()).stream().filter(o -> o.getIsDeleted() == 0).findFirst().orElse(null);
        if (Objects.isNull(drivRightsRecordPO)) {
            LOGGER.info("DriverRightsReverse","record is null or used");
            return null;
        }
        drivRightsRecordPO.setIsDeleted(1);
        return drivRightsRecordPO;
    }
}
