package com.ctrip.dcs.driver.domain.application.domain;

import com.ctrip.dcs.driver.account.domain.config.PhoneCheckConfig;
import com.ctrip.dcs.driver.account.domain.constant.PhoneCheckTaskState;
import com.ctrip.dcs.driver.account.domain.enums.PhoneCheckTaskType;
import com.ctrip.dcs.driver.account.domain.value.PhoneCheckVoiceCallTask;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.DriverEntity;
import com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity.PhoneCheckTaskEntity;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport.DriverDeviceInfoDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverDeviceInfoPO;
import com.ctrip.dcs.geo.domain.repository.TimeZoneRepository;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2024/12/11 14:41
 * @Description: 手机号校验任务生成器
 */
@Component
public class PhoneCheckTaskFactory {
    Log log = Log.getInstance(PhoneCheckTaskFactory.class);

    static final Long BJ_CITY = 1L;
    @Resource
    private PhoneCheckConfig config;
    @Resource
    private TimeZoneRepository timeZoneRepository;
    @Resource
    private DriverDeviceInfoDao driverDeviceInfoDao;

    static Map<String, String> table = new HashMap<>();
    static {
        table.put("zh-CN","CN");
        table.put("zh-HK","CN");
        table.put("zh-TW","CN");
        table.put("en-US","EN");
        table.put("ja-JP","JP");
        //泰语
        table.put("th-TH","TH");
        //越南
        table.put("vi-VN","VN");
        //印度尼西亚语
        table.put("id-ID","ID");
        //西班牙语
        table.put("es-ES","ES");
        //土耳其语
        table.put("tr-TR","TR");
        // 法语
        table.put("fr-FR","FR");
    }

    public PhoneCheckTaskEntity applyVoiceTask(DriverEntity driver, String verificationCode, String language) {
        return this.apply(driver, PhoneCheckTaskType.VOICE, verificationCode, Collections.singletonList(language), null);
    }

    public PhoneCheckTaskEntity applyVerificationCodeTask(DriverEntity driver, String verificationCode, String language) {
        return this.apply(driver, PhoneCheckTaskType.VOICE, verificationCode, Collections.singletonList(language), null);
    }

    public PhoneCheckTaskEntity applyPhoneCallTask(DriverEntity driver, String language) {
        String supportLanguage = table.get(language);
        return this.apply(driver, PhoneCheckTaskType.PHONE_VERIFY, StringUtils.EMPTY, Collections.singletonList(supportLanguage),null);
    }

    public PhoneCheckTaskEntity apply(DriverEntity driver, PhoneCheckTaskType taskType, LocalDateTime planTime) {
        List<String> languages = Optional.ofNullable(driver.getDriverId())
                .map(driverDeviceInfoDao::findOne)
                .map(DriverDeviceInfoPO::getLocal)
                .map(table::get)
                .map(Lists::newArrayList)
                .map(e -> (List<String>) e)
                .orElseGet(() -> {
                    // Get driver language from driver entity
                    List<String> driverLanguages = Arrays.stream(StringUtils.split(driver.getDriverLanguage(), ","))
                            .collect(Collectors.toList());

                    // Check if any of the driver languages are in the supported list (cn, en, fr, jp, th, es)
                    boolean hasValidLanguage = driverLanguages.stream()
                            .map(String::toUpperCase)
                            .anyMatch(lang -> Arrays.asList("CN", "EN", "FR", "JP", "TH", "ES").contains(lang));

                    if (hasValidLanguage) {
                        return driverLanguages;
                    } else {
                        String languageByCountry = getLanguageByCountryId(driver.getCountryId());
                        return Collections.singletonList(languageByCountry);
                    }
                });
        return this.apply(driver, taskType, StringUtils.EMPTY, languages,planTime);
    }

    /**
     * Maps country ID to language code according to the following mapping:
     * 1 - CN (China)
     * 4 - TH (Thailand)
     * 31 - FR (France)
     * 78 - JP (Japan)
     * 95 - ES (Spain)
     * Default - EN (English)
     *
     * @param countryId The country ID
     * @return The corresponding language code
     */
    private String getLanguageByCountryId(Long countryId) {
        if (countryId == null) {
            return "EN";
        }
        switch (countryId.intValue()) {
            case 1:
                return "CN";
            case 4:
                return "TH";
            case 31:
                return "FR";
            case 78:
                return "JP";
            case 95:
                return "ES";
            default:
                return "EN";
        }
    }

    private PhoneCheckTaskEntity apply(DriverEntity driver, PhoneCheckTaskType taskType, String verificationCode, List<String> languages, LocalDateTime planTime) {
        PhoneCheckTaskEntity task = new PhoneCheckTaskEntity();
        task.setDriverId(driver.getDriverId());
        task.setPhonePrefix(driver.getPhonePrefix());
        task.setPhoneNumber(driver.getPhoneNumber());

        PhoneCheckVoiceCallTask content = PhoneCheckVoiceCallTask.builder()
                .driverCityId(driver.getCityId())
                .driverLanguages(languages)
                .build();
        if (StringUtils.isNotBlank(verificationCode)) {
            content.setVerificationCode(verificationCode);
        }

        task.setTaskContent(JsonUtil.toString(content));
        task.setTaskType(taskType.getCode());
        task.setTaskState(PhoneCheckTaskState.Created);
        task.setSubmitTime(LocalDateTime.now());

        if (taskType.isIvr()) {
            if (Objects.nonNull(planTime)) {
                task.setPlanTime(planTime);
            } else {
                // 当地时间的早上10点
                LocalDateTime executeTimeLocal = timeZoneRepository.transform(BJ_CITY, LocalDateTime.now(), driver.getCityId()).plusDays(config.getFirstCallDay()).withHour(config.getFirstCallHour());
                log.info("LocalExecuteTime", JsonUtil.toString(executeTimeLocal));
                // 存入数据库需要转为BJ时间
                LocalDateTime executeTimeBJ = timeZoneRepository.transform(driver.getCityId(), executeTimeLocal, BJ_CITY);
                task.setPlanTime(executeTimeBJ);
            }
        }
        return task;
    }
}
