package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.domain.account.*;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.annontation.ServiceLogTag;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@ServiceLogTag(tagKeys= {"uid"})
public class QueryAccountIdentityByUidEngine extends ShoppingExecutor<QueryAccountIdentityByUidRequestType, QueryAccountIdentityByUidResponseType> implements Validator<QueryAccountIdentityByUidRequestType> {

    @Autowired
    private AccountService accountService;

    @Override
    public QueryAccountIdentityByUidResponseType execute(QueryAccountIdentityByUidRequestType request) {


        QueryAccountIdentityByUidResponseType resp = new QueryAccountIdentityByUidResponseType();
        resp.setIdentityList(Lists.newArrayList());

        List<AccountMapperPO> identityList = accountService.getIdentityByUid(request.getUid());
        if (CollectionUtils.isNotEmpty(identityList)) {
            List<AccountIdentitySourceDTO> identitySourceDTOList = identityList.stream().map(o -> new AccountIdentitySourceDTO(o.getSource(), o.getSourceId(), Objects.equals(o.getIsValid(), 1))).collect(Collectors.toList());
            resp.setUid(identityList.get(0).getUid());
            resp.setIdentityList(identitySourceDTOList);
        }
        return ServiceResponseUtils.success(resp);
    }

    @Override
    public Boolean isEmptyResult(QueryAccountIdentityByUidResponseType resp) {
        return !(resp!=null&&CollectionUtils.isNotEmpty(resp.getIdentityList()));

    }
}
