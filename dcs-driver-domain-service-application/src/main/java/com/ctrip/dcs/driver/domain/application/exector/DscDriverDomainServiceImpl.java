package com.ctrip.dcs.driver.domain.application.exector;

import com.ctrip.dcs.driver.domain.account.*;
import com.ctrip.dcs.driver.domain.device.QueryDriverDeviceInfoRequestType;
import com.ctrip.dcs.driver.domain.device.QueryDriverDeviceInfoResponseType;
import com.ctrip.dcs.driver.domain.exam.*;
import com.ctrip.dcs.driver.domain.extension.UploadDriverIVRInfoRequestType;
import com.ctrip.dcs.driver.domain.extension.UploadDriverIVRInfoResponseType;
import com.ctrip.dcs.driver.domain.facerecognize.*;
import com.ctrip.dcs.driver.domain.finance.*;
import com.ctrip.dcs.driver.domain.gray.QueryDriverBizGraySwitchRequestType;
import com.ctrip.dcs.driver.domain.gray.QueryDriverBizGraySwitchResponseType;
import com.ctrip.dcs.driver.domain.guidance.QueryDriverAppGuidanceRequestType;
import com.ctrip.dcs.driver.domain.guidance.QueryDriverAppGuidanceResponseType;
import com.ctrip.dcs.driver.domain.guidance.SaveDriverAppGuidanceRequestType;
import com.ctrip.dcs.driver.domain.guidance.SaveDriverAppGuidanceResponseType;
import com.ctrip.dcs.driver.domain.honour.*;
import com.ctrip.dcs.driver.domain.interfaces.api.DscDriverDomainService;
import com.ctrip.dcs.driver.domain.notice.QueryNoticeRequestType;
import com.ctrip.dcs.driver.domain.notice.QueryNoticeResponseType;
import com.ctrip.dcs.driver.domain.rights.*;
import com.ctrip.dcs.driver.domain.setting.*;
import com.ctrip.dcs.driver.domain.task.*;
import com.ctrip.dcs.driver.domain.trip.university.QueryDrvIdByCodeRequestType;
import com.ctrip.dcs.driver.domain.trip.university.QueryDrvIdByCodeResponseType;
import com.ctrip.dcs.driver.domain.trip.university.QueryTripUniversityAccessTokenRequestType;
import com.ctrip.dcs.driver.domain.trip.university.QueryTripUniversityAccessTokenResponseType;
import com.ctrip.dcs.driver.domain.verify.CreateDriverChangeEquipmentEventRequestType;
import com.ctrip.dcs.driver.domain.verify.CreateDriverChangeEquipmentEventResponseType;
import com.ctrip.dcs.driver.domain.verify.DriverIsNeedVerifyRequestType;
import com.ctrip.dcs.driver.domain.verify.DriverIsNeedVerifyResponseType;
import com.ctrip.igt.framework.soa.server.executor.ServiceExecutors;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.*;
import com.ctriposs.baiji.rpc.common.types.CheckHealthRequestType;
import com.ctriposs.baiji.rpc.common.types.CheckHealthResponseType;
import com.ctriposs.baiji.rpc.server.ServiceConfig;
import org.springframework.stereotype.Component;

@Component
@ServiceConfig(servicePath = "")
public class DscDriverDomainServiceImpl implements DscDriverDomainService {

  @Override
  public CheckHealthResponseType checkHealth(CheckHealthRequestType request) throws Exception {
    return ServiceResponseUtils.getDefaultCheckHealthResponse();
  }

  @Override
  public QueryDriverRankListResponseType queryDriverRankList(QueryDriverRankListRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverRankListResponseType.class);
  }

  @Override
  public QueryDirectorHonourSettingResponseType queryDirectorHonourSetting(QueryDirectorHonourSettingRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDirectorHonourSettingResponseType.class);
  }

  @Override
  public SubmitDriverLikeResponseType submitDriverLike(SubmitDriverLikeRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SubmitDriverLikeResponseType.class);
  }

  @Override
  public QueryVacationProcessResponseType queryVacationProcess(QueryVacationProcessRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryVacationProcessResponseType.class);
  }



  @Override
  public QueryRightsRecordByDriverResponseType queryRightsRecordByDriver(QueryRightsRecordByDriverRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryRightsRecordByDriverResponseType.class);
  }

  @Override
  public QueryDriverFinanceInfoResponseType queryDriverFinanceInfo(QueryDriverFinanceInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverFinanceInfoResponseType.class);
  }

  @Override
  public QueryDriverBankCardListResponseType queryDriverBankCardList(QueryDriverBankCardListRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverBankCardListResponseType.class);
  }

  @Override
  public SendFinanceVerificationResponseType sendFinanceVerification(SendFinanceVerificationRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SendFinanceVerificationResponseType.class);
  }

  @Override
  public ResetFinancePasswordResponseType resetFinancePassword(ResetFinancePasswordRequestType request) throws Exception {
    return ServiceExecutors.execute(request, ResetFinancePasswordResponseType.class);
  }

  @Override
  public FinanceWithdrawResponseType financeWithdraw(FinanceWithdrawRequestType request) throws Exception {
    return ServiceExecutors.execute(request, FinanceWithdrawResponseType.class);
  }

  @Override
  public OldProcessFinanceResponseType oldProcessFinance(OldProcessFinanceRequestType request) throws Exception {
    return ServiceExecutors.execute(request, OldProcessFinanceResponseType.class);
  }

  @Override
  public QueryDriverBalanceRecordResponseType queryDriverBalanceRecord(QueryDriverBalanceRecordRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverBalanceRecordResponseType.class);
  }

  @Override
  public QueryDriverPushConfigResponseType queryDriverPushConfig(QueryDriverPushConfigRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverPushConfigResponseType.class);
  }

  @Override
  public QueryDriverPointPopInfoResponseType queryDriverPointPopInfo(QueryDriverPointPopInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverPointPopInfoResponseType.class);
  }

  @Override
  public QueryNoticeResponseType queryNotice(QueryNoticeRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryNoticeResponseType.class);
  }

  @Override
  public SaveDriverPushConfigResponseType saveDriverPushConfig(SaveDriverPushConfigRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SaveDriverPushConfigResponseType.class);
  }

  @Override
  public QueryDriverMedalListResponseType queryDriverMedalList(QueryDriverMedalListRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverMedalListResponseType.class);
  }

  @Override
  public QueryDirectorCommemorationResponseType queryDirectorCommemoration(QueryDirectorCommemorationRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDirectorCommemorationResponseType.class);
  }

  @Override
  public UploadDriverIVRInfoResponseType uploadDriverIVRInfo(UploadDriverIVRInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, UploadDriverIVRInfoResponseType.class);
  }

  @Override
  public QueryRightsResponseType queryRights(QueryRightsRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryRightsResponseType.class);
  }

  @Override
  public QueryRightsRecordResponseType queryRightsRecord(QueryRightsRecordRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryRightsRecordResponseType.class);
  }

  @Override
  public CheckRightsResponseType checkRights(CheckRightsRequestType request) throws Exception {
    return ServiceExecutors.execute(request, CheckRightsResponseType.class);
  }

  @Override
  public UseRightsResponseType useRights(UseRightsRequestType request) throws Exception {
    return ServiceExecutors.execute(request, UseRightsResponseType.class);
  }

  @Override
  public QueryLevelResponseType queryLevel(QueryLevelRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryLevelResponseType.class);
  }

  @Override
  public CheckVacationRightsResponseType checkVacationRights(CheckVacationRightsRequestType request) throws Exception {
    return ServiceExecutors.execute(request, CheckVacationRightsResponseType.class);
  }

  @Override
  public QueryFormRightsAndLevelResponseType queryFormRightsAndLevel(QueryFormRightsAndLevelRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryFormRightsAndLevelResponseType.class);
  }

  @Override
  public EstimateLevelResponseType estimateLevel(EstimateLevelRequestType request) throws Exception {
    return ServiceExecutors.execute(request, EstimateLevelResponseType.class);
  }

  @Override
  public QueryRightsAndLevelResponseType queryRightsAndLevel(QueryRightsAndLevelRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryRightsAndLevelResponseType.class);
  }

  @Override
  public QueryDriverIdByRightsResponseType queryDriverIdByRights(QueryDriverIdByRightsRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverIdByRightsResponseType.class);
  }

  @Override
  public DealDriverLoginInfoResponseType dealDriverLoginInfo(DealDriverLoginInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, DealDriverLoginInfoResponseType.class);
  }

  @Override
  public QueryDriverVersionPublishInfoResponseType queryDriverVersionPublishInfo(QueryDriverVersionPublishInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverVersionPublishInfoResponseType.class);
  }

  @Override
  public SubmitApplyExamResponseType submitApplyExam(SubmitApplyExamRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SubmitApplyExamResponseType.class);
  }

  @Override
  public QueryExamResponseType queryGuideExamInfo(QueryExamRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryExamResponseType.class);
  }

  @Override
  public QueryCustomerMedalListResponseType queryCustomerMedalList(QueryCustomerMedalListRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryCustomerMedalListResponseType.class);
  }

  @Override
  public QueryValidityExamResponseType queryValidityExam(QueryValidityExamRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryValidityExamResponseType.class);
  }

  @Override
  public QuerySSOUrlResponseType querySSOUrl(QuerySSOUrlRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QuerySSOUrlResponseType.class);
  }

  @Override
  public QueryGuideTagExamResponseType queryGuideTagExam(QueryGuideTagExamRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryGuideTagExamResponseType.class);
  }

  @Override
  public ModifyPasswordResponseType modifyPassword(ModifyPasswordRequestType request) throws Exception {
    return ServiceExecutors.execute(request, ModifyPasswordResponseType.class);
  }

  @Override
  public SubmitVoiceCodeCallResponseType submitVoiceCodeCall(SubmitVoiceCodeCallRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SubmitVoiceCodeCallResponseType.class);
  }

  @Override
  public QueryAccountResponseType queryAccount(QueryAccountRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountResponseType.class);
  }


  @Override
  public RegisterAccountResponseType registerAccount(RegisterAccountRequestType request) throws Exception {
    return ServiceExecutors.execute(request, RegisterAccountResponseType.class);
  }

  @Override
  public QueryAccountProcessResponseType queryAccountProcess(QueryAccountProcessRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountProcessResponseType.class);
  }

  @Override
  public GenerateGlobalIdResponseType generateGlobalId(GenerateGlobalIdRequestType request) throws Exception {
    return ServiceExecutors.execute(request, GenerateGlobalIdResponseType.class);
  }

  @Override
  public RegisterNewAccountResponseType registerNewAccount(RegisterNewAccountRequestType request) throws Exception {
    return ServiceExecutors.execute(request, RegisterNewAccountResponseType.class);
  }

  @Override
  public SyncAccountResponseType syncAccount(SyncAccountRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SyncAccountResponseType.class);
  }

  @Override
  public UpdateAccountResponseType updateAccount(UpdateAccountRequestType request) throws Exception {
    return ServiceExecutors.execute(request, UpdateAccountResponseType.class);
  }

  @Override
  public QueryAccountByUIDResponseType queryAccountByUID(QueryAccountByUIDRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountByUIDResponseType.class);
  }

  @Override
  public QueryAccountByIdCardResponseType queryAccountByIdCard(QueryAccountByIdCardRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountByIdCardResponseType.class);
  }

  @Override
  public SaveOrUpdateCarInspectionInfoResponseType saveOrUpdateCarInspectionInfo(SaveOrUpdateCarInspectionInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SaveOrUpdateCarInspectionInfoResponseType.class);
  }

  @Override
  public QueryCarInspectionInfoResponseType queryCarInspectionInfo(QueryCarInspectionInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryCarInspectionInfoResponseType.class);
  }

  @Override
  public SaveOrUpdateDriverTaskInfoResponseType saveOrUpdateDriverTaskInfo(SaveOrUpdateDriverTaskInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SaveOrUpdateDriverTaskInfoResponseType.class);
  }

  @Override
  public QueryDriverTaskInfoResponseType queryDriverTaskInfo(QueryDriverTaskInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverTaskInfoResponseType.class);
  }

  @Override
  public QueryUDLByDriverIdResponseType queryUDLByDriverId(QueryUDLByDriverIdRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryUDLByDriverIdResponseType.class);
  }

  @Override
  public QueryAccountBySourceResponseType queryAccountBySource(QueryAccountBySourceRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountBySourceResponseType.class);
  }

  @Override
  public QueryAccountByNameResponseType queryAccountByName(QueryAccountByNameRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountByNameResponseType.class);
  }

  @Override
  public QueryAccountByMobilePhoneResponseType queryAccountByMobilePhone(QueryAccountByMobilePhoneRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountByMobilePhoneResponseType.class);
  }

  @Override
  public QueryAccountByEmailResponseType queryAccountByEmail(QueryAccountByEmailRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountByEmailResponseType.class);
  }

  @Override
  public BatchQueryAccountByUIDResponseType batchqueryAccountByUID(BatchQueryAccountByUIDRequestType request) throws Exception {
    return ServiceExecutors.execute(request, BatchQueryAccountByUIDResponseType.class);
  }

  @Override
  public QueryAccountByPayoneerAccountResponseType queryAccountByPayoneerAccount(QueryAccountByPayoneerAccountRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountByPayoneerAccountResponseType.class);
  }

  @Override
  public DriverIsNeedVerifyResponseType queryDriverIsNeedVerify(DriverIsNeedVerifyRequestType request) throws Exception {
    return ServiceExecutors.execute(request, DriverIsNeedVerifyResponseType.class);
  }

  @Override
  public UpdateAccountWithdrawalStatusByDriverIdResponseType updateAccountWithdrawalStatusByDriverId(UpdateAccountWithdrawalStatusByDriverIdRequestType request) throws Exception {
    return ServiceExecutors.execute(request, UpdateAccountWithdrawalStatusByDriverIdResponseType.class);
  }

  @Override
  public BindAccountBankCardResponseType bindAccountBankCard(BindAccountBankCardRequestType request) throws Exception {
    return ServiceExecutors.execute(request, BindAccountBankCardResponseType.class);
  }

  @Override
  public UnbindAccountBankCardResponseType unbindAccountBankCard(UnbindAccountBankCardRequestType request) throws Exception {
    return ServiceExecutors.execute(request, UnbindAccountBankCardResponseType.class);
  }

  @Override
  public QueryAccountCurrentlyBoundBankCardResponseType queryAccountCurrentlyBoundBankCard(QueryAccountCurrentlyBoundBankCardRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountCurrentlyBoundBankCardResponseType.class);
  }

  @Override
  public QueryDriverBizGraySwitchResponseType queryDriverBizGraySwitch(QueryDriverBizGraySwitchRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverBizGraySwitchResponseType.class);
  }

  @Override
  public QueryAccountBankCardByDriverIdResponseType queryAccountBankCardByDriverId(QueryAccountBankCardByDriverIdRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountBankCardByDriverIdResponseType.class);
  }

  @Override
  public QueryDriverDeviceInfoResponseType queryDriverDeviceInfo(QueryDriverDeviceInfoRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverDeviceInfoResponseType.class);
  }

  @Override
  public QueryTripUniversityAccessTokenResponseType queryTripUniversityAccessToken(
    QueryTripUniversityAccessTokenRequestType requestType) throws Exception {
    return ServiceExecutors.execute(requestType, QueryTripUniversityAccessTokenResponseType.class);
  }

  @Override
  public QueryDrvIdByCodeResponseType queryDrvIdByCode(QueryDrvIdByCodeRequestType requestType)
    throws Exception {
    return ServiceExecutors.execute(requestType, QueryDrvIdByCodeResponseType.class);
  }

  public QueryDriverAppGuidanceResponseType queryDriverAppGuidance(QueryDriverAppGuidanceRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryDriverAppGuidanceResponseType.class);
  }

  @Override
  public SaveDriverAppGuidanceResponseType saveDriverAppGuidance(SaveDriverAppGuidanceRequestType request) throws Exception {
    return ServiceExecutors.execute(request, SaveDriverAppGuidanceResponseType.class);
  }


  @Override
  public CreateDriverChangeEquipmentEventResponseType createDriverChangeEquipmentEvent(CreateDriverChangeEquipmentEventRequestType request) throws Exception {
    return ServiceExecutors.execute(request, CreateDriverChangeEquipmentEventResponseType.class);
  }

  @Override
  public QueryAccountIdentityByDriverIdResponseType queryAccountIdentityByDriverId(QueryAccountIdentityByDriverIdRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountIdentityByDriverIdResponseType.class);
  }

  @Override
  public QueryAccountIdentityByUidResponseType queryAccountIdentityByUid(QueryAccountIdentityByUidRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountIdentityByUidResponseType.class);
  }

  @Override
  public QueryAccountByDriverIdResponseType queryAccountByDriverId(QueryAccountByDriverIdRequestType request) throws Exception {
    return ServiceExecutors.execute(request, QueryAccountByDriverIdResponseType.class);
  }

  @Override
  public BatchQueryAccountByDriverIdResponseType batchQueryAccountByDriverId(BatchQueryAccountByDriverIdRequestType request) throws Exception {
    return ServiceExecutors.execute(request, BatchQueryAccountByDriverIdResponseType.class);
  }

  @Override
  public ParsePhoneNumberResponseType parsePhoneNumber(ParsePhoneNumberRequestType request) {
    return ServiceExecutors.execute(request, ParsePhoneNumberResponseType.class);
  }


  @Override
  public CreateDriverFrozenBufferResponseType createDriverFrozenBuffer(CreateDriverFrozenBufferRequestType request)  {
    return ServiceExecutors.execute(request, CreateDriverFrozenBufferResponseType.class);
  }

  @Override
  public QueryDriverFrozenBufferResponseType queryDriverFrozenBuffer(QueryDriverFrozenBufferRequestType request) {
    return ServiceExecutors.execute(request, QueryDriverFrozenBufferResponseType.class);
  }



  @Override
  public BindOverseaAccountBankCardResponseType bindOverseaAccountBankCard(BindOverseaAccountBankCardRequestType request) throws Exception {
    return ServiceExecutors.execute(request, BindOverseaAccountBankCardResponseType.class);
  }

  @Override
  public CreateDriverApplyFreezeRecordResponseType createDriverApplyFreezeRecord(CreateDriverApplyFreezeRecordRequestType request) {
    return ServiceExecutors.execute(request, CreateDriverApplyFreezeRecordResponseType.class);
  }

  @Override
  public NotifyVerificationCodeByCallPhoneResponseType notifyVerificationCodeByCallPhone(NotifyVerificationCodeByCallPhoneRequestType request)  {
    return ServiceExecutors.execute(request, NotifyVerificationCodeByCallPhoneResponseType.class);
  }

  @Override
  public CallPhoneForVerifyResponseType callPhoneForVerify(CallPhoneForVerifyRequestType request)  {
    return ServiceExecutors.execute(request, CallPhoneForVerifyResponseType.class);
  }

  @Override
  public QueryCallPhoneForVerifyResultResponseType queryCallPhoneForVerifyResult(QueryCallPhoneForVerifyResultRequestType request) {
    return ServiceExecutors.execute(request, QueryCallPhoneForVerifyResultResponseType.class);
  }

  @Override
  public CreatePhoneCheckResponseType createPhoneCheck(CreatePhoneCheckRequestType request){
    return ServiceExecutors.execute(request, CreatePhoneCheckResponseType.class);
  }
  @Override
  public QuerySystemContactPassengerInfoResponseType querySystemContactPassengerInfo(QuerySystemContactPassengerInfoRequestType querySystemContactPassengerInfoRequestType) throws Exception {
    return null;

  }
  @Override
  public BatchQueryAccountByMobilePhoneResponseType batchQueryAccountByMobilePhone(BatchQueryAccountByMobilePhoneRequestType batchQueryAccountByMobilePhoneRequestType) throws Exception {
    return  ServiceExecutors.execute(batchQueryAccountByMobilePhoneRequestType, BatchQueryAccountByMobilePhoneResponseType.class);
  }

  @Override
  public GetMegvFaceIdTokenResponseType getMegvFaceIdToken(GetMegvFaceIdTokenRequestType getMegvFaceIdTokenRequestType) throws Exception {
    return null;
  }

  @Override
  public SubmitMegvFaceIdVerifyResponseType submitMegvFaceIdVerify(SubmitMegvFaceIdVerifyRequestType submitMegvFaceIdVerifyRequestType) throws Exception {
    return null;
  }

  @Override
  public CompareDriverImageByMegvResponseType compareDriverImageByMegv(CompareDriverImageByMegvRequestType request) throws Exception {
    return ServiceExecutors.execute(request, CompareDriverImageByMegvResponseType.class);
  }
}
