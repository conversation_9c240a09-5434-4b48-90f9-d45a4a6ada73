package com.ctrip.dcs.driver.account.application.exector;

import com.ctrip.dcs.driver.account.application.service.GenerateGlobalIdService;
import com.ctrip.dcs.driver.account.infrastructure.common.AccountExceptionCode;
import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import com.ctrip.dcs.driver.domain.account.GenerateGlobalIdRequestType;
import com.ctrip.dcs.driver.domain.account.GenerateGlobalIdResponseType;
import com.ctrip.dcs.shopping.soa.ShoppingExecutor;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class GenerateGlobalIdEngine extends ShoppingExecutor<GenerateGlobalIdRequestType, GenerateGlobalIdResponseType> implements Validator<GenerateGlobalIdRequestType> {
    @Resource
    private GenerateGlobalIdService generateGlobalIdService;


    @Override
    public GenerateGlobalIdResponseType execute(GenerateGlobalIdRequestType request) {
        GenerateGlobalIdResponseType resp = new GenerateGlobalIdResponseType();
        if (request == null || StringUtils.isBlank(request.getCountryCode()) || StringUtils.isBlank(request.getPhoneNumber())) {
            return ServiceResponseUtils.fail(resp, AccountExceptionCode.PARAM_INVALID.getCode(), "phone is null");
        }
        if (!AccountSouceEnum.isDriver(request.getSource()) && !AccountSouceEnum.isDriverGuide(request.getSource())) {
            return ServiceResponseUtils.fail(resp, AccountExceptionCode.PARAM_INVALID.getCode(), "invalid source");
        }

        // 生成新的id
        resp.setGlobalId(generateGlobalIdService.generateGlobalId(request.getSource(), request.getCountryCode(), request.getPhoneNumber()));
        return ServiceResponseUtils.success(resp);
    }
}
