<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dcs-driver-domain-service</artifactId>
        <groupId>com.ctrip.dcs.driver</groupId>
        <version>1.0.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>1.0.2</version>
    <artifactId>dcs-driver-domain-service-application</artifactId>

    <name>dcs-driver-domain-service-application</name>

    <dependencies>
        <dependency>
            <groupId>com.ctrip.dcs.tms</groupId>
            <artifactId>transport-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.pom</groupId>
            <artifactId>dcs-shopping-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.domain</groupId>
            <artifactId>driver-domain-service-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-server</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>logging-interceptor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver</groupId>
            <artifactId>dcs-driver-domain-service-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver</groupId>
            <artifactId>dcs-driver-domain-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.domain</groupId>
            <artifactId>driver-domain-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.credis</groupId>
            <artifactId>credis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework</groupId>
            <artifactId>canal-json</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-test-junit5</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-testng</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>qmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>qschedule</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>coreinfo-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.model.groupchatmessage</groupId>
            <artifactId>com.ctrip.dcs.im</artifactId>
        </dependency>
        <dependency>
            <artifactId>geo-platform-sdk</artifactId>
            <groupId>com.ctrip.dcs.geo</groupId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.payment.router</groupId>
            <artifactId>router-api-soa-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log-annotation-lib</artifactId>
                    <groupId>com.ctrip.payment.structure</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>animal-sniffer-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-net</groupId>
                    <artifactId>commons-net</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk16</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver</groupId>
            <artifactId>dcs-driver-domain-service-port</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.tour</groupId>
            <artifactId>driver-utility</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.flight.intl.common</groupId>
                    <artifactId>metric-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.soa.tour.vendor.userservice.v1</groupId>
                    <artifactId>vendoruserservice4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.soa.22320</groupId>
                    <artifactId>ifsintranetssoservice</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.basebiz</groupId>
                    <artifactId>mbrofflineauthservice</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.hermes</groupId>
                    <artifactId>hermes-kafka</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.hermes</groupId>
                    <artifactId>hermes-producer</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.ctrip.hermes</groupId>
                    <artifactId>hermes-consumer</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.ckafka</groupId>
            <artifactId>hermes-over-ckafka</artifactId>
            <version>0.2.0</version>
        </dependency>
    </dependencies>

    <build>
    </build>
    <profiles>
        <profile>
            <id>testapi</id>

            <dependencies>
                <!-- https://mvnrepository.com/artifact/javax.servlet/javax.servlet-api -->
                <dependency>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                    <version>3.1.0</version>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

</project>
