package com.ctrip.dcs.common.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 司导端引导业务类型
 */
public enum AppGuidanceTypeEnum {
    /**
     * 首页司机分引导
     */
    HOME_SCORE,
    /**
     * 个人中心等级引导
     */
    USER_CENTER_LEVEL;

    public static boolean isSupport(String type) {
        List<String> enumTypes = Arrays.stream(AppGuidanceTypeEnum.values()).map(Enum::name).collect(Collectors.toList());
        return enumTypes.contains(type);
    }

    public static List<String> filter(List<String> types) {
        List<String> enumTypes = Arrays.stream(AppGuidanceTypeEnum.values()).map(Enum::name).collect(Collectors.toList());
        return types.stream().filter(o -> !enumTypes.contains(o)).collect(Collectors.toList());
    }
}
