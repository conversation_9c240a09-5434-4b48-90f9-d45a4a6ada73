<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip.dcs.driver</groupId>
        <artifactId>dcs-driver-domain-service</artifactId>
        <version>1.0.2</version>
    </parent>

    <artifactId>dcs-driver-domain-service-port</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.ctrip.dcs.driver</groupId>
            <artifactId>dcs-driver-domain-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.tour.driver</groupId>
            <artifactId>driver-platform-api-service</artifactId>
        </dependency>
        <dependency>
        <groupId>com.ctrip.payment</groupId>
        <artifactId>sec-crypto</artifactId>
        <exclusions>
            <exclusion>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcpkix-jdk15on</artifactId>
            </exclusion>
            <exclusion>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
            </exclusion>
            <exclusion>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk16</artifactId>
            </exclusion>
            <exclusion>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
            </exclusion>
            <exclusion>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
            </exclusion>
            <exclusion>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
            </exclusion>
        </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.rhb.settlement</groupId>
            <artifactId>rhb-settlement-bill-soa-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.domain</groupId>
            <artifactId>driver-domain-service-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.payment.router</groupId>
            <artifactId>router-api-soa-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log-annotation-lib</artifactId>
                    <groupId>com.ctrip.payment.structure</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>animal-sniffer-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-net</groupId>
                    <artifactId>commons-net</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-collections4</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-compress</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk16</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>
