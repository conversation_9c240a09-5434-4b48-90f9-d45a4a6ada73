package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/5/12-下午3:59-2025
*/


import com.ctrip.dcs.driver.account.domain.enums.BankCardResultEnum;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.gateway.PaymentRouterRepository;
import com.ctrip.dcs.driver.value.bank.BankCardResultModel;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.payment.router.api.contract.dto.comm.BankChannelInfoDto;
import com.ctrip.payment.router.api.contract.dto.comm.BankReqInfoDto;
import com.ctrip.payment.router.api.contract.dto.req.QueryBankChannelListRequestDto;
import com.ctrip.payment.router.api.contract.dto.rsp.QueryBankChannelListResponseDto;
import com.ctrip.payment.router.api.soa.PaymentRouterApiNonPciSoaClient;
import com.ctrip.payment.router.api.soa.config.SignConfig;
import com.ctrip.payment.router.api.soa.config.TimeoutConfig;
import com.ctrip.payment.trade.infra.unified.monad.UnifiedResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PaymentRouterRepositoryImpl
 * @Package com.ctrip.dcs.driver.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/12 下午3:59
 */

@Component
public class PaymentRouterRepositoryImpl implements PaymentRouterRepository {
    public static final Logger LOGGER = LoggerFactory.getLogger(PaymentRouterRepositoryImpl.class);
    @Autowired
    DriverWalletInfoConfig driverWalletInfoConfig;

    @Autowired
    PaymentTokenUtils paymentTokenUtils;

    /**
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
     * 银行卡通道信息查询 -queryBankChannelInfo
     * */
    public BankCardResultModel queryBankChannelInfo(String userId, String cardNo,boolean isOversea) {
        BankCardResultModel bankCardResultModel = new BankCardResultModel();
        bankCardResultModel.setResultCode(BankCardResultEnum.ERROR);
        bankCardResultModel.setCardBankType(0);
        PaymentRouterApiNonPciSoaClient client = null;
        QueryBankChannelListRequestDto requestDto = new QueryBankChannelListRequestDto();

        try {
            client = new PaymentRouterApiNonPciSoaClient(this.initSignConfig(), this.initTimeoutConfig());
            requestDto.setMerchId(driverWalletInfoConfig.getWalletPayMerid());       //请求商户号，平台商户号
            requestDto.setUid(userId);                                                //用户在商户处的原始uid
            requestDto.setBizFunc(2);                                                 //业务功能  1 即 卡信息验证 2 即 查询通道信息
            requestDto.setPaySdkType(2);                                              //接入支付平台类型 2 即 API 7 即 Android 8 即 iOS
            List<BankReqInfoDto> bankInfoList = new ArrayList<>();
            BankReqInfoDto bankReqInfoDto = new BankReqInfoDto();
            bankReqInfoDto.setSeqId(1);
            bankReqInfoDto.setCardNo(cardNo);

            bankInfoList.add(bankReqInfoDto);
            requestDto.setBankInfoList(bankInfoList);
        } catch (Exception e) {
            LOGGER.error(e);
            return bankCardResultModel;
        }

        UnifiedResponse<QueryBankChannelListResponseDto> responseType = null;
        try{
            responseType = client.queryBankChannelList(requestDto);
            if(Objects.isNull(responseType) || Objects.isNull(responseType.getHead()) || Objects.isNull(responseType.getBody()) || CollectionUtils.isEmpty(responseType.getBody().getBankChannelInfoList())){
                return bankCardResultModel;
            }

            int resultCode = ObjectUtils.defaultIfNull(responseType.getHead().getCode(), 0);
            if(resultCode != 200){
                bankCardResultModel.setResultCode(this.getCheckResultCode(resultCode));
                return bankCardResultModel;
            }

            BankChannelInfoDto bankChannelInfo = responseType.getBody().getBankChannelInfoList().get(0);
            int bankType = ObjectUtils.defaultIfNull(bankChannelInfo.getCardType(), 0);
            if (bankType != 2 ){
                //cardType!=2，则不是储蓄卡（1信用卡），toast提示：当前银行卡类型非储蓄卡，请添加储蓄卡
                bankCardResultModel.setResultCode(BankCardResultEnum.CARD_TYPE_FAILED);
                return bankCardResultModel;
            }
            String bankName = StringUtils.defaultString(bankChannelInfo.getBankName(), StringUtils.EMPTY);
            if(!isOversea){
                if (StringUtils.isBlank(bankChannelInfo.getBankName())){
                    //取不到银行名称提示错误
                    bankCardResultModel.setResultCode(BankCardResultEnum.CARD_NO_FAILED);
                    return bankCardResultModel;
                }
            }

            bankCardResultModel.setCardBankType(bankType);
            bankCardResultModel.setCardBankName(bankName);
            bankCardResultModel.setResultCode(BankCardResultEnum.OK);
        }catch (Exception e) {
            LOGGER.error(e);
        }
        return bankCardResultModel;
    }

    /**
     * 初始化Client请求参数
     * */
    private SignConfig initSignConfig() {
        return SignConfig.newBuilder()
                .merId(driverWalletInfoConfig.getWalletPayMerid())
                .keyId(driverWalletInfoConfig.getWalletPayKeyid())
                .keyAlg(SignConfig.KeyAlgorithmEnum.RSA)
                .priKey(paymentTokenUtils.driverPrivateEncryptKey())
                .pubKey(paymentTokenUtils.paymentPublicEncryptKey())
                .build();
    }

    /**
     * 初始化Timeout请求参数
     * */
    private TimeoutConfig initTimeoutConfig() {
        return TimeoutConfig.newBuilder()
                .connectTimeout(driverWalletInfoConfig.getConnectTimeout())
                .requestTimeout(driverWalletInfoConfig.getRequestTimeout())
                .socketTimeout(driverWalletInfoConfig.getSocketTimeout())
                .build();
    }

    /**
     * 校验失败信息
     * 若银行卡格式不正确，则报错，响应头中状态码（code）= 400，错误码（status）= FAILURE，提示：请输入正确的银行卡号
     * */
    private BankCardResultEnum getCheckResultCode(int resultCode){
        switch (resultCode){
            case 400:
                return BankCardResultEnum.CARD_NO_FAILED;
            case 500:
            default:
                return BankCardResultEnum.ERROR;
        }
    }
}
