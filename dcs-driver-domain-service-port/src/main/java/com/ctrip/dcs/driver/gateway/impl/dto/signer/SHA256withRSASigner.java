package com.ctrip.dcs.driver.gateway.impl.dto.signer;

import com.ctrip.infosec.sec.crypto.sign.base64url.Codec;

import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

public class SHA256withRSASigner implements Signer {
  private static final String JCA_NAME = "SHA256withRSA";

  @Override
  public byte[] sign(byte[] data, String priKey) {
    return doSign(data, getPrivateKey(priKey));
  }

  @Override
  public boolean verify(byte[] data, byte[] signature, String pubKey) {
    return doVerify(data, signature, getPublicKey(pubKey));
  }

  private byte[] doSign(byte[] data, Key priKey) {
    PrivateKey privateKey = (PrivateKey) priKey;
    Signature sig = createSignatureInstance();
    try {
      sig.initSign(privateKey);
      sig.update(data);

      return sig.sign();
    } catch (InvalidKeyException e) {
      throw new IllegalArgumentException("invalid key");
    } catch (java.security.SignatureException e) {
      throw new RuntimeException("sign error");
    }
  }

  private boolean doVerify(byte[] data, byte[] signature, Key pubKey) {
    PublicKey publicKey = (PublicKey) pubKey;
    Signature sig = createSignatureInstance();

    try {
      sig.initVerify(publicKey);
      sig.update(data);
      return sig.verify(signature);
    } catch (java.security.SignatureException e) {
      throw new RuntimeException("Error during verification process", e);
    } catch (InvalidKeyException e) {
      throw new IllegalArgumentException("invalid key", e);
    }
  }

  private Signature createSignatureInstance() {
    try {
      return Signature.getInstance(JCA_NAME);
    } catch (NoSuchAlgorithmException e) {
      throw new RuntimeException("SHA256withRSA init fail", e);
    }
  }

  private PublicKey getPublicKey(String base64Key) {
    if (base64Key == null || base64Key.length() == 0) {
      throw new IllegalArgumentException("Invalid public key");
    }
    String base64 = base64Key.trim();
    byte[] decode = Codec.BASE64.decode(base64);
    X509EncodedKeySpec spec = new X509EncodedKeySpec(decode);
    try {
      return KeyFactory.getInstance("RSA").generatePublic(spec);
    } catch (NoSuchAlgorithmException e) {
      throw new IllegalArgumentException("invalid algorithm", e);
    } catch (InvalidKeySpecException e) {
      throw new IllegalArgumentException("invalid key", e);
    }
  }

  private PrivateKey getPrivateKey(String base64Key) {
    if (base64Key == null || base64Key.length() == 0) {
      throw new IllegalArgumentException("Invalid private key");
    }

    String base64 = base64Key.trim();
    byte[] decode = Codec.BASE64.decode(base64);
    PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(decode);

    try {
      return KeyFactory.getInstance("RSA").generatePrivate(spec);
    } catch (NoSuchAlgorithmException e) {
      throw new IllegalArgumentException("invalid algorithm", e);
    } catch (InvalidKeySpecException e) {
      throw new IllegalArgumentException("invalid key", e);
    }

  }
}
