package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/5/28-上午11:16-2025
*/


import com.ctrip.dcs.driver.gateway.QmqSendRepository;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: QmqSendRepositoryImpl
 * @Package com.ctrip.dcs.driver.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/28 上午11:16
 */

@Component
public class QmqSendRepositoryImpl implements QmqSendRepository {
    @Override
    public boolean sendQmq(String topic, Map<String, Object> properties) {
        QmqProducerProvider.sendMessage(topic,properties);
        return true;
    }
}
