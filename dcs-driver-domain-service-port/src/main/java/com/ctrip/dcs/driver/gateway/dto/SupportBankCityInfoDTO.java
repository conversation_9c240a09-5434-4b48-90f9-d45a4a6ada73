package com.ctrip.dcs.driver.gateway.dto;
/*
作者：pl.yang
创建时间：2025/5/7-下午6:01-2025
*/


import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SupportBankCityInfo
 * @Package com.ctrip.dcs.driver.gateway.dto
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/7 下午6:01
 */

@Data
public class SupportBankCityInfoDTO {

    //国家id
    private Integer countryId;
    //省信息
    private Integer provinceId;
    //城市id
    private Integer cityId;
    //易宝的国家二字码
    private String countryTwoCode;
    //易宝的国家三字母码
    private String countryThreeCode;
    //易宝国家数字代码
    private String countryNumericCode;
    //国家名称
    private String countryName;
    //城市名称
    private String cityName;
    //支持收款币种
    private String receiveCurrency;
    //区域信息 1-境内 2-境外
    private Integer areaType;

}
