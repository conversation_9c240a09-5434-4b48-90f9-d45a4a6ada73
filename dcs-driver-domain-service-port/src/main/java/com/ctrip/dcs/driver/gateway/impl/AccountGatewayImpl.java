package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/4/24-下午8:05-2025
*/


import com.ctrip.dcs.driver.account.domain.condition.RegisterDriverUdlCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountUDLDo;
import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountMapperDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.adapter.dal.dao.DriverUdlDao;
import com.ctrip.dcs.driver.adapter.dal.po.DriverUdlPO;
import com.ctrip.dcs.driver.convert.AccountInfoConvert;
import com.ctrip.dcs.driver.gateway.AccountRepository;
import com.ctrip.dcs.geo.domain.repository.CityRepository;
import com.ctrip.dcs.geo.domain.value.City;
import com.ctrip.igt.framework.common.base.GsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountGatewayImpl
 * @Package com.ctrip.dcs.driver.account.infrastructure.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/24 下午8:05
 */

@Component
public class AccountGatewayImpl implements AccountRepository {
    public static final Logger logger = LoggerFactory.getLogger(AccountGatewayImpl.class);
    @Autowired
    private DriverUdlDao driverUdlDao;
    @Autowired
    CityRepository cityRepository;
    @Autowired
    AccountMapperDao accountMapperDao;
    @Autowired
    AccountInfoConvert accountConvert;

    @Override
    public AccountUDLDo saveOtaDriverUdl(RegisterDriverUdlCondition condition) {
        if (StringUtils.isEmpty(condition.getDriverId())) {
            return null;
        }
        DriverUdlPO driverUdlPO = driverUdlDao.queryByDriverId(condition.getDriverId());
        String uid = createOtaUid(condition.getDriverId());
        String udl = createUdl(condition.getCityId());
        AccountUDLDo accountUDLDo = AccountUDLDo
                .builder()
                .uid(uid)
                .udl(udl)
                .cityId(condition.getCityId())
                .sourceId(condition.getDriverId()).build();
        if (driverUdlPO == null) {
            driverUdlPO = new DriverUdlPO();
            driverUdlPO.setDriverId(accountUDLDo.getSourceId());
            driverUdlPO.setProviderDataLocation(udl);
            driverUdlPO.setRegisterSource(Constants.UDL_REGISTER_SOURCE_OTA);
            driverUdlPO.setUid(uid);
            driverUdlPO.setCityId(accountUDLDo.getCityId());
            insertData(driverUdlPO);
        } else {
            driverUdlPO.setProviderDataLocation(udl);
            driverUdlPO.setRegisterSource(Constants.UDL_REGISTER_SOURCE_OTA);
            driverUdlPO.setUid(uid);
            driverUdlPO.setCityId(condition.getCityId());
            logger.info("update driverUdlPO", GsonUtil.toJson(driverUdlPO));
            driverUdlDao.update(driverUdlPO);
        }
        return accountUDLDo;
    }

    @Override
    public List<AccountUDLDo> batchQueryByUid(List<String> uids) {
        List<AccountMapperPO> driverUdlPOS = accountMapperDao.batchQueryByUid(uids);
        List<AccountUDLDo> accountUDLDoList = accountConvert.convertAccountUDLDos(driverUdlPOS);
        List<DriverUdlPO> driverUdlPOList = driverUdlDao.batchQueryByUid(uids);
        List<AccountUDLDo> accountUDLDoList2 = accountConvert.convertDriverUdlPOs(driverUdlPOList);
        accountUDLDoList.addAll(accountUDLDoList2);
        //根据司机id去重，应该没有重复数据
        return distinct(accountUDLDoList);
    }

    //udl 保存两张表 driverUdlPO accountMapperPO
    @Override
    public List<AccountUDLDo> batchQueryBySourceId(List<String> sourceIds) {
        List<AccountMapperPO> driverUdlPOS_DB = accountMapperDao.queryByDriverIds(sourceIds);
        List<AccountUDLDo> accountUDLDoList = accountConvert.convertAccountUDLDos(driverUdlPOS_DB);
        List<DriverUdlPO> driverUdlPOList = driverUdlDao.batchQueryByDriverId(sourceIds);
        List<AccountUDLDo> accountUDLDoList2 = accountConvert.convertDriverUdlPOs(driverUdlPOList);
        accountUDLDoList.addAll(accountUDLDoList2);
        //根据司机id去重，应该没有重复数据
        return distinct(accountUDLDoList);
    }

    private List<AccountUDLDo> distinct(List<AccountUDLDo> accountUDLDoList) {
        Set<String> set = new HashSet<>();
        List<AccountUDLDo> newList=new ArrayList<>();
        for (AccountUDLDo accountUDLDo : accountUDLDoList) {
            if(set.contains(accountUDLDo.getSourceId())){
                Cat.logEvent(CatEventType.DISTINCT_ACCOUNTUDLDO,"duplicate");
                logger.warn("distinct_AccountUDLDo",accountUDLDo.getSourceId());
            } else {
                set.add(accountUDLDo.getSourceId());
                newList.add(accountUDLDo);
            }
        }
        return newList;
    }

    private void insertData(DriverUdlPO driverUdlPO) {
        //如果是新注册的，则插入 可能失败，driverId是唯一索引，因此这里try ,说明是重复 则更新数据
        driverUdlDao.insert(driverUdlPO);
    }

    private String createUdl(Long cityId) {
        City city = cityRepository.findOne(cityId);
        if (city == null) {
            logger.warn("city " + cityId + " not found");
            throw new RuntimeException("city " + cityId + " not found");
        }
        if (city.isChineseMainland()) {
            return Constants.UDL_CN_CSPD;
        } else {
            return Constants.UDL_US_SPD;
        }

    }

    @Override
    public String createOtaUid(String driverId) {
        return "ota_" + driverId;
    }
}
