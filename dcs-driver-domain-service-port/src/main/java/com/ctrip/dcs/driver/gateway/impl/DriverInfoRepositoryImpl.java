package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/5/19-下午9:20-2025
*/


import com.ctrip.dcs.driver.account.domain.value.DriverInfoDo;
import com.ctrip.dcs.driver.adapter.soa.DriverPlatformApiServiceProxy;
import com.ctrip.dcs.driver.convert.DriverInfoConvert;
import com.ctrip.dcs.driver.domain.infrastructure.adapter.transport.TmsTransportServiceProxy;
import com.ctrip.dcs.driver.gateway.DriverInfoRepository;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOARequestType;
import com.ctrip.dcs.tms.transport.api.model.DriverInfoSOAResponseType;
import com.ctrip.tour.driver.platform.api.contract.GetDriverFromDBV2RequestType;
import com.ctrip.tour.driver.platform.api.contract.GetDriverFromDBV2ResponseType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DriverInfoRepositoryImpl
 * @Package com.ctrip.dcs.driver.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/19 下午9:20
 */

@Component
public class DriverInfoRepositoryImpl implements DriverInfoRepository {
    @Autowired
    TmsTransportServiceProxy tmsTransportServiceProxy;
    @Autowired
    DriverPlatformApiServiceProxy driverPlatformApiServiceProxy;
    @Autowired
    DriverInfoConvert driverInfoConvert;


    @Override
    public DriverInfoDo queryDriverInfoByTms(Long  driverId) {
        if (driverId == null) {
            return null;
        }
        DriverInfoSOARequestType requestType = new DriverInfoSOARequestType();
        requestType.driverIds = String.valueOf(driverId);
        DriverInfoSOAResponseType responseType = tmsTransportServiceProxy.queryDriver(requestType);
        return driverInfoConvert.convertTmsDriverInfo(responseType);

    }


    @Override
    public DriverInfoDo queryDriverInfoByGms(Long driverId) {
        GetDriverFromDBV2RequestType getDriverFromDBV2RequestType=new GetDriverFromDBV2RequestType();
        getDriverFromDBV2RequestType.setDriverIdList(Lists.newArrayList(driverId));
        GetDriverFromDBV2ResponseType driverFromDBV2 = driverPlatformApiServiceProxy.getDriverFromDBV2(getDriverFromDBV2RequestType);
        return driverInfoConvert.convertGmsDriverInfo(driverFromDBV2);
    }
}
