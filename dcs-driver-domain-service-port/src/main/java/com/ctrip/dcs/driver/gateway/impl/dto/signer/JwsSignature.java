package com.ctrip.dcs.driver.gateway.impl.dto.signer;

import com.ctrip.infosec.sec.crypto.sign.base64url.Codec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class JwsSignature {

  private final static Charset UTF8 = StandardCharsets.UTF_8;

  public static JwsEntity sign(String key, SignatureAlgorithm alg, JwsEntity src) {
    JwsEntity dst = new JwsEntity();
    dst.setHeader(Codec.BASE64URL.encode(src.getHeader()));
    dst.setPayload(Codec.BASE64URL.encode(src.getPayload()));
    dst.setSignature(Codec.BASE64URL
        .encode(SignerFactory.createSigner(alg).sign((dst.getHeader() + "." + dst.getPayload()).getBytes(UTF8), key)));
    return dst;
  }

  public JwsEntity verifyAndDecode(String key, SignatureAlgorithm alg, JwsEntity src) {
    byte[] data = (src.getHeader() + "." + src.getPayload()).getBytes(UTF8);
    byte[] signData = Codec.BASE64URL.decode(src.getSignature());
    boolean verifyResult = SignerFactory.createSigner(alg).verify(data, signData, key);

    if (!verifyResult) {
      throw new RuntimeException("sign verify failed");
    }

    JwsEntity dst = new JwsEntity();
    dst.setHeader(Codec.BASE64URL.decodeToString(src.getHeader()));
    dst.setPayload(Codec.BASE64URL.decodeToString(src.getPayload()));
    return dst;
  }
}
