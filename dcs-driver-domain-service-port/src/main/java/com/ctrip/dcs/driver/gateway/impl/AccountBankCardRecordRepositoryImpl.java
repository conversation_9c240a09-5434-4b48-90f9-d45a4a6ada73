package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/4/28-下午4:11-2025
*/


import cn.hutool.core.text.CharSequenceUtil;
import com.ctrip.dcs.driver.account.domain.condition.AccountBankCardProcessingCondition;
import com.ctrip.dcs.driver.account.domain.service.AccountService;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBankCardRecordDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBankExtendInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankCardRecordPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankExtendInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo;
import com.ctrip.dcs.driver.convert.AccountBankCardConvert;
import com.ctrip.dcs.driver.convert.AccountBankExtendInfoConvert;
import com.ctrip.dcs.driver.gateway.AccountBankCardRecordRepository;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.dal.transaction.Transactional;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountBankCardRecordAdapter
 * @Package com.ctrip.dcs.driver.account.infrastructure.adapter
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 下午4:11
 */

@Component
public class AccountBankCardRecordRepositoryImpl implements AccountBankCardRecordRepository {
    public static final Logger logger = LoggerFactory.getLogger(AccountBankCardRecordRepositoryImpl.class);

    @Autowired
    AccountBankCardRecordDao accountBankCardRecordDao;
    @Autowired
    AccountBaseInfoDao accountBaseInfoDao;
    @Autowired
    AccountBankCardConvert accountBankCardConvert;
    @Autowired
    AccountBankExtendInfoConvert accountBankExtendInfoConvert;
    @Autowired
    AccountService accountService;
    @Autowired
    CardInfoManagementRepository cardInfoManagementRepository;
    @Autowired
    AccountBankExtendInfoDao accountBankExtendInfoDao;
    @Autowired
    AccountBankCardDoFactory accountBankCardDoFactory;


    //从db查询数据，又从金融那边查询数据
    @Override
    public BaseResult<AccountBankCardDo> queryAccountBindingLastBankCardV2(String uid) {
        //todo  queryAccountBindingBankCardV2Condition
        if (StringUtils.isBlank(uid)) {
            return BaseResult.failResult("204", "UID_IS_EMPTY");
        }
        AccountBaseInfoPO accountBaseInfoPO = accountBaseInfoDao.queryByUID(uid);
        if (Objects.isNull(accountBaseInfoPO)) {
            //账户不存在
            logger.warn("uid_not_exist", "uid=" + uid);
            return BaseResult.failResult("204", "UID_NOT_EXIST");
        }
        AccountBankCardRecordPO accountBankCardRecordPO = accountBankCardRecordDao.queryLastAccountBankCardByUid(uid);
        if (accountBankCardRecordPO == null) {
            return BaseResult.failResult("204", "BANKCARD NOT EXIST");
        }
        //只要绑卡，以及审核中的数据
        if (BankCardStatusDo.isBindOrAuditing(accountBankCardRecordPO.getOperateType())) {
            logger.info("queryCardInfo", CharSequenceUtil.format("BankCardNo:{}", accountBankCardRecordPO.getBankCardNo()));
            return BaseResult.successResult(accountBankCardDoFactory.buildAccountBankCardDo(accountBankCardRecordPO));
        }
        return BaseResult.failResult("204", "BANKCARD NOT EXIST");
    }


    @Override
    public AccountBankCardDo queryAccountBankCard(String uid, String idCard) {
        if (StringUtils.isBlank(uid)) {
            return null;
        }
        AccountBankCardRecordPO accountBankCardRecordPO = accountBankCardRecordDao.queryAccountBankCardByIdCard(uid, idCard);
        if (accountBankCardRecordPO == null) {
            return null;
        }
        return accountBankCardDoFactory.buildAccountBankCardDo(accountBankCardRecordPO);
    }

    @Override
    public BaseResult<AccountBankCardDo> queryByRequestId(String requestId) {
        if (StringUtils.isBlank(requestId)) {
            return BaseResult.failResult("204", "REQUEST_ID_IS_EMPTY");
        }
        AccountBankCardRecordPO accountBankCardRecordPO = accountBankCardRecordDao.findOneByRequestId(requestId);
        return BaseResult.successResult(accountBankCardDoFactory.buildAccountBankCardDo(accountBankCardRecordPO));
    }

    @Override
    public AccountBankCardDo queryAccountBankCardByCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return null;
        }
        AccountBankCardRecordPO accountBankCardRecordPO = accountBankCardRecordDao.findOneByBankCardNo(idCard);
        return accountBankCardDoFactory.buildAccountBankCardDo(accountBankCardRecordPO);
    }

    @Override
    public int updateBankCardRecordStatus(AccountBankCardDo data) {
        return accountBankCardRecordDao.updateStatus(data.getCardStatusCode(), data.getId());
    }

    @Override
    public List<AccountBankCardDo> scanDataBankCardProcessing(AccountBankCardProcessingCondition req) {
        if(req==null||req.getId()<=0||StringUtils.isEmpty(req.getEndDate())||StringUtils.isEmpty(req.getStartDate())){
            return Lists.newArrayList();
        }
        List<AccountBankCardRecordPO> lsit = accountBankCardRecordDao.queryBankCardProcessing(req.getStartDate(), req.getEndDate(), req.getId());
        //注意这里 转换没有扩展的数据的
        return accountBankCardConvert.batchConvertDetailDo(lsit);
    }

    @Override
    public BaseResult<AccountBankCardDo> queryAccountBankCard(String bardCardNo) {
        if(StringUtils.isEmpty(bardCardNo)){
            return BaseResult.failResult("204", "BANKCARD_NO_IS_EMPTY");
        }
        AccountBankCardRecordPO po = accountBankCardRecordDao.queryBankCardbyBardNO(bardCardNo);
        return BaseResult.successResult(accountBankCardDoFactory.buildAccountBankCardDo(po));
    }


    @Transactional
    @Override
    public int insert(AccountBankCardDo insertAccountBankCardRecord) {
        AccountBankCardRecordPO bankCardRecordPO = accountBankCardConvert.convertPo(insertAccountBankCardRecord);
        List<AccountBankExtendInfoPO> accountBankExtendInfoPOList = accountBankExtendInfoConvert.convertExtendInfo(insertAccountBankCardRecord);
        int insert = accountBankCardRecordDao.insert(bankCardRecordPO);
        int deleteByYeepayCardId = accountBankExtendInfoDao.deleteByYeepayCardId(bankCardRecordPO.getRequestId());
        int[] batchedInsert = accountBankExtendInfoDao.batchInsert(accountBankExtendInfoPOList);
        return insert + deleteByYeepayCardId + batchedInsert.length;
    }


    @Transactional
    @Override
    public int update(AccountBankCardDo accountBankCardRecord) {
        AccountBankCardRecordPO bankCardRecordPO = accountBankCardConvert.convertPo(accountBankCardRecord);
        List<AccountBankExtendInfoPO> accountBankExtendInfoPOList = accountBankExtendInfoConvert.convertExtendInfo(accountBankCardRecord);
        int update = accountBankCardRecordDao.update(bankCardRecordPO);
        int deleteByYeepayCardId = accountBankExtendInfoDao.deleteByYeepayCardId(bankCardRecordPO.getRequestId());
        int[] batchedInsert = accountBankExtendInfoDao.batchInsert(accountBankExtendInfoPOList);
        return update + deleteByYeepayCardId + batchedInsert.length;
    }
    @Override
    public int saveOrUpdate(AccountBankCardDo insertAccountBankCardRecord) {
        if(StringUtils.isEmpty(insertAccountBankCardRecord.getUid())||StringUtils.isEmpty(insertAccountBankCardRecord.getCardNo())){
            return 0;
        }
        AccountBankCardDo accountBankCardDo = queryAccountBankCard(insertAccountBankCardRecord.getUid(), insertAccountBankCardRecord.getCardNo());
        if (accountBankCardDo == null) {
            return insert(insertAccountBankCardRecord);
        }else {
            return update(insertAccountBankCardRecord);
        }
    }
}
