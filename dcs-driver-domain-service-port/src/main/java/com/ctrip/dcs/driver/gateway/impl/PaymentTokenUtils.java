package com.ctrip.dcs.driver.gateway.impl;

import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.domain.infrastructure.utils.KMSUtils;
import com.ctrip.dcs.shopping.json.ShoppingJsonUtils;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.infosec.kms.KmsUtilCacheable;
import com.ctrip.infosec.kms.pojo.KmsKey;
import com.ctrip.infosec.kms.pojo.KmsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Objects;

@Component
public class PaymentTokenUtils {

  public static final Logger LOGGER = LoggerFactory.getLogger(PaymentTokenUtils.class);

  @Autowired
  DriverWalletInfoConfig driverWalletInfoConfig;

  private String paymentPublicEncryptKey;
  private String driverPrivateEncryptKey;

  public String paymentPublicEncryptKey() {
    return paymentPublicEncryptKey;
  }

  public String driverPrivateEncryptKey() {
    return driverPrivateEncryptKey;
  }

  @Autowired
  public void initializeKeys() throws KMSUtils.KmsException {
    KmsResponse<KmsKey> kmsPaymentPublicResponse = KmsUtilCacheable.getKey(driverWalletInfoConfig.getWalletKmsPaymentPublicToken());
    if (Objects.isNull(kmsPaymentPublicResponse) || kmsPaymentPublicResponse.getCode() != 0) {
      LOGGER.warn("KMS", ShoppingJsonUtils.serializeDomainToJson(kmsPaymentPublicResponse));
      throw new KMSUtils.KmsException("PaymentTokenUtils getWalletKmsPaymentPublicToken error");
    }
    this.paymentPublicEncryptKey = kmsPaymentPublicResponse.getResult().getKeyValue();
    LOGGER.info("KMS", "PaymentTokenUtils getWalletKmsPaymentPublicToken key");

    KmsResponse<KmsKey> kmsDriverPrivateResponse = KmsUtilCacheable.getKey(driverWalletInfoConfig.getWalletKmsDriverPrivateToken());
    if (Objects.isNull(kmsDriverPrivateResponse) || kmsDriverPrivateResponse.getCode() != 0) {
      LOGGER.warn("KMS", ShoppingJsonUtils.serializeDomainToJson(kmsPaymentPublicResponse));
      throw new KMSUtils.KmsException("PaymentTokenUtils getWalletKmsDriverPrivateToken error");
    }
    this.driverPrivateEncryptKey = kmsDriverPrivateResponse.getResult().getKeyValue();
    LOGGER.info("KMS", "PaymentTokenUtils getWalletKmsDriverPrivateToken key");
  }
}
