package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/5/7-下午1:49-2025
*/


import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.ctrip.dcs.driver.account.domain.condition.DeleteYeepayRecipientReportCondition;
import com.ctrip.dcs.driver.account.domain.condition.QueryBankCardStatusCondition;
import com.ctrip.dcs.driver.account.domain.condition.SubmitYeepayRecipientReportCondition;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.infrastructure.adapter.DcsScmMerchantServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardStatusEnum;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.adapter.soa.RhbSettlementBillSoaServiceProxy;
import com.ctrip.dcs.driver.gateway.YeePayRepository;
import com.ctrip.dcs.driver.value.BaseResult;
import com.ctrip.dcs.scm.merchant.interfaces.dto.YeepayGetPayeeQueryDTO;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.dianping.cat.Cat;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: YeePayRepositoryImpl
 * @Package com.ctrip.dcs.driver.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/7 下午1:49
 */

@Component
public class YeePayRepositoryImpl implements YeePayRepository {
    public static final Logger logger = LoggerFactory.getLogger(YeePayRepositoryImpl.class);
    public static final String BANK_ACCOUNT = "BANK_ACCOUNT";
    public static final String SAVINGS = "SAVINGS";
    @Autowired
    DcsScmMerchantServiceProxy dcsScmMerchantServiceProxy;
    @Autowired
    RhbSettlementBillSoaServiceProxy rhbSettlementBillSoaServiceProxy;

    @Autowired
    DriverAccountConfig driverAccountConfig;
    public static final String OVERSEAS_SUPPLIER = "OVERSEAS_SUPPLIER";
    //ENTERPRISE: 企业 INDIVIDUAL: 个人
    public static final String INDIVIDUAL = "INDIVIDUAL";
    public static final String PRI = "PRI";

    /**
     * 校验银行卡
     */
    @Override
    public CheckDriverBandCardValidResponseType checkDriverBandCardValid(long driverId,
                                                                         String driverName,
                                                                         String idCardNo,
                                                                         String driverMobile,
                                                                         String driverBankReservedMobile,
                                                                         String bankCardNo) {
        CheckDriverBandCardValidRequestType requestType = new CheckDriverBandCardValidRequestType();
        requestType.driverId = driverId;
        requestType.driverName = driverName;
        requestType.idCardNo = idCardNo;
        requestType.idCardType = "ID";
        requestType.driverMobile = driverMobile;
        requestType.driverBankReservedMobile = driverBankReservedMobile;
        requestType.bankAmountNo = bankCardNo;
        try {
            return dcsScmMerchantServiceProxy.checkDriverBandCardValid(requestType);
        } catch (Exception e) {
            logger.warn("checkDriverBandCardValid error");
        }
        return null;
    }

    public BaseResult<Boolean> submitYeepayRecipientReport(SubmitYeepayRecipientReportCondition submitYeepayRecipientReportCondition) {
        if (Objects.isNull(submitYeepayRecipientReportCondition) || Objects.isNull(submitYeepayRecipientReportCondition.getAccountBankCardDo())) {
            return BaseResult.failResultCat("-1", "param is empty", CatEventType.Bind_Oversea_BankCard, "param is empty");
        }

        YeepayRecipientReportRequestType requestType = buildAddRequest(submitYeepayRecipientReportCondition.getAccountBankCardDo());
        YeepayRecipientReportResponseType responseType = dcsScmMerchantServiceProxy.yeepayRecipientReport(requestType);
        if ("200".equals(responseType.getResponseResult().getReturnCode())) {
            return BaseResult.successResult(true);
        } else {
            //记录日志 返回对应的错误吗
            return BaseResult.failResultCat(responseType.getResponseResult().getReturnCode(), responseType.getResponseResult().getReturnMessage(), CatEventType.Bind_Oversea_BankCard, responseType.getResponseResult().getReturnMessage());
        }

    }


    //从易宝中 解绑银行卡
    public boolean unBindBankCard(AccountBankCardDo accountBankCardDo) {
        YeepayRecipientDeleteRequestType requestType = buildUnBindRequest(accountBankCardDo);
        YeepayRecipientDeleteResponseType responseType = dcsScmMerchantServiceProxy.yeepayRecipientDelete(requestType);
        if (responseType != null && responseType.getResponseResult() != null && "200".equals(responseType.getResponseResult().getReturnCode())) {
            return true;
        } else {
            return false;
        }
    }

    private YeepayRecipientDeleteRequestType buildUnBindRequest(AccountBankCardDo accountBankCardDo) {
        YeepayRecipientDeleteRequestType request = new YeepayRecipientDeleteRequestType();
        if (StrUtil.isEmpty(accountBankCardDo.getRequestId())) {
            logger.warn("requestId is null");
            Cat.logEvent(CatEventType.Unbind_Oversea_BankCard, "requestId is null");
        }
        request.setRequestId(accountBankCardDo.getRequestId());
        return request;
    }

    private YeepayRecipientReportRequestType buildAddRequest(AccountBankCardDo bankCardInfo) {
        //https://open.gptransfer.hk/docs/apis/psp-exchange/post__rest__v1.0__gpt__supplier__request
        YeepayRecipientReportRequestType request = new YeepayRecipientReportRequestType();
        //ADD：新增收款人（默认）
        //UPDATE：修改收款人
        //【必填条件】需要更新收款人填写
        request.setOperate("ADD");
        //OVERSEAS_SUPPLIER: 海外供应商报备 DOMESTIC_SUPPLIER: 境内供应商报备
        request.setType(bankCardInfo.getType());
        //供应商名称，当供应商为个人时填写accountName
        request.setSupplierName(bankCardInfo.getAccountName());
        request.setClearingType("LOCAL");
        request.setAccountType(PRI);//供应商银行账户类型 PRI: 个人 PUB: 企业
        //枚举值：
        //CHECKING；
        //SAVINGS；
        request.setAccountNoType(SAVINGS);  //bankCountry字段为BR其中之一；且clearingType=LOCAL时必填。枚举： "CHECKING" "SAVINGS"
        request.setRecType(BANK_ACCOUNT); //收款人账户类型： 银行账号 BANK_ACCOUNT 电子钱包 WALLET_ACCOUNT 环球通汇账户GFC_ACCOUNT（海外付款不涉及） 默认值：BANK_ACCOUNT
        request.setSupplierType(INDIVIDUAL);
        if(bankCardInfo.isOverseaBankCard()){
            request.setBusinessType(driverAccountConfig.getOverseaYeePayBusinessType());
            request.setBusinessDetail(driverAccountConfig.getOverseaYeePayBusinessDetail());
        } else {
            request.setBusinessType(driverAccountConfig.getDomesticYeePayBusinessType());
            request.setBusinessDetail(driverAccountConfig.getDomesticYeePayBusinessDetail());
        }
           //供应商业务类型(物流|仓储|原材料|广告推荐|货物采购|出行采购|广告服务|软件服务|泛娱乐收款人|其他）
        request.setRequestId(bankCardInfo.getRequestId());
        request.setCountry(bankCardInfo.getYeePayCountryThreeCode());//供应商所在国家/ 地区，详见国家/ 地区列表
        request.setBankCountry(bankCardInfo.getYeePayCountryThreeCode());
        request.setCity(bankCardInfo.getYeePayCityName());

        request.setAddress(bankCardInfo.getBankAddress());
        request.setSortCode(bankCardInfo.getSortCode());  //sort code；bankCountry字段为AU/ GB/ BD，且clearingType=LOCAL时必填
        request.setSwiftCode(bankCardInfo.getSwiftCode());  //收款人银行的Swift Code
        request.setFedWireNumber(bankCardInfo.getFedwireNumber());//FedWire号码；bankCountry字段为US，且clearingType=LOCAL时必填
        request.setBranchCode(bankCardInfo.getBranchCode()); //香港地区branch code

        request.setFirstName(bankCardInfo.getFirstName());  ////海外供应商为个人时填写first name
        //middleName
        request.setMiddleName(bankCardInfo.getMiddleName());
        //lastName
        request.setLastName(bankCardInfo.getLastName());//海外供应商为个人时填写last name
        request.setBranchName(bankCardInfo.getBranchName() );
        //【必填条件】当type=OVERSEAS_SUPPLIER，bankCountry=AE/CO/SA，clearingType=LOCAL时必填
        if (bankCardInfo.getIdType() != null) {
            //枚举：0 : "PASSPORT"1 : "NATIONAL_ID"2 : "DRIVING_LICENSE"3 : "SOCIAL_SECURITY"4 : "TAX_ID"5 : "SENIOR_CITIZEN_ID"6 : "BIRTH_CERTIFICATE"7 : "VILLAGE_ELDER_ID"8 : "RESIDENT_CARD"9 : "ALIEN_REGISTRATION"10 : "PAN_CARD"11 : "VOTERS_ID"12 : "HEALTH_CARD"13 : "EMPLOYER_ID"14 : "OTHER"
            request.setIdType(bankCardInfo.getIdType().getCode() + "");
        }
        request.setIdNumber(bankCardInfo.getIdNumber()); //收款人证件号


        request.setIban(bankCardInfo.getIban());  //iban number；bankCountry为阿联酋及欧元区国家其中之一，且clearingType=LOCAL时必填
        request.setPostCode(bankCardInfo.getPostCode()); //开户银行所在地的邮编；
        request.setAbaNumber(bankCardInfo.getAbaNumber()); //ABA number；bankCountry字段为CA，且clearingType=LOCAL时必填
        request.setEmail(bankCardInfo.getEmail());
        //mobile
        request.setMobile(bankCardInfo.getPhoneNo());//【手机号】
        request.setRoutingNumber(bankCardInfo.getRoutingNumber());//bankCountry字段为SG，且clearingType=LOCAL时必填
        request.setIfsCode(bankCardInfo.getIfsCode());   //IFS code；bankCountry字段为IN，且clearingType=LOCAL时必填
        //供应商地址
        request.setStreetAddress(bankCardInfo.getAddress());
        request.setAccountName(bankCardInfo.getAccountName());  //银行账户名称
        request.setAccountNumber(bankCardInfo.getCardNo());
        request.setAccountCurrency(bankCardInfo.getAccountCurrency());
        request.setBankName(bankCardInfo.getBankName());//银行名称，付款到银行账户时必填
        request.setBankCode(bankCardInfo.getBankCode());   //香港地区bank code
        //province
        request.setProvince(bankCardInfo.getProvince());
        request.setBankAddress(bankCardInfo.getBankAddress());  //【银行地址】
        return request;
    }


    /**
     * @param
     * @return
     */
    @Override
    public BankCardStatusDo queryBankCardStatus(QueryBankCardStatusCondition condition) {
        if (StringUtils.isBlank(condition.getRequestId())) {
            return BankCardStatusDo.builder().build();
        }
        YeepayGetPayeeQueryRequestType yeepayGetPayeeQueryRequestType = buildYeepayGetPayeeQueryRequest(condition);
        YeepayGetPayeeQueryResponseType yeepayGetPayeeQueryResponseType = null;
        yeepayGetPayeeQueryResponseType = dcsScmMerchantServiceProxy.yeepayGetPayeeQuery(yeepayGetPayeeQueryRequestType);
        BankCardStatusDo.BankCardStatusDoBuilder builder = BankCardStatusDo.builder();
        if (yeepayGetPayeeQueryResponseType == null || yeepayGetPayeeQueryResponseType.getData() == null
                || Boolean.FALSE.equals(yeepayGetPayeeQueryResponseType.getResponseResult().isSuccess())) {
            builder.bankCardStatusEnum(BankCardStatusEnum.UNKNOW);
            builder.requestId(condition.getRequestId());
            return builder.build();
        }
        YeepayGetPayeeQueryDTO data = yeepayGetPayeeQueryResponseType.getData();
        if(!CharSequenceUtil.equals(data.getRequestId(),condition.getRequestId())){
            logger.warn("requestId_unequal", data.getRequestId(),condition.getRequestId());
            Cat.logEvent("requestId unequal","requestId unequal");
            //保证 一张银行卡 就只有一个唯一的requestId


        }
        builder.requestId(data.getRequestId());
        builder.bankCardStatusEnum(BankCardStatusEnum.ofByYeePayStatusCode(data.getStatus()));
        return builder.build();
    }

    @Override
    public BaseResult<Boolean> deleteYeepayRecipientReport(DeleteYeepayRecipientReportCondition condition) {
        if (condition.getAccountBankCardDo() == null) {
            return BaseResult.failResult("500", "accountBankCardDo is null");
        }
        YeepayRecipientDeleteRequestType requestType = new YeepayRecipientDeleteRequestType();
        requestType.setRequestId(condition.getAccountBankCardDo().getRequestId());
        AccountBankCardDo accountBankCardDo = condition.getAccountBankCardDo();
        requestType.setType(accountBankCardDo.getType());
        YeepayRecipientDeleteResponseType responseType = dcsScmMerchantServiceProxy.yeepayRecipientDelete(requestType);
        if(responseType == null||responseType.getResponseResult() == null){
            return BaseResult.failResult("500","responseType is null");
        }
        if("200".equals(responseType.getResponseResult().getReturnCode())){
            return BaseResult.successResult(null);
        }
        if("G06321".equals(responseType.getResponseResult().getReturnCode())){
            //删除成功 供应商结算信息不存在
            return BaseResult.successResult(null);
        }
        return BaseResult.failResult(responseType.getResponseResult().getReturnCode(), responseType.getResponseResult().getReturnCode() + ":" + responseType.getResponseResult().returnMessage);
    }

    private static @NotNull YeepayGetPayeeQueryRequestType buildYeepayGetPayeeQueryRequest(QueryBankCardStatusCondition condition) {
        YeepayGetPayeeQueryRequestType yeepayGetPayeeQueryRequestType = new YeepayGetPayeeQueryRequestType();
        yeepayGetPayeeQueryRequestType.setRequestId(condition.getRequestId());
        yeepayGetPayeeQueryRequestType.setAccountNumber(condition.getAccountNumber());
        yeepayGetPayeeQueryRequestType.setType(condition.getType());
        return yeepayGetPayeeQueryRequestType;
    }

}
