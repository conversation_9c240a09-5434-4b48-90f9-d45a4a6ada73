package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/5/15-下午4:54-2025
*/


import cn.hutool.core.text.CharSequenceUtil;
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo;
import com.ctrip.dcs.driver.account.domain.value.CardInfoDto;
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBankExtendInfoDao;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankCardRecordPO;
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankExtendInfoPO;
import com.ctrip.dcs.driver.convert.AccountBankCardConvert;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountBankCardDoFactry
 * @Package com.ctrip.dcs.driver.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/15 下午4:54
 */

@Component
public class AccountBankCardDoFactory {
    @Autowired
    AccountBankExtendInfoDao accountBankExtendInfoDao;

    @Autowired
    CardInfoManagementRepository cardInfoManagementRepository;
    @Autowired
    AccountBankCardConvert accountBankCardConvert;

    AccountBankCardDo buildAccountBankCardDo(AccountBankCardRecordPO accountBankCardRecordPO) {
        if (accountBankCardRecordPO != null) {
            List<AccountBankExtendInfoPO> accountBankExtendInfoPOS = accountBankExtendInfoDao.quertyByYeepayCardId(accountBankCardRecordPO.getRequestId());
            //只要绑卡，以及审核中的数据
            //查询金融相关信息 不查询金融信息 ，只保存了银行卡号字段，在上次 convert的时候单独查询了
//            CardInfoDto cardInfoDto = cardInfoManagementRepository.queryCardInfo(accountBankCardRecordPO.getBankCardNo());
            return accountBankCardConvert.convertDetailDo(accountBankCardRecordPO, accountBankExtendInfoPOS, null);
        }
        return null;
    }



}
