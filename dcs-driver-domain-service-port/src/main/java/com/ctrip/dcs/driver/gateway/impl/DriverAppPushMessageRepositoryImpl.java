package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/5/8-下午9:10-2025
*/


import com.ctrip.dcs.driver.account.infrastructure.constant.Constants;
import com.ctrip.dcs.driver.gateway.DriverAppPushMessageRepository;
import com.ctrip.dcs.driver.message.dto.PushMessageDTO;
import com.ctrip.dcs.driver.value.PushMessageDo;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.qmq.producer.QmqProducerProvider;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: DriverAppPushMessageRepositoryImpl
 * @Package com.ctrip.dcs.driver.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 下午9:10
 */

@Component
public class DriverAppPushMessageRepositoryImpl implements DriverAppPushMessageRepository {
    public static final Logger logger = LoggerFactory.getLogger(DriverAppPushMessageRepositoryImpl.class);

    @Override
    public void sendPushMessage(PushMessageDo pushMessageDo) {
        PushMessageDTO pushMessageDTO = convertPusMessageDto(pushMessageDo);

        String data = JsonUtil.toString(pushMessageDTO);
        Map<String, Object> qmqContent = Maps.newHashMap();
        qmqContent.put("data", data);
        QmqProducerProvider.sendMessage(Constants.DRIVER_PUSH_MESSAGE_TOPIC, qmqContent);
        logger.info("send driver offline app push", data);
    }

    private PushMessageDTO convertPusMessageDto(PushMessageDo pushMessageDo) {
        PushMessageDTO pushMessageDTO = new PushMessageDTO();
        pushMessageDTO.setDriverIds(pushMessageDo.getDriverIds());
        pushMessageDTO.setGuideIds(pushMessageDo.getGuideIds());
        pushMessageDTO.setPushUidList(pushMessageDo.getPushUidList());
        pushMessageDTO.setTemplateId(pushMessageDo.getTemplateId());
        pushMessageDTO.setSharkValues(pushMessageDo.getSharkValues());
        pushMessageDTO.setData(pushMessageDo.getData());
        return pushMessageDTO;
    }
}
