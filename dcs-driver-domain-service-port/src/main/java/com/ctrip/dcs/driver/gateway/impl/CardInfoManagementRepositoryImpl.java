package com.ctrip.dcs.driver.gateway.impl;
/*
作者：pl.yang
创建时间：2025/5/7-下午2:11-2025
*/


import cn.hutool.core.collection.CollUtil;
import com.ctrip.dcs.driver.account.domain.value.CardInfoDto;
import com.ctrip.dcs.driver.account.infrastructure.adapter.ThirdpartyCardInfoManagementServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import com.ctrip.dcs.driver.account.domain.value.AccountInfoDTO;
import com.ctrip.dcs.driver.convert.CardInfoManagementConvert;
import com.ctrip.dcs.driver.domain.infrastructure.utils.RsaUtil;
import com.ctrip.dcs.driver.gateway.ArchCoreInfoRepository;
import com.ctrip.dcs.driver.gateway.CardInfoManagementRepository;
import com.ctrip.dcs.driver.gateway.dto.CardNoRefInfoItemDto;
import com.ctrip.dcs.driver.gateway.dto.QueryUsedCardsRequestDto;
import com.ctrip.dcs.driver.gateway.dto.QueryUsedCardsResponseDto;
import com.ctrip.dcs.driver.gateway.impl.dto.signer.JwsEntity;
import com.ctrip.dcs.driver.gateway.impl.dto.signer.JwsSignature;
import com.ctrip.dcs.driver.gateway.impl.dto.signer.SignatureAlgorithm;
import com.ctrip.dcs.shopping.json.ShoppingJsonUtils;
import com.ctrip.finance.soa.fi.thridpartycardinfomanagement.base.model.*;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.infosec.sec.crypto.AesRsaUtil;
import com.ctrip.infosec.sec.crypto.sign.base64url.Codec;
import com.ctrip.payment.soa.thirdpartycardinfo.service.JwsRequestType;
import com.ctrip.payment.soa.thirdpartycardinfo.service.JwsResponseType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: CardInfoManagementRepositoryImpl
 * @Package com.ctrip.dcs.driver.gateway.impl
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/7 下午2:11
 */

@Component
public class CardInfoManagementRepositoryImpl implements CardInfoManagementRepository {
    private static final Logger LOGGER = LoggerFactory.getLogger(CardInfoManagementRepositoryImpl.class);
    @Autowired
    DriverWalletInfoConfig driverWalletInfoConfig;

    @Autowired
    PaymentTokenUtils paymentTokenUtils;

    @Autowired
    ThirdpartyCardInfoManagementServiceProxy thirdpartyCardInfoManagementServiceProxy;
    @Autowired
    CardInfoManagementConvert cardInfoManagementConvert;
    @Autowired
    RsaUtil rsaUtil;


    /*
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
     * 4.19）新增卡信息接口-createPayCardInfo
     **/
    @Override
    public String createCardInfo(String driverName, String encryptCardNo) {
        try {
            CreatePayCardInfoRequest cardInfoRequest = new CreatePayCardInfoRequest();
            cardInfoRequest.creditCardNumber = encryptCardNo;
            cardInfoRequest.cardHolder = driverName;
            return postData(cardInfoRequest);
        } catch (Exception e) {
            LOGGER.warn("createCardInfo fail", e);
        }
        return Strings.EMPTY;
    }

    public String createCardInfo(AccountInfoDTO accountInfoDTO, CardInfoDto bankCardInfoDo) {
        try {
            CreatePayCardInfoRequest cardInfoRequest = cardInfoManagementConvert.buildOverCreateCardInfo(accountInfoDTO, bankCardInfoDo);
            return postData(cardInfoRequest);
        } catch (Exception e) {
            LOGGER.warn("createCardInfo fail", e);
        }
        return Strings.EMPTY;
    }

    /**
     * http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
     * 4.15）根据卡信息ID查询卡信息-getPayCardInfos
     * 返回传输加密卡号
     **/
    @Override
    public String getSinglePayCardInfo(String cardInfoId) {
        CardInfoItem bankCardInfo = getBankCardInfo(cardInfoId);
        if (bankCardInfo != null) {
            return bankCardInfo.getCreditCardNumber();
        }
        return Strings.EMPTY;
    }

    private CardInfoItem getBankCardInfo(String cardInfoId) {
        try {
            GetCardInfosRequest cardInfoRequest = new GetCardInfosRequest();
            cardInfoRequest.cardInfoIds = new ArrayList<>(Collections.singletonList(cardInfoId));
            cardInfoRequest.extendCardItems = new ArrayList<>();
            JwsRequestType jwsRequestType = this.getJwsRequestType(ShoppingJsonUtils.serializeToJson(cardInfoRequest));
            JwsResponseType jwsResponseType = thirdpartyCardInfoManagementServiceProxy.getPayCardInfos(jwsRequestType);
            if (Objects.isNull(jwsResponseType)) {
                return null;
            }
            JwsEntity decodeEntity = this.getDecodeEntity(jwsResponseType);
            if (Objects.isNull(decodeEntity) || StringUtils.isBlank(decodeEntity.getPayload())) {
                return null;
            }
            GetCardInfosResponse responseType = ShoppingJsonUtils.deserializeFromJson(decodeEntity.getPayload(), GetCardInfosResponse.class);
            if (Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.cardInfoItems)) {
                return responseType.cardInfoItems.get(0);
            }
        } catch (Exception e) {
            LOGGER.warn("getPayCardInfos fail", e);
        }
        return null;
    }



    /**
     * 金融id
     *
     * @param cardInfoId
     * @return
     */
    @Override
    public CardInfoDto queryCardInfo(String cardInfoId) {
        CardInfoItem bankCardInfo = getBankCardInfo(cardInfoId);
        return cardInfoManagementConvert.convdrtCardInfoDto(bankCardInfo);
    }

    @Override
    public Map<String/*卡号*/,String/*金融id*/> queryCardInfoByCardNo(List<String> cardNo) {
        if(CollUtil.isEmpty(cardNo)){
            return Maps.newHashMap();
        }
        List<CardNoRefInfoItemDto> bankCardInfoByCardNo = getBankCardInfoByCardNo(cardNo);
        Map<String,String> cardNoMap = Maps.newHashMap();
        if(bankCardInfoByCardNo!=null){
            bankCardInfoByCardNo.stream().forEach(cardInfoItemDto -> {
                cardNoMap.put(cardInfoItemDto.getCardNo(),cardInfoItemDto.getCardNoRefId());
            });
        }
        return cardNoMap;
    }

    private List<CardNoRefInfoItemDto> getBankCardInfoByCardNo(List<String> cardNo) {
        try {
            cardNo= enCardNo(cardNo);
            QueryUsedCardsRequestDto cardInfoRequest = new QueryUsedCardsRequestDto();
            cardInfoRequest.setCardNoList(Lists.newArrayList(cardNo));
            JwsRequestType jwsRequestType = this.getJwsRequestType(ShoppingJsonUtils.serializeToJson(cardInfoRequest));
            JwsResponseType jwsResponseType = thirdpartyCardInfoManagementServiceProxy.batchGetCardNoRefIdByCardNo(jwsRequestType);
            if (Objects.isNull(jwsResponseType)) {
                return null;
            }
            JwsEntity decodeEntity = this.getDecodeEntity(jwsResponseType);
            if (Objects.isNull(decodeEntity) || StringUtils.isBlank(decodeEntity.getPayload())) {
                return null;
            }
            QueryUsedCardsResponseDto responseType = ShoppingJsonUtils.deserializeFromJson(decodeEntity.getPayload(), QueryUsedCardsResponseDto.class);
            if (Objects.nonNull(responseType) && CollectionUtils.isNotEmpty(responseType.getCardNoRefInfoItemList())) {
                return responseType.getCardNoRefInfoItemList();
            }
        } catch (Exception e) {
            LOGGER.warn("getPayCardInfos fail", e);
        }
        return null;
    }

    private List<String> enCardNo(List<String> cardNo) {
        return cardNo.stream().map(t-> rsaUtil.encrypt(t)).toList();
    }

    /**
     * 生成加密请求体
     */
    private JwsRequestType getJwsRequestType(String strCardInfoRequest) {
        Map<String, String> jwsHeaderMap = new HashMap<>();
        jwsHeaderMap.put("alg", "RS256");
        jwsHeaderMap.put("key", driverWalletInfoConfig.getWalletPayMerid());

        JwsEntity jwsRequestEntity = new JwsEntity();
        jwsRequestEntity.setHeader(ShoppingJsonUtils.serializeToJson(jwsHeaderMap));
        jwsRequestEntity.setPayload(strCardInfoRequest);
        JwsEntity encodeEntity = JwsSignature.sign(paymentTokenUtils.driverPrivateEncryptKey(), SignatureAlgorithm.RS256, jwsRequestEntity);
        JwsRequestType jwsRequestType = new JwsRequestType();
        jwsRequestType.signature = encodeEntity.getSignature();
        jwsRequestType.header = Codec.BASE64URL.encode(jwsRequestEntity.getHeader());
        jwsRequestType.payload = Codec.BASE64URL.encode(jwsRequestEntity.getPayload());
        return jwsRequestType;
    }

    /**
     * 解密响应体
     */
    private JwsEntity getDecodeEntity(JwsResponseType jwsResponseType) {
        JwsEntity jwsResponseEntity = new JwsEntity();
        jwsResponseEntity.setHeader(jwsResponseType.header);
        jwsResponseEntity.setPayload(jwsResponseType.payload);
        jwsResponseEntity.setSignature(jwsResponseType.signature);
        return new JwsSignature().verifyAndDecode(paymentTokenUtils.paymentPublicEncryptKey(), SignatureAlgorithm.RS256, jwsResponseEntity);
    }

    private String postData(CreatePayCardInfoRequest cardInfoRequest) {
        JwsRequestType jwsRequestType = this.getJwsRequestType(ShoppingJsonUtils.serializeToJson(cardInfoRequest));
        JwsResponseType jwsResponseType = thirdpartyCardInfoManagementServiceProxy.createPayCardInfo(jwsRequestType);
        if (Objects.isNull(jwsResponseType)) {
            return Strings.EMPTY;
        }
        JwsEntity decodeEntity = this.getDecodeEntity(jwsResponseType);
        if (Objects.isNull(decodeEntity) || StringUtils.isBlank(decodeEntity.getPayload())) {
            return Strings.EMPTY;
        }
        CreatePayCardInfoResponse responseType = ShoppingJsonUtils.deserializeFromJson(decodeEntity.getPayload(), CreatePayCardInfoResponse.class);
        if (Objects.nonNull(responseType) && Objects.nonNull(responseType.responseHeader) &&
                "0".equals(responseType.responseHeader.resultCode) && StringUtils.isNotBlank(responseType.cardInfoId)) {
            return responseType.cardInfoId;
        }
        return Strings.EMPTY;
    }
}
