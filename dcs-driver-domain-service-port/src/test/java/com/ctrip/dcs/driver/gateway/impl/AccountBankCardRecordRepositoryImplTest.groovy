package com.ctrip.dcs.driver.gateway.impl

import com.ctrip.dcs.driver.account.domain.condition.AccountBankCardProcessingCondition
import com.ctrip.dcs.driver.account.domain.value.AccountBankCardDo
import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardStatusEnum
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBankCardRecordDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBankExtendInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.dao.AccountBaseInfoDao
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankCardRecordPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankExtendInfoPO
import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO
import com.ctrip.dcs.driver.account.infrastructure.value.BankCardStatusDo
import com.ctrip.dcs.driver.convert.AccountBankCardConvert
import com.ctrip.dcs.driver.convert.AccountBankExtendInfoConvert
import spock.lang.Specification

/*
作者：pl.yang
创建时间：2025/6/3-下午6:00-2025
*/

class AccountBankCardRecordRepositoryImplTest extends Specification {
    AccountBankCardRecordRepositoryImpl repository
    AccountBankCardRecordDao accountBankCardRecordDao = Mock()
    AccountBaseInfoDao accountBaseInfoDao = Mock()
    AccountBankCardConvert accountBankCardConvert = Mock()
    AccountBankExtendInfoConvert accountBankExtendInfoConvert = Mock()
    AccountBankExtendInfoDao accountBankExtendInfoDao = Mock(AccountBankExtendInfoDao)
    AccountBankCardDoFactory accountBankCardDoFactory = Mock()

    def setup() {
        repository = new AccountBankCardRecordRepositoryImpl(
                accountBankCardRecordDao: accountBankCardRecordDao,
                accountBaseInfoDao: accountBaseInfoDao,
                accountBankCardConvert: accountBankCardConvert,
                accountBankExtendInfoConvert: accountBankExtendInfoConvert,
                accountBankExtendInfoDao: accountBankExtendInfoDao,
                accountBankCardDoFactory: accountBankCardDoFactory
        )
    }

    // 测试场景：查询用户最后绑定的银行卡（UID为空）
    def "testQueryAccountBindingLastBankCardV2 when uid is empty"() {
        when:
        def result = repository.queryAccountBindingLastBankCardV2("")

        then:
        result.code == "204"
        result.message == "UID_IS_EMPTY"
    }

    // 测试场景：查询用户最后绑定的银行卡（用户不存在）
    def "testQueryAccountBindingLastBankCardV2 when uid not exist"() {
        given:
        def uid = "user123"
        accountBaseInfoDao.queryByUID(uid) >> null

        when:
        def result = repository.queryAccountBindingLastBankCardV2(uid)

        then:
        result.code == "204"
        result.message == "UID_NOT_EXIST"
    }

    // 测试场景：查询用户最后绑定的银行卡（银行卡不存在）
    def "testQueryAccountBindingLastBankCardV2 when bank card not exist"() {
        given:
        def uid = "user123"
        accountBaseInfoDao.queryByUID(uid) >> new AccountBaseInfoPO()
        accountBankCardRecordDao.queryLastAccountBankCardByUid(uid) >> null

        when:
        def result = repository.queryAccountBindingLastBankCardV2(uid)

        then:
        result.code == "204"
        result.message == "BANKCARD NOT EXIST"
    }

    // 测试场景：查询用户最后绑定的银行卡（银行卡状态有效）
    def "testQueryAccountBindingLastBankCardV2 when valid bank card exists"() {
        given:
        def uid = "user123"
        def cardPO = new AccountBankCardRecordPO(operateType: BankCardStatusEnum.BIND.getCode())
        def cardDO = AccountBankCardDo.builder().build()

        accountBaseInfoDao.queryByUID(uid) >> new AccountBaseInfoPO()
        accountBankCardRecordDao.queryLastAccountBankCardByUid(uid) >> cardPO
        accountBankCardDoFactory.buildAccountBankCardDo(cardPO) >> cardDO

        when:
        def result = repository.queryAccountBindingLastBankCardV2(uid)

        then:
        result.success
        result.data == cardDO
    }

    // 测试场景：根据UID和身份证查询银行卡（UID为空）
    def "testQueryAccountBankCardByUidAndIdCard when uid is empty"() {
        when:
        def result = repository.queryAccountBankCard("", "id123")

        then:
        result == null
    }

    // 测试场景：根据UID和身份证查询银行卡（记录存在）
    def "testQueryAccountBankCardByUidAndIdCard when record exists"() {
        given:
        def uid = "user123"
        def idCard = "id123"
        def cardPO = new AccountBankCardRecordPO()
        def cardDO = AccountBankCardDo.builder().build()

        accountBankCardRecordDao.queryAccountBankCardByIdCard(uid, idCard) >> cardPO
        accountBankCardDoFactory.buildAccountBankCardDo(cardPO) >> cardDO

        when:
        def result = repository.queryAccountBankCard(uid, idCard)

        then:
        result == cardDO
    }

    // 测试场景：根据请求ID查询银行卡（请求ID为空）
    def "testQueryByRequestId when requestId is empty"() {
        when:
        def result = repository.queryByRequestId("")

        then:
        result.code == "204"
        result.message == "REQUEST_ID_IS_EMPTY"
    }

    // 测试场景：根据请求ID查询银行卡（记录存在）
    def "testQueryByRequestId when record exists"() {
        given:
        def requestId = "req123"
        def cardPO = new AccountBankCardRecordPO()
        def cardDO = AccountBankCardDo.builder().build()

        accountBankCardRecordDao.findOneByRequestId(requestId) >> cardPO
        accountBankCardDoFactory.buildAccountBankCardDo(cardPO) >> cardDO

        when:
        def result = repository.queryByRequestId(requestId)

        then:
        result.success
        result.data == cardDO
    }

    // 测试场景：根据银行卡号查询（银行卡号为空）
    def "testQueryAccountBankCardByCard when cardNo is empty"() {
        when:
        def result = repository.queryAccountBankCardByCard("")

        then:
        result == null
    }

    // 测试场景：根据银行卡号查询（记录存在）
    def "testQueryAccountBankCardByCard when record exists"() {
        given:
        def cardNo = "card123"
        def cardPO = new AccountBankCardRecordPO()
        def cardDO = AccountBankCardDo.builder().build()

        accountBankCardRecordDao.findOneByBankCardNo(cardNo) >> cardPO
        accountBankCardDoFactory.buildAccountBankCardDo(cardPO) >> cardDO

        when:
        def result = repository.queryAccountBankCardByCard(cardNo)

        then:
        result == cardDO
    }

    // 测试场景：更新银行卡状态
    def "testUpdateBankCardRecordStatus"() {
        given:
        def cardDO = AccountBankCardDo.builder()
                .id(1)
                .cardStatus(BankCardStatusDo.builder().bankCardStatusEnum(BankCardStatusEnum.BIND).build())
                .build()
        accountBankCardRecordDao.updateStatus(1, 1) >> 1

        when:
        def result = repository.updateBankCardRecordStatus(cardDO)

        then:
        result == 1
    }

    // 测试场景：扫描处理中的银行卡数据（无效参数）
    def "testScanDataBankCardProcessing with invalid params"() {
        given:
        def condition = new AccountBankCardProcessingCondition(id: 0)

        when:
        def result = repository.scanDataBankCardProcessing(condition)

        then:
        result.isEmpty()
    }

    // 测试场景：扫描处理中的银行卡数据（有效参数）
    def "testScanDataBankCardProcessing with valid params"() {
        given:
        def condition = new AccountBankCardProcessingCondition(
                id: 1,
                startDate: "2023-01-01",
                endDate: "2023-12-31"
        )
        def poList = [new AccountBankCardRecordPO()]
        def doList = [AccountBankCardDo.builder().build()]

        accountBankCardRecordDao.queryBankCardProcessing("2023-01-01", "2023-12-31", 1) >> poList
        accountBankCardConvert.batchConvertDetailDo(poList) >> doList

        when:
        def result = repository.scanDataBankCardProcessing(condition)

        then:
        result == doList
    }

    // 测试场景：根据银行卡号查询（银行卡号为空）
    def "testQueryAccountBankCardByCardNo when cardNo is empty"() {
        when:
        def result = repository.queryAccountBankCard("")

        then:
        result.code == "204"
        result.message == "BANKCARD_NO_IS_EMPTY"
    }

    // 测试场景：根据银行卡号查询（记录存在）
    def "testQueryAccountBankCardByCardNo when record exists"() {
        given:
        def cardNo = "card123"
        def cardPO = new AccountBankCardRecordPO()
        def cardDO = AccountBankCardDo.builder().build()

        accountBankCardRecordDao.queryBankCardbyBardNO(cardNo) >> cardPO
        accountBankCardDoFactory.buildAccountBankCardDo(cardPO) >> cardDO

        when:
        def result = repository.queryAccountBankCard(cardNo)

        then:
        result.success
        result.data == cardDO
    }

    // 测试场景：插入新银行卡记录
    def "testInsert"() {
        given:
        def cardDO = AccountBankCardDo.builder()
                .requestId("req123")
                .build()
        def cardPO = new AccountBankCardRecordPO(requestId: "req123")
        def extendPOList = [new AccountBankExtendInfoPO(id: 1, yeepayCardId: "req123")]

        accountBankCardConvert.convertPo(cardDO) >> cardPO
        accountBankExtendInfoConvert.convertExtendInfo(cardDO) >> extendPOList
        accountBankCardRecordDao.insert(cardPO) >> 1
        accountBankExtendInfoDao.deleteByYeepayCardId("req123") >> 2
        accountBankExtendInfoDao.batchInsert(extendPOList) >> [1]

        when:
        def result = repository.insert(cardDO)

        then:
        result == 1 + 2 + 1
    }

    // 测试场景：更新银行卡记录
    def "testUpdate"() {
        given:
        def cardDO = AccountBankCardDo.builder()
                .requestId("req123")
                .build()
        def cardPO = new AccountBankCardRecordPO(requestId: "req123")
        def extendPOList = [new AccountBankExtendInfoPO()]

        accountBankCardConvert.convertPo(cardDO) >> cardPO
        accountBankExtendInfoConvert.convertExtendInfo(cardDO) >> extendPOList
        accountBankCardRecordDao.update(cardPO) >> 1
        accountBankExtendInfoDao.deleteByYeepayCardId("req123") >> 2
        accountBankExtendInfoDao.batchInsert(extendPOList) >> [1]

        when:
        def result = repository.update(cardDO)

        then:
        result == 1 + 2 + 1
    }

    // 测试场景：保存或更新银行卡（参数无效）
    def "testSaveOrUpdate with invalid params"() {
        given:
        def cardDO = AccountBankCardDo.builder().build()

        when:
        def result = repository.saveOrUpdate(cardDO)

        then:
        result == 0
    }

    // 测试场景：保存或更新银行卡（插入新记录）
    def "testSaveOrUpdate when insert new record"() {
        given:
        def cardDO = AccountBankCardDo.builder()
                .uid("user123")
                .cardNo("card123")
                .build()

        repository.queryAccountBankCard("user123", "card123") >> null
        accountBankCardConvert.convertPo(_) >> Mock(AccountBankCardRecordPO) {
            getRequestId()>>"trerst"
        }
        accountBankExtendInfoDao.batchInsert(_)>>[1]

        when:
        def result = repository.saveOrUpdate(cardDO)

        then:
        result == 1
    }

    // 测试场景：保存或更新银行卡（更新现有记录）
    def "testSaveOrUpdate when update existing record"() {
        given:
        def cardDO = AccountBankCardDo.builder()
                .uid("user123")
                .cardNo("card123")
                .build()

        def existingCard = AccountBankCardDo.builder().build()
        repository.queryAccountBankCard("user123", "card123") >> existingCard
        repository.update(cardDO) >> 1
        accountBankCardConvert.convertPo(_) >> Mock(AccountBankCardRecordPO) {
            getRequestId()>>"trerst"
        }
        accountBankExtendInfoDao.batchInsert(_)>>[1]
        when:
        def result = repository.saveOrUpdate(cardDO)

        then:
        result == 1
    }

}

