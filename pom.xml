<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"

         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ctrip.dcs.pom</groupId>
        <artifactId>dcs-super-pom</artifactId>
        <version>7.3.0</version>

    </parent>

    <modules>
        <module>dcs-driver-domain-service-application</module>
        <module>dcs-driver-domain-service-domain</module>
        <module>dcs-driver-domain-service-infrastructure</module>
        <module>dcs-driver-domain-service-start</module>
        <module>dcs-driver-common</module>
        <module>dcs-driver-domain-service-port</module>
    </modules>

    <groupId>com.ctrip.dcs.driver</groupId>
    <artifactId>dcs-driver-domain-service</artifactId>
    <version>1.0.2</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.release>21</maven.compiler.release> <!-- 推荐（Maven 3.8.4+） -->
        <java.version>21</java.version>
        <log4j2.version>2.17.1</log4j2.version>
        <enforcer.skip>false</enforcer.skip>
        <commons-lang3.version>3.9</commons-lang3.version>
        <lombok.version>1.18.34</lombok.version>
        <jmockit.version>1.52.0-jdk21</jmockit.version>
        <spock.version>2.1-groovy-3.0</spock.version>
        <groovy.version>3.0.9</groovy.version>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <driver-domain-service.version>1.0.61-gyj-SNAPSHOT</driver-domain-service.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>4.11.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-junit-jupiter</artifactId>
                <version>4.11.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.rhb.settlement</groupId>
                <artifactId>rhb-settlement-bill-soa-service</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.tour.driver</groupId>
                <artifactId>driver-platform-api-service</artifactId>
                <version>1.0.17</version>
            </dependency>
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>4.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>4.0.2</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.groovy</groupId>
                <artifactId>groovy-all</artifactId>
                <version>4.0.11</version>
                <type>pom</type>
                <exclusions>
                    <exclusion>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm-analysis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.ow2.asm</groupId>
                        <artifactId>asm-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.basebiz.accountloginapiserverinternal</groupId>
                <artifactId>accountloginapiserverinternal</artifactId>
                <version>1.0.25</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.basebiz.accountmanagerapiserverinternal</groupId>
                <artifactId>accountmanagerapiserverinternal</artifactId>
                <version>0.0.19</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.basebiz</groupId>
                <artifactId>account-service-client</artifactId>
                <version>0.0.4</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-mq</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <artifactId>geo-platform-sdk</artifactId>
                <groupId>com.ctrip.dcs.geo</groupId>
                <version>1.1.9</version>
            </dependency>
            <!--            <dependency>-->
            <!--                <groupId>org.bouncycastle</groupId>-->
            <!--                <artifactId>bcprov-jdk15on</artifactId>-->
            <!--                <version>1.69</version>-->
            <!--            </dependency>-->
            <dependency>
                <groupId>com.ctrip.dcs.pom</groupId>
                <artifactId>dcs-shopping-utils</artifactId>
                <version>0.0.4</version>
                <exclusions>
                    <exclusion>
                        <artifactId>basicdata</artifactId>
                        <groupId>com.ctrip.igt.bizcomponent</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver</groupId>
                <artifactId>dcs-driver-domain-service-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver</groupId>
                <artifactId>dcs-driver-domain-service-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver</groupId>
                <artifactId>dcs-driver-domain-service-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver.domain</groupId>
                <artifactId>driver-domain-service</artifactId>
                <version>${driver-domain-service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver.domain</groupId>
                <artifactId>driver-domain-service-mq</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.tms</groupId>
                <artifactId>transport-api</artifactId>
                <version>1.5.82</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.23718</groupId>
                <artifactId>gms-transport-domain-api</artifactId>
                <version>1.1.0</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.8.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver.message</groupId>
                <artifactId>message-service-client</artifactId>
                <version>1.0.7</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.infosec.kms</groupId>
                <artifactId>kms-sdk</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.infosec</groupId>
                <artifactId>sso-client-new</artifactId>
                <version>0.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.infrastructure.service</groupId>
                <artifactId>client</artifactId>
                <version>1.2.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>ibu-shark-sdk</artifactId>
                <version>5.1.3</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.supply</groupId>
                <artifactId>supply-account-transfer-service</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.platform.softpbx</groupId>
                <artifactId>softpbxvoipcloud</artifactId>
                <version>2.3.5</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.im</groupId>
                <artifactId>im-service-client</artifactId>
                <version>1.0.26</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.model.groupchatmessage</groupId>
                <artifactId>com.ctrip.dcs.im</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.tour.ai</groupId>
                <artifactId>one-service-client</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.go</groupId>
                <artifactId>http</artifactId>
                <version>8.35.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.go</groupId>
                <artifactId>dal</artifactId>
                <version>8.35.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.platform.basesystem.emailservice.v1</groupId>
                <artifactId>emailservice</artifactId>
                <version>1.2.9</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.frt</groupId>
                <artifactId>product-basic-client</artifactId>
                <version>1.0.161</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.dcs.driver</groupId>
                <artifactId>dcs-driver-common</artifactId>
                <version>1.0.2</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.payment.fi</groupId>
                <artifactId>thirdparty-cardinfo-management-client</artifactId>
                <version>1.0.21</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.payment</groupId>
                <artifactId>sec-crypto</artifactId>
                <version>0.1.30</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.payment.router</groupId>
                <artifactId>router-api-soa-client</artifactId>
                <version>1.0.32</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.dcs.jnt.dcsscmmerchantservice.v1</groupId>
                <artifactId>dcsscmmerchantservice</artifactId>
                <version>1.0.50</version>
            </dependency>
            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.23343</groupId>
                <artifactId>phonenumbersplitservice</artifactId>
                <version>1.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.sysdev</groupId>
                <artifactId>daas-client-soa</artifactId>
                <version>1.0.10</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.json</groupId>
                        <artifactId>json</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.ctrip.infosec.kms</groupId>
                        <artifactId>key-escrow-infra</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.tour</groupId>
                <artifactId>driver-utility</artifactId>
                <version>1.0.6</version>
            </dependency>
            <dependency>
                <groupId>net.coobird</groupId>
                <artifactId>thumbnailator</artifactId>
                <version>0.4.9</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- spock -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- spock依赖的groovy -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-test-junit5</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-testng</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>test</artifactId>
            <scope>test</scope>
            <exclusions>
                <!-- 排除旧版 junit-jupiter-engine 和 hamcrest -->
                <exclusion>
                    <groupId>org.hamcrest</groupId>
                    <artifactId>hamcrest</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter-engine</artifactId>
                </exclusion>
                <!-- 额外排除旧版 groovy（如果存在传递依赖） -->
                <exclusion>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <!--groovy 编译-->
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>addSources</goal>
                            <goal>addTestSources</goal>
                            <goal>generateStubs</goal>
                            <goal>compile</goal>
                            <goal>generateTestStubs</goal>
                            <goal>compileTests</goal>
                            <goal>removeStubs</goal>
                            <goal>removeTestStubs</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <!--                    <forkCount>1</forkCount>-->
                    <!--                    <reuseForks>false</reuseForks>-->
                    <argLine>
                        -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
                        -javaagent:"${settings.localRepository}"/org/jacoco/org.jacoco.agent/${jacoco.version}/org.jacoco.agent-${jacoco.version}-runtime.jar=destfile=${project.build.directory}/jacoco.exec
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-opens java.base/java.math=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED
                        --add-opens java.base/java.io=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                        --add-opens java.management/sun.management=ALL-UNNAMED
                        --add-opens java.management/java.lang.management=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                        --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.vm.annotation=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.access=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.util.random=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.misc=ALL-UNNAMED
                        --add-opens jdk.jfr/jdk.jfr.internal.tool=ALL-UNNAMED
                        --add-opens jdk.internal.jvmstat/sun.jvmstat.monitor=ALL-UNNAMED
                        --add-opens java.base/java.lang.ref=ALL-UNNAMED
                        --add-opens java.base/java.time=ALL-UNNAMED
                        --add-opens java.base/java.time.format=ALL-UNNAMED
                    </argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
