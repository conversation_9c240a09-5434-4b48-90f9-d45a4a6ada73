package com.ctrip.dcs.driver.domain.infrastructure.geateway.impl;

import com.ctrip.daas.controller.DaasApiRequestType;
import com.ctrip.daas.controller.DaasApiResponseType;
import com.ctrip.daas.controller.Head;
import com.ctrip.dcs.driver.account.infrastructure.value.CatEventType;
import com.ctrip.dcs.driver.domain.infrastructure.geateway.DaasGateway;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.sysdev.daas.client.DaasClient;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class DaasGatewayImpl implements DaasGateway {
    private static final Logger LOGGER = LoggerFactory.getLogger(DaasGateway.class);

    private static final String DEFAULT_TOKEN = "lkH/sRA5fT25iwJtD6GUpw==";
    private static final String DEFAULT_API = "getAdmSevTrhHighDrvSelectTNew";

    @Autowired
    private SystemQConfig systemQConfig;

    DaasClient client = DaasClient.getInstance();

    @Override
    public GoldMedalDriverEntity queryGoldMedalDriverInfo(String driverId) throws Exception {
        try {
            String token = StringUtils.defaultIfBlank(systemQConfig.getString("daas_query_gold_medal_driver_token"), DEFAULT_TOKEN);
            String apiName = StringUtils.defaultIfBlank(systemQConfig.getString("daas_query_gold_medal_driver_api_name"), DEFAULT_API);

            Gson gson = new Gson();

            DaasApiRequestType requestType = new DaasApiRequestType();
            Map<String, Object> params = new HashMap<>();
            params.put("driver_id_last", Lists.newArrayList(driverId));
            requestType.setHead(new Head(token, 100038374, apiName));
            requestType.setParams(gson.toJson(params));

            DaasApiResponseType responseType = invoke(requestType);
            if (responseType == null || StringUtils.isBlank(responseType.getData())) {
                // 未查询到
                Cat.logEvent(CatEventType.QUERY_GOLD_MEDAL_DRIVER_INFO_RESULT, "NoResult");
                LOGGER.warn("queryGoldMedalDriverInfo failed_" + driverId, "no result");
                return null;
            }
            GoldMedalDriverEntity[] goldMedalDriverEntityList = gson.fromJson(responseType.getData(), GoldMedalDriverEntity[].class);

            if (ArrayUtils.isEmpty(goldMedalDriverEntityList) || goldMedalDriverEntityList[0] == null || ObjectUtils.notEqual(goldMedalDriverEntityList[0].getDriverId(), driverId)) {
                // 未查询到
                Cat.logEvent(CatEventType.QUERY_GOLD_MEDAL_DRIVER_INFO_RESULT, "ErrorResult");
                LOGGER.warn("queryGoldMedalDriverInfo failed_" + driverId, "error result");
                return null;
            }

            Cat.logEvent(CatEventType.QUERY_GOLD_MEDAL_DRIVER_INFO_RESULT, "Success");
            return goldMedalDriverEntityList[0];
        } catch (Exception e) {
            // 处理异常，对异常做好兜底降级方案，不要影响主流程
            Cat.logEvent(CatEventType.QUERY_GOLD_MEDAL_DRIVER_INFO_RESULT, "Error");
            LOGGER.warn("queryGoldMedalDriverInfo failed_" + driverId, "query error");
            throw e;
        }

    }

    private DaasApiResponseType invoke(DaasApiRequestType requestType) throws Exception {
        LOGGER.info("invoke daas api request", new Gson().toJson(requestType));
        DaasApiResponseType responseType = client.invoke2(requestType);
        LOGGER.info("invoke daas api response", new Gson().toJson(responseType));
        return responseType;
    }

    @Getter
    @Setter
    public static class GoldMedalDriverEntity {

        /**
         * id，分页查询时第2页以后使用
         */
        @SerializedName("mysql_id")
        private Long mysqlId;

        @SerializedName("driver_id_last")
        private String driverId;

        @SerializedName("is_good_driver")
        private int goodDriver;

        @SerializedName("total_good_driver_cnt")
        private int totalGoodDriverCnt;
    }


}
