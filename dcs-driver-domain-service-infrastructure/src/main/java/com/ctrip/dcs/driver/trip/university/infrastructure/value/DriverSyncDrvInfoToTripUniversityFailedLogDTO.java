package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DriverSyncDrvInfoToTripUniversityFailedLogDTO {

  private Long id;

  /**
   * 司机UID
   */
  private String uid;

  /**
   * 司机ID
   */
  private Long drvId;

  /**
   * 重试次数
   */
  private Integer retryCount;

  /**
   * 创建时间
   */
  private LocalDateTime datachangeCreatetime;

  /**
   * 创建人
   */
  private String createUser;

  /**
   * 变更人
   */
  private String modifyUser;

  /**
   * 更新时间
   */
  private LocalDateTime datachangeLasttime;

  /**
   * 失败原因代码
   */
  private Integer failedReasonCode;

  /**
   * 失败具体原因
   */
  private String failedReasonDetail;
}
