package com.ctrip.dcs.driver.account.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;


@Entity
@Database(name = "dcsdriverappdb")
@Table(name = "account_bank_card_record")
@Data
public class AccountBankCardRecordPO implements DalPojo {

	/**
	 * id
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

	/**
	 * uid
	 */
	@Column(name = "uid")
	@Type(value = Types.VARCHAR)
	private String uid;

	/**
	 * 绑卡人姓名
	 */
	@Column(name = "name")
	@Type(value = Types.VARCHAR)
	private String name;

	/**
	 * 身份证号，密文
	 */
	@Column(name = "id_card_no")
	@Type(value = Types.VARCHAR)
	private String idCardNo;

	/**
	 * 银行卡号、密文 (金融那边的id)
	 */
	@Column(name = "bank_card_no")
	@Type(value = Types.VARCHAR)
	private String bankCardNo;

	/**
	 * 卡号对应的银行名称
	 */
	@Column(name = "bank_name")
	@Type(value = Types.VARCHAR)
	private String bankName;

	/**
	 * 银行预留手机号、密文
	 */
	@Column(name = "bank_phone_number")
	@Type(value = Types.VARCHAR)
	private String bankPhoneNumber;

	@Column(name = "tel_country_code")
	@Type(value = Types.VARCHAR)
	private String telCountryCode;

	/**
	 * 0默认1绑卡2解绑
	 */
	@Column(name = "operate_type")
	@Type(value = Types.INTEGER)
	private Integer operateType;

	/**
	 * 操作时间
	 */
	@Column(name = "operate_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp operateTime;

	/**
	 * 创建时间
	 */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

	/**
	 * 修改时间
	 */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 是否境外，0：境内；1：境外
	 */
	@Column(name = "is_oversea")
	@Type(value = Types.INTEGER)
	private Integer isOversea;

	/**
	 * udl  todo 需要加上udl 考虑上云
	 */
	@Column(name = "provider_data_location")
	@Type(value = Types.VARCHAR)
	private String providerDataLocation;

	@Column(name = "request_id")
	@Type(value = Types.VARCHAR)
	private String requestId;
	@Column(name = "country_id")
	@Type(value = Types.INTEGER)
	private Long countryId;

	@Column(name = "cityId")
	@Type(value = Types.INTEGER)
	private Long cityId;
	/**
	 * 币种
	 */
	@Column(name = "currency")
	@Type(value = Types.VARCHAR)
	private String currency;
	// '1:境内司机境内卡
	//2：境外司机境内卡
	//3：境外司机境外卡'
	@Column(name = "area_type")
	@Type(value = Types.INTEGER)
	private Integer areaType;
	//driverId
	@Column(name = "driverId")
	@Type(value = Types.VARCHAR)
	private String driverId;


}
