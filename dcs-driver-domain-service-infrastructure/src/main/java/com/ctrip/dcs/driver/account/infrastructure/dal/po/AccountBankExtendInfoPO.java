package com.ctrip.dcs.driver.account.infrastructure.dal.po;
/*
作者：pl.yang
创建时间：2025/5/15-下午4:21-2025
*/


import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: AccountBankExtendInfoPO
 * @Package com.ctrip.dcs.driver.account.infrastructure.dal.po
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/15 下午4:21
 */

@Entity
@Database(name = "dcsdriverappdb")
@Table(name = "account_bank_extend_info")
@Data
public class AccountBankExtendInfoPO implements DalPojo {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;
    /**
     * 易宝绑卡ID
     */
    @Column(name = "yeepay_card_id")
    @Type(value = Types.VARCHAR)
    private String yeepayCardId;
    /**
     * 扩展信息Code
     */
    @Column(name = "extend_key")
    @Type(value = Types.VARCHAR)
    private String extendKey;
    /**
     * 扩展信息内容
     */
    @Column(name = "extend_value")
    @Type(value = Types.VARCHAR)
    private String extendValue;
    /**
     * 扩展信息的类型
     */
    @Column(name = "extend_type")
    @Type(value = Types.INTEGER)
    private Integer extendType;
    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 修改时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}
