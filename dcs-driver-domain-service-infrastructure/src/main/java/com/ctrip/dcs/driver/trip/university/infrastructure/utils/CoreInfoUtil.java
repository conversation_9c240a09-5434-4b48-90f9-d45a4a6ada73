package com.ctrip.dcs.driver.trip.university.infrastructure.utils;

import com.ctrip.arch.coreinfo.CoreInfoClient;
import com.ctrip.arch.coreinfo.entity.InfoData;
import com.ctrip.arch.coreinfo.entity.InfoKey;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.arch.coreinfo.enums.Status;
import com.dianping.cat.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CoreInfoUtil {

    private static final Logger log = LoggerFactory.getLogger(CoreInfoUtil.class);

    /**
     * 解密
     * @param paramKey
     * @return
     */
    public static String decrypt(String paramKey,KeyType keyType){
        if(StringUtils.isEmpty(paramKey)){
            return paramKey;
        }
        try{
            for (int i = 0; i < 3; i++) {
                InfoData infoData = CoreInfoClient.getInstance().decrypt(new InfoKey(keyType, paramKey));
                if(Status.SUCCESS == infoData.getStatus()){
                    String result = infoData.getResult(); //获取解密后结果
                    return result;
                }
            }
        }catch (Exception e){
            log.error("decrypt error,",e);
        }
        return paramKey;
    }
}
