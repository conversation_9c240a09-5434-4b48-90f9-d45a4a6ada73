package com.ctrip.dcs.driver.account.infrastructure.value;

public interface CatEventType {

    String GENERATE_GLOBAL_ID_SOURCE = "Account.GenerateGlobalIdSource";

    String GENERATE_GLOBAL_ID_UID_FROM = "Account.GenerateGlobalUidFrom";

    String GENERATE_GLOBAL_ID_RESULT = "Account.GenerateGlobalIdResult";

    String REGISTER_SOURCE = "Account.RegisterSource";

    String REGISTER_COUNTRY = "Account.RegisterCountry";

    String REGISTER_RESULT = "Account.RegisterResult";

    String REGISTER_IDENTITY = "Account.RegisterIdentity";

    String REGISTER_UID_FROM = "Account.RegisterUidFrom";

    String REGISTER_FIRST_SOURCE = "Account.RegisterFirstSource";

    String REGISTER_CONFLICT_FIELD = "Account.RegisterConflictField";

    String REGISTER_AREA_CONFLICT = "Account.RegisterAreaConflict";

    String SYNC_DRIVER_ID = "Account.SyncDriverId";

    String SYNC_ACCOUNT_RESULT = "Account.SyncAccountResult";

    String SYNC_ACCOUNT_PHONE_CHECK = "Account.SyncAccountPhoneCheck";


    String UPDATE_SOURCE = "Account.UpdateSource";
    String UPDATE_RESULT = "Account.UpdateResult";
    String UPDATE_FIELD = "Account.UpdateField";

    String UPDATE_IDENTITY_STATE_SOURCE = "Account.UpdateIdentityStateSource";
    String UPDATE_IDENTITY_STATE_RESULT = "Account.UpdateIdentityStateResult";

    String RE_CALC_DRIVER_IDENTITY_STATE_RESULT = "Account.ReCalcDriverIdentityStateResult";

    String QUERY_TYPE = "Account.QueryType";
    String QUERY_DATA_SOURCE = "Account.QueryDataSource";

    String BATCH_QUERY_BY_UID_RESULT = "Account.BatchQueryByUidResult";
    String BATCH_QUERY_BY_DRIVER_ID_RESULT = "Account.BatchQueryByDriverIdResult";
    String QUERY_BY_UID_RESULT = "Account.QueryByUidResult";
    String QUERY_BY_SOURCE_RESULT = "Account.QueryBySourceResult";
    String QUERY_BY_NAME_RESULT = "Account.QueryByNameResult";
    String QUERY_BY_ID_CARD_RESULT = "Account.QueryByIdCardResult";
    String QUERY_BY_EMAIL_RESULT = "Account.QueryByEmailResult";
    String QUERY_BY_PAYONEER_RESULT = "Account.QueryByPayoneerResult";
    String QUERY_BY_PHONE_RESULT = "Account.QueryByPhoneResult";
    String QUERY_BY_DRIVER_ID_RESULT = "Account.QueryByDriverIdResult";

    String QUERY_UID_BY_DRIVER_ID_RESULT = "Account.QueryByDriverIdResult.Uid";

    String GUIDE_BIND_PHONE = "Account.GuideBindPhone";
    String SYNC_GUIDE_ACCOUNT = "Account.SyncGuideAccount";

    String UNFREEZE_DRIVER_ACCOUNT = "Account.UnfreezeDriverAccount";

    String PHONE_ENCRYPT_START_WITH_0 = "Phone.Encrypt.StartWith0";

    String JOB_BIND_EMAIL_RESULT = "Account.JobBindEmailResult";

    String JOB_CHECK_ACCOUNT_BASE_INFO_RESULT = "Account.JobCheckAccountBaseInfoResult";

    String JOB_REFRESH_ACCOUNT_UDL_RESULT = "Account.JobRefreshAccountUdlResult";

    String JOB_SYNC_USER_CENTER_PHONE_RESULT = "Account.JobSyncUserCenterPhoneResult";

    String PARSE_PHONE_NUMBER = "Account.ParsePhoneNumberResult";

    String DEVICE_INFO_QUERY = "Device.Info.Query";
    String DEVICE_INFO_UPDATE_UID = "Device.Info.Update.Uid";
    String QUERY_BIZ_GRAY_SWITCH = "BizGraySwitch";
    String QUERY_BIZ_GRAY_SWITCH_ACTIVE_INTERVAL_TIMES = "bizGraySwitchActiveIntervalTime_%s";
    /**
     * 语音验证码外呼
     */
    String SUBMIT_VOICE_CODE_CALL = "Account.voiceIvrCall";
    /**
     * 语音验证码外呼
     */
    String IVR_VOICE_CODE_CALL_RES = "Account.ivrCallRes";
    /**
     * 语音验证码没有匹配的语言
     */
    String VOICE_CODE_CALL_NOT_MATCH_LOCAL = "Account.notMatchLocal";

    String QUERY_GOLD_MEDAL_DRIVER_INFO_RESULT = "QueryGoldMedalDriverInfoResult";

    /**
     * IVR外呼挂断码
     */
    String IVR_CALL_LISTENER_RESULT = "ivr.hangupCode";


    String ACCOUNT_UDL_QUEYR = "Account.UDL.Query";
    //注册码
    String REGISTER_ACCESS_CODE = "Account.register.access_code";
    //udl 查询情况
    String QUERY_BY_UID_UDL = "Account.udl.access_code";
    String QUERY_ACCESS_CODE_BY_UID = "Account.query.access_code.byUid";

    String DRIVER_TASK_QUERY = "driver.QueryTask";
    String DRIVER_TASK_SAVE = "driver.SaveTask";
    String DRIVER_CARINSPECTION_QUERY = "driver.QueryCarInspection";
    String DRIVER_CARINSPECTION_SAVE = "driver.SaveCarInspection";
    String Bind_Oversea_BankCard = "driver.bind.bankCard";
    String Unbind_Oversea_BankCard = "Unbind_Oversea_BankCard";
    String Unbind_Domestic_BankCard = "Unbind_Domestic_BankCard";
    String JOB_REFRESH_ACCOUNT_PAYTYPE_RESULT = "job_refresh_account_payType_result";
    String QUERY_BANK_CARD_AREA_TYPE_ENUM_RESULT = "QUERY_BANK_CARD_AREA_TYPE_ENUM_RESULT";
    String PayChannelTypeGrey = "PayChannelTypeGrey";
    String RE_CALC_DRIVER_BANK_CARD_STATUS_RESULT = "RE_CALC_DRIVER_BANK_CARD_STATUS_RESULT";
    String DISTINCT_ACCOUNTUDLDO = "DISTINCT_ACCOUNTUDLDO";
}
