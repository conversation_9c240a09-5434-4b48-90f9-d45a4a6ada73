package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import com.ctrip.dcs.go.domain.page.Pageable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DriverSyncDrvInfoToTripUniversityQueryCondition {
    private Pageable pagination;
    private List<Long> drvIdList;
    private Timestamp from;
    private Timestamp to;
    private Long sortId;
}
