package com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-05-26
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driv_level")
public class DrivLevelPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 等级定义ID
     */
	@Column(name = "level_config_id")
	@Type(value = Types.BIGINT)
	private Long levelConfigId;

    /**
     * 等级名称
     */
	@Column(name = "level_name")
	@Type(value = Types.VARCHAR)
	private String levelName;

    /**
     * 城市ID
     */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 月份索引，yyyy-MM
     */
	@Column(name = "month_idx")
	@Type(value = Types.VARCHAR)
	private String monthIdx;

    /**
     * 司机ID
     */
	@Column(name = "driv_id")
	@Type(value = Types.BIGINT)
	private Long drivId;

    /**
     * 司机等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
	@Column(name = "driv_level")
	@Type(value = Types.TINYINT)
	private Integer drivLevel;

    /**
     * 司机服务分
     */
	@Column(name = "driv_point")
	@Type(value = Types.VARCHAR)
	private String drivPoint;

    /**
     * 司机活跃分
     */
	@Column(name = "driv_activity")
	@Type(value = Types.VARCHAR)
	private String drivActivity;

    /**
     * 司机排名
     */
	@Column(name = "driv_rank")
	@Type(value = Types.VARCHAR)
	private String drivRank;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;
}