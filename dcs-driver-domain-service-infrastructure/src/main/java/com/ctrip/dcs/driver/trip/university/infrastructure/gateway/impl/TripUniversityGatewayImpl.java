package com.ctrip.dcs.driver.trip.university.infrastructure.gateway.impl;

import com.ctrip.dcs.driver.trip.university.infrastructure.dal.dao.TripUniversitySyncFailedLogDao;
import com.ctrip.dcs.driver.trip.university.infrastructure.dal.po.DriverSyncDrvInfoToTripUniversityFailedLogPO;
import com.ctrip.dcs.driver.trip.university.infrastructure.dal.repository.TripUniversitySyncFailedLogRepository;
import com.ctrip.dcs.driver.trip.university.infrastructure.gateway.TripUniversityGateway;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityFailedLogDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityQueryCondition;
import com.ctrip.platform.dal.dao.DalHints;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class TripUniversityGatewayImpl implements TripUniversityGateway {

  @Autowired
  TripUniversitySyncFailedLogDao tripUniversitySyncFailedLogDao;

  @Autowired
  TripUniversitySyncFailedLogRepository universitySyncFailedLogRepository;

  @SneakyThrows
  @Override
  public boolean saveSyncFailedLog(DriverSyncDrvInfoToTripUniversityFailedLogDTO failedLogDTO) {
    universitySyncFailedLogRepository.getDalRepository().insertDuplicateUpdate(new DalHints().setIdentityBack(), convert(failedLogDTO));
    return false;
  }

  private DriverSyncDrvInfoToTripUniversityFailedLogPO convert(DriverSyncDrvInfoToTripUniversityFailedLogDTO failedLogDTO) {
      DriverSyncDrvInfoToTripUniversityFailedLogPO driverSyncDrvInfoToTripUniversityFailedLogPO = new DriverSyncDrvInfoToTripUniversityFailedLogPO();
      driverSyncDrvInfoToTripUniversityFailedLogPO.setId(failedLogDTO.getId());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setUid(failedLogDTO.getUid());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setDrvId(failedLogDTO.getDrvId());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setRetryCount(failedLogDTO.getRetryCount());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setDatachangeCreatetime(failedLogDTO.getDatachangeCreatetime());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setCreateUser(failedLogDTO.getCreateUser());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setModifyUser(failedLogDTO.getModifyUser());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setDatachangeLasttime(failedLogDTO.getDatachangeLasttime());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setFailedReasonCode(failedLogDTO.getFailedReasonCode());
      driverSyncDrvInfoToTripUniversityFailedLogPO.setFailedReasonDetail(failedLogDTO.getFailedReasonDetail());
      return driverSyncDrvInfoToTripUniversityFailedLogPO;
  }

  @Override
  public List<DriverSyncDrvInfoToTripUniversityFailedLogDTO> querySyncFailedLog(
    DriverSyncDrvInfoToTripUniversityQueryCondition condition) {
    return convert2List(universitySyncFailedLogRepository.querySyncFailedLog(condition));
  }

  @SneakyThrows
  @Override
  public void deleteFailedLog(String uid) {
    tripUniversitySyncFailedLogDao.deleteByUid(uid);
  }

  private List<DriverSyncDrvInfoToTripUniversityFailedLogDTO> convert2List(List<DriverSyncDrvInfoToTripUniversityFailedLogPO> driverSyncDrvInfoToTripUniversityFailedLogPOList) {
    return driverSyncDrvInfoToTripUniversityFailedLogPOList.stream().map(this::convert).collect(Collectors.toList());
  }
  private DriverSyncDrvInfoToTripUniversityFailedLogDTO convert(DriverSyncDrvInfoToTripUniversityFailedLogPO failedLogPO) {
      DriverSyncDrvInfoToTripUniversityFailedLogDTO driverSyncDrvInfoToTripUniversityFailedLogDTO = new DriverSyncDrvInfoToTripUniversityFailedLogDTO();
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setId(failedLogPO.getId());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setUid(failedLogPO.getUid());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setDrvId(failedLogPO.getDrvId());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setRetryCount(failedLogPO.getRetryCount());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setDatachangeCreatetime(failedLogPO.getDatachangeCreatetime());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setCreateUser(failedLogPO.getCreateUser());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setModifyUser(failedLogPO.getModifyUser());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setDatachangeLasttime(failedLogPO.getDatachangeLasttime());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setFailedReasonCode(failedLogPO.getFailedReasonCode());
      driverSyncDrvInfoToTripUniversityFailedLogDTO.setFailedReasonDetail(failedLogPO.getFailedReasonDetail());
      return driverSyncDrvInfoToTripUniversityFailedLogDTO;
  }
}
