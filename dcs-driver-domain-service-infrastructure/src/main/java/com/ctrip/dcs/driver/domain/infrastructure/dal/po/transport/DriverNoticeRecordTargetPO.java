package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2021-08-18
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_notice_record_target")
public class DriverNoticeRecordTargetPO implements DalPojo {
  /**
   * 记录ID，自增
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * 通知记录的id
   */
  @Column(name = "notice_id")
  @Type(value = Types.BIGINT)
  private Long noticeId;

  /**
   * 司机id
   */
  @Column(name = "driver_id")
  @Type(value = Types.BIGINT)
  private Long driverId;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeCreatetime;

  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime", insertable = false, updatable = false)
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLasttime;
}
