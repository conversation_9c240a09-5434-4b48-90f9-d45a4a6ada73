package com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity;

import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Types;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "phone_check")
@Database(name = "dcstransportdb_w")
public class PhoneCheckEntity {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    Long id;

    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    Long driverId;

    @Column(name = "phone_prefix")
    @Type(value = Types.VARCHAR)
    String phonePrefix;

    @Column(name = "phone_number")
    @Type(value = Types.VARCHAR)
    String phoneNumber;

    @Column(name = "check_type")
    @Type(value = Types.VARCHAR)
    Integer checkType;

    @Column(name = "check_state")
    @Type(value = Types.VARCHAR)
    Integer checkState;

}