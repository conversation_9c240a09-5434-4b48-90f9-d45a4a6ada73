package com.ctrip.dcs.driver.account.infrastructure.dal.dao;

import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBaseInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.sql.Timestamp;
import java.util.List;

@Dal("dcstransportdb_w")
public interface AccountBaseInfoDao extends DalRepository<AccountBaseInfoPO> {
    @Sql("select * from account_base_info where country_code = ? and phone_number = ? limit 1")
    AccountBaseInfoPO queryByMobilePhone(String countryCode, String phoneNumber);

    @Sql("select * from account_base_info where name = ?")
    List<AccountBaseInfoPO> queryByName(String name);

    @Sql("select * from account_base_info where id_card_no = ?")
    List<AccountBaseInfoPO> queryByIdCardNo(String idCard);

    @Sql("select * from account_base_info where email = ? limit 1")
    AccountBaseInfoPO queryByEmail(String name);

    @Sql("select * from account_base_info where uid = ? limit 1")
    AccountBaseInfoPO queryByUID(String uid);

    @Sql("select * from account_base_info where payoneer_account_id = ? limit 1")
    AccountBaseInfoPO queryByPayoneerAccountId(String payoneerAccountId);

    @Sql("select * from account_base_info where uid in (?)")
    List<AccountBaseInfoPO> batchQueryByUID(List<String> uidList);

    @Sql("select * from account_base_info where id > ? order by id limit ?")
    List<AccountBaseInfoPO> batchQueryByPage(Long id, int limit);

    @Sql("select * from account_base_info where id > ? and datachange_lasttime >= ? and datachange_lasttime <= ? order by id asc limit ? ")
    List<AccountBaseInfoPO> batchQueryPageByDateRange(Long id, Timestamp dateFrom, Timestamp dateTo, int limit);

    @Sql("update account_base_info set provider_data_location = ? where uid = ?")
    void updateUdl(String udl, String uid);


    @Sql("select * from account_base_info where  phone_number in (?)")
    List<AccountBaseInfoPO> batchQueryByMobilePhone(List<String> phoneNumber);
}
