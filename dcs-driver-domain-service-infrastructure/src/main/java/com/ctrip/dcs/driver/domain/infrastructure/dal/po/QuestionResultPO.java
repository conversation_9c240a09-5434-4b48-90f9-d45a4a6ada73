package com.ctrip.dcs.driver.domain.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Database(name = "dcsquestiondb")
@Table(name = "question_result")
public class QuestionResultPO implements DalPojo {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 场景ID
     */
    @Column(name = "scene_code")
    @Type(value = Types.VARCHAR)
    private String sceneCode;

    /**
     * 问题ID
     */
    @Column(name = "question_id")
    @Type(value = Types.BIGINT)
    private Long questionId;

    /**
     * 业务ID
     */
    @Column(name = "business_id")
    @Type(value = Types.VARCHAR)
    private String businessId;

    /**
     * 选项ID
     */
    @Column(name = "option_id")
    @Type(value = Types.BIGINT)
    private Long optionId;

    /**
     * 提交人ID
     */
    @Column(name = "user_id")
    @Type(value = Types.VARCHAR)
    private String userId;

    /**
     * 答案选项性质，1：积极选项，2：消极选项，3：中性选项，默认值为-1，必问维度条件
     */
    @Column(name = "answer_nature")
    @Type(value = Types.TINYINT)
    private Integer answerNature;

    /**
     * 提交追问标签sharkKey列表，"+"分割
     */
    @Column(name = "label_shark_keys")
    @Type(value = Types.VARCHAR)
    private String labelSharkKeys;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp dataChangeCreateTime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp dataChangeLastTime;
}
