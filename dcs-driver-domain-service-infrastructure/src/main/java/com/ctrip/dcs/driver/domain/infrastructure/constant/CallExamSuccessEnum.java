package com.ctrip.dcs.driver.domain.infrastructure.constant;

public enum CallExamSuccessEnum {
    CALL_SUCCESS(1, "callSuccess"),
    CALL_FAILED(0, "callFailed");

    private int code;
    private String name;

    CallExamSuccessEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CallExamSuccessEnum getByCode(Integer code) {
        if(code == null){
            return CALL_FAILED;
        }
        for (CallExamSuccessEnum callExamSuccessEnum : CallExamSuccessEnum.values()) {
            if (callExamSuccessEnum.code == code) {
                return callExamSuccessEnum;
            }
        }
        return CALL_FAILED;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
