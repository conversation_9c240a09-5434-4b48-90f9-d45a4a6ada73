package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TripUniversityUserDataResponse {
  private int code;
  private String msg;
  private List<TripUniversityUserData> data;
  private boolean success;
}
