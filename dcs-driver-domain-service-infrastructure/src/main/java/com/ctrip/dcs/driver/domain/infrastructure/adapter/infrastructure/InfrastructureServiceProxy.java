package com.ctrip.dcs.driver.domain.infrastructure.adapter.infrastructure;

import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.igt.infrastructureservice.executor.contract.*;


/**
 * <AUTHOR>
 */
@ServiceClient(value = IGTInfrastructureServiceClient.class, format = "json")
public interface InfrastructureServiceProxy {
  
  /**
   * 发送验证码
   * */
  SendMesByPhoneResponseType sendMessageByPhone(SendMesByPhoneRequestType request);
  
  /**
   * 校验验证码
   * */
  CheckMobilePhoneCodeResponseType checkPhoneCode(CheckMobilePhoneCodeRequestType request);
}
