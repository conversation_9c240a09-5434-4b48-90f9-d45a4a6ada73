package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

@Dal("dcstransportdb_w")
public interface DrivLevelDao extends DalRepository<DrivLevelPO> {
    @Sql("select count(*) from driv_level where city_id = ? and month_idx = ?")
    Long countByCityIdAndMonthIdx(long cityId, String monthIdx);

    @Sql("select * from driv_level where driv_id = ? and month_idx = ?")
    DrivLevelPO queryDriverLevel(long driverId, String monthIdx);
}
