package com.ctrip.dcs.driver.domain.infrastructure.model.form;

import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FormLevelInfo {
    private long id;
    /**
     * 司机等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
    private int level;
    private String levelName;
    private String cityIdsStr;
    private BigDecimal driverPointLow;
    private BigDecimal activityLow;
    private BigDecimal safePointLow;
    /**
     * 排名门槛 0无 1有
     */
    private int hasRank;
    private int rankLow;
    /**
     * 状态 0可用 1不可用
     */
    private int status;
    private List<Long> cityList;
}
