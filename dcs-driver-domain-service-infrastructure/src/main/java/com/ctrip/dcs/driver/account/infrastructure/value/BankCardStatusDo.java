package com.ctrip.dcs.driver.account.infrastructure.value;
/*
作者：pl.yang
创建时间：2025/5/9-下午4:25-2025
*/


import com.ctrip.dcs.driver.account.infrastructure.constant.BankCardStatusEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BankCardStatusDo
 * @Package com.ctrip.dcs.driver.account.infrastructure.value
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/9 下午4:25
 */

@Builder
@Getter
public class BankCardStatusDo {


    public static BankCardStatusDo defaultUNKNOW() {
        return BankCardStatusDo.builder()
                .bankCardStatusEnum(BankCardStatusEnum.UNKNOW)
                .build();
    }

    public static BankCardStatusDo ofDefaultUNKNOW(Integer operateType) {

        return BankCardStatusDo.builder()
                .bankCardStatusEnum(BankCardStatusEnum.ofByCodeDefaultUNKNOW(operateType))
                .build();
    }


    //银行卡状态
    private BankCardStatusEnum bankCardStatusEnum;
    private String requestId;

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public static boolean isUndid(Integer operateType) {
        //解绑，绑卡失败
        return BankCardStatusEnum.ofByCode(operateType) == BankCardStatusEnum.UNBIND || BankCardStatusEnum.ofByCode(operateType) == BankCardStatusEnum.BINDFAILED;
    }
    public static boolean isBindOrAuditing(Integer operateType) {
        return BankCardStatusEnum.ofByCode(operateType) == BankCardStatusEnum.BIND || BankCardStatusEnum.ofByCode(operateType) == BankCardStatusEnum.AUDITING;
    }

    public boolean isBind() {
        return bankCardStatusEnum == BankCardStatusEnum.BIND;
    }
    public boolean isAuditing() {
        return bankCardStatusEnum == BankCardStatusEnum.AUDITING;
    }
    public boolean isBindFailed() {
        return bankCardStatusEnum == BankCardStatusEnum.BINDFAILED;
    }

    public boolean isNUllOrUNKNOWorUNBIND() {
        return bankCardStatusEnum == null || bankCardStatusEnum == BankCardStatusEnum.UNKNOW || bankCardStatusEnum == BankCardStatusEnum.UNBIND;
    }

    public void unbind() {
        this.bankCardStatusEnum = BankCardStatusEnum.UNBIND;
    }

    //审核中
    public void bindBankAuditing() {
        this.bankCardStatusEnum = BankCardStatusEnum.AUDITING;
    }

    public void bindBankSuccess() {
        this.bankCardStatusEnum = BankCardStatusEnum.BIND;
    }

    public void bindBankFailed() {
        this.bankCardStatusEnum = BankCardStatusEnum.BINDFAILED;
    }


}
