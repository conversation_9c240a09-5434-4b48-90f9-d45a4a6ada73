package com.ctrip.dcs.driver.account.infrastructure.dal.dao;

import com.ctrip.dcs.driver.account.infrastructure.dal.po.GlobalIdRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

@Dal("dcstransportdb_w")
public interface GlobalIdRecordDao extends DalRepository<GlobalIdRecordPO> {
    @Sql("select * from global_id_record where country_code = ? and phone_number = ? limit 1")
    GlobalIdRecordPO queryByPhone(String countryCode, String phoneNumber);

    @Sql("delete global_id_record where country_code = ? and phone_number = ?")
    int deleteByPhone(String countryCode, String phoneNumber);
}
