package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.dcs.driver.trip.university.infrastructure.constant.DrvStatusEnum;
import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants;
import com.ctrip.dcs.driver.trip.university.infrastructure.utils.CoreInfoUtil;
import lombok.Data;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Data
public class ZhiXLUserDTO {
    //用户编号
    private String id;
    //用户名称
    private String user_name;
    //新的用户编号
    //用于修改用户编号，如果传了 new_code（不为空），则会把用户编号
    //修改为 new_code，请注意，新增的用户或是不用修改用户编号时，不需要传入 new_code
    private String new_code;
    //岗位编号
    private String post_id;
    //部门编号
    private String dept_id;
    //职位编号
    private String position_id;
    //就职状态:0 试用、1 正式、2 停薪留职、3 离职
    private Integer working_status;
    //入职日期
    private Integer entry_date;
    //转正日期
    private Integer qualified_date;
    //离职日期
    private Integer leave_date;
    //性别：0 保密，1 男，2 女
    private Integer sex;
    //手机号
    private String mobile_phone;
    // string 型，就职类型号
    private String working_type;
    // 辖区
    private String precincts;
    // 国家
    private String def_field1;
    // 邮箱
    private String email;
    //第三方唯一id
    private String customer_openid;
    //区分司机境内外标签
    private String tags;

    //语言偏好
    private String language_preference;

    public static  ZhiXLUserDTO buildDTO(TripUniversityDriverDTO driver, String deptId){
        ZhiXLUserDTO userDTO = new ZhiXLUserDTO();
        userDTO.setUser_name(driver.getName());
        userDTO.setCustomer_openid(TripUniversityConstants.CTRIP_UNIVERSITY_USERID+driver.getDrvId());
        userDTO.setId(TripUniversityConstants.CTRIP_UNIVERSITY_USERID+driver.getDrvId());
        userDTO.setDept_id(deptId);
        userDTO.setWorking_status(DrvStatusEnum.ONLINE.getCode());
        userDTO.setMobile_phone(CoreInfoUtil.decrypt(driver.getPhoneNumber(), KeyType.Phone));
        userDTO.setEmail(CoreInfoUtil.decrypt(driver.getEmail(), KeyType.Mail));
        userDTO.setTags(Objects.equals(true, driver.getOversea()) ? TripUniversityConstants.CTRIP_UNIVERSITY_OVERSEAS_TAGS : TripUniversityConstants.CTRIP_UNIVERSITY_DOMESTIC_TAGS);
//        userDTO.setLanguage_preference(driver.getDriverLanguage());
        return userDTO;
    }

    public static  ZhiXLUserDTO buildDTOForDiscard(TripUniversityDriverForDisCardDTO driver){
        ZhiXLUserDTO userDTO = new ZhiXLUserDTO();
        userDTO.setUser_name(driver.getName());
        userDTO.setId(driver.getDrvId());
        userDTO.setMobile_phone(Optional.ofNullable(CoreInfoUtil.decrypt(driver.getPhoneNumber(), KeyType.Phone)).map(s -> s + "0").orElse(null));
        userDTO.setEmail(Optional.ofNullable(CoreInfoUtil.decrypt(driver.getEmail(), KeyType.Mail)).map(s -> "0" + s).orElse(null));
        return userDTO;
    }
}
