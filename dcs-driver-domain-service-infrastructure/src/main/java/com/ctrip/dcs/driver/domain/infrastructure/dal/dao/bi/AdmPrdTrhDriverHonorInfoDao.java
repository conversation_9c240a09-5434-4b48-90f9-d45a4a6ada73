package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.AdmPrdTrhDriverHonorInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("igtbidb_W")
public interface AdmPrdTrhDriverHonorInfoDao extends DalRepository<AdmPrdTrhDriverHonorInfoPO> {

    @Sql("select max(batch_time) from adm_prd_trh_driver_honor_info")
    public Long findMaxBatch();

    @Sql("select * from adm_prd_trh_driver_honor_info where batch_time = ? limit 1")
    public AdmPrdTrhDriverHonorInfoPO findMaxBatchInfo(long batchTime);

    @Sql("select * from adm_prd_trh_driver_honor_info where drv_id = ? order by batch_time desc limit 1")
    public AdmPrdTrhDriverHonorInfoPO findDriverInfo(long driverId);

    @Sql("select * from adm_prd_trh_driver_honor_info where batch_time = ? and city_id = ? and week_order_cnt > 0")
    public List<AdmPrdTrhDriverHonorInfoPO> findCityWeekRankList(long batchTime, long cityId);

    @Sql("select * from adm_prd_trh_driver_honor_info where batch_time = ? and city_id = ? and month_order_cnt > 0")
    public List<AdmPrdTrhDriverHonorInfoPO> findCityMonthRankList(long batchTime, long cityId);
}
