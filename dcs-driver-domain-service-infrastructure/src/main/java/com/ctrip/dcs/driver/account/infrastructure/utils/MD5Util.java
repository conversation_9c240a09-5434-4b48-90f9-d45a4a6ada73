package com.ctrip.dcs.driver.account.infrastructure.utils;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


public class MD5Util {
	/**
	 * md5加密
	 */
	public static String getMD5Digest(String str, String salt) {
		try {
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			byte[] digest = md5.digest((str + salt).getBytes(StandardCharsets.UTF_8));
			return new BigInteger(1, digest).toString(16);
		} catch (NoSuchAlgorithmException e) {
			return null;
		}
	}
}
