package com.ctrip.dcs.driver.trip.university.infrastructure.common.config;

import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;

@Configuration
public class TripUniversityThreadPoolConfig {

  @Autowired
  private TripUniversityQconfig tripUniversityQconfig;

  @Bean("TripUniversityThreadPool")
  public ExecutorService getExecutorService() {
    return getPool("tripUniversityThreadPool", tripUniversityQconfig.getPoolCoreSize(), tripUniversityQconfig.getDriverMaxSize(), tripUniversityQconfig.getPoolWorkQueueSize());
  }

  protected synchronized ExecutorService  getPool(String poolName, int coreSize, int maxSize, int workQueueSize) {
    return ThreadPoolBuilder.pool().setCoreSize(coreSize).setMaxSize(maxSize).setDaemon(false).setKeepAliveSecs(50)
      .setUseTtl(true).setWorkQueue(new ArrayBlockingQueue<>(workQueueSize))
      .setThreadNamePrefix(poolName).setRejectHanlder((r, executor) -> {
        LoggerFactory.getLogger(getClass()).warn("ThreadPool", "Task " + r.toString() + " rejected from " + executor.toString());
        // 超出队列上限在主线程中同步执行
        r.run();
      }).build();
  }
}
