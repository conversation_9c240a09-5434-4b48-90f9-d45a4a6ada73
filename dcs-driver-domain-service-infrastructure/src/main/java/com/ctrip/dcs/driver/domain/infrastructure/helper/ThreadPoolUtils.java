package com.ctrip.dcs.driver.domain.infrastructure.helper;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder.FixedThreadPoolBuilder;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.ctrip.igt.framework.infrastructure.context.ServiceExecuteContext;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.AbstractFuture;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import qunar.tc.qconfig.client.Feature;
import qunar.tc.qconfig.client.TypedConfig;
import qunar.tc.qconfig.client.exception.ResultUnexpectedException;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

public final class ThreadPoolUtils {

  private static final Logger LOGGER = LoggerFactory.getLogger(ThreadPoolUtils.class);
  private static final InitFuture FUTURE = new InitFuture();
  private static Map<String, String> MAPPING = Maps.newHashMap();
  private static Map<String, ExecutorServiceWrapper> CONTAINER = Maps.newHashMap();

  static {
    ConfigurationHandler.loadConfig();
  }

  private ThreadPoolUtils() {

  }

  public static ExecutorService getThreadPool(){
    ExecutorServiceWrapper executorServiceWrapper = getPool();
    if (executorServiceWrapper == null){
      return null;
    }
    if (ServiceExecuteContext.getCurrent() != null){
      String operationName = StringUtils.defaultIfEmpty(ServiceExecuteContext.getCurrent().getOperationName(),"UnknowOperationName");
      Cat.logEvent("Igt.ThreadPool", String.format("%s -> %s",operationName, executorServiceWrapper.getPoolName()));
    }
    return executorServiceWrapper.getExecutorService();
  }

  public static ExecutorServiceWrapper getPool() {
    waitFirstLoad();
    if (Objects.nonNull(ServiceExecuteContext.getCurrent())) {
      String operationName = ServiceExecuteContext.getCurrent().getOperationName();
      ExecutorServiceWrapper executorService = getPoolByName(MAPPING.get(ConfigurationHandler.format(operationName)));
      if (Objects.nonNull(executorService)) {
        return executorService;
      }
      String serviceName = ServiceExecuteContext.getCurrent().getServiceName();
      executorService = getPoolByName(MAPPING.get(ConfigurationHandler.format(serviceName)));
      if (Objects.nonNull(executorService)) {
        return executorService;
      }
    }
    return CONTAINER.get(CommonSetting.DEFAULT_POOL_NAME);
  }

  public static ExecutorServiceWrapper getPoolByName(String poolName) {
    waitFirstLoad();
    if (StringUtils.isEmpty(poolName)) {
      return null;
    }
    return CONTAINER.get(poolName);
  }

  private static void waitFirstLoad() {
    if (MapUtils.isNotEmpty(CONTAINER))return;
    try {
      FUTURE.get();
    } catch (ExecutionException e) {
      Throwable cause = e.getCause();
      if (cause instanceof ResultUnexpectedException) {
        throw (ResultUnexpectedException) cause;
      } else {
        throw new ResultUnexpectedException(e.getMessage());
      }
    } catch (Throwable e) {
      throw new ResultUnexpectedException(e.getMessage());
    }
  }

  private static final class ConfigurationHandler {

    private static Map<String, ConfigurationItem> CUR_CONFIG;
    private static Map<String, ConfigurationItem> OLD_CONFIG = Maps.newHashMap();

    public static void loadConfig() {
      TypedConfig<String> configuration = TypedConfig.get(CommonSetting.CONFIG_FILE_NAME,
          Feature.create().setFailOnNotExists(false).build(), TypedConfig.STRING_PARSER);
      configuration.addListener(conf -> {
        LOGGER.info("threadPool initialized...", conf);
        Configuration config = Optional.ofNullable(JacksonUtil.deserialize(conf, Configuration.class))
            .orElse(new Configuration());

        CUR_CONFIG = Optional.ofNullable(config.threadPoolConfigItems).orElse(Lists.newArrayList()).stream()
            .collect(Collectors.toMap(ConfigurationItem::getPoolName, c -> c));
        init();
      });
    }

    private static void init() {
      Map<String, String> new4mapping = Maps.newHashMap();
      Map<String, ExecutorServiceWrapper> new4container = Maps.newHashMap();

      Map<String, ExecutorServiceWrapper> needClosedPool = Maps.newHashMap();

      CUR_CONFIG.forEach((poolName, configurationItem) -> {
        ExecutorServiceWrapper pool = null;
        if (OLD_CONFIG.containsKey(poolName)) {
          if (checkEqual(OLD_CONFIG.get(poolName), configurationItem)) {
            pool = CONTAINER.get(poolName);
          } else {
            needClosedPool.put(poolName, CONTAINER.get(poolName));
          }
        }
        new4container.put(configurationItem.getPoolName(), pool != null ? pool : buildThreadPool(configurationItem));
        if (StringUtils.isNotEmpty(configurationItem.getServiceName())) {
          new4mapping.put(format(configurationItem.getServiceName()), configurationItem.getPoolName());
        }
        if (CollectionUtils.isNotEmpty(configurationItem.getOperationNames())) {
          configurationItem.getOperationNames()
              .forEach(operationName -> new4mapping.put(format(operationName), configurationItem.getPoolName()));
        }
      });
      checkNotInCurrentConfigThreadPool(needClosedPool);

      MAPPING = new4mapping;
      CONTAINER = new4container;
      OLD_CONFIG = CUR_CONFIG;

      if (MapUtils.isNotEmpty(needClosedPool)) {
        needClosedPool.forEach((key, pool) -> close(pool));
      }

      if (!FUTURE.isDone()) {
        FUTURE.set(true);
      }
      LOGGER.info("threadPool initialized done", String.format("%s%s", CONTAINER.keySet(), " is completed"));
    }

    private static ExecutorServiceWrapper buildThreadPool(ConfigurationItem configurationItem) {
      ExecutorService executorService = new FixedThreadPoolBuilder().setPoolSize(configurationItem.getPoolSize())
          .setQueueSize(configurationItem.getQueueSize()).setThreadNamePrefix(configurationItem.getPoolName())
          .setRejectHanlder(buildRejectedExecutionHandler(configurationItem.getRejectHandler(),
            configurationItem.getPoolName()))
          .setUseTtl(true).build();
      return ExecutorServiceWrapper.newBuilder().withPoolName(configurationItem.getPoolName())
          .withExecutorService(executorService).build();
    }

    private static RejectedPolicy buildRejectedExecutionHandler(String config, String poolName) {
      if ("CallerRunsPolicy".equalsIgnoreCase(config)) {
        return new CallerRunsPolicy(poolName);
      } else if ("AbortPolicy".equalsIgnoreCase(config)) {
        return new AbortPolicy(poolName);
      }
      return null;
    }

    private static boolean checkEqual(ConfigurationItem old, ConfigurationItem cur) {
      return old.equals(cur);
    }

    private static void checkNotInCurrentConfigThreadPool(Map<String, ExecutorServiceWrapper> needClosedPool) {
      Set<String> newPoolNames = CUR_CONFIG.keySet();
      OLD_CONFIG.keySet().forEach(poolName -> {
        if (!newPoolNames.contains(poolName)) {
          needClosedPool.put(poolName, CONTAINER.get(poolName));
        }
      });
    }

    private static String format(String name) {
      return StringUtils.defaultIfEmpty(name, StringUtils.EMPTY).toLowerCase();
    }

    private static void close(ExecutorServiceWrapper executorServiceWrapper) {
      try {
        com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolUtils.gracefulShutdown(executorServiceWrapper.getExecutorService(), 10, TimeUnit.SECONDS);
      } catch (Exception e) {
        LOGGER.error(e);
      }
    }
  }

  public static final class ExecutorServiceWrapper {
    private String poolName;
    private ExecutorService executorService;

    private ExecutorServiceWrapper(Builder builder) {
      poolName = builder.poolName;
      executorService = builder.executorService;
    }

    public static Builder newBuilder() {
      return new Builder();
    }

    public String getPoolName() {
      return poolName;
    }

    public ExecutorService getExecutorService() {
      return executorService;
    }

    public static final class Builder {
      private String poolName;
      private ExecutorService executorService;

      private Builder() {}

      public Builder withPoolName(String val) {
        poolName = val;
        return this;
      }

      public Builder withExecutorService(ExecutorService val) {
        executorService = val;
        return this;
      }

      public ExecutorServiceWrapper build() {
        return new ExecutorServiceWrapper(this);
      }
    }
  }

  static abstract class RejectedPolicy implements RejectedExecutionHandler {

    private String poolName;

    RejectedPolicy(String poolName) {
      this.poolName = poolName;
    }

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
      LOGGER.warn("rejectedExecution", String.format("poolName:%s,detail:%s", this.poolName, executor.toString()));
      this.rejectedHandler(r, executor);
    }

    protected abstract void rejectedHandler(Runnable r, ThreadPoolExecutor executor);
  }

  static class CallerRunsPolicy extends RejectedPolicy {

    CallerRunsPolicy(String poolName) {
      super(poolName);
    }

    @Override
    protected void rejectedHandler(Runnable r, ThreadPoolExecutor executor) {
      LOGGER.warn("rejectedWaring from CallerRunsPolicy",
          String.format("poolName:%s,this task runs directly in the calling thread", super.poolName));
      r.run();
    }
  }

  static class AbortPolicy extends RejectedPolicy {

    AbortPolicy(String poolName) {
      super(poolName);
    }

    @Override
    protected void rejectedHandler(Runnable r, ThreadPoolExecutor executor) {
      LOGGER.warn("rejectedWaring from AbortPolicy",
          String.format("poolName:%s,this task will be discard", super.poolName));
    }
  }

  public static class InitFuture extends AbstractFuture<Boolean> {
    public void set(boolean value) {
      super.set(value);
    }
  }

  private static final class Configuration {
    private List<ConfigurationItem> threadPoolConfigItems;
  }

  private final class ConfigurationItem {
    private String serviceName;
    private String poolName;
    private int poolSize;
    private int queueSize;
    private Set<String> operationNames;
    private String rejectHandler;

    public String getPoolName() {
      return this.poolName;
    }

    public String getServiceName() {
      return StringUtils.defaultString(serviceName, "").toLowerCase();
    }

    public Set<String> getOperationNames() {
      return CollectionUtils.isEmpty(this.operationNames) ? Sets.newHashSet() : this.operationNames;
    }

    @Override
    public int hashCode() {
      return Objects.hash(this.getPoolSize(), this.getQueueSize(), this.getServiceName(), this.getOperationNames(),
        this.getRejectHandler());
    }

    @Override
    public boolean equals(Object obj) {
  
      if (obj instanceof ConfigurationItem) {
        ConfigurationItem var = (ConfigurationItem)obj;
        if (var.getOperationNames() == null) {
          return false;
        }
    
        boolean compareStringSet = compareStringSet(this.getOperationNames(), var.getOperationNames());
    
        return this.getPoolSize() == var.getPoolSize() && this.getQueueSize() == var.getQueueSize() && this.getServiceName().equalsIgnoreCase(
          var.getServiceName())
          && compareStringSet && this.getRejectHandler().equalsIgnoreCase(var.getRejectHandler());
      }
  
      return false;
    }
  
    public void setServiceName(String serviceName) {
      this.serviceName = serviceName;
    }
  
    public void setPoolName(String poolName) {
      this.poolName = poolName;
    }
  
    public int getPoolSize() {
      return poolSize;
    }
  
    public void setPoolSize(int poolSize) {
      this.poolSize = poolSize;
    }
  
    public int getQueueSize() {
      return queueSize;
    }
  
    public void setQueueSize(int queueSize) {
      this.queueSize = queueSize;
    }
  
    public void setOperationNames(Set<String> operationNames) {
      this.operationNames = operationNames;
    }
  
    public String getRejectHandler() {
      return rejectHandler;
    }
  
    public void setRejectHandler(String rejectHandler) {
      this.rejectHandler = rejectHandler;
    }
  }

  private final class CommonSetting {
    public static final String CONFIG_FILE_NAME = "ThreadPoolConfiguration";
    public static final String DEFAULT_POOL_NAME = "defaultPoolName";

  }
  
  /**
   *
   * */
  private boolean compareStringSet(Set<String> set1, Set<String> set2) {
    if (set1 == null || set2 == null) {
      return false;
    }
    if (set1.size() != set2.size()) {
      return false;
    }
    return set1.containsAll(set2);
  }
  
}
