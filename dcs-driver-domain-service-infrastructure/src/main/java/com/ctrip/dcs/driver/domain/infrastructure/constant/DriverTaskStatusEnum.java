package com.ctrip.dcs.driver.domain.infrastructure.constant;

import com.ctrip.dcs.driver.domain.task.DriverTaskStatus;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Getter
/**
 * 司机任务枚举
 * 0：默认  1：初始化（生成）；2：已完成；3：已过期；4、取消（系统取消、该任务结束）
 * */
public enum DriverTaskStatusEnum {
    DEFAULT(0),
    CREATE(1),
    FINISHED(2),
    EXPIRE(3),
    CANCEL(4);

    private int value;

    DriverTaskStatusEnum(int value) {
        this.value = value;
    }

    public static DriverTaskStatusEnum ofValue(int value) {
        List<DriverTaskStatusEnum> list = Arrays.stream(values())
                .filter(r -> value == r.value)
                .collect(Collectors.toList());
        return CollectionUtils.isEmpty(list) ? DriverTaskStatusEnum.DEFAULT : list.get(0);
    }

    public static DriverTaskStatusEnum ofValue(DriverTaskStatus value) {
        switch (value){
            case CREATE:
                return DriverTaskStatusEnum.CREATE;
            case FINISHED:
                return DriverTaskStatusEnum.FINISHED;
            case EXPIRE:
                return DriverTaskStatusEnum.EXPIRE;
            case CANCEL:
                return DriverTaskStatusEnum.CANCEL;
            case DEFAULT:
            default:
                return DriverTaskStatusEnum.DEFAULT;

        }
    }

    public boolean isActive() {
        return CREATE == this;
    }

}
