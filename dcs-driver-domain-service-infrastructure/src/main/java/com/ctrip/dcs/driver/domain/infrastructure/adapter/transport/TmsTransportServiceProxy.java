package com.ctrip.dcs.driver.domain.infrastructure.adapter.transport;

import com.ctrip.dcs.tms.transport.api.TmsTransportServiceClient;
import com.ctrip.dcs.tms.transport.api.model.*;
import com.ctrip.dcs.tms.transport.api.regulation.QueryHistoryDrvDataRequestType;
import com.ctrip.dcs.tms.transport.api.regulation.QueryHistoryDrvDataResponseType;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOARequestType;
import com.ctrip.dcs.tms.transport.api.resource.driver.QueryDriver4BaseSOAResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = TmsTransportServiceClient.class, format = "json")
public interface TmsTransportServiceProxy {

    /**
     * *查询司机列表 FAST
     */
    DriverInfoSOAResponseType queryDriver(DriverInfoSOARequestType request);

    /**
     * 批量查询司机
     */
    QueryDriver4BaseSOAResponseType queryDriver4Base(QueryDriver4BaseSOARequestType request);

    /**
     * 查询老司机
     */
    QueryHistoryDrvDataResponseType queryHistoryDrvData(QueryHistoryDrvDataRequestType request);

    /**
     * 查询单个司机详情
     */
    QueryDrvDetailSOAResponseType queryDrvDetail(QueryDrvDetailSOARequestType request);

    QueryDrvCodeByDrvIdSOAResponseType queryDrvCodeByDrvId(QueryDrvCodeByDrvIdSOARequestType request);
}
