package com.ctrip.dcs.driver.domain.infrastructure.condition.freeze;

import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Getter
@Setter
public class DriverApplyFreezeRecordCondition {
    private Long driverId;
    private String applyFreezeReason;
    private Integer applyFreezeHours;
    private Timestamp applyFreezeTime;
    private String applyFreezeResult;
    private String applyFreezeResultDesc;
    private String deviceId;
}
