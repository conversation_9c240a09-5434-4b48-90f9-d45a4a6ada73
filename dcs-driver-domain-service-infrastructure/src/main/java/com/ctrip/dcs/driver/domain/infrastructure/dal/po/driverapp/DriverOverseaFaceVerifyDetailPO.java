package com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Getter
@Setter
@Entity
@Database(name = "dcsdriverappdb")
@Table(name = "driver_oversea_face_verify_detail")
public class DriverOverseaFaceVerifyDetailPO implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 用户id
     */
    @Column(name = "driver_id")
    @Type(value = Types.INTEGER)
    private Integer driverId;

    /**
     * 用户uid
     */
    @Column(name = "driver_uid")
    @Type(value = Types.VARCHAR)
    private String driverUid;

    /**
     * 请求认证传给腾讯的唯一标识
     */
    @Column(name = "verify_unique_no")
    @Type(value = Types.VARCHAR)
    private String verifyUniqueNo;

    /**
     * 认证返回的整体结果报文
     */
    @Column(name = "verify_result_detail")
    @Type(value = Types.VARCHAR)
    private String verifyResultDetail;

    /**
     * 认证返回结果存储到携程图片库的图片地址
     */
    @Column(name = "result_picture_url")
    @Type(value = Types.VARCHAR)
    private String resultPictureUrl;

    /**
     * 司机udl
     */
    @Column(name = "provider_data_location")
    @Type(value = Types.VARCHAR)
    private String providerDataLocation;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

}
