package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverDeviceInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;


@Dal("dcstransportdb_w")
public interface DriverDeviceInfoDao extends DalRepository<DriverDeviceInfoPO> {

	@Sql("select * from driver_device_info where driver_id = ? order by datachange_lasttime desc limit 1")
	DriverDeviceInfoPO findOne(Long driverId);

	@Sql("select * from driver_device_info where uid = ? order by datachange_lasttime desc limit 1")
	DriverDeviceInfoPO findOne(String uid);

	@Sql("update driver_device_info set provider_data_location = ? where driver_id = ?")
	void updateUdlByDriverId(String udl, String driverId);
}
