package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DriverPointTotalInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Dal("dcstransportdb_w")
public interface DriverPointTotalInfoDao extends DalRepository<DriverPointTotalInfoPO> {
    @Sql("select * from driver_point_total_info where city_id = ? and driv_level = ?")
    List<DriverPointTotalInfoPO> queryByCityIdAndLevel(long cityId, int level);

    @Sql("select count(*) from driver_point_total_info where city_id = ? and driv_level = ?")
    Long countByCityIdAndLevel(long cityId, int level);

    @Sql("select count(*) from driver_point_total_info where city_id = ?")
    Long countByCityId(long cityId);

    @Sql("delete from driver_point_total_info where id = ?")
    void deleteDriverPointTotal(long id);

    @Sql("update driver_point_total_info set city_id =?, driv_level =?,order_info_point=?,reward_info_point=?, safe_point =?, driver_status=?,datachange_lasttime=?  where id = ?")
    void updateDriverPointTotal(String cityId, Integer drivLevel, BigDecimal orderInfoPoint, BigDecimal rewardInfoPoint, BigDecimal safePoint, Integer driverStatus, Timestamp datachangeLasttime, Long id);

    @Sql("INSERT INTO driver_point_total_info VALUES (?,?,?,?,?,?,?,?,?,?)")
    void insertDriverPointTotal(Long id, Integer drivLevel, String driverId, String cityId, BigDecimal orderInfoPoint, BigDecimal rewardInfoPoint, Integer driverStatus, Timestamp createTime, Timestamp datachangeLasttime, BigDecimal safePoint);

}
