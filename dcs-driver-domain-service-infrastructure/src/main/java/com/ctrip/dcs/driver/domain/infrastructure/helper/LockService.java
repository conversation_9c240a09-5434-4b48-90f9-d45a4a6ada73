package com.ctrip.dcs.driver.domain.infrastructure.helper;

import com.ctrip.arch.distlock.DLock;
import com.ctrip.arch.distlock.DistributedLockService;
import com.ctrip.arch.distlock.exception.DistlockRejectedException;
import com.ctrip.igt.framework.common.exception.BizException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

@Component
public class LockService {

  @Resource
  private DistributedLockService distributedLockService;

  public <T> T executeInLock(String lockKey, long mills, Callable<T> callable) {
    return getLock(lockKey).execute(mills, callable);
  }

  private LockExecutor getLock(String lockKey) {
    DLock lock = distributedLockService.getLock(lockKey);
    return new LockExecutor(lock);
  }

  private static final class LockExecutor {

    private DLock lock;

    private LockExecutor(DLock lock) {
      this.lock = lock;
    }

    public <T> T execute(long mills, Callable<T> callable) {
      boolean locked = false;
      try {
        locked = lock.tryLock(mills, TimeUnit.MILLISECONDS);
        if(locked){
          return callable.call();
        }else{
          throw new BizException("500", "lock_failed");
        }
      } catch (RuntimeException e) {
        throw e;
      } catch (DistlockRejectedException e) {
        throw new BizException("500", "lock_exception");
      } catch (Exception e) {
        throw new BizException(e);
      } finally {
        if (locked) {
          lock.unlock();
        }
      }
    }
  }
}
