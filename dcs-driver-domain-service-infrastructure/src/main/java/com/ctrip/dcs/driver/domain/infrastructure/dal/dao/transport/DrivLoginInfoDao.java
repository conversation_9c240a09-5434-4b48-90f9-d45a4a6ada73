package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivLoginInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

/**
 * <AUTHOR>
 * @date 2020-10-13
 */
@<PERSON>("dcstransportdb_w")
public interface DrivLoginInfoDao extends DalRepository<DrivLoginInfoPO> {

	@Sql("select * from driv_login_info where driv_id = ?  limit 1")
	DrivLoginInfoPO findOne(Long driverId);
	@Sql("update driv_login_info set provider_data_location = ? where driv_id = ?")
	void updateUdlByDriverId(String udl, String driverId);
}
