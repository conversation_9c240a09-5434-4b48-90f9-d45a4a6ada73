package com.ctrip.dcs.driver.domain.infrastructure.dto.condition;

import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class UseRightsCondition {
    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 权益类型 1福利金 2改派 3城市王者 4初级投诉豁免卡 5高级投诉豁免卡 6每日提现
     */
    private Integer rightsType;
    /**
     * 用户订单号
     */
    private String rightsName;
    /**
     * 用户订单号
     */
    private String userOrderId;

    /**
     * 采购单号
     */
    private String purchaseOrderId;

    /**
     * 司机单号
     */
    private String supplyOrderId;

    /**
     * 判罚单号
     */
    private String punishOrderId;

    /**
     * 金额(福利金，每日提现)
     */
    private BigDecimal money;

    /**
     * 预计用车时间
     */
    private String sysExpectBookTime;
}
