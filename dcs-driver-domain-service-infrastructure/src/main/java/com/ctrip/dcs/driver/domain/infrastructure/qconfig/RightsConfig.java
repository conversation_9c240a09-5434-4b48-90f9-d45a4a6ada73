package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsChoiceLevelInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsIntroduceInfo;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class RightsConfig {

    private List<FormRightsInfo> rightsInfoList;

    public Map<Long, List<FormRightsInfo>> cityRightsMap = new HashMap<>();
    public Map<Integer, List<Long>> levelRightsMap = new HashMap<>();
    private Map<Long, FormRightsInfo> rightsIdMap = new HashMap<>();

    @QConfig("rights_config.json")
    public void rightsConfigChange(String value) {
        this.rightsInfoList = JacksonUtil.parseArray(value, FormRightsInfo.class);

        Map<Long, FormRightsInfo> newRightsIdmap = new HashMap<>();
        Map<Integer, List<Long>> newLevelRightsMap = new HashMap<>();
        Map<Long, List<FormRightsInfo>> newCityMap = new HashMap<>();
        rightsInfoList.stream().filter(o -> o.getStatus() == 0).forEach(rightsInfo -> {
            newRightsIdmap.put(rightsInfo.getId(), rightsInfo);

            String cityIdsStr = rightsInfo.getCityIdsStr();
            if (StringUtils.isNotEmpty(cityIdsStr)) {
                rightsInfo.setCityList(ShoppingCollectionUtils.toList(cityIdsStr.split(","), (ShoppingFuncUtils.Func1<String, Long>) Long::parseLong).stream().distinct().collect(Collectors.toList()));
            }
            rightsInfo.getCityList().stream().forEach(city -> {
                if (!newCityMap.containsKey(city)) {
                    newCityMap.put(city, new ArrayList<>());
                }
                newCityMap.get(city).add(rightsInfo);
            });

            if (CollectionUtils.isNotEmpty(rightsInfo.getLevelList())) {
                rightsInfo.getLevelList().stream().forEach(level->{
                    if (!newLevelRightsMap.containsKey(level.getLevel())) {
                        newLevelRightsMap.put(level.getLevel(), new ArrayList<>());
                    }
                    newLevelRightsMap.get(level.getLevel()).add(rightsInfo.getId());
                });
            }
        });

        this.cityRightsMap=newCityMap;
        this.rightsIdMap=newRightsIdmap;
        this.levelRightsMap=newLevelRightsMap;
    }


    public List<FormRightsInfo> getRightsInfo(Long cityId, int level) {
        List<FormRightsInfo> forms = new ArrayList<>();
        List<FormRightsInfo> formRightsInfos = cityRightsMap.get(cityId);
        if (CollectionUtils.isEmpty(formRightsInfos)) {
            return forms;
        }

        formRightsInfos.stream().forEach(rightsInfo -> {
            rightsInfo.getLevelList().stream().forEach(levelInfo -> {
                if (levelInfo.getLevel() == level) {
                    FormRightsInfo form = new FormRightsInfo();
                    form.setId(rightsInfo.getId());
                    form.setRightsType(rightsInfo.getRightsType());
                    form.setRightsName(rightsInfo.getRightsName());
                    form.setRightsDesc(rightsInfo.getRightsDesc());
                    form.setUseLimit(levelInfo.getIsLimit() == 0 ? 0 : levelInfo.getUseLimit());
                    form.setExtend(levelInfo.getExtend());

                    forms.add(form);
                }
            });
        });

        return forms;
    }

    public FormRightsInfo getRightsInfo(Long cityId, int level, int rightsType) {
        List<FormRightsInfo> formRightsInfos = cityRightsMap.get(cityId);
        if (CollectionUtils.isEmpty(formRightsInfos)) {
            return null;
        }

        //查询当前城市有没有该权益
        FormRightsInfo formRightsInfo = formRightsInfos.stream().filter(rightsInfo -> rightsInfo.getRightsType() == rightsType).findFirst().orElse(null);
        if (Objects.isNull(formRightsInfo) || CollectionUtils.isEmpty(formRightsInfo.getLevelList())) {
            return null;
        }

        //查询权益有没有该等级
        FormRightsChoiceLevelInfo formRightsChoiceLevelInfo = formRightsInfo.getLevelList().stream().filter(levelInfo -> levelInfo.getLevel() == level).findFirst().orElse(null);
        if(Objects.isNull(formRightsChoiceLevelInfo)){
            return null;
        }

        FormRightsInfo form = new FormRightsInfo();
        form.setId(formRightsInfo.getId());
        form.setRightsType(formRightsInfo.getRightsType());
        form.setRightsName(formRightsInfo.getRightsName());
        form.setRightsDesc(formRightsInfo.getRightsDesc());
        form.setUseLimit(formRightsChoiceLevelInfo.getIsLimit() == 0 ? 0 : formRightsChoiceLevelInfo.getUseLimit());
        form.setExtend(formRightsChoiceLevelInfo.getExtend());

        return form;
    }

    public List<FormRightsIntroduceInfo> getRightsIntroduces(Long cityId, int rightsType){
        List<FormRightsInfo> formRightsInfos = cityRightsMap.get(cityId);
        if (CollectionUtils.isEmpty(formRightsInfos)) {
            return Collections.emptyList();
        }

        FormRightsInfo formRightsInfo = formRightsInfos.stream().filter(o -> o.getRightsType() == rightsType).findFirst().orElse(null);
        if(Objects.isNull(formRightsInfo)){
            return Collections.emptyList();
        }

        return formRightsInfo.getRightsIntroduceList();
    }
}