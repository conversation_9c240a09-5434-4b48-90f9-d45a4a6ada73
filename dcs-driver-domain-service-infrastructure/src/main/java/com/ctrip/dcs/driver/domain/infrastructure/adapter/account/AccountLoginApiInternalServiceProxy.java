package com.ctrip.dcs.driver.domain.infrastructure.adapter.account;


import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.soa.platform.accountapiserverinternal.v1.*;

@ServiceClient(value = AccountLoginApiInternalServiceClient.class, format = "json")
public interface AccountLoginApiInternalServiceProxy {

    RegisterByMobilePhoneResponseType registerByMobilePhone(RegisterByMobilePhoneRequestType request);

    RegisterByEmailResponseType registerByEmail(RegisterByEmailRequestType request);
}
