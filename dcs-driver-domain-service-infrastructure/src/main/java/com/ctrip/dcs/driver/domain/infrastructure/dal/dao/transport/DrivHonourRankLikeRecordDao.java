package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourRankLikeRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

@Dal("dcstransportdb_w")
public interface DrivHonourRankLikeRecordDao extends DalRepository<DrivHonourRankLikeRecordPO> {

    @Sql("select * from driv_honour_rank_like_record where rank_refs = ? and driver_id = ? limit 1")
    DrivHonourRankLikeRecordPO findOne(int rankRefs, long driverId);

}
