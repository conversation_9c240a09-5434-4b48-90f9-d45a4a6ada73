package com.ctrip.dcs.driver.account.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.*;
import java.sql.Types;
import java.time.LocalDateTime;


@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "account_change_log")
@Data
public class AccountChangeLogPO implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 携程uid
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 类型
     */
    @Column(name = "type")
    @Type(value = Types.VARCHAR)
    private String type;

    /**
     * 变更之前
     */
    @Column(name = "change_before")
    @Type(value = Types.LONGVARCHAR)
    private String changeBefore;

    /**
     * 变更之后
     */
    @Column(name = "change_after")
    @Type(value = Types.LONGVARCHAR)
    private String changeAfter;

    /**
     * 变更来源
     */
    @Column(name = "operator")
    @Type(value = Types.VARCHAR)
    private String operator;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private LocalDateTime datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private LocalDateTime datachangeLasttime;

    /**
     * udl
     */
    @Column(name = "provider_data_location")
    @Type(value = Types.VARCHAR)
    private String providerDataLocation;

}
