package com.ctrip.dcs.driver.account.infrastructure.qconfig;

import com.ctrip.dcs.driver.account.infrastructure.value.emil.EmailConfigInfo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

@Getter
@Setter
@Component
public class EmailNotifyConfig {

    private EmailConfigInfo registerAccountInfoConflictConfig;

    private EmailConfigInfo syncGuideAccountResultConfig;

    @QConfig("email_notify.json")
    private void onChange(EmailNotifyConfig config) {//监听模式
        this.registerAccountInfoConflictConfig = config.getRegisterAccountInfoConflictConfig();
        this.syncGuideAccountResultConfig = config.getSyncGuideAccountResultConfig();
    }
}