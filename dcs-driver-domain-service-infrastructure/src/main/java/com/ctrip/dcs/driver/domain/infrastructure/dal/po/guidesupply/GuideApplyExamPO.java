package com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-10-25
 */
@Getter
@Setter
@Entity
@Database(name = "dcsguidesupplydb")
@Table(name = "guide_apply_exam")
public class GuideApplyExamPO implements DalPojo {
  /**
   * id
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * 司导id
   */
  @Column(name = "guide_id")
  @Type(value = Types.BIGINT)
  private Long guideId;

  /**
   * 司导职行力第三方id
   */
  @Column(name = "exam_account_id")
  @Type(value = Types.VARCHAR)
  private String examAccountId;

  /**
   * 司导姓名
   */
  @Column(name = "guide_name")
  @Type(value = Types.VARCHAR)
  private String guideName;

  /**
   * 登录账号，手机号/邮箱
   */
  @Column(name = "account")
  @Type(value = Types.VARCHAR)
  private String account;

  /**
   * 报考科目
   */
  @Column(name = "apply_subject")
  @Type(value = Types.VARCHAR)
  private String applySubject;

  /**
   * 报考时间
   */
  @Column(name = "apply_time")
  @Type(value = Types.TIMESTAMP)
  private Timestamp applyTime;

  /**
   * 时区
   */
  @Column(name = "time_zone")
  @Type(value = Types.DECIMAL)
  private BigDecimal timeZone;

  /**
   * 考试名称
   */
  @Column(name = "subject_name")
  @Type(value = Types.VARCHAR)
  private String subjectName;

  /**
   * 报考结果 0-未成功 1-成功
   */
  @Column(name = "apply_result")
  @Type(value = Types.TINYINT)
  private Integer applyResult;

  /**
   * 考试结果 0-未通过 1-通过
   */
  @Column(name = "exam_is_passed")
  @Type(value = Types.TINYINT)
  private Integer examIsPassed;

  /**
   * 调用职行力报考接口是否成功 0-失败 1-成功
   */
  @Column(name = "call_exam_success")
  @Type(value = Types.TINYINT)
  private Integer callExamSuccess;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeCreatetime;

  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLasttime;
}
