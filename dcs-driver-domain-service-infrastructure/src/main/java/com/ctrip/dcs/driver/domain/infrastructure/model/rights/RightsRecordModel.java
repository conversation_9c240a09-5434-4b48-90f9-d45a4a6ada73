package com.ctrip.dcs.driver.domain.infrastructure.model.rights;

import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class RightsRecordModel {
    /**
     * 权益下发id
     */
    private Long rightsIssueId;

    /**
     * 司机ID
     */
    private Long drivId;

    /**
     * 权益类型 1福利金 2改派 3城市王者 4初级投诉豁免卡 5高级投诉豁免卡 6每日提现
     */
    private Integer rightsType;

    /**
     * 权益名称
     */
    private String rightsName;

    /**
     * 司机使用等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
    private Integer useLevel;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 用户订单号
     */
    private String userOrderId;

    /**
     * 采购单号 只有判罚权益有
     */
    private String purchaseOrderId;

    /**
     * 司机单号
     */
    private String supplyOrderId;

    /**
     * 判罚单号
     */
    private String punishOrderId;

    /**
     * 金额(福利金，每日提现)
     */
    private String money;

    /**
     * 权益使用时间
     */
    private LocalDateTime rightsUseTime;

}
