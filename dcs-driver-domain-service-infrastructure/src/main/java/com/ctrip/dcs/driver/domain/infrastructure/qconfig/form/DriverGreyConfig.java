package com.ctrip.dcs.driver.domain.infrastructure.qconfig.form;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 灰度配置
 */
@Component
public class DriverGreyConfig {

  private static final Logger LOGGER = LoggerFactory.getLogger(DriverGreyConfig.class);

  /**
   * IM新增push推送灰度key
   */
  public static final String IM_PUSH_GREY_KEY = "im_push";
  public static final String VOIP_GRAY_KEY = "voip";
  public static final String SAFE_POINT = "safe_point";
  public static final String LEVEL_WITH_SAFE_POINT = "level_with_safe_point";
  public static final String DRIVER_POINT_VIP_ORDER = "driver_point_vip_order";


  private static final Set<String> GREY_KEY_SET;

  static {
    GREY_KEY_SET = new HashSet<>();
    GREY_KEY_SET.add(IM_PUSH_GREY_KEY);
    GREY_KEY_SET.add(VOIP_GRAY_KEY);
    GREY_KEY_SET.add(SAFE_POINT);
    GREY_KEY_SET.add(LEVEL_WITH_SAFE_POINT);
    GREY_KEY_SET.add(DRIVER_POINT_VIP_ORDER);
  }

  private Map<String, BizGreyConfigEntity> config;

  @QConfig("driver_biz_grey.json")
  public void onConfigChange(String conf) {
    try {
      if (StringUtils.isNotEmpty(conf)) {
        List<BizGreyConfigEntity> configEntity = JacksonUtil.parseArray(conf, BizGreyConfigEntity.class);
        if (configEntity != null) {
          config = configEntity.stream().filter(e -> GREY_KEY_SET.contains(e.getKey())).collect(
              Collectors.toMap(BizGreyConfigEntity::getKey, Function.identity()));
        }
      }
    } catch (Exception e) {
      LOGGER.error(e);
    }
  }

  /**
   * 是否灰度逻辑（只支持RN版本）
   * */
  public boolean isGrey(String greyKey, long cityId, long countryId, BigDecimal rnVersion) {
    boolean isGrey = false;
    if (config.containsKey(greyKey)) {
      isGrey = true;
      BizGreyConfigEntity greyConfig = config.get(greyKey);
      if (StringUtils.isNotEmpty(greyConfig.getCityIds())) {
        isGrey = Arrays.stream(greyConfig.getCityIds().split(",")).map(Long::parseLong)
            .collect(Collectors.toSet()).contains(cityId);
      }

      //城市已灰度，不判断国家
      if(!isGrey){
        if (StringUtils.isNotEmpty(greyConfig.getCountryIds())) {
          isGrey = Arrays.stream(greyConfig.getCountryIds().split(",")).map(Long::parseLong)
                  .collect(Collectors.toSet()).contains(countryId);
        }
      }

      //RN版本判断， 0为不限制
      if (isGrey) {
        if (StringUtils.isNotEmpty(greyConfig.getRnVer()) && !"0".equals(greyConfig.getRnVer())) {
          BigDecimal formRnVersion = new BigDecimal(ObjectUtils.defaultIfNull(greyConfig.getRnVer(), "0"));
          isGrey = formRnVersion.compareTo(BigDecimal.ZERO) == 0 || rnVersion.compareTo(formRnVersion) >= 0;
        }
      }
    }
    return isGrey;
  }

  @Data
  public static class BizGreyConfigEntity {
    private String key;
    private String driverIds;
    private String cityIds;
    private String countryIds;
    private String appVer;
    private String rnVer;
  }

  public Map<String, BizGreyConfigEntity> getConfig() {
    return config;
  }

}
