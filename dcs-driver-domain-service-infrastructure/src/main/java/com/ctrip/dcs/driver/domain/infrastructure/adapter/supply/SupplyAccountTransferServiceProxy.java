package com.ctrip.dcs.driver.domain.infrastructure.adapter.supply;

import com.ctrip.dcs.supply.driver.dto.*;
import com.ctrip.dcs.supply.transfer.api.SupplyAccountTransferServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = SupplyAccountTransferServiceClient.class, format = "json")
public interface SupplyAccountTransferServiceProxy {

  /**
   * 查询司机提现记录
   * */
  QueryDriverBalanceRecordResponseType queryDriverBalanceRecord(QueryDriverBalanceRecordRequestType request);

  /**
   * 查询司机可提现余额
   * */
  QueryDriverWithdrawBalanceResponseType queryDriverWithdrawBalance(QueryDriverWithdrawBalanceRequestType request);

  /**
   * 	查询司机银行卡列表
   * */
  QueryDriverCardListResponseType queryDriverCardList(QueryDriverCardListRequestType request);

  /**
   * 司机提现
   * */
  DriverWithdrawResponseType driverWithdraw(DriverWithdrawRequestType request);

  /**
   * 司机密码重置
   * */
  DriverResetPswResponseType driverResetPsw(DriverResetPswRequestType request);
}
