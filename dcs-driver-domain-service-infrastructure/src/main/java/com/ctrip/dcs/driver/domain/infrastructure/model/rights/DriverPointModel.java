package com.ctrip.dcs.driver.domain.infrastructure.model.rights;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Description
 */

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class DriverPointModel {
    /**
     * 等级定义ID
     */
    private Long levelConfigId;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 司机ID
     */
    private Long drivId;

    /**
     * 司机等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
    private Integer drivLevel;

    /**
     * 司机服务分
     */
    private BigDecimal drivPoint;

    /**
     * 司机活跃分
     */
    private BigDecimal drivActivity;

    /**
     * 司机安全分
     */
    private BigDecimal driverSafePoint;

    /**
     * 司机总分
     */
    private BigDecimal totalPoint;

    /**
     * 司机排名
     */
    private Integer drivRank;

    /**
     * 距离下一等级还差排名
     */
    private Integer rankGap;

    /**
     * 司机服务分
     */
    private BigDecimal drivPointLow;

    /**
     * 司机活跃分
     */
    private BigDecimal drivActivityLow;

    /**
     * 司机安全分门槛
     */
    private BigDecimal driverSafePointLow;

    /**
     * 司机总排名
     */
    private Integer totalRank;
}
