package com.ctrip.dcs.driver.account.infrastructure.gateway.impl;

import com.ctrip.dcs.driver.account.infrastructure.common.AccountExceptionCode;
import com.ctrip.dcs.driver.account.infrastructure.gateway.QunarAccountGateway;
import com.ctrip.dcs.driver.account.infrastructure.value.QunarFetchUserIdResultDTO;
import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverAccountConfig;
import com.ctrip.dcs.driver.account.infrastructure.utils.MD5Util;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.form.DriverGreyConfig;
import com.ctrip.dcs.go.http.HttpUtil;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class QunarAccountGatewayImpl implements QunarAccountGateway {
    private static final Logger logger = LoggerFactory.getLogger(DriverGreyConfig.class);


    private final static String SALT = "$jC&*qwA";

    @Resource
    private DriverAccountConfig accountConfig;

    /**
     * 获取ppm账户（幂等）
     */
    @Override
    public String getPPMAccountIDFromQunar(String driverId) {
        String url = accountConfig.getQunarCarInnerHost() + accountConfig.getQunarUserCenterFetchUserIdUrl();
        Map<String, String> param = buildFetchUserIdParam(driverId);
        int tryCount = 0;
        while (tryCount <= accountConfig.getFetchUserIdRetryCount()) {
            try {
                tryCount++;
                QunarFetchUserIdResultDTO result = HttpUtil.get(url, param, QunarFetchUserIdResultDTO.class);
                if (result == null || result.getBstatus() == null || result.getData() == null || result.getBstatus().getCode() != 0) {
                    continue;
                }
                return result.getData().getUserid();
            } catch (Exception e) {
                logger.warn("fail to get drv qunar account, drvId={}", driverId);
            }
        }
        throw new BizException(AccountExceptionCode.INVOKE_QUNAR_FETCH_ACCOUNT_ID_ERROR.getCode(), AccountExceptionCode.INVOKE_QUNAR_FETCH_ACCOUNT_ID_ERROR.getMsg());
    }


    private Map<String, String> buildFetchUserIdParam(String driverId) {
        if (driverId == null) {
            return new HashMap<>();
        }
        Map<String, String> param = new HashMap<>();
        param.put("driverId", driverId);
        // 增加md5token参数
        String md5Token = MD5Util.getMD5Digest(driverId, SALT);
        if (StringUtils.isNotBlank(md5Token)) {
            param.put("token", md5Token);
        }
        return param;
    }
}
