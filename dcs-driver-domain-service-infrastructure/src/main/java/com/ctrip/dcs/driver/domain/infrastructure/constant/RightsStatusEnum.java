package com.ctrip.dcs.driver.domain.infrastructure.constant;


public enum RightsStatusEnum {
    RIGHTS_STATUS_ISSUED(0,"issued"),
    RIGHTS_STATUS_USED(1,"used"),
    RIGHTS_STATUS_EXPIRED(2,"expired"),
    RIGHTS_STATUS_CANCELLED(3,"cancelled"),
    RIGHTS_STATUS_UNKNOWN(99,"unknown");
    private int status;
    private String name;

    RightsStatusEnum(int status, String name) {
        this.status = status;
        this.name = name;
    }

    public static RightsStatusEnum getByStatus(Integer status) {
        for (RightsStatusEnum rightsStatusEnum : RightsStatusEnum.values()) {
            if (rightsStatusEnum.status == status) {
                return rightsStatusEnum;
            }
        }
        return RIGHTS_STATUS_UNKNOWN;
    }
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
