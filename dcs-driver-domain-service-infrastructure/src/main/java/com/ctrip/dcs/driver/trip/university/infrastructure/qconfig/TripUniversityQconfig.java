package com.ctrip.dcs.driver.trip.university.infrastructure.qconfig;

import com.ctrip.dcs.driver.trip.university.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@QMapConfig("trip.university.properties")
@Data
public class TripUniversityQconfig {

    /**
     * 携程大学-智行力token
     */
    String zhixlTokenUrl;

    /**
     * 携程大学-智行力接口地址
     */
    String zhixlApiInterfaceUrl;

  /**
   * 执行力力-获取用户信息接口地址
   */
  String zhixlGetUserUrl;

  /**
   * 携程大学-智行力批量创建接口地址
   */
  String zhixlApiBatchCreateUserInterfaceUrl;

  /**
   * 智行力获取批量司机创建结果地址
   */
  String zhixlBatchUserResultApiInterfaceUrl;

    /**
     * 携程大学-智行力token 账号密码
     */
   String zhixlTokenMap;

   String driverLanguageMap;
    
  private Integer poolCoreSize;
  private Integer driverMaxSize;
  private Integer poolWorkQueueSize;
  private Integer batchUserResultSleepMilliseconds;
  private Integer batchUserResultWaitMilliseconds;
  private Integer failedMaxRetryCount;
  private String departIdMap;
  private Integer syncBatchSize;
  private Boolean createDrvCodeSwitch;
  private List<Long> tripUniversityGrayDrvIdList;

  public Map<String,String> getZhixlAccount(){
    return JsonUtil.fromJson(zhixlTokenMap, new TypeReference<Map<String, String>>() {});
  }

  public String gettTenantId() {
    return getZhixlAccount().getOrDefault("tenantid", "ctrip");
  }

  public int getPoolCoreSize() {
    return Optional.ofNullable(poolCoreSize).orElse(4);
  }

  public int getDriverMaxSize() {
    return Optional.ofNullable(driverMaxSize).orElse(10);
  }

  public int getPoolWorkQueueSize() {
    return Optional.ofNullable(poolWorkQueueSize).orElse(200);
  }

  public String getDriverLanguageMap(String languageCode) {
    return JsonUtil.fromJson(Optional.ofNullable(driverLanguageMap).orElse("{}"), new TypeReference<Map<String, String>>() {}).getOrDefault(languageCode, "en-us");
  }

  public int getBatchUserResultSleepMilliseconds() {
    return Optional.ofNullable(batchUserResultSleepMilliseconds).orElse(200);
  }

  public int getBatchUserResultWaitMilliseconds() {
    return Optional.ofNullable(batchUserResultWaitMilliseconds).orElse(5000);
  }

  public boolean isCreateDrvCodeSwitch() {
    return Optional.ofNullable(createDrvCodeSwitch).orElse(false);
  }

  public int getFailedMaxRetryCount() {
    return Optional.ofNullable(failedMaxRetryCount).orElse(5);
  }

  public String getDepartIdMap(String name) {
    return JsonUtil.fromJson(Optional.ofNullable(departIdMap).orElse("{\"Driver\":\"ZTYV0\",\"DriverGuide\":\"ZTYV1\",\"DriverAndDriverGuide\":\"ZTYV2\"}"), new TypeReference<Map<String, String>>() {}).getOrDefault(name, "");
  }

  public int getSyncBatchSize() {
    return Optional.ofNullable(syncBatchSize).orElse(20);
  }

  public List<Long> getTripUniversityGrayDrvIdList() {
    return Optional.ofNullable(tripUniversityGrayDrvIdList).orElse(Lists.newArrayList(1L));
  }
}
