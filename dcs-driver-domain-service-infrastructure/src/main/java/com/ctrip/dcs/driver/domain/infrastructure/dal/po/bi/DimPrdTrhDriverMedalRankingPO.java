package com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-07-25
 */
@Getter
@Setter
@Entity
@Database(name = "igtbidb_W")
@Table(name = "dim_prd_trh_driver_medal_ranking")
public class DimPrdTrhDriverMedalRankingPO implements DalPojo {

    /**
     * 自增id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机id
     */
    @Column(name = "drv_id")
    @Type(value = Types.BIGINT)
    private Long drvId;

    /**
     * 勋章类型
     */
    @Column(name = "medal_type")
    @Type(value = Types.VARCHAR)
    private String medalType;

    /**
     * 勋章等级
     */
    @Column(name = "medal_grade")
    @Type(value = Types.INTEGER)
    private Integer medalGrade;

    /**
     * 勋章获取时间
     */
    @Column(name = "medal_grade_get_time")
    @Type(value = Types.VARCHAR)
    private String medalGradeGetTime;

    /**
     * 勋章获取名次
     */
    @Column(name = "medal_grade_get_rank")
    @Type(value = Types.BIGINT)
    private Long medalGradeGetRank;

    /**
     * 该勋章当前已有多少人
     */
    @Column(name = "grade_get_drv_cnt")
    @Type(value = Types.BIGINT)
    private Long gradeGetDrvCnt;
}