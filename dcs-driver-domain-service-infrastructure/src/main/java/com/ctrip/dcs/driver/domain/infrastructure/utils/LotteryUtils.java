package com.ctrip.dcs.driver.domain.infrastructure.utils;

import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.ProbabilityDto;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
public class LotteryUtils {

    public static   <T> ProbabilityDto<T> probabilityLottery(List<ProbabilityDto<T>> probabilityDtoList, ProbabilityDto<T> defaultValue) {

        // 基数建立
        Map<Integer, Integer> numbers = new HashMap<>();
        for (ProbabilityDto entity : probabilityDtoList) {
            numbers.put(entity.getId(), (int) (entity.getProbabilityValue() * 10.0));
        }

        // 基数分区
        int idxStart = 0;
        int idxEnd = 0;
        List<Integer> nums = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            nums.add(i);
        }
        Map<Integer, List<Integer>> numParts = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : numbers.entrySet()) {
            idxEnd = idxStart + entry.getValue();
            if (idxEnd >= 1000) {
                idxEnd = 1000;
            }
            if (entry.getValue() > 0) {
                numParts.put(entry.getKey(), nums.subList(idxStart, idxEnd));
            }
            idxStart = idxEnd;
        }

        // 随机抽取
        Random random = new Random();
        int r = random.nextInt(1000);
        // 基础概率数
        for (Map.Entry<Integer, List<Integer>> entry : numParts.entrySet()) {
            List<Integer> value = entry.getValue();
            if (value.contains(r)) {
                List<ProbabilityDto> entities = probabilityDtoList.stream().filter(e -> e.getId().equals(entry.getKey())).collect(Collectors.toList());
                if (entities != null && entities.size() > 0) {
                    return entities.get(0);
                }
            }
        }
        return defaultValue;
    }

}