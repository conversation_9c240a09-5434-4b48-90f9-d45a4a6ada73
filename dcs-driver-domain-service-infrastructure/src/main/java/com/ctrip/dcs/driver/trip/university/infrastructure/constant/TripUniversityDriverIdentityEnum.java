
package com.ctrip.dcs.driver.trip.university.infrastructure.constant;


import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */


@Getter
public enum TripUniversityDriverIdentityEnum {
    DRIVER(1, "Driver"),
    DRIVER_GUIDE(2, "DriverGuide"),
    DRIVER_AND_DRIVER_GUIDE(3, "DriverAndDriverGuide");

    private int code;
    private String name;

    TripUniversityDriverIdentityEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
