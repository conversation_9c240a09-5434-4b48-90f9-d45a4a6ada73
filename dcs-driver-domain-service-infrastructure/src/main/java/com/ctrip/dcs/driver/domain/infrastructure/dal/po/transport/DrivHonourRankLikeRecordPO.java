package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-07-22
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driv_honour_rank_like_record")
public class DrivHonourRankLikeRecordPO implements DalPojo {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 1周榜;2月榜
     */
    @Column(name = "type")
    @Type(value = Types.INTEGER)
    private Integer type;

    /**
     * 排行榜refs
     */
    @Column(name = "rank_refs")
    @Type(value = Types.INTEGER)
    private Integer rankRefs;

    /**
     * 司机ID
     */
    @Column(name = "driver_id")
    @Type(value = Types.INTEGER)
    private Long driverId;

    /**
     * 收获的点赞量
     */
    @Column(name = "like_count")
    @Type(value = Types.INTEGER)
    private Integer likeCount;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}
