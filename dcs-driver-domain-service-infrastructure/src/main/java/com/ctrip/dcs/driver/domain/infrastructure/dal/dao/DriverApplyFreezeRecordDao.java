package com.ctrip.dcs.driver.domain.infrastructure.dal.dao;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.DriverApplyFreezeRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;


@Dal("dcstransportdb_w")
public interface DriverApplyFreezeRecordDao extends DalRepository<DriverApplyFreezeRecordPO> {

    @Sql("update driver_apply_freeze_record set provider_data_location = ? where drv_id = ?")
    void updateUdlByDriverId(String udl, String driverId);
}
