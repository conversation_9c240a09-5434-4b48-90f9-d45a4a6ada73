package com.ctrip.dcs.driver.account.infrastructure.value;

import com.ctrip.dcs.driver.account.infrastructure.constant.AccountSouceEnum;
import lombok.Data;

@Data
public class AccountIdentityDTO {

    private String source;

    private String sourceId;

    private boolean valid;

    public boolean isDriverOrDriverGuide(){
        return AccountSouceEnum.isDriver(this.getSource()) || AccountSouceEnum.isDriverGuide(this.getSource());
    }
}
