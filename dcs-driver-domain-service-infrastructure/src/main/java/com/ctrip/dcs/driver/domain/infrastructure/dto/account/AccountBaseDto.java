package com.ctrip.dcs.driver.domain.infrastructure.dto.account;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Description
 */
@Getter
@Setter
public class AccountBaseDto {
    /**
     * 来源 司机：Driver 向导：Guide
     */
    public String source;

    /**
     * 司机id/向导id
     */
    public String sourceId;

    /**
     * 注册账号类型 1 手机 2邮箱
     */
    public Integer accountType;

    /**
     * 绑定账号类型 1 手机 2邮箱
     */
    public Integer bindAccountType;

    /**
     * 手机国家码
     */
    public String countryCode;

    /**
     * 手机号
     */
    public String phoneNumber;

    /**
     * 邮箱
     */
    public String email;

    /**
     * 密码
     */
    public String psw;
    /**
     * 操作人
     */
    public String operator;

    /**
     * 城市id
     */
    public Long cityId;

    public Boolean oversea;
}
