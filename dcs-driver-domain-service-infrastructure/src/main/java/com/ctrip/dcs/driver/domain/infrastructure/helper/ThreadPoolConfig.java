package com.ctrip.dcs.driver.domain.infrastructure.helper;

import com.ctrip.igt.framework.common.concurrent.threadpool.ThreadPoolBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;

@Configuration
public class ThreadPoolConfig {

  @Bean("CommonThreadPool")
  public ExecutorService getThreadPoolExecutor() {
    ExecutorService threadPool = ThreadPoolUtils.getThreadPool();
    if (threadPool != null)return threadPool;
    return ThreadPoolBuilder.fixedPool().setPoolSize(30).setQueueSize(100).setDaemon(false).setThreadNamePrefix("common")
            .setUseTtl(true).build();
  }
}
