package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class DriverAccountParamConfig {
  boolean open;

  Set<Long> driverIds = new HashSet<>();

  Set<Long> cityIds = new HashSet<>();

  public boolean isGrey(Long driverId,Long driverCityId){
    if(this.open){
      return true;
    }
    if(this.driverIds != null && this.driverIds.contains(driverId)){
      return true;
    }
    if(this.cityIds != null && this.cityIds.contains(driverCityId)){
      return true;
    }
    return false;
  }

  @QConfig("driver_account_param.json")
  private void onChange(OrderAccountParamSwitchDTO orderAccountParamSwitchDTO) {
    if (Objects.nonNull(orderAccountParamSwitchDTO)) {
      if ("1".equals(ObjectUtils.defaultIfNull(orderAccountParamSwitchDTO.open, "0"))) {
        this.open = true;
      }else {
        this.open = false;
      }
      if(StringUtils.isNotBlank(orderAccountParamSwitchDTO.getWhiteDriverIds())) {
        driverIds = Arrays.stream(orderAccountParamSwitchDTO.getWhiteDriverIds().split(",")).map(Long::parseLong).collect(
            Collectors.toSet());
      } else {
        driverIds = new HashSet<>();
      }
      if(StringUtils.isNotBlank(orderAccountParamSwitchDTO.getCityIds())){
        cityIds = Arrays.stream(orderAccountParamSwitchDTO.getCityIds().split(",")).map(Long::parseLong).collect(
            Collectors.toSet());
      }else {
        cityIds = new HashSet<>();
      }
    }
  }

  private static class OrderAccountParamSwitchDTO {
    private String open;
    private String whiteDriverIds;

    private String cityIds;

    public String getOpen() {
      return open;
    }

    public void setOpen(String open) {
      this.open = open;
    }

    public String getWhiteDriverIds() {
      return whiteDriverIds;
    }

    public void setWhiteDriverIds(String whiteDriverIds) {
      this.whiteDriverIds = whiteDriverIds;
    }

    public String getCityIds() {
      return cityIds;
    }

    public void setCityIds(String cityIds) {
      this.cityIds = cityIds;
    }
  }
}
