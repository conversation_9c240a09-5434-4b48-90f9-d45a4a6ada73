package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2020-10-13
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driv_login_info")
public class DrivLoginInfoPO implements DalPojo {
  
  /**
   * 空
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;
  
  /**
   * 司机ID
   */
  @Column(name = "driv_id")
  @Type(value = Types.BIGINT)
  private Long drivId;
  
  /**
   * phone_sign值，用于处理重复登录
   */
  @Column(name = "phone_sign")
  @Type(value = Types.VARCHAR)
  private String phoneSign;
  
  /**
   * 司机星级，起始5星
   */
  @Column(name = "service_level")
  @Type(value = Types.DECIMAL)
  private BigDecimal serviceLevel;
  
  /**
   * 1.接单状态 2.停止接单状态
   */
  @Column(name = "work_status")
  @Type(value = Types.TINYINT)
  private Integer workStatus;
  
  /**
   * 个信token
   */
  @Column(name = "igexin")
  @Type(value = Types.VARCHAR)
  private String igexin;
  
  /**
   * yacca的token
   */
  @Column(name = "yacca")
  @Type(value = Types.VARCHAR)
  private String yacca;
  
  /**
   * 小米推送的token
   */
  @Column(name = "xiaomi")
  @Type(value = Types.VARCHAR)
  private String xiaomi;
  
  /**
   * 司机最近上传的经度
   */
  @Column(name = "longitude")
  @Type(value = Types.DECIMAL)
  private BigDecimal longitude;
  
  /**
   * 司机最近上传的纬度
   */
  @Column(name = "latitude")
  @Type(value = Types.DECIMAL)
  private BigDecimal latitude;
  
  /**
   * 终端信息
   */
  @Column(name = "terminal_info")
  @Type(value = Types.VARCHAR)
  private String terminalInfo;
  
  /**
   * 操作系统类型，0:安卓，1:iOS
   */
  @Column(name = "os_type")
  @Type(value = Types.TINYINT)
  private Integer osType;
  
  /**
   * 生成时间
   */
  @Column(name = "create_time")
  @Type(value = Types.TIMESTAMP)
  private Timestamp createTime;
  
  /**
   * 更新时间
   */
  @Column(name = "update_time")
  @Type(value = Types.TIMESTAMP)
  private Timestamp updateTime;
  
  /**
   * 司机地址
   */
  @Column(name = "address")
  @Type(value = Types.VARCHAR)
  private String address;
  
  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime", insertable = false, updatable = false)
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLasttime;
}
