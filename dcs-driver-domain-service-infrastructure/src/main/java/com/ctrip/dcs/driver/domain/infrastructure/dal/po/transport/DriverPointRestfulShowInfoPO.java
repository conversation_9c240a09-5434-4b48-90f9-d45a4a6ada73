package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-02-13
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_point_restful_show_info")
public class DriverPointRestfulShowInfoPO implements DalPojo {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机ID
     */
    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    /**
     * 新手司机首页弹窗 0:未提醒;1:我知道了
     */
    @Column(name = "novice_point_guide_pop")
    @Type(value = Types.INTEGER)
    private Integer novicePointGuidePop;

    /**
     * 新手司机首页弹窗确认时间
     */
    @Column(name = "novice_point_guide_pop_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp novicePointGuidePopTime;

    /**
     * 新手司机分过期弹窗 0:未提醒;1:我知道了
     */
    @Column(name = "novice_point_voidance_pop")
    @Type(value = Types.INTEGER)
    private Integer novicePointVoidancePop;

    /**
     * 新手司机分过期弹窗确认时间
     */
    @Column(name = "novice_point_voidance_pop_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp novicePointVoidancePopTime;
}