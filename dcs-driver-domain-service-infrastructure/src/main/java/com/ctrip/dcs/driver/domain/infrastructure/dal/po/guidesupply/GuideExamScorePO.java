package com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-10-25
 */
@Getter
@Setter
@Entity
@Database(name = "dcsguidesupplydb")
@Table(name = "guide_exam_score")
public class GuideExamScorePO implements DalPojo {
  /**
   * id
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * 司导职行力第三方id
   */
  @Column(name = "exam_account_id")
  @Type(value = Types.VARCHAR)
  private String examAccountId;

  /**
   * 报考科目
   */
  @Column(name = "apply_subject")
  @Type(value = Types.VARCHAR)
  private String applySubject;

  /**
   * 报考科目
   */
  @Column(name = "exam_score")
  @Type(value = Types.DECIMAL)
  private BigDecimal examScore;

  /**
   * 报考时间
   */
  @Column(name = "complete_time")
  @Type(value = Types.TIMESTAMP)
  private Timestamp completeTime;

  /**
   * 时区
   */
  @Column(name = "time_zone")
  @Type(value = Types.DECIMAL)
  private BigDecimal timeZone;

  /**
   * 考试结果 0-未通过 1-通过
   */
  @Column(name = "exam_is_passed")
  @Type(value = Types.TINYINT)
  private Integer examIsPassed;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeCreatetime;

  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLasttime;
}
