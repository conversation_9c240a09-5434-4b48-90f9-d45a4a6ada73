package com.ctrip.dcs.driver.domain.infrastructure.utils;

import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.joda.time.DateTime;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;

public class LocalDateTimeUtils {
    public LocalDateTimeUtils() {
    }

    public static final String PATTERN_DATE_TIME = "yyyy-MM-dd";

    public static final String DATE_FORMAT_DAY_KEY = "yyyyMMdd";

    public static final DateTimeFormatter DATE_FORMAT_DAT_TIME_KEY = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static final DateTimeFormatter DATE_FORMAT_MONTH_END = DateTimeFormatter.ofPattern("yyyy-MM");


    public static final LocalDateTime localDateTime(String date) {
        return localDateTime(date, (LocalDateTime) null);
    }

    public static final LocalDateTime localDateTime(String date, LocalDateTime defaultValue) {
        if (StringUtils.isBlank(date)) {
            return defaultValue;
        } else {
            LocalDateTime localDateTime = (LocalDateTime) ShoppingFuncUtils.tryFunc(() -> {
                return LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            });
            return (LocalDateTime) ObjectUtils.defaultIfNull(localDateTime, defaultValue);
        }
    }

    public static final LocalDate localDate(String date) {
        if (StringUtils.isBlank(date)) {
            return LocalDate.MIN;
        } else {
            String finalDate = date;
            LocalDate localDate = (LocalDate) ShoppingFuncUtils.tryFunc(() -> {
                return LocalDate.parse(finalDate, DateTimeFormatter.ofPattern(PATTERN_DATE_TIME));
            });
            return (LocalDate) ObjectUtils.defaultIfNull(localDate, LocalDate.MIN);
        }
    }

    public static boolean isNowInDate(String startDate, String endDate) {
        if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
            String nowDate = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            return nowDate.compareTo(startDate) >= 0 && nowDate.compareTo(endDate) <= 0;
        }
        return false;
    }


    public static String fitstDayOfMonthStr() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate formatDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
        return formatDate.format(df);
    }

    public static String firstDayOfMonthStr(LocalDateTime localDateTime) {
        LocalDateTime formatDate = localDateTime.with(TemporalAdjusters.firstDayOfMonth());
        return formatDate.format(DATE_FORMAT_DAT_TIME_KEY);
    }

    public static String todayStr() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate formatDate = LocalDate.now();
        return formatDate.format(df);
    }

    public static String todayStr(LocalDateTime localDateTime) {
        return localDateTime.format(DATE_FORMAT_DAT_TIME_KEY);
    }

    public static String yesterDayStr() {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate formatDate = LocalDate.now().minusDays(1);
        return formatDate.format(df);
    }

    public static String yesterdayStr(LocalDateTime localDateTime) {
        return localDateTime.minusDays(1).format(DATE_FORMAT_DAT_TIME_KEY);
    }

    public static String monthIndexStr() {
        return DateTime.now().dayOfMonth().withMinimumValue().toString("yyyy-MM");
    }

    public static String monthIndexStr(LocalDateTime localDateTime) {
        return localDateTime.format(DATE_FORMAT_MONTH_END);
    }

    public static LocalDateTime firstDayOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).atStartOfDay();
    }

    public static LocalDateTime lastDayOfMonth() {
        return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);
    }

    public static LocalDateTime lastDayOfWeek() {
        return LocalDate.now().with(DayOfWeek.SUNDAY).atTime(23, 59, 59);
    }

    public static LocalDateTime firstDayOfWeek() {
        return LocalDate.now().with(DayOfWeek.MONDAY).atTime(00, 00, 00);
    }
    public static String format(LocalDateTime time) {
        return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    public static String format(Timestamp time) {
        return  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time);
    }

    public static String formatDate(LocalDateTime date, String format) {
        if (date == null) {
            return "";
        } else {
            return DateTimeFormatter.ofPattern(format).format(date);
        }
    }
}
