package com.ctrip.dcs.driver.domain.infrastructure.helper;

import com.ctrip.arch.distlock.DistributedLockService;
import com.ctrip.arch.distlock.redis.RedisDistributedLockService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created by <AUTHOR> on 2022/9/20 18:08
 */
@Configuration
public class RedisDistributedLockConfig {

  @Bean
  public DistributedLockService distributedLockService() {
    return new RedisDistributedLockService("dcs.driver.notice.update");
  }
}
