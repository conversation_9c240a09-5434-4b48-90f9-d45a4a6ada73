package com.ctrip.dcs.driver.account.infrastructure.dal.dao;

import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountMapperPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcstransportdb_w")
public interface AccountMapperDao extends DalRepository<AccountMapperPO> {
    @Sql("select * from account_mapper where uid = ?")
    List<AccountMapperPO> queryByUid(String uid);

    @Sql("select * from account_mapper where uid in (?)")
    List<AccountMapperPO> batchQueryByUid(List<String> uidList);

    @Sql("select * from account_mapper where source = ? and source_id = ? limit 1")
    AccountMapperPO queryBySource(String source, String sourceId);

    @Sql("select * from account_mapper where source in (?) and source_id = ?")
    List<AccountMapperPO> batchQueryBySource(List<String> sourceList, String sourceId);

    @Sql("select * from account_mapper where source in (?) and source_id in (?)")
    List<AccountMapperPO> batchQueryBySource(List<String> sourceList, List<String> sourceIdList);

    @Sql("select * from account_mapper where id > ? order by id limit ?")
    List<AccountMapperPO> batchQueryByPage(Long id, int limit);

    @Sql("update account_mapper set provider_data_location = ? where uid = ?")
    void updateUdl(String udl, String uid);

    @Sql("select * from account_mapper where source_id in (?)")
    List<AccountMapperPO> queryUdlByDriverIds(List<String> driverIds);


    @Sql("select * from account_mapper where source_id in (?)")
    List<AccountMapperPO> queryByDriverIds(List<String> driverIds);
}
