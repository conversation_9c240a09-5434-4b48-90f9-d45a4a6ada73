package com.ctrip.dcs.driver.domain.infrastructure.adapter.tourai;

import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClientConfigOptions;
import com.ctrip.tour.ai.one.service.GetDataRequestType;
import com.ctrip.tour.ai.one.service.GetDataResponseType;
import com.ctrip.tour.ai.one.service.TourAIOneServiceClient;

import java.net.SocketTimeoutException;

@ServiceClient(value = TourAIOneServiceClient.class, format = "json")
public interface TourAIOneServiceProxy {

    /**
     * *查询BI信息
     */
    @ServiceClientConfigOptions(format = "json", logWarn = true, logWarnExceptionInclude = {SocketTimeoutException.class})
    GetDataResponseType getData(GetDataRequestType request);
}
