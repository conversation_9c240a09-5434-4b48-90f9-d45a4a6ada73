package com.ctrip.dcs.driver.domain.infrastructure.model.form;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class FormRightsInfo {
    private long id;
    private int rightsType;
    private String rightsName;
    private String rightsDesc;
    private List<FormRightsIntroduceInfo> rightsIntroduceList;
    private List<FormRightsChoiceLevelInfo> levelList;
    private String cityIdsStr;
    private List<Long> cityList;
    private int status;
    private int useLimit;
    private String extend;

}
