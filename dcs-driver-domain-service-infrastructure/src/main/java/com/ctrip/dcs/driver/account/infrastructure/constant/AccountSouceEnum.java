package com.ctrip.dcs.driver.account.infrastructure.constant;


import lombok.Getter;

import java.util.Arrays;

@Getter
public enum AccountSouceEnum {
    //司机
    DRIVER_SOURCE(1, "Driver"),
    //向导
    GUIDE_SOURCE(2, "Guide"),
    //司导
    DRIVER_GUIDE(3, "DriverGuide");

    private int code;
    private String name;

    AccountSouceEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static boolean isGuide(String source) {
        return GUIDE_SOURCE.name.equals(source);
    }

    public static boolean isDriver(String source) {
        return DRIVER_SOURCE.name.equals(source);
    }

    public static boolean isDriverGuide(String source) {
        return DRIVER_GUIDE.name.equals(source);
    }

    public static AccountSouceEnum instanceOf(String name) {
        return Arrays.stream(values()).filter(o -> o.getName().equals(name)).findAny().orElse(null);
    }
}
