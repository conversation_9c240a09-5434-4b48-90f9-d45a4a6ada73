package com.ctrip.dcs.driver.account.infrastructure.constant;
/*
作者：pl.yang
创建时间：2025/4/28-上午11:18-2025
*/



/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BankCardInfoTypeEnum
 * @Package com.ctrip.dcs.driver.account.infrastructure.constant
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/4/28 上午11:18
 */


public enum BankCardInfoTypeEnum {
    /**
     * 银行账户币种
     */
    ACCOUNTCURRENCY(0),

    /**
     * 境内境外卡
     */
    TYPE(1),

    /**
     * 供应商所在地址
     */
    ADDRESS(2),

    /**
     * 银行账户名称
     */
    ACCOUNTNAME(3),

    /**
     * 银行卡号
     */
    ACCOUNTNUMBER(4),

    /**
     * 列表选择的开户银行对应的易宝CODE
     */
    BANKNAMECODE(5),

    /**
     * 开户银行名称
     */
    BANKNAME(6),

    /**
     * 银行详细地址
     */
    BANKADDRESS(7),

    /**
     * 银行编码（香港）
     */
    BANKCODE(8),

    /**
     * 分支行编码
     */
    BRANCHCODE(9),

    /**
     * 列表选择的开户银行所在的省/州对应的易宝CODE
     */
    PROVINCECODE(10),

    /**
     * 开户银行所在的省/州
     */
    PROVINCE(11),

    /**
     * 证件类型
     */
    IDTYPE(12),

    /**
     * 证件号码
     */
    IDNUMBER(13),

    /**
     * FEDWIRE号码
     */
    FEDWIRENUMBER(14),

    /**
     * 清算号
     */
    SORTCODE(15),

    /**
     * IBAN 号码
     */
    IBAN(16),

    /**
     * 开户银行所在地的邮编
     */
    POSTCODE(17),

    /**
     * ABA NUMBER
     */
    ABANUMBER(18),

    /**
     * EMAIL
     */
    EMAIL(19),

    /**
     * 国际区号
     */
    IGTCODE(20),

    /**
     * 电话号码
     */
    MOBILE(21),

    /**
     * 银行路由号码
     */
    ROUTINGNUMBER(22),

    /**
     * 银行的IFS码
     */
    IFSCODE(23),

    /**
     * SWIFTCODE
     */
    SWIFTCODE(24);

    private final int value;

    BankCardInfoTypeEnum(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public int getValue() {
        return value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Baiji IDL.
     */
    public static BankCardInfoTypeEnum findByValue(int value) {
        switch (value) {
            case 0:
                return ACCOUNTCURRENCY;
            case 1:
                return TYPE;
            case 2:
                return ADDRESS;
            case 3:
                return ACCOUNTNAME;
            case 4:
                return ACCOUNTNUMBER;
            case 5:
                return BANKNAMECODE;
            case 6:
                return BANKNAME;
            case 7:
                return BANKADDRESS;
            case 8:
                return BANKCODE;
            case 9:
                return BRANCHCODE;
            case 10:
                return PROVINCECODE;
            case 11:
                return PROVINCE;
            case 12:
                return IDTYPE;
            case 13:
                return IDNUMBER;
            case 14:
                return FEDWIRENUMBER;
            case 15:
                return SORTCODE;
            case 16:
                return IBAN;
            case 17:
                return POSTCODE;
            case 18:
                return ABANUMBER;
            case 19:
                return EMAIL;
            case 20:
                return IGTCODE;
            case 21:
                return MOBILE;
            case 22:
                return ROUTINGNUMBER;
            case 23:
                return IFSCODE;
            case 24:
                return SWIFTCODE;
            default:
                return null;
        }
    }
}
