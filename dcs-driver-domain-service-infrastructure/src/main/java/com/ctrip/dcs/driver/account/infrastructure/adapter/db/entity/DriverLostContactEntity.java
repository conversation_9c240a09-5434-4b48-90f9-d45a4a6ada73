package com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity;

import java.sql.Types;

import javax.persistence.*;

import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "driver_lost_contact")
@Database(name = "dcstransportdb_w")
public class DriverLostContactEntity {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    @Column(name = "drv_id")
    @Type(value = Types.BIGINT)
    private Long drvId;

    @Column(name = "order_id")
    @Type(value = Types.VARCHAR)
    private String orderId;
}
