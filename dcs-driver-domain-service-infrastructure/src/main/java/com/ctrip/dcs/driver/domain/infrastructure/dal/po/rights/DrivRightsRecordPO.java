package com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-08-22
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driv_rights_record")
public class DrivRightsRecordPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 权益下发ID-司机权益表主键
     */
	@Column(name = "rights_id")
	@Type(value = Types.BIGINT)
	private Long rightsId;

    /**
     * 司机ID
     */
	@Column(name = "driv_id")
	@Type(value = Types.BIGINT)
	private Long drivId;

    /**
     * 权益类型 1福利金 2改派 3城市王者 4初级投诉豁免卡 5高级投诉豁免卡 6每日提现
     */
	@Column(name = "rights_type")
	@Type(value = Types.TINYINT)
	private Integer rightsType;

    /**
     * 权益名称
     */
	@Column(name = "rights_name")
	@Type(value = Types.VARCHAR)
	private String rightsName;

    /**
     * 司机使用等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
	@Column(name = "use_level")
	@Type(value = Types.TINYINT)
	private Integer useLevel;

    /**
     * 等级名称
     */
	@Column(name = "level_name")
	@Type(value = Types.VARCHAR)
	private String levelName;

    /**
     * 用户订单号
     */
	@Column(name = "user_order_id")
	@Type(value = Types.VARCHAR)
	private String userOrderId;

    /**
     * 采购单号 只有判罚权益有
     */
	@Column(name = "purchase_order_id")
	@Type(value = Types.VARCHAR)
	private String purchaseOrderId;

    /**
     * 司机单号
     */
	@Column(name = "supply_order_id")
	@Type(value = Types.VARCHAR)
	private String supplyOrderId;

    /**
     * 判罚单号
     */
	@Column(name = "punish_order_id")
	@Type(value = Types.VARCHAR)
	private String punishOrderId;

    /**
     * 金额(福利金，每日提现)
     */
	@Column(name = "money")
	@Type(value = Types.VARCHAR)
	private String money;

    /**
     * 创建时间-权益使用时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 最后更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 0未删除 1已删除
     */
	@Column(name = "is_deleted")
	@Type(value = Types.TINYINT)
	private Integer isDeleted;

	/**
	 * 预计用车时间
	 */
	@Column(name = "expect_book_time")
	@Type(value = Types.VARCHAR)
	private String expectBookTime;
}