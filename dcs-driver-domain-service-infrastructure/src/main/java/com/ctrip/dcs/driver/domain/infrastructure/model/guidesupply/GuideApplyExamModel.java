package com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class GuideApplyExamModel {

    private Long id;

    private Long guideId;

    private String examAccountId;

    private String guideName;

    private String account;

    private String applySubject;

    private String applyTime;

    private BigDecimal timeZone;

    private String subjectName;

    private Integer applyResult;

    private Integer examIsPassed;

    private Integer callExamSuccess;

    private String datachangeCreatetime;
}
