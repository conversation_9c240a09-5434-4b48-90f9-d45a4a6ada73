package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideExamScorePO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.sql.Timestamp;
import java.util.List;

@Dal("dcsguidesupplydb")
public interface GuideExamScoreDao extends DalRepository<GuideExamScorePO> {
  @Sql("select * from guide_exam_score where exam_account_id = ? and exam_is_passed = 1")
  List<GuideExamScorePO> queryPassedGuideExamScore(String examAccountId);

  @Sql("select count(*) from guide_exam_score where exam_account_id = ? and apply_subject = ?")
  Long countByExamAccountIdApplySubject(String examAccountId, String applySubject);

  @Sql("delete from guide_exam_score where exam_account_id = ? and apply_subject = ? and complete_time in ?")
  void deleteGuideExamScore(String examAccountId, String applySubject,List<Timestamp> completeTimes);
}
