package com.ctrip.dcs.driver.domain.infrastructure.model.device;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class DriverDeviceInfoModel {
    private Long driverId;
    private String uid;
    private String appId;
    private String cid;
    private String appVer;
    private String rnVer;
    private String os;
    private String osVer;
    private String local;
    private LocalDateTime activeTime;
    private LocalDateTime loginTime;
    private String loginAccount;
    private Integer loginType;
}
