package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.utils.StringUtils;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Component
public final class FinanceConfig {

  private ConcurrentMap<String, String> codeList = new ConcurrentHashMap<>();
  private ConcurrentMap<String, String> sharkList = new ConcurrentHashMap<>();
  private long threadWaitTime;

  @QConfig("finance.properties")
  public void updateData(Map<String, String> map) {
    String strFinanceCodes = map.containsKey("finance_code_convert") ? map.get("finance_code_convert").trim() : Strings.EMPTY;
    if(StringUtils.isNotEmpty(strFinanceCodes)) {
      List<FinanceCodeModel> codeModelList =
              JacksonUtil.deserialize(strFinanceCodes, new TypeToken<List<FinanceCodeModel>>(){}.getType());
      this.codeList.clear();
      this.sharkList.clear();
      if(CollectionUtils.isNotEmpty(codeModelList)){
        codeModelList.forEach(c -> {
          codeList.putIfAbsent(c.code, c.convert);
          sharkList.putIfAbsent(c.convert, c.shark);
        });
      }
    } else {
      this.codeList.clear();
      this.sharkList.clear();
    }

    this.setThreadWaitTime(map.containsKey("thread_wait_time") ?
            Long.parseLong(ObjectUtils.defaultIfNull(map.get("thread_wait_time"), "5000").trim()) : 5000L);
  }

  public String getConvertCode(String code) {
    return this.codeList.getOrDefault(code, Strings.EMPTY);
  }

  public String getConvertShark(String convertcode) {
    return this.sharkList.getOrDefault(convertcode, Strings.EMPTY);
  }

  public long getThreadWaitTime() {
    return threadWaitTime;
  }

  public void setThreadWaitTime(long threadWaitTime) {
    this.threadWaitTime = threadWaitTime;
  }

  public static class FinanceCodeModel implements Serializable {
    private String code;
    private String convert;
    private String shark;

    public String getCode() {
      return code;
    }

    public void setCode(String code) {
      this.code = code;
    }

    public String getConvert() {
      return convert;
    }

    public void setConvert(String convert) {
      this.convert = convert;
    }

    public String getShark() {
      return shark;
    }

    public void setShark(String shark) {
      this.shark = shark;
    }
  }
}
