package com.ctrip.dcs.driver.trip.university.infrastructure.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;


/**
 * <AUTHOR>
 */
public abstract class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static <T> T fromJson(String json, TypeReference<T> typeRef){
        try {
            T rst = (T)objectMapper.readValue(json, typeRef);
            return rst;
        } catch (Exception e) {
            throw new RuntimeException("<PERSON>son Error",e);
        }
    }

}
