package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcstransportdb_w")
public interface DrivRightsDao extends DalRepository<DrivRightsPO> {
    @Sql("select * from driv_rights where driv_id = ? and month_idx = ?")
    List<DrivRightsPO> queryDriverRights(long driverId, String monthIdx);

    @Sql("select count(*) from driv_rights where rights_type = ? and month_idx = ? and rights_status != 3")
    Long countByRightsAndMonth(int rightsType, String monthIdx);

    @Sql("update driv_rights set rights_status = 3  where driv_id = ? and rights_type = ? and month_idx = ?")
    int updateRightsCancell(Long driverId, Integer rightsType, String month);
}
