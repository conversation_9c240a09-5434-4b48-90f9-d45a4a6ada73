package com.ctrip.dcs.driver.domain.infrastructure.adapter.account;


import com.ctrip.basebiz.account.service.soa.*;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = AccountServiceClient.class, format = "json")
public interface AccountServiceProxy {

    AccountInfoResponseType getAccountByMobilePhone(GetAccountByMobilePhoneRequestType request);
    AccountInfoListResponseType getAccountsByMobilePhoneList(GetAccountsByMobilePhoneListRequestType request);
    AccountInfoResponseType getAccountByEmail(GetAccountByEmailRequestType request);

    AccountInfoResponseType getAccountByUid(GetAccountByUidRequestType request);

}
