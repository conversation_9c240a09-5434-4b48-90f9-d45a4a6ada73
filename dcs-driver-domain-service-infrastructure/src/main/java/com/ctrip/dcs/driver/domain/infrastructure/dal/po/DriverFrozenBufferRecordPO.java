package com.ctrip.dcs.driver.domain.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_frozen_buffer_record")
public class DriverFrozenBufferRecordPO implements DalPojo {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机ID
     */
    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    /**
     * 缓冲类型
     */
    @Column(name = "reason_type")
    @Type(value = Types.INTEGER)
    private Integer reasonType;

    /**
     * 触发冻结缓冲期的设备id
     */
    @Column(name = "device_id")
    @Type(value = Types.VARCHAR)
    private String deviceId;

    /**
     * 缓冲期天数
     */
    @Column(name = "buffer_days")
    @Type(value = Types.INTEGER)
    private Integer bufferDays;

    /**
     * 缓冲期开始时间
     */
    @Column(name = "buffer_start_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp bufferStartTime;

    /**
     * 缓冲期结束时间
     */
    @Column(name = "buffer_end_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp bufferEndTime;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 变更时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}
