package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivVersionPublishInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcstransportdb_w")
public interface DrivVersionPublishInfoDao extends DalRepository<DrivVersionPublishInfoPO> {

  @Sql("select * from driv_version_publish_record where id > 0")
  List<DrivVersionPublishInfoPO> selectAll();
}
