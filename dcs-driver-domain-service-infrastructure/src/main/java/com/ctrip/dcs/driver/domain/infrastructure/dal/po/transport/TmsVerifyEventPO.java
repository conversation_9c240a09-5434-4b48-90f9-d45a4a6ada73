package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_verify_event")
public class TmsVerifyEventPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 校验来源ID(司机ID，车辆ID)
     */
	@Column(name = "verify_source_id")
	@Type(value = Types.BIGINT)
	private Long verifySourceId;

    /**
     * 校验来源类型(1.司机,2.车辆)
     */
	@Column(name = "verify_source_type")
	@Type(value = Types.TINYINT)
	private Integer verifySourceType;

    /**
     * 司机位置经度
     */
	@Column(name = "drv_loc_long")
	@Type(value = Types.DOUBLE)
	private Double drvLocLong;

    /**
     * 司机位置纬度
     */
	@Column(name = "drv_loc_lat")
	@Type(value = Types.DOUBLE)
	private Double drvLocLat;

    /**
     * 司机位置坐标系
     */
	@Column(name = "drv_loc_csys")
	@Type(value = Types.VARCHAR)
	private String drvLocCsys;

    /**
     * 是否进行过验证1进行验证2未进行验证
     */
	@Column(name = "verify_flag")
	@Type(value = Types.TINYINT)
	private Integer verifyFlag;

    /**
     * 司机完成验证的时间
     */
	@Column(name = "verify_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp verifyTime;

    /**
     * 验证结果(0.成功,1.参数不合法,2.请求不合法，参数有误)
	 * 参考 https://cloud.tencent.com/document/product/1007/47912
     */
	@Column(name = "verify_result_code")
	@Type(value = Types.VARCHAR)
	private String verifyResultCode;

    /**
     * 验证失败的原因
     */
	@Column(name = "fail_reason")
	@Type(value = Types.VARCHAR)
	private String failReason;

    /**
     * 司机手机设备号(imei)
     */
	@Column(name = "drv_imei")
	@Type(value = Types.VARCHAR)
	private String drvImei;

    /**
     * 核验原因(1.换设备登录,2.系统抽查,3.主动完成)
     */
	@Column(name = "verify_reason_status")
	@Type(value = Types.TINYINT)
	private Integer verifyReasonStatus;

    /**
     * 计划开始验证时间
     */
	@Column(name = "verify_start_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp verifyStartTime;

    /**
     * 第一次通知验证的时间
     */
	@Column(name = "verify_notice_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp verifyNoticeTime;

    /**
     * 计划结束验证时间
     */
	@Column(name = "verify_end_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp verifyEndTime;

    /**
     * 验证类型(1.人脸验证,2.车辆验证)
     */
	@Column(name = "verify_type")
	@Type(value = Types.TINYINT)
	private Integer verifyType;

    /**
     * 事件状态1未验证2已验证
     */
	@Column(name = "verify_status")
	@Type(value = Types.TINYINT)
	private Integer verifyStatus;

    /**
     * 提醒次数
     */
	@Column(name = "notice_times")
	@Type(value = Types.INTEGER)
	private Integer noticeTimes;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 变更人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;
}