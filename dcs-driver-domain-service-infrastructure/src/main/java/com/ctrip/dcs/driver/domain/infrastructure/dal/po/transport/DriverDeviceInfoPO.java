package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Types;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_device_info")
public class DriverDeviceInfoPO implements DalPojo {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    /**
     * uid
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 客户端唯一标识
     */
    @Column(name = "cid")
    @Type(value = Types.VARCHAR)
    private String cid;

    /**
     * 最近登录的客户端id
     */
    @Column(name = "app_id")
    @Type(value = Types.VARCHAR)
    private String appId;

    /**
     * 最近登录的客户端版本
     */
    @Column(name = "app_version")
    @Type(value = Types.VARCHAR)
    private String appVersion;

    /**
     * 最近登录的客户端rn版本
     */
    @Column(name = "rn_version")
    @Type(value = Types.VARCHAR)
    private String rnVersion;

    /**
     * 端上语言设置
     */
    @Column(name = "local")
    @Type(value = Types.VARCHAR)
    private String local;

    /**
     * 手机操作系统类型，android，ios
     */
    @Column(name = "os_type")
    @Type(value = Types.VARCHAR)
    private String osType;

    /**
     * 最近登录的手机系统版本
     */
    @Column(name = "os_version")
    @Type(value = Types.VARCHAR)
    private String osVersion;

    /**
     * 最近端活跃时间
     */
    @Column(name = "active_time")
    @Type(value = Types.TIMESTAMP)
    private LocalDateTime activeTime;

    /**
     * 最近一次登陆时间
     */
    @Column(name = "login_time")
    @Type(value = Types.TIMESTAMP)
    private LocalDateTime loginTime;

    /**
     * login_account
     */
    @Column(name = "login_account")
    @Type(value = Types.VARCHAR)
    private String loginAccount;

    /**
     * 最近一次登录方式
     */
    @Column(name = "login_type")
    @Type(value = Types.INTEGER)
    private Integer loginType;

}
