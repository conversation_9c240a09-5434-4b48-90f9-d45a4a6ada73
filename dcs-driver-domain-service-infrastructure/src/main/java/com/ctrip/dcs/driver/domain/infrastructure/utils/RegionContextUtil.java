package com.ctrip.dcs.driver.domain.infrastructure.utils;
/*
作者：pl.yang
创建时间：2023/11/13-15:56-2023
*/


import com.ctrip.framework.foundation.Foundation;
import com.ctrip.framework.foundation.spi.provider.ServerProvider;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.exception.BizException;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RegionContextUtil
 * @Package com.ctrip.igt.userservice.util
 * @Description: (用一句话描述该文件做什么)
 * @date 2023/11/13 15:56
 */

public class RegionContextUtil {
    public static final Logger logger = LoggerFactory.getLogger(RegionContextUtil.class);



    public static void checkRegionRedisContext() {
        //如果是 环境不是 aws和sha 能就会报错，目前线上 就两个环境 sha 和aws
        if(isFatReginContext()||isLocalReginContext()||RegionContextUtil.isSHAReginContext()||RegionContextUtil.isSINReginContext()){
            return;
        }
        throw new BizException("The current cluster does not support redis");
    }
    public static boolean isLocalReginContext() {
        ServerProvider serverProvider = Foundation.server();
        String region = serverProvider.getRegion();
        logger.info("dataCenter", region);
        return StringUtils.equalsIgnoreCase("local", region);
    }
    public static boolean isFatReginContext() {
        ServerProvider serverProvider = Foundation.server();
        String region = serverProvider.getRegion();
        logger.info("dataCenter", region);
        return StringUtils.equalsIgnoreCase("ntg", region);
    }

    //当前集权是否是 上海集群  http://conf.ctripcorp.com/pages/viewpage.action?pageId=980494831#id-1.1%E4%BA%91%E4%B8%8A%E4%B8%80%E4%BA%9B%E5%9F%BA%E7%A1%80%E6%A6%82%E5%BF%B5-Region/AZ%E6%A6%82%E5%BF%B5
    public static boolean isSHAReginContext() {
        ServerProvider serverProvider = Foundation.server();
        String region = serverProvider.getRegion();
        logger.info("dataCenter", region);
        return StringUtils.equalsIgnoreCase("sha", region);
    }

    //当前集群是否是 aws环境
    public static boolean isSINReginContext() {
        ServerProvider serverProvider = Foundation.server();
        String region = serverProvider.getRegion();
        logger.info("dataCenter", region);
        //这里 从sin修改成 sgp,公司新加坡集群 从aws机房变成了 阿里服务器
        return StringUtils.equalsIgnoreCase("sgp", region);
    }
}
