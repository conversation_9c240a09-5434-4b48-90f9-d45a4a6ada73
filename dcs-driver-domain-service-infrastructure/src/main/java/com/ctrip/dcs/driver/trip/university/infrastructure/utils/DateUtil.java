package com.ctrip.dcs.driver.trip.university.infrastructure.utils;

import com.google.common.base.Strings;
import com.google.common.collect.Maps;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.time.format.DateTimeFormatter;

public class DateUtil {
  private static Map<String, DateTimeFormatter> FORMATTER_CACHE_NEW = Maps.newConcurrentMap();

  public static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

  public static Timestamp string2Timestamp(String time, String format) {
    if (Strings.isNullOrEmpty(time) || Strings.isNullOrEmpty(format)) {
      return null;
    }
    try {
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
      return new Timestamp(simpleDateFormat.parse(time).getTime());
    } catch (Exception e) {
      e.printStackTrace();
    }
    return null;
  }

  public static String formatDate(LocalDateTime localDateTime, String pattern) {
    DateTimeFormatter formatter = getFormatter(pattern);
    return localDateTime.format(formatter);
  }

  private static DateTimeFormatter getFormatter(String pattern) {
    DateTimeFormatter formatter = FORMATTER_CACHE_NEW.get(pattern);
    if (formatter == null) {
      formatter = DateTimeFormatter.ofPattern(pattern);
      FORMATTER_CACHE_NEW.put(pattern, formatter);
    }
    return formatter;
  }
}
