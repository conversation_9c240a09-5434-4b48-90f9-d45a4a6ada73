package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZhiXingLiUserResultResponse {
  private int code;
  private boolean success;
  private String message;
  private ZhiXingLiUserResult data;

  public boolean isSuccess() {
    return success && Objects.equals(code, TripUniversityConstants.SUCCESS_CODE);
  }

}
