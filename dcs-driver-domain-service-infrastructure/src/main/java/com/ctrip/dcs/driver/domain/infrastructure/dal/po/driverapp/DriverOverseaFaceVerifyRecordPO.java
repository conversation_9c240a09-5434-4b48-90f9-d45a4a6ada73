package com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Getter
@Setter
@Entity
@Database(name = "dcsdriverappdb")
@Builder
@Table(name = "driver_oversea_face_verify_record")
public class DriverOverseaFaceVerifyRecordPO implements DalPojo {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    @Column(name = "driver_type")
    @Type(value = Types.INTEGER)
    private Integer driverType;

    @Column(name = "driver_uid")
    @Type(value = Types.VARCHAR)
    private String driverUid;

    @Column(name = "verify_unique_no")
    @Type(value = Types.VARCHAR)
    private String verifyUniqueNo;

    @Column(name = "verify_img_type")
    @Type(value = Types.INTEGER)
    private Integer verifyImgType;

    @Column(name = "verify_img_standby_type")
    @Type(value = Types.INTEGER)
    private Integer verifyImgStandbyType;

    @Column(name = "verify_extend_info")
    @Type(value = Types.VARCHAR)
    private Integer verifyExtendInfo;


    @Column(name = "verify_result_code")
    @Type(value = Types.VARCHAR)
    private String verifyResultCode;

    @Column(name = "provider_data_location")
    @Type(value = Types.VARCHAR)
    private String providerDataLocation;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 变更时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

}
