package com.ctrip.dcs.driver.domain.infrastructure.model.guidesupply;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class GuideExamScoreModel {

    private Long id;

    private String examAccountId;

    private String applySubject;

    private BigDecimal examScore;

    private String completeTime;

    private BigDecimal timeZone;

    private Integer examIsPassed;

    private String datachangeCreatetime;
}
