package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import com.google.common.collect.Lists;
import lombok.SneakyThrows;

import java.lang.reflect.Field;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @param <T>
 */
public class ZhiXLDTO<T> {

    private List<String> fields;

    private  T data;

    public List<String> getFields() {
        return fields;
    }

    public void setFields(List<String> fields) {
        this.fields = fields;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> ZhiXLDTO<T> build(List<String> fields, T t){
        ZhiXLDTO zhiXLDTO = new ZhiXLDTO();
        zhiXLDTO.setFields(fields);
        zhiXLDTO.setData(t);
        return zhiXLDTO;
    }

    @SneakyThrows
    public static List<String> getFieldValues(List<String> whiteList, Object object){
        List<String> fieldValues = Lists.newArrayList();
        Field[] fields = object.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            if (!whiteList.contains(field.getName())) {
                continue;
            }
            Object value = field.get(object);

            if (value != null) {
                fieldValues.add(field.getName());
            }
        }

       return fieldValues;
    }
}
