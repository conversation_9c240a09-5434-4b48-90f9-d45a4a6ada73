package com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2025-03-26
 */
@Getter
@Setter
@Entity
@Database(name = "dcsdriverappdb")
@Table(name = "driver_car_inspection_record")
public class DriverCarInspectionRecordPO implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机id
     */
    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @Type(value = Types.VARCHAR)
    private String taskId;

    /**
     * 任务子项目key
     */
    @Column(name = "task_step_key")
    @Type(value = Types.VARCHAR)
    private String taskStepKey;

    /**
     * 任务子项目值
     */
    @Column(name = "task_step_value")
    @Type(value = Types.VARCHAR)
    private String taskStepValue;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}