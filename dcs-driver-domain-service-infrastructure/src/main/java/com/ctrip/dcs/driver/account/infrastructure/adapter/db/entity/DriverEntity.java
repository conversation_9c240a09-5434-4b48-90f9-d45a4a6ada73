package com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity;

import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Types;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "drv_driver")
@Database(name = "dcstransportdb_w")
public class DriverEntity {

    @Id
    @Column(name = "drv_id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    Long driverId;

    @Column(name = "drv_name")
    @Type(value = Types.VARCHAR)
    String driverName;

    @Column(name = "supplier_id")
    @Type(value = Types.BIGINT)
    Long supplierId;

    @Column(name = "drv_language")
    @Type(value = Types.VARCHAR)
    String driverLanguage;

    @Column(name = "country_id")
    @Type(value = Types.BIGINT)
    Long countryId;

    @Column(name = "city_id")
    @Type(value = Types.BIGINT)
    Long cityId;

    @Column(name = "igt_code")
    @Type(value = Types.VARCHAR)
    String phonePrefix;

    @Column(name = "drv_phone")
    @Type(value = Types.VARCHAR)
    String phoneNumber;

    @Column(name = "drv_status")
    @Type(value = Types.BIGINT)
    Integer driverStatus;

    @Column(name = "active")
    @Type(value = Types.BIGINT)
    Integer driverActive;

}