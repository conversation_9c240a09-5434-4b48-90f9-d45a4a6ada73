package com.ctrip.dcs.driver.domain.infrastructure.utils;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.security.Security;
import java.util.Base64;

public class ExamAESUtils {
  private static final Logger logger = LoggerFactory.getLogger(ExamAESUtils.class);


  public ExamAESUtils() {

  }

  static {
    // PKCS7Padding
    if (Security.getProvider(BouncyCastleProvider.PROVIDER_NAME) == null)
      Security.addProvider(new BouncyCastleProvider());
  }

  public static String encrypt(String content, String SK, String IV) {
    try {
      Cipher cipher = Cipher.getInstance("AES/CBC/PKCS7Padding");
      SecretKey secretKey = new SecretKeySpec(SK.getBytes(), "AES");
      IvParameterSpec iv = new IvParameterSpec(IV.getBytes());
      cipher.init(Cipher.ENCRYPT_MODE, secretKey, iv);
      byte[] encrypted = cipher.doFinal(content.getBytes());
      String encryptedStr = Base64.getEncoder().encodeToString(encrypted);
      return encryptedStr;
    } catch (Exception var5) {
      logger.warn(var5);
      return null;
    }
  }

  public static String encode(String content) {
    try {
      String result = URLEncoder.encode(content, "UTF-8");
      return result;
    } catch (Exception var5) {
      logger.warn(var5);
      return null;
    }
  }
}
