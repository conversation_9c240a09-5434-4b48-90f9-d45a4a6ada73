package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;


import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "drv_driver_order_push_config")
public class DriverOrderPushConfigPO implements DalPojo {
	
	/**
	 * 主键
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;
	
	/**
	 * 司机id
	 */
	@Column(name = "drv_id")
	@Type(value = Types.BIGINT)
	private Long drvId;
	
	/**
	 * 服务时间
	 */
	@Column(name = "service_time")
	@Type(value = Types.VARCHAR)
	private String serviceTime;
	
	/**
	 * 订单类型：1717接机，1718送机，1617接站，1618送站，appoint预约打车
	 */
	@Column(name = "order_typs")
	@Type(value = Types.VARCHAR)
	private String orderTyps;
	
	/**
	 * 播报状态
	 */
	@Column(name = "order_push_status")
	@Type(value = Types.BIT)
	private Boolean orderPushStatus;
	
	/**
	 * 创建人
	 */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;
	
	/**
	 * 修改人
	 */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;
	
	/**
	 * 创建时间
	 */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;
	
	/**
	 * 最后更新时间
	 */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;
	
	/**
	 * 距离司机公里数
	 */
	@Column(name = "drv_order_distance")
	@Type(value = Types.INTEGER)
	private Integer drvOrderDistance;
	
	/**
	 * 订单车型
	 */
	@Column(name = "drv_intend_vehicle_type")
	@Type(value = Types.VARCHAR)
	private String drvIntendVehicleType;

	/**
	 * 司机端设置语言
	 */
	@Column(name = "drv_language")
	@Type(value = Types.VARCHAR)
	private String drvLanguage;
}
