package com.ctrip.dcs.driver.account.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2024-03-27
 */
@Data
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "account_mapper")
public class AccountMapperPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 携程uid
     */
	@Column(name = "uid")
	@Type(value = Types.VARCHAR)
	private String uid;

    /**
     * 业务来源 司机：Driver，向导：Guide
     */
	@Column(name = "source")
	@Type(value = Types.VARCHAR)
	private String source;

    /**
     * 业务id
     */
	@Column(name = "source_id")
	@Type(value = Types.VARCHAR)
	private String sourceId;

	/**
	 * 是否有效
	 */
	@Column(name = "is_valid")
	@Type(value = Types.INTEGER)
	private Integer isValid;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * udl
	 */
	@Column(name = "provider_data_location")
	@Type(value = Types.VARCHAR)
	private String providerDataLocation;

}
