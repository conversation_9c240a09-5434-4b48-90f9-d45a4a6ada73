package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum;
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.ProbabilityDto;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormWelfareDetailInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormWelfareInfo;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */

@Component
public class WelfareConfig {

    private List<FormWelfareDetailInfo> diamondList;
    private List<FormWelfareDetailInfo> platinumList;

    @QConfig("welfare_config.json")
    public void welfareConfigChange(String value) {
        FormWelfareInfo form = JacksonUtil.deserialize(value, FormWelfareInfo.class);
        diamondList = form.getDiamondList().stream().sorted(Comparator.comparing(FormWelfareDetailInfo::getProbability).reversed()).collect(Collectors.toList());
        platinumList = form.getPlatinumList().stream().sorted(Comparator.comparing(FormWelfareDetailInfo::getProbability).reversed()).collect(Collectors.toList());
    }

    public List<ProbabilityDto<BigDecimal>> getProbabilityList(LevelEnum levelEnum) {
        List<ProbabilityDto<BigDecimal>> list = new ArrayList<>();
        List<FormWelfareDetailInfo> formList = choiceByRightType(levelEnum);
        for (int i = 0; i < formList.size(); i++) {
            list.add(ProbabilityDto.<BigDecimal>builder().id(i+1).entity(formList.get(i).getWelfare()).ProbabilityValue(formList.get(i).getProbability()).build());
        }
        return list;
    }

    private List<FormWelfareDetailInfo> choiceByRightType(LevelEnum levelEnum) {
        switch (levelEnum) {
            case LEVEL_TYPE_PLATINUM:
                return platinumList;
            case LEVEL_TYPE_DIAMOND:
                return diamondList;
            default:
                return Collections.emptyList();
        }
    }
}
