package com.ctrip.dcs.driver.domain.infrastructure.adapter.verify;

import com.ctrip.dcs.driver.domain.infrastructure.value.DriverCarInspectionRecordDO;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO;
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum;

import java.util.List;

public interface DriverTaskAdapter {

    DriverTaskRecordDO queryDriverTaskByTaskId(long driverId, String taskId);

    DriverTaskRecordDO queryDriverTaskByDriverOrderId(long driverId, String driverOrderId);

    int insertTaskRecord(DriverTaskRecordDO driverTaskRecord);

    void updateTaskStatus(String taskId, DriverTaskStatusEnum newTaskStatus);

    void updateTaskValidEndTime(String taskId, int expireMinutes);

    List<DriverCarInspectionRecordDO> queryCarInspectionInfoList(String taskId);

    void updateCarInspectionInfo(List<DriverCarInspectionRecordDO> carInspectionRecordList);

}
