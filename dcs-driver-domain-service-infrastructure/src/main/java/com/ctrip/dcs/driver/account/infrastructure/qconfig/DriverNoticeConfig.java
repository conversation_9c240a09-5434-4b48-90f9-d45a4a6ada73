package com.ctrip.dcs.driver.account.infrastructure.qconfig;

import com.ctrip.dcs.driver.account.infrastructure.value.ImproveServiceQualityConfig;
import com.ctrip.dcs.driver.account.infrastructure.value.emil.EmailConfigInfo;
import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;

@Getter
@Setter
@Component
public class DriverNoticeConfig {

    private List<ImproveServiceQualityConfig> improveServiceQualityConfigs;

    @QConfig("driver.notice.json")
    private void onChange(DriverNoticeConfig config) {
        this.improveServiceQualityConfigs = config.getImproveServiceQualityConfigs();
    }
}