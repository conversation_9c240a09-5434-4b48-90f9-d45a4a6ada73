package com.ctrip.dcs.driver.account.infrastructure.qconfig;

import com.ctrip.dcs.driver.account.infrastructure.value.BindFailedConfigDto;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.lang.reflect.Type;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Getter
@Setter
@Component
public class DriverWalletInfoConfig {
  public static final Logger logger = LoggerFactory.getLogger(DriverWalletInfoConfig.class);
  //司机钱包新版本支付商户8001005密钥
  private String walletPayMerid;
  private String walletPayKeyid;
  private String walletKmsDriverPrivateToken;
  private String walletKmsPaymentPublicToken;
  private int connectTimeout;
  private int requestTimeout;
  private int socketTimeout;
  private volatile ConcurrentHashMap<String, String> settlementResultCodeMap = new ConcurrentHashMap<>();
  private volatile ConcurrentHashMap<String, BindFailedConfigDto> overseaBindCardFailReasonMap = new ConcurrentHashMap<>();

  @QConfig("driverWalletInfo.properties")
  public void onChange(Map<String, String> map) {
    this.setWalletPayMerid(map.getOrDefault("wallet_pay_merId", Strings.EMPTY).trim());
    this.setWalletPayKeyid(map.getOrDefault("wallet_pay_keyid", Strings.EMPTY).trim());
    this.setWalletKmsPaymentPublicToken(map.getOrDefault("wallet_kms_payment_public_token", Strings.EMPTY).trim());
    this.setWalletKmsDriverPrivateToken(map.getOrDefault("wallet_kms_driver_private_token", Strings.EMPTY).trim());
    this.setConnectTimeout(Integer.parseInt(map.getOrDefault("connectTimeout", "3000").trim()));
    this.setRequestTimeout(Integer.parseInt(map.getOrDefault("requestTimeout", "3000").trim()));
    this.setSocketTimeout(Integer.parseInt(map.getOrDefault("socketTimeout", "3000").trim()));
    String settlementResultCodeStr = map.getOrDefault("settlementResultCode", Strings.EMPTY).trim();
    if(StringUtils.isNotEmpty(settlementResultCodeStr)){
      this.settlementResultCodeMap = JacksonUtil.deserialize(settlementResultCodeStr.trim(), ConcurrentHashMap.class);
    } else {
      this.settlementResultCodeMap.clear();
    }

    String oversea_bindcard_fail_reason = map.getOrDefault("oversea_bindcard_fail_reason", Strings.EMPTY).trim();
    if (StringUtils.isNotEmpty(oversea_bindcard_fail_reason)) {
      this.overseaBindCardFailReasonMap = JacksonUtil.deserialize(oversea_bindcard_fail_reason.trim(), new TypeReference<ConcurrentHashMap<String, BindFailedConfigDto>>() {
        @Override
        public Type getType() {
          return super.getType();
        }
      });
    } else {
      this.overseaBindCardFailReasonMap.clear();
    }
  }

  public String getSettlementResultCode(String code) {
    if(Objects.isNull(this.settlementResultCodeMap) || this.settlementResultCodeMap.isEmpty() || StringUtils.isBlank(code)){
      return "617";
    }
    return this.settlementResultCodeMap.getOrDefault(code, "617");
  }

  public String getOverseaBindCardFailReasonSharkKey(String code) {
    if (Objects.isNull(this.overseaBindCardFailReasonMap) || this.overseaBindCardFailReasonMap.isEmpty() || StringUtils.isBlank(code)) {
      logger.error("OverseaBindCardFailReason config is empty");
      return "binding.info.invalid";
    }
    BindFailedConfigDto bindFailedConfigDto = this.overseaBindCardFailReasonMap.get(code);
    if (bindFailedConfigDto != null) {
      return bindFailedConfigDto.getSharkKey();
    }
    return "binding.info.invalid";
  }
  public String getOverseaBindCardFailReasonErrorCode(String code) {
    if (Objects.isNull(this.overseaBindCardFailReasonMap) || this.overseaBindCardFailReasonMap.isEmpty() || StringUtils.isBlank(code)) {
      logger.error("OverseaBindCardFailReason config is empty");
      return "15700";
    }
    BindFailedConfigDto bindFailedConfigDto = this.overseaBindCardFailReasonMap.get(code);
    if (bindFailedConfigDto != null) {
      return bindFailedConfigDto.getCode();
    }
    return "15700";
  }
}
