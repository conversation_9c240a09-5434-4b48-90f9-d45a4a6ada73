package com.ctrip.dcs.driver.domain.infrastructure.dal.dao;


import com.ctrip.dcs.driver.domain.infrastructure.dal.po.QuestionResultPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;


@Dal("dcsquestiondb")
public interface QuestionResultDao extends DalRepository<QuestionResultPO> {

    /**
     * 查询指定范围内，负面评论
     *
     * @param questionIds
     * @param startTime
     * @param endTime
     * @return
     */
    @Sql("select * from question_result where scene_code in ('DcsQuestion','DcsSpecialQuestion') and answer_nature = 2 and  question_id in (?) and  datachange_createtime >=  ? and datachange_createtime <= ?")
    List<QuestionResultPO> queryNegativeResult(List<Long> questionIds, String startTime, String endTime);
}
