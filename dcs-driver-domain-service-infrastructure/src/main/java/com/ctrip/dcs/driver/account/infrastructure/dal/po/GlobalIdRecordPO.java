package com.ctrip.dcs.driver.account.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.*;
import java.sql.Types;
import java.time.LocalDateTime;

@Data
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "global_id_record")
public class GlobalIdRecordPO implements DalPojo {

    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 国家码
     */
    @Column(name = "country_code")
    @Type(value = Types.VARCHAR)
    private String countryCode;

    /**
     * 手机号
     */
    @Column(name = "phone_number")
    @Type(value = Types.VARCHAR)
    private String phoneNumber;

    /**
     * 业务来源 司机：Driver，向导：Guide，司导：DriverGuide
     */
    @Column(name = "source")
    @Type(value = Types.VARCHAR)
    private String source;


    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private LocalDateTime datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private LocalDateTime datachangeLasttime;

}