package com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-11-27
 */
@Getter
@Setter
@Entity
@Database(name = "dcsguidesupplydb")
@Table(name = "guide_info")
public class GuideInfoPO implements DalPojo {
  /**
   * guide_id
   */
  @Id
  @Column(name = "guide_id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long guideId;
}
