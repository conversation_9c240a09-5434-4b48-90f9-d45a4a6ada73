package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@QMapConfig("kms.token.properties")
public class KmsTokenConfig {

  private String exexmKeyToken;

  private String exexmIvToken;

  private String exexmInterfaceAccessToken;

  public String getExexmKeyToken() {
    return exexmKeyToken;
  }

  public void setExexmKeyToken(String exexmKeyToken) {
    this.exexmKeyToken = exexmKeyToken;
  }

  public String getExexmIvToken() {
    return exexmIvToken;
  }

  public void setExexmIvToken(String exexmIvToken) {
    this.exexmIvToken = exexmIvToken;
  }

  public String getExexmInterfaceAccessToken() {
    return exexmInterfaceAccessToken;
  }

  public void setExexmInterfaceAccessToken(String exexmInterfaceAccessToken) {
    this.exexmInterfaceAccessToken = exexmInterfaceAccessToken;
  }
}
