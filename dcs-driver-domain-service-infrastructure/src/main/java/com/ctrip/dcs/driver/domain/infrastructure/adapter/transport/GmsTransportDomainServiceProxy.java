package com.ctrip.dcs.driver.domain.infrastructure.adapter.transport;

import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideAllInfoRequestType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideAllInfoResponseType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideBaseInfoByIdRequestType;
import com.ctrip.dcs.gms.guide.supply.domain.api.guide.QueryGuideBaseInfoByIdResponseType;
import com.ctrip.dcs.guide.supply.domain.api.GmsTransportDomainServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = GmsTransportDomainServiceClient.class, format = "json")
public interface GmsTransportDomainServiceProxy {

  QueryGuideBaseInfoByIdResponseType queryGuideBaseInfoById(QueryGuideBaseInfoByIdRequestType request);

  QueryGuideAllInfoResponseType queryGuideAllInfo(QueryGuideAllInfoRequestType request);
}
