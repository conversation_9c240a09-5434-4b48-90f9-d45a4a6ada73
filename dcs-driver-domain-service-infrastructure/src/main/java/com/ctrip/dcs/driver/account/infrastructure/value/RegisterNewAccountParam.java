package com.ctrip.dcs.driver.account.infrastructure.value;

import lombok.Data;

@Data
public class RegisterNewAccountParam {

    private String source;

    /**
     * 司机id/向导id/司导id
     */
    private String sourceId;

    /**
     * 手机国家码，必填
     */
    private String countryCode;

    /**
     * 注册手机号，必填
     */
    private String phoneNumber;

    /**
     * 绑定邮箱
     */
    private String email;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 派安盈账户id，可为空
     */
    private String payoneerAccountId;

    /**
     * 是否境外，true：境外；false：境内
     */
    private Boolean isOversea;

    /**
     * 仅上线初期，同步司机存量账户信息时，境内司机必填，数据搬迁完毕即可废弃
     */
    private String ppmAccountId;

    /**
     * 是否有效，即是否可登录
     */
    private Boolean valid;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 城市id
     */
    private Long cityId;
}
