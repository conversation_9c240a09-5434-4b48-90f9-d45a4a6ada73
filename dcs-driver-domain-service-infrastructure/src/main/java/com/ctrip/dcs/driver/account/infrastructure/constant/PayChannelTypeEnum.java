package com.ctrip.dcs.driver.account.infrastructure.constant;
/*
作者：pl.yang
创建时间：2025/5/8-下午2:46-2025
*/


import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: PayChannelTypeEnum
 * @Package com.ctrip.dcs.driver.account.infrastructure.constant
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/8 下午2:46
 */


@Getter
public enum PayChannelTypeEnum {
    //支付方式 境内：1 ppm
    //境外：2 境外派安盈 ：4 境外的易宝
    PPM(1),
    PAYONEER(2),
    YEEPAY(4);


    PayChannelTypeEnum(int code) {
        this.code = code;
    }

    private int code;

    public static PayChannelTypeEnum of(String id) {
        for (PayChannelTypeEnum payChannelTypeEnum : PayChannelTypeEnum.values()) {
            if (StringUtils.equals(payChannelTypeEnum.getCode() + "", id)) {
                return payChannelTypeEnum;
            }
        }
        return null;

    }

}
