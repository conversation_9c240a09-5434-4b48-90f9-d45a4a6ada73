package com.ctrip.dcs.driver.domain.infrastructure.utils;

import com.ctrip.infosec.kms.KmsUtil;
import com.ctrip.infosec.kms.pojo.KmsKey;
import com.ctrip.infosec.kms.pojo.KmsResponse;

import java.util.Objects;

public final class KMSUtils {

  private KMSUtils(){}

  public static final class KmsException extends Exception {
    public KmsException(String message) {
      super(message);
    }
  }

  public static String key(String token) throws KmsException {
    KmsResponse<KmsKey> keyKmsResponse = KmsUtil.getKey(token);

    if (Objects.isNull(keyKmsResponse) || keyKmsResponse.getCode() != 0) {
      throw new KmsException("KMS initialize error");
    }
    return keyKmsResponse.getResult().getKeyValue();
  }
}
