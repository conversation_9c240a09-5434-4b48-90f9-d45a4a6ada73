package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Types;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_app_guidance_control")
public class DriverAppGuidanceControlPO implements DalPojo {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * uid
     */
    @Column(name = "uid")
    @Type(value = Types.VARCHAR)
    private String uid;

    /**
     * 提醒业务类型（首页司机分引导，等级更引导）
     */
    @Column(name = "guidance_type")
    @Type(value = Types.VARCHAR)
    private String guidanceType;

    /**
     * 点击完成时间
     */
    @Column(name = "click_time")
    @Type(value = Types.TIMESTAMP)
    private LocalDateTime clickTime;

}
