package com.ctrip.dcs.driver.domain.infrastructure.adapter.tour;

import com.ctrip.frt.product.soa.DriverInfoRequestType;
import com.ctrip.frt.product.soa.DriverInfoResponseType;
import com.ctrip.frt.product.soa.ProductBasicServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = ProductBasicServiceClient.class, format = "json")
public interface ProductBasicServiceProxy {

  /**
   * 查询司导
   */
  DriverInfoResponseType getDriverInfo(DriverInfoRequestType request);
}
