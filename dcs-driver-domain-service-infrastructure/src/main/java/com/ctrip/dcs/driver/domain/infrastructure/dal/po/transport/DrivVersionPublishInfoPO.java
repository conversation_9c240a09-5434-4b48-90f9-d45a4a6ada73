package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-04-13
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driv_version_publish_record")
public class DrivVersionPublishInfoPO implements DalPojo {

    /**
     * 空
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 备注
     */
    @Column(name = "remark")
    @Type(value = Types.VARCHAR)
    private String remark;

    /**
     * 发布包版本8.33.9
     */
    @Column(name = "package_version")
    @Type(value = Types.VARCHAR)
    private String packageVersion;

    /**
     * 内部版本 833009000
     */
    @Column(name = "inner_version")
    @Type(value = Types.VARCHAR)
    private String innerVersion;

    /**
     * 操作系统类型，0:安卓，1:iOS
     */
    @Column(name = "platform_type")
    @Type(value = Types.INTEGER)
    private Integer platformType;

    /**
     * 是否全量，0:否，1:是
     */
    @Column(name = "is_full_publish")
    @Type(value = Types.BIT)
    private Boolean isFullPublish;

    /**
     * 灰度城市列表
     */
    @Column(name = "gray_city_list")
    @Type(value = Types.VARCHAR)
    private String grayCityList;

    /**
     * 更新文案
     */
    @Column(name = "update_desc")
    @Type(value = Types.VARCHAR)
    private String updateDesc;

    /**
     * 最后修改人
     */
    @Column(name = "last_update_user")
    @Type(value = Types.VARCHAR)
    private String lastUpdateUser;

    /**
     * 最后修改时间
     */
    @Column(name = "last_update_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp lastUpdateTime;

    /**
     * 下载链接
     */
    @Column(name = "download_url")
    @Type(value = Types.VARCHAR)
    private String downloadUrl;

    /**
     * 是否H5下载地址，0:否，1:是
     */
    @Column(name = "is_h5_download_uri")
    @Type(value = Types.BIT)
    private Boolean isH5DownloadUri;

    /**
     * 是否最小登录版本，0:否，1:是
     */
    @Column(name = "is_lowest_version")
    @Type(value = Types.BIT)
    private Boolean isLowestVersion;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;

    /**
     * 更新文案（英语）
     */
    @Column(name = "update_desc_en")
    @Type(value = Types.VARCHAR)
    private String updateDescEn;

    /**
     * 境内外区分，0:境内，1:境外
     */
    @Column(name = "app_type")
    @Type(value = Types.INTEGER)
    private Integer appType;

    /**
     * 渠道信息
     */
    @Column(name = "channel_info")
    @Type(value = Types.VARCHAR)
    private String channelInfo;
}
