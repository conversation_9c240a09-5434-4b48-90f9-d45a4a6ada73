package com.ctrip.dcs.driver.domain.infrastructure.constant;

public enum ExamApplyResultEnum {
    APPLY_SUCCESS(1, "applySuccess"),
    APPLY_FAILED(0, "applyFailed");

    private int code;
    private String name;

    ExamApplyResultEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExamApplyResultEnum getByCode(Integer code) {
        if(code == null){
            return APPLY_FAILED;
        }
        for (ExamApplyResultEnum examApplyResultEnum : ExamApplyResultEnum.values()) {
            if (examApplyResultEnum.code == code) {
                return examApplyResultEnum;
            }
        }
        return APPLY_FAILED;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
