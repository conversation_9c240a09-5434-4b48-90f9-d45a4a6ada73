package com.ctrip.dcs.driver.trip.university.infrastructure.dal.repository;

import com.ctrip.dcs.driver.trip.university.infrastructure.dal.po.DriverSyncDrvInfoToTripUniversityFailedLogPO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityQueryCondition;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.base.SQLArg;
import com.ctrip.platform.dal.dao.base.SQLArgList;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import lombok.Getter;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Getter
public class TripUniversitySyncFailedLogRepository {

  private DalTableOperations<DriverSyncDrvInfoToTripUniversityFailedLogPO> dalRepository;

  public TripUniversitySyncFailedLogRepository() {
    this.dalRepository = DalOperationsFactory.getDalTableOperations(DriverSyncDrvInfoToTripUniversityFailedLogPO.class);
  }

  @SneakyThrows
  public List<DriverSyncDrvInfoToTripUniversityFailedLogPO> querySyncFailedLog(
    DriverSyncDrvInfoToTripUniversityQueryCondition condition) {
    StringBuilder sql = new StringBuilder();
    SQLArgList args = SQLArg.list();
    sql.append("select * from driver_sync_drv_info_to_trip_university_failed_log ");

    sql.append(" where id > ?");
    args.add(condition.getSortId());

    if (condition.getFrom() != null) {
      sql.append(" and  datachange_lasttime >= ? ");
      args.add(condition.getFrom());
    }

    if (condition.getTo() != null) {
      sql.append(" and datachange_lasttime <= ?");
      args.add(condition.getTo());
    }

    if (CollectionUtils.isNotEmpty(condition.getDrvIdList())) {
      sql.append(" and drv_id in (?) ");
      args.add(condition.getDrvIdList());
    }

    if (condition.getPagination() != null) {
      sql.append(" order by id asc ");
      sql.append(" limit ? ");
      args.add(condition.getPagination().getPageSize());
    }

    return dalRepository.query(sql.toString(), new DalHints(), args);
  }

}
