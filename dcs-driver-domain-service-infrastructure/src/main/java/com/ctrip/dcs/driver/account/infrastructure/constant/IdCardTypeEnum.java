package com.ctrip.dcs.driver.account.infrastructure.constant;
/*
作者：pl.yang
创建时间：2025/5/8-上午10:57-2025
*/

// 定义一个名为 IdCardTypeEnum 的枚举类，用于表示不同的身份证类型
public enum IdCardTypeEnum {
    // 身份证，代码为 1
    ID_CARD(1),
    // 护照，代码为 2
    PASSPORT(2),
    // 军官证，代码为 4
    OFFICER_CARD(4),
    // 回乡证，代码为 7
    HOME_RETURN_PERMIT(7),
    // 台胞证，代码为 8
    TAIWAN_COMPATRIOT_TRAVEL_PERMIT(8),
    // 港澳通行证，代码为 10
    HONG_KONG_MACAO_PASSPORT(10),
    // 国际海员证，代码为 11
    INTERNATIONAL_SEAMAN_CARD(11),
    // 外国人永久居留证，代码为 20
    FOREIGNER_PERMANENT_RESIDENCE_PERMIT(20),
    // 台湾通行证，代码为 22
    TAIWAN_PASSPORT(22),
    // 士兵证，代码为 23
    SOLDIER_CARD(23),
    // 临时身份证，代码为 24
    TEMPORARY_ID_CARD(24),
    // 户口簿，代码为 25
    HOUSEHOLD_REGISTER(25),
    // 警官证，代码为 26
    POLICE_OFFICER_CARD(26),
    // 出生证明，代码为 27
    BIRTH_CERTIFICATE(27),
    // 港澳居民居住证，代码为 28
    HONG_KONG_MACAO_RESIDENT_PERMIT(28),
    // 台湾居民居住证，代码为 29
    TAIWAN_RESIDENT_PERMIT(29),
    // 海外个人证件，代码为 70
    OVERSEAS_INDIVIDUAL_DOCUMENT(70),
    // 海外公司证件，代码为 71
    OVERSEAS_COMPANY_DOCUMENT(71),
    // 其它，代码为 99
    OTHER(99);

    // 身份证类型的代码
    private int code;

    // 枚举类的构造函数，用于初始化代码
    IdCardTypeEnum(int code) {
        this.code = code;
    }

    // 获取身份证类型代码的方法
    public int getCode() {
        return code;
    }

    // 根据代码查找对应的枚举实例的方法
    public static IdCardTypeEnum getById(int id) {
        for (IdCardTypeEnum type : values()) {
            if (type.getCode() == id) {
                return type;
            }
        }
        return null;
    }
    public static IdCardTypeEnum getByIdString(String id){
        for (IdCardTypeEnum type : values()) {
            if ((type.getCode()+"").equals(id)) {
                return type;
            }
        }
        return null;
    }

}
