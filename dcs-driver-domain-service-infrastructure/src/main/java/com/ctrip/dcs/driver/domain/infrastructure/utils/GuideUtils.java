package com.ctrip.dcs.driver.domain.infrastructure.utils;

public class GuideUtils {
    private static final String GUIDE_PREFIX_FORMAT = "69159644";
    public static final int GUIDE_TYPE = 2;

    public static boolean isGuide(long id, int type){
        if(id == 0){
            return false;
        }
        if(type == GUIDE_TYPE){
            return true;
        }
        return String.valueOf(id).startsWith(GUIDE_PREFIX_FORMAT);
    }

    public static String guideId(long guideId){
        return GUIDE_PREFIX_FORMAT + guideId;
    }

    public static long guideId(String targetId){
        if(isGuide(targetId)){
            return Long.parseLong(targetId.substring(GUIDE_PREFIX_FORMAT.length()));
        }
        return Long.parseLong(targetId);
    }

    public static boolean isGuide(String id){
        return id.startsWith(GUIDE_PREFIX_FORMAT);
    }
}
