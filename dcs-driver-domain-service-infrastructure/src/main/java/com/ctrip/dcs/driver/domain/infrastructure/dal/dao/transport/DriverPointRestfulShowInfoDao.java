package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverPointRestfulShowInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

/**
 * <AUTHOR>
 * @date 2023-02-13
 */
@<PERSON>("dcstransportdb_w")
public interface DriverPointRestfulShowInfoDao extends DalRepository<DriverPointRestfulShowInfoPO>{

	@Sql("select * from driver_point_restful_show_info where driver_id = ?")
	DriverPointRestfulShowInfoPO findOne(long driverId);
}
