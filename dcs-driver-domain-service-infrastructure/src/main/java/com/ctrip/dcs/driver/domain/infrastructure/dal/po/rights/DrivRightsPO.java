package com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-05-26
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driv_rights")
public class DrivRightsPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 权益定义ID
     */
	@Column(name = "rights_config_id")
	@Type(value = Types.BIGINT)
	private Long rightsConfigId;

    /**
     * 权益名称
     */
	@Column(name = "rights_name")
	@Type(value = Types.VARCHAR)
	private String rightsName;

    /**
     * 权益简介
     */
	@Column(name = "rights_desc")
	@Type(value = Types.VARCHAR)
	private String rightsDesc;

    /**
     * 城市ID
     */
	@Column(name = "city_id")
	@Type(value = Types.BIGINT)
	private Long cityId;

    /**
     * 月份索引，yyyy-MM
     */
	@Column(name = "month_idx")
	@Type(value = Types.VARCHAR)
	private String monthIdx;

    /**
     * 司机ID
     */
	@Column(name = "driv_id")
	@Type(value = Types.BIGINT)
	private Long drivId;

    /**
     * 司机等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
	@Column(name = "driv_level")
	@Type(value = Types.TINYINT)
	private Integer drivLevel;

    /**
     * 权益类型 1福利金 2改派 3城市王者 4初级投诉豁免卡 5高级投诉豁免卡 6每日提现
     */
	@Column(name = "rights_type")
	@Type(value = Types.TINYINT)
	private Integer rightsType;

    /**
     * 使用限制 0无限次
     */
	@Column(name = "use_limit")
	@Type(value = Types.INTEGER)
	private Integer useLimit;

    /**
     * 使用次数
     */
	@Column(name = "use_count")
	@Type(value = Types.INTEGER)
	private Integer useCount;

    /**
     * 权益状态 0 已发放 1已使用 2已过期 3已作废
     */
	@Column(name = "rights_status")
	@Type(value = Types.TINYINT)
	private Integer rightsStatus;

    /**
     * 权益生效开始时间,默认北京时间
     */
	@Column(name = "rights_strat_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp rightsStratTime;

    /**
     * 权益生效结束时间,默认北京时间
     */
	@Column(name = "rights_end_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp rightsEndTime;

    /**
     * 扩展字段 例如:福利金上限金额
     */
	@Column(name = "extend")
	@Type(value = Types.VARCHAR)
	private String extend;

    /**
     * 创建时间-权益发放时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 最后更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;
}