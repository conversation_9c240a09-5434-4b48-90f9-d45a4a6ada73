package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class DriverLevelGreyConfig {

    @Autowired
    private SystemQConfig systemQConfig;

    public Map<Long, LocalDate> cityMap = new HashMap<>();


    @QConfig("driverLevelGrey.json")
    public void onConfigChange(String value) {
        List<BizLevelGreyConfigEntity> configEntity = JacksonUtil.parseArray(value, BizLevelGreyConfigEntity.class);
        Map<Long, LocalDate> newCityMap = new HashMap<>();
        configEntity.stream().forEach(greyConfigEntity -> {
            String cityIdsStr = greyConfigEntity.getCityIds();
            if (com.dianping.cat.utils.StringUtils.isNotEmpty(cityIdsStr)) {
                greyConfigEntity.setCityList(ShoppingCollectionUtils.toList(cityIdsStr.split(","), (ShoppingFuncUtils.Func1<String, Long>) Long::parseLong).stream().distinct().collect(Collectors.toList()));
            }
            greyConfigEntity.getCityList().stream().forEach(city -> {
                newCityMap.put(city, LocalDateTimeUtils.localDate(greyConfigEntity.effectTime));
            });
        });
        this.cityMap = newCityMap;
    }

    public boolean canUseVacationRights(long cityId) {
        //不在灰度中
        if (!isInGrey(cityId)) {
            return true;
        }
        //是否在配置中
        if(Arrays.stream(systemQConfig.getString("vacation_rights_citys").split(",")).anyMatch(s->s.equals(String.valueOf(cityId)))){
            return true;
        }
        //在灰度中
        return this.cityMap.get(cityId).plusMonths(1L).with(TemporalAdjusters.firstDayOfMonth()).isAfter(LocalDate.now());
    }

    public Boolean isInGrey(long cityId) {
        LocalDate effectTime = this.cityMap.get(cityId);
        if (Objects.isNull(effectTime)) {
            return false;
        }
        return effectTime.isBefore(LocalDate.now()) || effectTime.isEqual(LocalDate.now());
    }

    public static class BizLevelGreyConfigEntity {
        private String cityIds;
        private String effectTime;
        private List<Long> cityList;

        public String getCityIds() {
            return cityIds;
        }

        public void setCityIds(String cityIds) {
            this.cityIds = cityIds;
        }

        public String getEffectTime() {
            return effectTime;
        }

        public void setEffectTime(String effectTime) {
            this.effectTime = effectTime;
        }

        public List<Long> getCityList() {
            return cityList;
        }

        public void setCityList(List<Long> cityList) {
            this.cityList = cityList;
        }
    }
}
