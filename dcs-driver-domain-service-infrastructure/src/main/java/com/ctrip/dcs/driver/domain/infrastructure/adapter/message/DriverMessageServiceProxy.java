package com.ctrip.dcs.driver.domain.infrastructure.adapter.message;

import com.ctrip.dcs.driver.message.api.DcsDriverMessageServiceClient;
import com.ctrip.dcs.driver.message.api.PushMessageRequestType;
import com.ctrip.dcs.driver.message.api.PushMessageResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = DcsDriverMessageServiceClient.class, format = "json")
public interface DriverMessageServiceProxy {
  PushMessageResponseType pushMessage(PushMessageRequestType request);
}
