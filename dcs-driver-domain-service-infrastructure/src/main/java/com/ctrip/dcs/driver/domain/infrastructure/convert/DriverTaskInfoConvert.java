package com.ctrip.dcs.driver.domain.infrastructure.convert;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverCarInspectionRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverTaskRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverCarInspectionRecordDO;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO;
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

public class DriverTaskInfoConvert {

    public static DriverCarInspectionRecordDO toDriverCarInspectionRecordDO(DriverCarInspectionRecordPO driverCarInspectionRecordPO) {
        DriverCarInspectionRecordDO driverCarInspectionRecord = new DriverCarInspectionRecordDO();
        driverCarInspectionRecord.setDriverId(ObjectUtils.defaultIfNull(driverCarInspectionRecordPO.getDriverId(), 0L));
        driverCarInspectionRecord.setTaskId(driverCarInspectionRecordPO.getTaskId());
        driverCarInspectionRecord.setTaskStepKey(driverCarInspectionRecordPO.getTaskStepKey());
        driverCarInspectionRecord.setTaskStepValue(driverCarInspectionRecordPO.getTaskStepValue());
        return driverCarInspectionRecord;
    }

    public static DriverTaskRecordDO toDriverTaskRecordDO(DriverTaskRecordPO driverTaskRecordPO) {
        DriverTaskRecordDO driverTaskRecord = new DriverTaskRecordDO();
        driverTaskRecord.setDriverId(ObjectUtils.defaultIfNull(driverTaskRecordPO.getDriverId(), 0L));
        driverTaskRecord.setTaskId(driverTaskRecordPO.getTaskId());
        driverTaskRecord.setTaskStatus(DriverTaskStatusEnum.ofValue(ObjectUtils.defaultIfNull(driverTaskRecordPO.getTaskStatus(), 0)));
        driverTaskRecord.setTaskPeriodWorkTime(driverTaskRecordPO.getTaskPeriodWorkTime());
        driverTaskRecord.setTaskValidEndTime(ObjectUtils.defaultIfNull(driverTaskRecordPO.getTaskValidEndTime(), null));
        driverTaskRecord.setCustomerOrderId(driverTaskRecordPO.getCustomerOrderId());
        driverTaskRecord.setDriverOrderId(driverTaskRecordPO.getDriverOrderId());
        driverTaskRecord.setCustomerOrderCarId(ObjectUtils.defaultIfNull(driverTaskRecordPO.getCustomerOrderCarId(), 0L));
        if(Objects.nonNull(driverTaskRecordPO.getDatachangeCreatetime())) {
            driverTaskRecord.setDataChangeCreateTime(LocalDateTime.ofInstant(driverTaskRecordPO.getDatachangeCreatetime().toInstant(), ZoneId.systemDefault()));
        }
        return driverTaskRecord;
    }
}