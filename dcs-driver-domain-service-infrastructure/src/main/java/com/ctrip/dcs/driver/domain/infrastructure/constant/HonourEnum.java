package com.ctrip.dcs.driver.domain.infrastructure.constant;

import org.owasp.csrfguard.util.Strings;

public class HonourEnum {

    private static final String SHARK_GOOD_ACTION = "goodaction";

    public enum MedalTypeEnum {
        SERVICEDAYS(10,"servicedays" , Strings.EMPTY, true),            //服务天数
        FINISHORDERS(20,"orders",  Strings.EMPTY, true),                //服务完单
        GOODCOMMENTS(30,"comments",  Strings.EMPTY, true),              //服务好评
        GOODMANNER(41,SHARK_GOOD_ACTION, "goodmanner",false),               //服务好态度佳
        GOODCAR(42,SHARK_GOOD_ACTION, "goodcar",false),                     //车内整洁
        GOODMAP(43,SHARK_GOOD_ACTION, "goodmap",false),                     //活地图认路
        GOODLUGGAGE(44,SHARK_GOOD_ACTION, "goodluggage",false),             //主动帮拿行李
        HONOURWALL(50,"honourwall",  Strings.EMPTY, false);             //荣誉勋章

        private int index;
        //类别名称：服务天数/服务完单/服务好评/行为勋章/荣誉勋章 大类
        private String typeCode;
        // serviceday1,order1,comment1,goodcar....
        private String code;
        // 是否有等级
        private boolean hasLevel;

        MedalTypeEnum(int index,String typeCode, String code, boolean hasLevel) {
            this.index = index;
            this.typeCode = typeCode;
            this.code = code;
            this.hasLevel = hasLevel;
        }

        public boolean isCommonType(){
            return this.hasLevel;
        }

        public boolean isGoogAction() {
            return !this.hasLevel && !this.equals(HONOURWALL);
        }

        public boolean isHonourWall() {
            return this.equals(HONOURWALL);
        }

        public String getTypeCode() {
            return typeCode.toLowerCase();
        }

        public boolean isHasLevel() {
            return hasLevel;
        }

        public String getCode() {
            return code;
        }

        public int getIndex() {
            return index;
        }
    }

    /**
     * order_cnt_medal    完单量勋章
     * order_day_medal    完单天数勋章
     * good_comment_medal 好评数勋章
     * tdhfwj_medal       态度好服务佳勋章
     * cnzj_medal         车内整洁标勋章
     * hdtrlz_medal       活地图认路准勋章
     * zdbnxl_medal       主动帮拿行李勋章
     * */
    public static HonourEnum.MedalTypeEnum mathMedalType(String medalType) {
        medalType = medalType.toLowerCase();
        switch (medalType){
            case "order_day_medal":
                return HonourEnum.MedalTypeEnum.SERVICEDAYS;
            case "order_cnt_medal":
                return HonourEnum.MedalTypeEnum.FINISHORDERS;
            case "good_comment_medal":
                return HonourEnum.MedalTypeEnum.GOODCOMMENTS;
            case "tdhfwj_medal":
                return HonourEnum.MedalTypeEnum.GOODMANNER;
            case "cnzj_medal":
                return HonourEnum.MedalTypeEnum.GOODCAR;
            case "hdtrlz_medal":
                return HonourEnum.MedalTypeEnum.GOODMAP;
            case "zdbnxl_medal":
                return HonourEnum.MedalTypeEnum.GOODLUGGAGE;
            default:
                return null;
        }
    }

    /**
     * 勋章CODE
     * */
    public static String bulidMedalCode(HonourEnum.MedalTypeEnum medalTypeEnum, long grade) {
        if(medalTypeEnum.isHasLevel()){
            return String.format("%s%s", medalTypeEnum.getTypeCode(), grade);
        }
        return medalTypeEnum.getCode();
    }
}
