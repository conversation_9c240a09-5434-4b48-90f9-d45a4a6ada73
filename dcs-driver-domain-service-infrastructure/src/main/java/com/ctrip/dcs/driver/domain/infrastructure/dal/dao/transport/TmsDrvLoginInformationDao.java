package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.TmsDrvLoginInformationPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

/**
 * <AUTHOR>
 * @date 2020-10-13
 */
@<PERSON>("dcstransportdb_w")
public interface TmsDrvLoginInformationDao extends DalRepository<TmsDrvLoginInformationPO> {
	@Sql("select * from tms_drv_login_information where drv_id = ? order by datachange_createtime desc limit 1")
	TmsDrvLoginInformationPO findOne(Long driverId);


	@Sql("update tms_drv_login_information set provider_data_location = ? where drv_id = ?")
	void updateUdlByDriverId(String udl, String drvId);
}
