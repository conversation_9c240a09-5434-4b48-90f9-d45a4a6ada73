package com.ctrip.dcs.driver.account.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.*;
import java.sql.Types;
import java.time.LocalDateTime;


@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "account_base_info")
@Data
public class AccountBaseInfoPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 携程uid
     */
	@Column(name = "uid")
	@Type(value = Types.VARCHAR)
	private String uid;

	/**
	 * 姓名
	 */
	@Column(name = "name")
	@Type(value = Types.VARCHAR)
	private String name;

	/**
	 * 身份证号
	 */
	@Column(name = "id_card_no")
	@Type(value = Types.VARCHAR)
	private String idCardNo;

	/**
	 * 国家码
	 */
	@Column(name = "country_code")
	@Type(value = Types.VARCHAR)
	private String countryCode;

	/**
	 * 手机号
	 */
	@Column(name = "phone_number")
	@Type(value = Types.VARCHAR)
	private String phoneNumber;

	/**
	 * 邮箱
	 */
	@Column(name = "email")
	@Type(value = Types.VARCHAR)
	private String email;

	/**
	 * 派安盈账户id
	 */
	@Column(name = "payoneer_account_id")
	@Type(value = Types.VARCHAR)
	private String payoneerAccountId;

	/**
	 * ppm账户id
	 */
	@Column(name = "ppm_account_id")
	@Type(value = Types.VARCHAR)
	private String ppmAccountId;


	/**
     * 注册来源 司机：Driver，向导：Guide，司导：DriverGuide
     */
	@Column(name = "register_source")
	@Type(value = Types.VARCHAR)
	private String registerSource;

	/**
	 * 是否境外，0：境内；1：境外
	 */
	@Column(name = "is_oversea")
	@Type(value = Types.INTEGER)
	private Integer isOversea;

	/**
	 * 提现状态，0：不可提现；1：可提现。默认1
	 */
	@Column(name = "withdraw_status")
	@Type(value = Types.INTEGER)
	private Integer withdrawStatus;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private LocalDateTime datachangeCreatetime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private LocalDateTime datachangeLasttime;

	/**
	 * 修改人
	 */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;

	/**
	 * udl
	 */
	@Column(name = "provider_data_location")
	@Type(value = Types.VARCHAR)
	private String providerDataLocation;

}
