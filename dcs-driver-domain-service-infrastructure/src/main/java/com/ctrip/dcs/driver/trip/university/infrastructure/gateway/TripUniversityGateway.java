package com.ctrip.dcs.driver.trip.university.infrastructure.gateway;

import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityFailedLogDTO;
import com.ctrip.dcs.driver.trip.university.infrastructure.value.DriverSyncDrvInfoToTripUniversityQueryCondition;

import java.util.List;

public interface TripUniversityGateway {

  boolean saveSyncFailedLog(DriverSyncDrvInfoToTripUniversityFailedLogDTO failedLogDTO);

  List<DriverSyncDrvInfoToTripUniversityFailedLogDTO> querySyncFailedLog(
    DriverSyncDrvInfoToTripUniversityQueryCondition condition);

  void deleteFailedLog(String uid);
}
