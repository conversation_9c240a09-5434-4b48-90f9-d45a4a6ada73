package com.ctrip.dcs.driver.account.infrastructure.value;
/*
作者：pl.yang
创建时间：2025/2/24-下午8:23-2025
*/


import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RegisterNewAccountV1Param
 * @Package com.ctrip.dcs.driver.account.infrastructure.value
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/2/24 下午8:23
 */

@Data
public class RegisterNewAccountV1Param {
    /**
     * 来源 司机：Driver 向导：Guide
     */
    public String source;

    /**
     * 司机id/向导id
     */
    public String sourceId;

    /**
     * 注册账号类型 1 手机 2邮箱
     */
    public Integer accountType;

    /**
     * 绑定账号类型 1 手机 2邮箱
     */
    public Integer bindAccountType;

    /**
     * 手机国家码
     */
    public String countryCode;

    /**
     * 手机号
     */
    public String phoneNumber;

    /**
     * 邮箱
     */
    public String email;

    /**
     * 密码
     */
    public String psw;
    /**
     * 操作人
     */
    public String operator;

    /**
     * 城市id
     */
    public Long cityId;

    public Boolean oversea;
    /**
     * 是否是job
     */
    public boolean isJob;
}
