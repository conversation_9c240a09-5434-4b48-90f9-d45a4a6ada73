package com.ctrip.dcs.driver.domain.infrastructure.model.notice;

/**
 * Created by <AUTHOR> on 2023/3/6 10:35
 */
public class NoticeModel {
  /**
   * 是否存在
   */
  private Boolean exist;

  /**
   * 公告id
   */
  private String id;

  /**
   * 公告标题
   */
  private String title;

  /**
   * 公告内容
   */
  private String content;

  /**
   * 公告跳转链接
   */
  private String uri;

  /**
   * 公告状态 1上线 2上线且强提醒
   */
  private Integer status;

  /**
   * 生效开始时间
   */
  private String startDate;

  /**
   * 生效结束时间
   */
  private String endDate;

  private NoticeModel(Builder builder) {
    exist = builder.exist;
    id = builder.id;
    title = builder.title;
    content = builder.content;
    uri = builder.uri;
    status = builder.status;
    startDate = builder.startDate;
    endDate = builder.endDate;
  }

  public static Builder newBuilder() {
    return new Builder();
  }

  public Boolean getExist() {
    return exist;
  }

  public String getId() {
    return id;
  }

  public String getTitle() {
    return title;
  }

  public String getContent() {
    return content;
  }

  public String getUri() {
    return uri;
  }

  public Integer getStatus() {
    return status;
  }

  public String getStartDate() {
    return startDate;
  }

  public String getEndDate() {
    return endDate;
  }

  public static final class Builder {
    private Boolean exist;
    private String id;
    private String title;
    private String content;
    private String uri;
    private Integer status;
    private String startDate;
    private String endDate;

    private Builder() {
    }

    public Builder withExist(Boolean val) {
      exist = val;
      return this;
    }

    public Builder withId(String val) {
      id = val;
      return this;
    }

    public Builder withTitle(String val) {
      title = val;
      return this;
    }

    public Builder withContent(String val) {
      content = val;
      return this;
    }

    public Builder withUri(String val) {
      uri = val;
      return this;
    }

    public Builder withStatus(Integer val) {
      status = val;
      return this;
    }

    public Builder withStartDate(String val) {
      startDate = val;
      return this;
    }

    public Builder withEndDate(String val) {
      endDate = val;
      return this;
    }

    public NoticeModel build() {
      return new NoticeModel(this);
    }
  }
}
