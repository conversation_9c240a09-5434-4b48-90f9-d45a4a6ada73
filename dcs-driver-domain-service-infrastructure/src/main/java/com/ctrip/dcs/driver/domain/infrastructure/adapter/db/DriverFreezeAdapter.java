package com.ctrip.dcs.driver.domain.infrastructure.adapter.db;

import com.ctrip.dcs.driver.domain.infrastructure.condition.freeze.DriverApplyFreezeRecordCondition;
import com.ctrip.dcs.driver.domain.infrastructure.condition.freeze.DriverFrozenBufferCondition;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.DriverApplyFreezeRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.DriverFrozenBufferRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.DriverApplyFreezeRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.DriverFrozenBufferRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class DriverFreezeAdapter {
    @Autowired
    private DriverApplyFreezeRecordDao driverApplyFreezeRecordDao;
    @Autowired
    private DriverFrozenBufferRecordDao driverFrozenBufferRecordDao;

    public void createDriverApplyFreezeRecord(DriverApplyFreezeRecordCondition condition) {
        DriverApplyFreezeRecordPO record = new DriverApplyFreezeRecordPO();
        record.setDriverId(condition.getDriverId());
        record.setApplyFreezeReason(condition.getApplyFreezeReason());
        record.setApplyFreezeHours(condition.getApplyFreezeHours());
        record.setApplyFreezeTime(condition.getApplyFreezeTime());
        record.setApplyFreezeResult(condition.getApplyFreezeResult());
        record.setApplyFreezeResultDesc(condition.getApplyFreezeResultDesc());
        record.setDeviceId(condition.getDeviceId());
        driverApplyFreezeRecordDao.insert(record);
    }

    public void createDriverFrozenBuffer(DriverFrozenBufferCondition condition) {
        DriverFrozenBufferRecordPO buffer = new DriverFrozenBufferRecordPO();
        buffer.setDriverId(condition.getDriverId());
        buffer.setDeviceId(condition.getDeviceId());
        buffer.setReasonType(condition.getReasonType());
        Integer bufferDays = condition.getBufferDays();
        buffer.setBufferStartTime(condition.getBufferStartTime());
        buffer.setBufferEndTime(condition.getBufferEndTime());
        buffer.setBufferDays(bufferDays);
        driverFrozenBufferRecordDao.insert(buffer);
    }

    public String queryDriverFrozenBufferEndTime(Long driverId) {
        DriverFrozenBufferRecordPO recordPO = driverFrozenBufferRecordDao.findOneByDriverId(driverId);
        if (Objects.isNull(recordPO) || Objects.isNull(recordPO.getBufferEndTime())) {
            return null;
        }
        return LocalDateTimeUtils.format(recordPO.getBufferEndTime());
    }
}
