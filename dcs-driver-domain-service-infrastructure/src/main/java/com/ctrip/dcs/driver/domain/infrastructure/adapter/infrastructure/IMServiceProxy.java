package com.ctrip.dcs.driver.domain.infrastructure.adapter.infrastructure;

import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.igt.im.interfaces.ImServiceClient;
import com.ctrip.igt.im.interfaces.service.implus.message.ImPlusQueryImUrlRequestType;
import com.ctrip.igt.im.interfaces.service.implus.message.ImPlusQueryImUrlResponseType;

@ServiceClient(value = ImServiceClient.class, format = "json")
public interface IMServiceProxy {
  
  ImPlusQueryImUrlResponseType imPlusQueryImUrl(ImPlusQueryImUrlRequestType request);
  
}
