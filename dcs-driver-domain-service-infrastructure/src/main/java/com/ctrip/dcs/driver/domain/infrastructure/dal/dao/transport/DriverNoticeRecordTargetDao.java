package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverNoticeRecordTargetPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcstransportdb_w")
public interface DriverNoticeRecordTargetDao extends DalRepository<DriverNoticeRecordTargetPO> {

  @Sql("select driver_id from driver_notice_record_target where notice_id = ? limit 2000")
  List<Long> getNoticeDriverIds(long noticeId);
}
