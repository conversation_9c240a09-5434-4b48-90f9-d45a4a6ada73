package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2021-08-18
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_notice_record")
public class DriverNoticeRecordPO implements DalPojo {

  /**
   * 记录ID，自增
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * 通知分类 1手动推送 2公告
   */
  @Column(name = "notice_type")
  @Type(value = Types.TINYINT)
  private Integer noticeType;

  /**
   * 0公告 1系统推送 2端内弹框 4站内信 8语音播报 16短信 可多选（权重之和），如7代表 系统推送 端内弹框 站内信
   */
  @Column(name = "push_type")
  @Type(value = Types.SMALLINT)
  private Integer pushType;

  /**
   * 通知标题
   */
  @Column(name = "notice_title")
  @Type(value = Types.VARCHAR)
  private String noticeTitle;

  /**
   * 通知内容
   */
  @Column(name = "notice_content")
  @Type(value = Types.VARCHAR)
  private String noticeContent;

  /**
   * 通知跳转链接
   */
  @Column(name = "notice_uri")
  @Type(value = Types.VARCHAR)
  private String noticeUri;

  /**
   * 国家ID，逗号分隔
   */
  @Column(name = "country_ids")
  @Type(value = Types.VARCHAR)
  private String countryIds;

  /**
   * 城市ID，逗号分隔
   */
  @Column(name = "city_ids")
  @Type(value = Types.VARCHAR)
  private String cityIds;

  /**
   * 车型id，逗号分隔
   */
  @Column(name = "vehicle_types")
  @Type(value = Types.VARCHAR)
  private String vehicleTypes;

  /**
   * 发送时间
   */
  @Column(name = "send_time")
  @Type(value = Types.TIMESTAMP)
  private Timestamp sendTime;

  /**
   * 生效开始时间
   */
  @Column(name = "start_date")
  @Type(value = Types.TIMESTAMP)
  private Timestamp startDate;

  /**
   * 生效结束时间
   */
  @Column(name = "end_date")
  @Type(value = Types.TIMESTAMP)
  private Timestamp endDate;

  /**
   * 公告状态0下线 1上线 2上线且强提醒
   */
  @Column(name = "notice_status")
  @Type(value = Types.TINYINT)
  private Integer noticeStatus;

  /**
   * 操作人
   */
  @Column(name = "operate_user")
  @Type(value = Types.VARCHAR)
  private String operateUser;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeCreatetime;

  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime", insertable = false, updatable = false)
  @Type(value = Types.TIMESTAMP)
  private Timestamp datachangeLasttime;

  /**
   * 语言设置
   */
  @Column(name = "multi_language")
  @Type(value = Types.VARCHAR)
  private String multiLanguage;

  /**
   * 通知标题（多语言）
   */
  @Column(name = "notice_title_en")
  @Type(value = Types.VARCHAR)
  private String noticeTitleEn;

  /**
   * 通知内容（多语言）
   */
  @Column(name = "notice_content_en")
  @Type(value = Types.VARCHAR)
  private String noticeContentEn;

  /**
   * 司机状态
   */
  @Column(name = "driver_status")
  @Type(value = Types.TINYINT)
  private Integer driverStatus;

  /**
   * 产线id，逗号分隔
   */
  @Column(name = "product_type")
  @Type(value = Types.VARCHAR)
  private String productType;

  /**
   * 1 待处理 2发送中 3发送成功 4异常失败
   */
  @Column(name = "send_status")
  @Type(value = Types.TINYINT)
  private Integer sendStatus;

  /**
   * 通知目标选取方式 1指定ID 2条件筛选
   */
  @Column(name = "notice_condition_type")
  @Type(value = Types.TINYINT)
  private Integer noticeConditionType;

  /**
   * 用户类型id，逗号分隔
   */
  @Column(name = "user_type")
  @Type(value = Types.VARCHAR)
  private String userType;
}