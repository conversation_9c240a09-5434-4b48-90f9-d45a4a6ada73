package com.ctrip.dcs.driver.domain.infrastructure.utils;

import org.owasp.csrfguard.util.Strings;

public class MaskHelperUtils {

    public static String maskName(String value) {
        if (value != null && !value.trim().equals("")) {
            if (value.length() == 1) {
                return value;
            } else {
                return value.length() == 2 ? value.substring(0, 1) + "*" :
                        doMask(value, 1, value.length() - 2);
            }
        } else {
            return Strings.EMPTY;
        }
    }

    private static String doMask(String value, int maskStart, int maskLength) {
        if (value == null) {
            return null;
        } else {
            char[] chars = new char[value.length()];
            value.getChars(0, value.length(), chars, 0);
            int index = maskStart;

            for(int maskEnd = maskStart + maskLength - 1; index <= maskEnd; ++index) {
                chars[index] = '*';
            }

            return new String(chars);
        }
    }
}
