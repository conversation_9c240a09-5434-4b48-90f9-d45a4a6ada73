package com.ctrip.dcs.driver.domain.infrastructure.constant;

import java.util.Arrays;

/**
 * <AUTHOR>
 * 短信、验证码模板类
 * */
public enum FinanceResultEnum {
    OK("200"),
    ERROR("500"),
    //可提现余额查询失败
    BALANCE_FAIL("601"),
    //查询我的银行卡列表失败
    BANKCARDLIST_FAIL("602"),
    //重置提现密码失败
    RESET_PASSWORD_FAIL("603"),
    //提现操作失败
    WITHDRAW_FAIL("604"),
    //获取数据失败，请重试
    DATA_FAIL("605"),
    //司机信息不存在
    DRIVER_NOT_EXIST("606"),
    //抱歉，您已经被开除
    DRIVER_STATUS_CHECKFAIL("607"),
    //身份验证失败，请联系客服
    IDENTITYCODE_FAIL("608"),
    //身份证号码输入错误，请重新输入
    IDENTITYCODE_WRONG("609"),
    //已达今日提现最大次数
    WITHDRAW_OVERCOUNT("610"),
    //提现金额超过单次最大金额
    AMOUNT_OVER("611"),
    //银行卡号或密码错误，如有疑问请联系客服
    BANKCARD_FAIL("612"),
    //司机账号未激活
    DRIVER_STATUS_UNACTIVE("613"),
    //验证码发送失败，请重试
    SEND_VERIFYCODE_FAIL("651"),
    //验证码发送失败，请重试
    SEND_VERIFYCODE_FREQUENTLY("652"),
    //验证码发送失败，请重试
    SEND_VERIFYCODE_OVERLIMIT("653"),
    //您的验证码输入有误
    VERIFYCODE_FAIL("661"),
    //该手机号对应的司机姓名和输入的不一致
    NAME_CHECK_FAIL("671"),
    //该手机号对应的司机已迁移，请登录司机端提现
    DRIVER_REGULATION("672");

    private String code;

    FinanceResultEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static FinanceResultEnum typeOf(String code){
        return Arrays.stream(FinanceResultEnum.values()).filter(e -> e.getCode().equalsIgnoreCase(code)).findAny().orElse(null);
    }

    public static boolean isValidOK(String code) {
        return OK.getCode().equalsIgnoreCase(code);
    }

    public static boolean isValidError(String code) {
        return !OK.getCode().equalsIgnoreCase(code);
    }
}
