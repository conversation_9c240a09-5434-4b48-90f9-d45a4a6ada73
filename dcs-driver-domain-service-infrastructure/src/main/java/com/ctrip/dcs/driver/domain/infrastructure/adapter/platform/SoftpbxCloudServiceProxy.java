package com.ctrip.dcs.driver.domain.infrastructure.adapter.platform;

import com.ctrip.basebiz.softpbxcloudportal.contact.SendAppUserInfoRequestType;
import com.ctrip.basebiz.softpbxcloudportal.contact.SendAppUserInfoResponseType;
import com.ctrip.basebiz.softpbxcloudportal.contact.SoftpbxCloudClient;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = SoftpbxCloudClient.class, format = "json")
public interface SoftpbxCloudServiceProxy {

  /**
   * 客户APP端唤起电话组件落数据到IVR直落
   * */
  SendAppUserInfoResponseType sendAppUserInfo(SendAppUserInfoRequestType request);
}
