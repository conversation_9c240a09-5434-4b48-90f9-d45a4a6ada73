package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivRightsRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.sql.Timestamp;
import java.util.List;

@Dal("dcstransportdb_w")
public interface DrivRightsRecordDao extends DalRepository<DrivRightsRecordPO> {

    @Sql("select * from driv_rights_record where driv_id = ?  and datachange_createtime >= ? and datachange_createtime <= ?")
    List<DrivRightsRecordPO> queryDriverRecords(long driverId, Timestamp startTime, Timestamp endTime);

    @Sql("select * from driv_rights_record where rights_id = ?")
    List<DrivRightsRecordPO> queryDriverRecordsByRightsId(long rightsId);

    @Sql("select * from driv_rights_record where driv_id = ? and rights_type = ? and supply_order_id = ?")
    List<DrivRightsRecordPO> queryDriverRecordsBySupplyOrderId(long driverId, int rightType, String supplyOrderId);

    @Sql("select * from driv_rights_record where driv_id = ? and rights_type = ?")
    List<DrivRightsRecordPO> queryDriverRecordsByDriver(long driverId, int rightType);
}
