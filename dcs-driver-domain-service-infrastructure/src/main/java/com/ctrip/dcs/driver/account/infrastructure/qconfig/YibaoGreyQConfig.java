package com.ctrip.dcs.driver.account.infrastructure.qconfig;

import com.ctrip.dcs.driver.trip.university.infrastructure.utils.DateUtil;
import com.ctrip.igt.framework.common.jackson.JacksonSerializer;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class YibaoGreyQConfig {

    private YibaoGreyDO configDO;

    @QConfig("yibaoGrey.json")
    //todo 灰度配置 需要加上
    public void onChange(String conf) {
        this.configDO = JacksonSerializer.INSTANCE().deserialize(conf, YibaoGreyDO.class);
    }

    /**
     * 获取灰度总开关
     * @return
     */
    public boolean getYibaoGreySwitch(){
        String yibaoGreySwitch = configDO.getYibaoGreySwitch();
        if (StringUtils.isNotBlank(yibaoGreySwitch)) {
            return Boolean.TRUE.equals(Boolean.parseBoolean(yibaoGreySwitch));
        }
        return false;
    }
    /**
     * 获取灰度配置
     * @return
     */
    public List<YibaoGreyConfigDO> getYibaoGreyConfig(){
        return configDO.getConfig();
    }

    public static class YibaoGreyDO{
        private String yibaoGreySwitch;
        private List<YibaoGreyConfigDO> config;

        public String getYibaoGreySwitch() {
            return yibaoGreySwitch;
        }

        public void setYibaoGreySwitch(String yibaoGreySwitch) {
            this.yibaoGreySwitch = yibaoGreySwitch;
        }

        public List<YibaoGreyConfigDO> getConfig() {
            return config;
        }

        public void setConfig(List<YibaoGreyConfigDO> config) {
            this.config = config;
        }
    }

    public static class YibaoGreyConfigDO {
        private String driverIds;
        private String cityIds;
        private String time;

        public List<Integer> getDriverIds() {
            if (StringUtils.isBlank(driverIds)){
                return Lists.newArrayList();
            }
            return Arrays.stream(driverIds.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        public void setDriverIds(String driverIds) {
            this.driverIds = driverIds;
        }

        public List<Integer> getCityIds() {
            if (StringUtils.isBlank(cityIds)){
                return Lists.newArrayList();
            }
            return Arrays.stream(cityIds.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        }

        public void setCityIds(String cityIds) {
            this.cityIds = cityIds;
        }

        public String getTime() {
            return time;
        }



        public LocalDateTime getTimeAsLocalDateTime() {
            return LocalDateTime.parse(time, DateUtil.dateTimeFormatter);
        }

        public void setTime(String time) {
            this.time = time;
        }
    }
}




