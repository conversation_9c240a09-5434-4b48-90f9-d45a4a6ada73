package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideApplyExamPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.sql.Timestamp;
import java.util.List;

@Dal("dcsguidesupplydb")
public interface GuideApplyExamDao extends DalRepository<GuideApplyExamPO> {
  @Sql("select * from guide_apply_exam where exam_account_id = ? and apply_subject = ?")
  List<GuideApplyExamPO> queryGuideApplyExam(String examAccountId, String applySubject);

  @Sql(
      "select * from guide_apply_exam where exam_account_id = ? order by datachange_createtime desc")
  List<GuideApplyExamPO> queryInfoByExamAccountId(String examAccountId);


  @Sql(
      "select * from guide_apply_exam where exam_account_id = ? and exam_is_passed = 1")
  List<GuideApplyExamPO> queryPassedInfoByExamAccountId(String examAccountId);

  @Sql("select count(*) from guide_apply_exam where call_exam_success = 0")
  Long countCallExamFailed();

  @Sql("select * from guide_apply_exam where call_exam_success = 0 order by id limit ?,?")
  List<GuideApplyExamPO> queryCallExamFailedRecords(int startIndex, int count);

  @Sql("select * from guide_apply_exam where call_exam_success = 1 and apply_result = 0 order by id limit ?,?")
  List<GuideApplyExamPO> queryApplyFailedRecords(int startIndex, int count);


  @Sql("select * from guide_apply_exam where datachange_lasttime > ? order by id desc limit 1")
  GuideApplyExamPO queryMaxIdByChangeTime(Timestamp time);

  @Sql("select * from guide_apply_exam where datachange_lasttime > ? order by id asc limit 1")
  GuideApplyExamPO queryMinIdByChangeTime(Timestamp time);

  @Sql("select * from guide_apply_exam where id >= ? and id < ? and exam_is_passed = 0")
  List<GuideApplyExamPO> queryUpassedBetweenIds(Long startId,Long endId);
}
