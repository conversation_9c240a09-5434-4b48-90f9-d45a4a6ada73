package com.ctrip.dcs.driver.account.infrastructure.adapter.soa;

import com.ctrip.basebiz.outcall.service.predict.facade.contract.OutCallSOAServiceClient;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskRequestType;
import com.ctrip.basebiz.outcall.service.predict.facade.contract.SubmitOutboundTaskResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = OutCallSOAServiceClient.class)
public interface VoiceCallServiceProxy {

	SubmitOutboundTaskResponseType submitOutboundTask(SubmitOutboundTaskRequestType request);

}