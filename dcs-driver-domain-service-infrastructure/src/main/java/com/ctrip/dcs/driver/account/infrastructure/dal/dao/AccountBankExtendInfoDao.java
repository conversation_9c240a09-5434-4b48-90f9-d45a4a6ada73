package com.ctrip.dcs.driver.account.infrastructure.dal.dao;
/*
作者：pl.yang
创建时间：2025/5/15-下午4:23-2025
*/

import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankExtendInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcsdriverappdb")
public interface AccountBankExtendInfoDao extends DalRepository<AccountBankExtendInfoPO> {

    @Sql("delete from account_bank_extend_info where yeepay_card_id=?")
    public int deleteByYeepayCardId(String yeepayCardId);

    @Sql("select * from account_bank_extend_info where yeepay_card_id=?")
    public List<AccountBankExtendInfoPO> quertyByYeepayCardId(String yeepayCardId);
}
