package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverNoticeRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.Date;
import java.util.List;

@Dal("dcstransportdb_w")
public interface DriverNoticeRecordDao extends DalRepository<DriverNoticeRecordPO> {

  @Sql("select * from driver_notice_record where notice_type = 2 and notice_status > 0 " +
      "and end_date >= ? order by start_date desc,send_time desc")
  List<DriverNoticeRecordPO> queryActiveNotices(Date now);

  @Sql("select * from driver_notice_record where id = ?")
  DriverNoticeRecordPO findNoticeById(long noticeId);

  @Sql("update driver_notice_record set send_status = ? where id = ?")
  int updateSendStatus(int status, long noticeId);
}
