package com.ctrip.dcs.driver.account.infrastructure.adapter.soa;

import com.ctrip.basebiz.callcenter.splitservice.contract.PhoneNumberSplitServiceClient;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberRequestType;
import com.ctrip.basebiz.callcenter.splitservice.contract.SplitNumberResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = PhoneNumberSplitServiceClient.class)
public interface PhoneNumberServiceProxy {

	SplitNumberResponseType splitNumber(SplitNumberRequestType request);

}