package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TripUniversityDriverForDisCardDTO {

  private String drvId;

  /**
   * 姓名
   */
  private String name;

  /**
   * 手机号
   */
  private String phoneNumber;

  /**
   * 邮箱
   */
  private String email;

  private String emailPrefix;

  private String phoneSuffix;

}
