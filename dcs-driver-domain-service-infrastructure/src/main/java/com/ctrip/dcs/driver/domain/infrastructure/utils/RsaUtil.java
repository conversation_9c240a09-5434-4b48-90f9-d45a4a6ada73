package com.ctrip.dcs.driver.domain.infrastructure.utils;
/*
作者：pl.yang
创建时间：2025/6/3-下午5:44-2025
*/


import com.ctrip.infosec.sec.crypto.AesRsaUtil;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: RsaUtil
 * @Package com.ctrip.dcs.driver.domain.infrastructure.utils
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/6/3 下午5:44
 */

@Component
public class RsaUtil {

    private AesRsaUtil aesRsaUtil = null;

    @PostConstruct
    public void PostConstruct() {
        aesRsaUtil = AesRsaUtil.getInstance();
    }

    public String encrypt(String value) {
        return aesRsaUtil.encrypt(value);
    }
}
