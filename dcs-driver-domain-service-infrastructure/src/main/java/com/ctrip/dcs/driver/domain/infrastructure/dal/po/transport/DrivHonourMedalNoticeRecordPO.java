package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-07-22
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driv_honour_medal_notice_record")
public class DrivHonourMedalNoticeRecordPO implements DalPojo {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机ID
     */
    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    /**
     * 提醒过的一般勋章列表，","分割
     */
    @Column(name = "common_medal_notice")
    @Type(value = Types.VARCHAR)
    private String commonMedalNotice;

    /**
     * 提醒过的行为勋章，","分割
     */
    @Column(name = "active_medal_notice")
    @Type(value = Types.VARCHAR)
    private String activeMedalNotice;

    /**
     * 提醒过的荣誉墙勋章，","分割
     */
    @Column(name = "form_medal_notice")
    @Type(value = Types.VARCHAR)
    private String formMedalNotice;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}

