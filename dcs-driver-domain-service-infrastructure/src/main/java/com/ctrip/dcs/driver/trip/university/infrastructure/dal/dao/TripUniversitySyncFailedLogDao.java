package com.ctrip.dcs.driver.trip.university.infrastructure.dal.dao;

import com.ctrip.dcs.driver.trip.university.infrastructure.dal.po.DriverSyncDrvInfoToTripUniversityFailedLogPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

/**
 * <AUTHOR>
 */
@Dal("dcstransportdb_w")
public interface TripUniversitySyncFailedLogDao extends DalRepository<DriverSyncDrvInfoToTripUniversityFailedLogPO> {

  @Sql("select * from driver_sync_drv_info_to_trip_university_failed_log where drv_id in ( ? )")
  List<DriverSyncDrvInfoToTripUniversityFailedLogPO> queryByMobilePhone(List<Long> drvIdList);

  @Sql("delete from driver_sync_drv_info_to_trip_university_failed_log where uid =  ? ")
  int deleteByUid(String uid);

}
