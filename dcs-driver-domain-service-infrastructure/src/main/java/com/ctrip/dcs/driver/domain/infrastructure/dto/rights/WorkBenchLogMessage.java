package com.ctrip.dcs.driver.domain.infrastructure.dto.rights;

/**
 * <AUTHOR>
 * @Description
 */
public class WorkBenchLogMessage {
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     *操作日志类型
     */
    private Integer logType;
    /**
     * 操作日志来源
     */
    private Integer sourceType;
    /**
     * 操作日志内容类型
     */
    private Integer contentType;
    /**
     * 操作日志概要SharkKey
     */
    private String titleKey;
    /**
     * 操作日志内容SharkKey
     */
    private String contentKey;
    /**
     * 操作日志内容数据MAP
     */
    private String contentParams;
    /**
     * 操作日志时间
     */
    private Long logTime;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 扩展信息ID（如 事件ID，投诉ID）
     */
    private String extId;
    /**
     * 其他扩展信息，不展示给客户，比如：错误日志等
     */
    private String extInfo;
    /**
     * 生产消息应用ID
     */
    private String appId;

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public Integer getSourceType() {
        return sourceType;
    }

    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public String getTitleKey() {
        return titleKey;
    }

    public void setTitleKey(String titleKey) {
        this.titleKey = titleKey;
    }

    public String getContentKey() {
        return contentKey;
    }

    public void setContentKey(String contentKey) {
        this.contentKey = contentKey;
    }

    public String getContentParams() {
        return contentParams;
    }

    public void setContentParams(String contentParams) {
        this.contentParams = contentParams;
    }

    public Long getLogTime() {
        return logTime;
    }

    public void setLogTime(Long logTime) {
        this.logTime = logTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getExtId() {
        return extId;
    }

    public void setExtId(String extId) {
        this.extId = extId;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }
}
