package com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "tms_drv_login_information")
public class TmsDrvLoginInformationPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 司机ID
     */
	@Column(name = "drv_id")
	@Type(value = Types.BIGINT)
	private Long drvId;

    /**
     * 司机登录时间
     */
	@Column(name = "drv_login_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp drvLoginTime;

    /**
     * 司机IMEI号
     */
	@Column(name = "drv_imei")
	@Type(value = Types.VARCHAR)
	private String drvImei;

    /**
     * 司机位置经度
     */
	@Column(name = "drv_loc_long")
	@Type(value = Types.DOUBLE)
	private Double drvLocLong;

    /**
     * 司机位置纬度
     */
	@Column(name = "drv_loc_lat")
	@Type(value = Types.DOUBLE)
	private Double drvLocLat;

    /**
     * 司机位置坐标系
     */
	@Column(name = "drv_loc_csys")
	@Type(value = Types.VARCHAR)
	private String drvLocCsys;

    /**
     * 创建时间
     */
	@Column(name = "datachange_createtime")
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeCreatetime;

    /**
     * 变更时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

    /**
     * 创建人
     */
	@Column(name = "create_user")
	@Type(value = Types.VARCHAR)
	private String createUser;

    /**
     * 变更人
     */
	@Column(name = "modify_user")
	@Type(value = Types.VARCHAR)
	private String modifyUser;
}