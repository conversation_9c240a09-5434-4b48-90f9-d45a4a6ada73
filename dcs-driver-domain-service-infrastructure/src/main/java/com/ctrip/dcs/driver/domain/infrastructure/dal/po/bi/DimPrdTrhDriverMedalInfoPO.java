package com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-07-25
 */
@Getter
@Setter
@Entity
@Database(name = "igtbidb_W")
@Table(name = "dim_prd_trh_driver_medal_info")
public class DimPrdTrhDriverMedalInfoPO implements DalPojo {

    /**
     * 自增id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 勋章类型
     */
    @Column(name = "medal_type")
    @Type(value = Types.VARCHAR)
    private String medalType;

    /**
     * 勋章等级
     */
    @Column(name = "medal_grade")
    @Type(value = Types.BIGINT)
    private Long medalGrade;

    /**
     * 勋章等级区间最小值	 勋章等级区间最小值
     */
    @Column(name = "medal_grade_min")
    @Type(value = Types.BIGINT)
    private Long medalGradeMin;

    /**
     * 勋章等级区间最大值
     */
    @Column(name = "medal_grade_max")
    @Type(value = Types.BIGINT)
    private Long medalGradeMax;
}
