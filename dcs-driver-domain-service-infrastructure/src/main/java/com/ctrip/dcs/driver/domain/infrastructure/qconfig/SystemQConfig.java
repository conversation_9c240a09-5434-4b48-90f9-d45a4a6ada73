package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.MapConfig;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class SystemQConfig extends BaseConfig {
    private MapConfig config;

    private SystemQConfig() {
        config = MapConfig.get("system.properties");
        kv = config.asMap();
        config.addListener(conf -> kv = conf);
    }

    public Map<String, String> getMap() {
        if (config == null) {
            return null;
        }

        return config.asMap();
    }
}
