package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.dcs.driver.domain.infrastructure.model.form.FromGuideExamInfo;
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils;
import com.ctrip.igt.framework.common.datetime.DateUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 */
@Component
public class GuideExamConfig {
    public List<FromGuideExamInfo> guideExamInfoList;

    public Map<String,FromGuideExamInfo> deptExamMap;

    public static final String START_HOUR_MINUTE_SECOND = " 00:00:00";

    public static final String END_HOUR_MINUTE_SECOND = " 23:59:59";

    public static final long ONE_DAY_MIlI_SECONDS = 24*60*60*1000;


    @QConfig("guide_exam_config.json")
    public void guideExamConfigChange(String value) {
        if(StringUtils.isEmpty(value)){
            return;
        }
        List<FromGuideExamInfo> fromGuideExamInfos = JacksonUtil.parseArray(value, FromGuideExamInfo.class);
        fromGuideExamInfos.stream().forEach(fromGuideExamInfo ->{
            String startTime = fromGuideExamInfo.getStartTime();
            fromGuideExamInfo.setStartTime(startTime + START_HOUR_MINUTE_SECOND);
            fromGuideExamInfo.setStartTimeSecond(DateUtils.parse(fromGuideExamInfo.getStartTime(), "yyyy-MM-dd HH:mm:ss").getTime());
            String endTime = fromGuideExamInfo.getEndTime();
            fromGuideExamInfo.setEndTime(endTime + END_HOUR_MINUTE_SECOND);
            fromGuideExamInfo.setEndTimeSecond(DateUtils.parse(fromGuideExamInfo.getEndTime(), "yyyy-MM-dd HH:mm:ss").getTime());
        });
        this.guideExamInfoList = fromGuideExamInfos;
        this.deptExamMap = this.guideExamInfoList.stream().collect(Collectors.toMap(FromGuideExamInfo::getDeptId,fromGuideExamInfo -> fromGuideExamInfo));
    }

    public List<FromGuideExamInfo> getQueryExamScoreGuideExamInfoList(){
        return guideExamInfoList.stream().filter(e ->
            (LocalDateTimeUtils.isNowInDate(e.getStartTime(),e.getEndTime()) || ((System.currentTimeMillis()-e.getEndTimeSecond()) > 0 && (System.currentTimeMillis()-e.getEndTimeSecond()) <= ONE_DAY_MIlI_SECONDS)))
            .collect(Collectors.toList());
    }

    public List<FromGuideExamInfo> queryValidityExam(){
        if(CollectionUtils.isEmpty(guideExamInfoList)){
            return Collections.emptyList();
        }
        return guideExamInfoList.stream()
                .filter(e -> LocalDateTimeUtils.isNowInDate(e.getStartTime(),e.getEndTime()))
                .collect(Collectors.toList());
    }


    public Map<String,FromGuideExamInfo> queryValidityExamMap(){
        if(CollectionUtils.isEmpty(guideExamInfoList)){
            return new HashMap<>();
        }
        return guideExamInfoList.stream()
            .filter(e -> LocalDateTimeUtils.isNowInDate(e.getStartTime(),e.getEndTime()))
            .collect(Collectors.toMap(FromGuideExamInfo::getDeptId,fromGuideExamInfo -> fromGuideExamInfo));
    }

    public String getExamNameByDeptId(String deptId){
        FromGuideExamInfo fromGuideExamInfo = deptExamMap.get(deptId);
        if(fromGuideExamInfo != null){
            return fromGuideExamInfo.getDeptName();
        }
        return "";
    }

    public FromGuideExamInfo getExamInfoByDeptId(String deptId){
        FromGuideExamInfo fromGuideExamInfo = deptExamMap.get(deptId);
        return fromGuideExamInfo;
    }
}
