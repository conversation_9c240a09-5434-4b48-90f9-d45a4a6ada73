package com.ctrip.dcs.driver.domain.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;
import java.sql.Types;

@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_apply_freeze_record")
public class DriverApplyFreezeRecordPO implements DalPojo {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机ID
     */
    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    /**
     * 申请冻结原因类型
     */
    @Column(name = "apply_freeze_reason")
    @Type(value = Types.VARCHAR)
    private String applyFreezeReason;

    /**
     * 申请冻结小时数
     */
    @Column(name = "apply_freeze_hours")
    @Type(value = Types.INTEGER)
    private Integer applyFreezeHours;

    /**
     * 申请冻结的时间
     */
    @Column(name = "apply_freeze_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp applyFreezeTime;

    /**
     * 申请冻结结果
     */
    @Column(name = "apply_freeze_result")
    @Type(value = Types.VARCHAR)
    private String applyFreezeResult;

    /**
     * 申请冻结结果描述
     * 备注：失败时才有值
     */
    @Column(name = "apply_freeze_result_desc")
    @Type(value = Types.VARCHAR)
    private String applyFreezeResultDesc;

    /**
     * 设备id
     */
    @Column(name = "device_id")
    @Type(value = Types.VARCHAR)
    private String deviceId;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 变更时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}
