package com.ctrip.dcs.driver.trip.university.infrastructure.value;


/**
 * <AUTHOR>
 * @Description //TODO $
 * @Date $ $
 * @Param $
 * @return $
 **/
public class ZhiXLDeptDTO {
    //部门编号
    private String id;
    //部门名称
    private String name;
    //父部门编号
    private String parent_id;
    //公司级部门,0：否，1：是
    private Integer is_company;
    //部门负责人编号
    private String legal_person;
    //部门标签
    private String tags;
    //区域编号
    private String area_id;
    //排序序号
    private Integer sort_no;
    //有效期开始日期，为 0 保存即有效
    private Long expire_from;
    //有效期截至日期，为 0 长期有效
    private Long expire_to;
    //部门第三方唯一编号
    private String customer_openid;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParent_id() {
        return parent_id;
    }

    public void setParent_id(String parent_id) {
        this.parent_id = parent_id;
    }

    public Integer getIs_company() {
        return is_company;
    }

    public void setIs_company(Integer is_company) {
        this.is_company = is_company;
    }

    public String getLegal_person() {
        return legal_person;
    }

    public void setLegal_person(String legal_person) {
        this.legal_person = legal_person;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getArea_id() {
        return area_id;
    }

    public void setArea_id(String area_id) {
        this.area_id = area_id;
    }

    public Integer getSort_no() {
        return sort_no;
    }

    public void setSort_no(Integer sort_no) {
        this.sort_no = sort_no;
    }

    public Long getExpire_from() {
        return expire_from;
    }

    public void setExpire_from(Long expire_from) {
        this.expire_from = expire_from;
    }

    public Long getExpire_to() {
        return expire_to;
    }

    public void setExpire_to(Long expire_to) {
        this.expire_to = expire_to;
    }

    public String getCustomer_openid() {
        return customer_openid;
    }

    public void setCustomer_openid(String customer_openid) {
        this.customer_openid = customer_openid;
    }

}
