package com.ctrip.dcs.driver.domain.infrastructure.dal.dao;


import com.ctrip.dcs.driver.domain.infrastructure.dal.po.DriverFrozenBufferRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;


@Dal("dcstransportdb_w")
public interface DriverFrozenBufferRecordDao extends DalRepository<DriverFrozenBufferRecordPO> {
    @Sql("select * from driver_frozen_buffer_record where driver_id = ?")
    DriverFrozenBufferRecordPO findOneByDriverId(Long driverId);
    @Sql("update driver_frozen_buffer_record set provider_data_location = ? where driver_id = ?")
    void updateUdlByDriverId(String udl, String driverId);
}
