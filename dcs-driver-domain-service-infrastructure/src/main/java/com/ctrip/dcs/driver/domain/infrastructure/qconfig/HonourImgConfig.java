package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.dcs.driver.domain.infrastructure.model.honour.CommemorationConfigImgModel;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.MedalImgConfigModel;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.utils.StringUtils;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.owasp.csrfguard.util.Strings;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Component
public final class HonourImgConfig {

  private String birthdayImg;
  private ConcurrentMap<Integer, String> activeImgList = new ConcurrentHashMap<>();
  private ConcurrentMap<String, MedalImgConfigModel> medalImgList = new ConcurrentHashMap<>();
  private boolean customerMedalCache;

  @QConfig("honour.image.properties")
  public void updateData(Map<String, String> map) {
    this.activeImgList.clear();
    this.medalImgList.clear();
    String strCommemorationImg = map.containsKey("commemorationImg") ? map.get("commemorationImg").trim() : Strings.EMPTY;
    if(StringUtils.isNotEmpty(strCommemorationImg)) {
      CommemorationConfigImgModel commemorationImg = JacksonUtil.deserialize(strCommemorationImg, CommemorationConfigImgModel.class);
      if(Objects.nonNull(commemorationImg)) {
        birthdayImg = commemorationImg.getBirthday();
        if(CollectionUtils.isNotEmpty(commemorationImg.getActive())){
          commemorationImg.getActive().forEach(a -> {
            activeImgList.put(a.getKey(), a.getImg());
          });
        }
      }
    }
    String strMedalImg = map.containsKey("medalImg") ? map.get("medalImg").trim() : Strings.EMPTY;
    if(StringUtils.isNotEmpty(strMedalImg)) {
      List<MedalImgConfigModel> medalImgConfigDTOList =
              JacksonUtil.deserialize(strMedalImg, new TypeToken<List<MedalImgConfigModel>>(){}.getType());
      if(CollectionUtils.isNotEmpty(medalImgConfigDTOList)) {
        medalImgConfigDTOList.forEach(m -> {
          medalImgList.put(m.getCode().toLowerCase(), m);
        });
      }
    }
    customerMedalCache = map.getOrDefault("customerMedalCache", "0").equals("0");
  }

  public String getBirthdayImg() {
    return birthdayImg;
  }

  public String getActiveDayImg(int grade) {
    if(activeImgList.containsKey(grade)){
      return activeImgList.get(grade);
    }
    return Strings.EMPTY;
  }

  public String getMedalImg(String code, boolean isLight, boolean isNew) {
    if(medalImgList.containsKey(code)){
      MedalImgConfigModel medalImgConfigDTO = medalImgList.get(code);
      if(isNew) {
        return medalImgConfigDTO.getNewImg();
      }
      return isLight ? medalImgConfigDTO.getLightImg() : medalImgConfigDTO.getGrayImg();
    }
    return Strings.EMPTY;
  }

  public boolean isCustomerMedalCache() {
    return customerMedalCache;
  }

  public void setCustomerMedalCache(boolean customerMedalCache) {
    this.customerMedalCache = customerMedalCache;
  }
}
