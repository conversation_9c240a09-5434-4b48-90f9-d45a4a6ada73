package com.ctrip.dcs.driver.domain.infrastructure.qconfig.form;

import com.ctrip.dcs.driver.domain.infrastructure.model.form.*;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.utils.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
public class HonourFormConfig {

  private FormHonourInfo formHonourInfo;
  /**
   * 荣誉墙配置的所有勋章列表
   * 没有配置的司机也要展示，未点亮
   * */
  private List<FormHonourWallMedalSimpleInfo> formHonourWallMedalList = new ArrayList();
  /**
   * 荣誉墙获得勋章的司机
   * */
  private Map<Long, Map<String, DriverHonourWallInfo>> mapDriverHonourWall = new HashMap<>();
  /**对客的信息*/
  private Map<String, CustomerMedalSimpleInfo> customerMedalSimpleInfoMap = new HashMap<>();

  /*金牌司机*/
  private final static String GOLD_MEDAL = "goldMedal";

   /**
   * form配置
   * 看勋章强需要解析
   * */
  @QConfig("driver.honour.json")
  public void driverHonourChange(String value) {

    if(StringUtils.isEmpty(value)) {
      this.mapDriverHonourWall.clear();
      this.formHonourWallMedalList.clear();
      this.customerMedalSimpleInfoMap.clear();
      return;
    }

    this.formHonourInfo = JacksonUtil.deserialize(value, FormHonourInfo.class);
    if(Objects.isNull(this.formHonourInfo)) {
      this.formHonourInfo = new FormHonourInfo();
    }
    // 排行榜开放城市
    this.formHonourInfo.setRankCityList(new ArrayList<>());
    String strRankCityList = this.formHonourInfo.getRankCityListStr();
    if(StringUtils.isNotEmpty(strRankCityList)) {
      this.formHonourInfo.setRankCityList(ShoppingCollectionUtils.toList
              (strRankCityList.split(","), (ShoppingFuncUtils.Func1<String, Long>) Long::parseLong)
              .stream()
              .distinct()
              .collect(Collectors.toList()));
    }

    //荣誉墙
    this.bulidHonourWall();
  }

  /**
   * FORM对应的勋章墙配置
   * */
  private void bulidHonourWall() {
    if (CollectionUtils.isEmpty(this.formHonourInfo.getHonourWall())) {
      return;
    }

    Map<Long, Map<String, DriverHonourWallInfo>> localMapDriverHonourWall = new HashMap<>();
    List<FormHonourWallMedalSimpleInfo> localFormHonourWallMedalList = new ArrayList();
    Map<String, CustomerMedalSimpleInfo> localCustomerMedalSimpleInfoMap = new HashMap<>();
    for(FormHonourWallMedalInfo medal : this.formHonourInfo.getHonourWall()){
      FormHonourWallMedalSimpleInfo medalSimpleInfo = new FormHonourWallMedalSimpleInfo();
      medalSimpleInfo.setCode(medal.getCode());
      medalSimpleInfo.setGrayIcon(medal.getGrayIcon());
      medalSimpleInfo.setIcon(medal.getIcon());
      medalSimpleInfo.setNewIcon(medal.getNewIcon());
      medalSimpleInfo.setTotalRank(0);
      localFormHonourWallMedalList.add(medalSimpleInfo);
      if (CollectionUtils.isNotEmpty(medal.getList())) {
        medal.getList().sort(Comparator.comparing(FormHonourWallDetailInfo::getDate));
        // 需要根据时间和司机ID的顺序重新排序决定司机获得该勋章的排名
        AtomicInteger index = new AtomicInteger();
        index.getAndIncrement();  //名次从 1 开始
        for(FormHonourWallDetailInfo date : medal.getList()){
          final LocalDate finalLocalDate = this.convertLocalDate(date.getDate());
          if(StringUtils.isNotEmpty(date.getDriverIdListStr())) {
            List<Long> driverIdList = ShoppingCollectionUtils.toList
                    (date.getDriverIdListStr().split(","), (ShoppingFuncUtils.Func1<String, Long>) Long::parseLong);
            if(CollectionUtils.isEmpty(driverIdList)){
              continue;
            }
            //获得勋章的司机总数
            medalSimpleInfo.setTotalRank(Math.addExact(medalSimpleInfo.getTotalRank(),driverIdList.size()));
            driverIdList.forEach(divId -> {
              Map<String, DriverHonourWallInfo> mapDriverMedal = new HashMap<>();
              if(localMapDriverHonourWall.containsKey(divId)) {
                mapDriverMedal = localMapDriverHonourWall.get(divId);
              }
              DriverHonourWallInfo wallInfo = new DriverHonourWallInfo();
              wallInfo.rank = index.getAndIncrement();
              wallInfo.time = finalLocalDate;
              mapDriverMedal.put(medal.getCode(), wallInfo);
              localMapDriverHonourWall.put(divId, mapDriverMedal);
            });
          }
        }
      }

      if(StringUtils.isNotEmpty(medal.getCode()) && BooleanUtils.isTrue(medal.isCustomerShow())) {
        CustomerMedalSimpleInfo customerMedalSimpleInfo = new CustomerMedalSimpleInfo();
        customerMedalSimpleInfo.setCode(medal.getCode());
        customerMedalSimpleInfo.setCustomerShow(medal.isCustomerShow());
        customerMedalSimpleInfo.setCustomerShowIndex(ObjectUtils.defaultIfNull(medal.getCustomerShowIndex(), 999));
        customerMedalSimpleInfo.setName(medal.getName());
        customerMedalSimpleInfo.setToCustomerDesc(medal.getToCustomerDesc());
        localCustomerMedalSimpleInfoMap.put(customerMedalSimpleInfo.getCode().toLowerCase(), customerMedalSimpleInfo);
      }
    }

    if(CollectionUtils.isNotEmpty(this.formHonourInfo.getServiceMedalTocDesc())){
      this.formHonourInfo.getServiceMedalTocDesc().forEach(c -> {
        if(Objects.nonNull(c) && StringUtils.isNotEmpty(c.getCode()) && BooleanUtils.isTrue(c.isCustomerShow())) {
          c.setCustomerShowIndex(ObjectUtils.defaultIfNull(c.getCustomerShowIndex(), 999));
          localCustomerMedalSimpleInfoMap.put(c.getCode().toLowerCase(), c);
        }
      });
    }

    this.mapDriverHonourWall = localMapDriverHonourWall;
    this.formHonourWallMedalList = localFormHonourWallMedalList;
    this.customerMedalSimpleInfoMap = localCustomerMedalSimpleInfoMap;
  }

  /**
   * 是否展示勋章
   * */
  public boolean isShowMedal(){
    return Boolean.TRUE.equals(this.formHonourInfo.isShowMedal());
  }

  /**
   * 是否提示纪念日
   * */
  public boolean isShowCommemoration(){
    return Boolean.TRUE.equals(this.formHonourInfo.isShowCommemoration());
  }

  /**
   * 城市是否开放排行榜
   * */
  public boolean isRankOpenCity(long cityId){
    return this.formHonourInfo.getRankCityList().contains(cityId);
  }

  /**
   * 排行榜开放城市列表
   * */
  public List<Long> getRankOpenCity() {
    return this.formHonourInfo.getRankCityList();
  }

  /**
   * 勋章墙数量
   * */
  public long getHonourWallMedalCount(long driverId) {
    if(this.mapDriverHonourWall.containsKey(driverId)){
      return this.mapDriverHonourWall.get(driverId).size();
    }
    return 0;
  }

  /**
   * 勋章墙数量
   * */
  public int getHonourWallMedalTotalCount() {
    return this.formHonourWallMedalList.size();
  }

  /**
   * 勋章墙
   * */
  public List<FormHonourWallDriverMedalInfo> getHonourWallMedalInfo(long driverId, boolean isQueryNew, GoldMedalDriverModel goldMedalDriverModel) {
    List<FormHonourWallDriverMedalInfo> formMedalList = new ArrayList<>();

    if(CollectionUtils.isEmpty(this.formHonourWallMedalList)){
      return formMedalList;
    }

    final Map<String, DriverHonourWallInfo> ownMedalList = this.mapDriverHonourWall.getOrDefault(driverId, new HashMap<>());
    this.formHonourWallMedalList.forEach(m -> {
      FormHonourWallDriverMedalInfo medalInfo = new FormHonourWallDriverMedalInfo();
      medalInfo.setCode(m.getCode());

      if(ownMedalList.containsKey(m.getCode())){
        DriverHonourWallInfo wallInfo = ownMedalList.get(m.getCode());
        medalInfo.setLight(true);
        medalInfo.setIcon(isQueryNew ? m.getNewIcon() : m.getIcon());
        medalInfo.setDriverRank(ObjectUtils.defaultIfNull(wallInfo.rank, 0));
        medalInfo.setHonourTime(ObjectUtils.defaultIfNull(wallInfo.time, LocalDate.MAX));
      } else {
        medalInfo.setLight(false);
        medalInfo.setIcon(m.getGrayIcon());
        medalInfo.setDriverRank(0);
        medalInfo.setHonourTime(LocalDate.MIN);
        if(GOLD_MEDAL.equalsIgnoreCase(m.getCode()) && goldMedalDriverModel != null && goldMedalDriverModel.getTotalCount() > 0){
          medalInfo.setIcon(isQueryNew ? m.getNewIcon() : m.getIcon());
        }
      }
      medalInfo.setTotalRank(m.getTotalRank());
      formMedalList.add(medalInfo);
    });
    return formMedalList;
  }

  /**
   * 获取新勋章的图片
   * */
  public String getMedalNewImg(String code) {
    return this.formHonourWallMedalList.stream()
            .filter(o -> o.getCode().equalsIgnoreCase(code))
            .findFirst().orElse(new FormHonourWallMedalSimpleInfo()).getNewIcon();
}

  /***/
  public CustomerMedalSimpleInfo getCustomerMedalInfo(String medalCode) {
    if(StringUtils.isEmpty(medalCode)){
      return null;
    }
    return this.customerMedalSimpleInfoMap.getOrDefault(medalCode.toLowerCase(), null);
  }

  private LocalDate convertLocalDate(String strDate) {
    LocalDate localDate = LocalDate.MIN;
    try {
      localDate = LocalDate.parse(strDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }catch(Exception e) {
      return localDate;
    }
    return localDate;
  }

  private static class DriverHonourWallInfo {
    private int rank;
    private LocalDate time;

    public int getRank() {
      return rank;
    }

    public void setRank(int rank) {
      this.rank = rank;
    }

    public LocalDate getTime() {
      return time;
    }

    public void setTime(LocalDate time) {
      this.time = time;
    }
  }
}
