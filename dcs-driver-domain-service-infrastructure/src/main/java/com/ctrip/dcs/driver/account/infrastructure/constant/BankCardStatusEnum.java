package com.ctrip.dcs.driver.account.infrastructure.constant;

import lombok.Getter;

/*
作者：pl.yang
创建时间：2025/5/9-下午4:23-2025
*/
@Getter
public enum BankCardStatusEnum {
    //0:默认  1:绑卡 2解绑 3:审核中 4:绑卡失败
    UNKNOW(0, ""),
    BIND(1, "ACTIVATED"),
    UNBIND(2, ""),
    AUDITING(3, "PROCESSING"),
    BINDFAILED(4, "FAILED");
    private final int code;
    //"状态：
    //ACTIVATED：审核通过
    //PROCESSING：审核中
    //FAILED：审核不通过"
    private final String yeepayStatusCode;

    BankCardStatusEnum(int code, String yeepayStatusCode) {
        this.code = code;
        this.yeepayStatusCode = yeepayStatusCode;
    }

    public static BankCardStatusEnum ofByYeePayStatusCode(String yeepayStatusCode) {
        for (BankCardStatusEnum value : BankCardStatusEnum.values()) {
            if (value.yeepayStatusCode.equals(yeepayStatusCode)) {
                return value;
            }
        }
        return null;
    }

    public static BankCardStatusEnum ofByCode(Integer operateType) {
        for (BankCardStatusEnum value : BankCardStatusEnum.values()) {
            if (value.code == operateType) {
                return value;
            }
        }
        return null;
    }
    public static BankCardStatusEnum ofByCodeDefaultUNKNOW(Integer operateType) {
        BankCardStatusEnum bankCardStatusEnum = ofByCode(operateType);
        if(bankCardStatusEnum==null){
            return UNKNOW;
        }
        return bankCardStatusEnum;
    }
}
