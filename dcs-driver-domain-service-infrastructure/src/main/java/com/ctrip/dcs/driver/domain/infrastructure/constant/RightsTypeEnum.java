package com.ctrip.dcs.driver.domain.infrastructure.constant;


public enum RightsTypeEnum {
    RIGHTS_TYPE_WELFARE(1,"welfare"),
    RIGHTS_TYPE_REASSIGN(2,"reassign"),
    RIGHTS_TYPE_CITYKING(3,"cityKing"),
    RIGHTS_TYPE_JUNIOR_PUNISH(4,"juniorPunish"),
    RIGHTS_TYPE_SENIOR_PUNISH(5,"seniorPunish"),
    RIGHTS_TYPE_WITHDRAW(6,"withdraw"),
    RIGHTS_TYPE_VACATION(7, "vacation"),
    RIGHTS_TYPE_UNKNOWN(99,"unknown"),
    ;

    private int code;
    private String name;

    RightsTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static RightsTypeEnum getByCode(Integer code) {
        for (RightsTypeEnum rightsTypeEnum : RightsTypeEnum.values()) {
            if (rightsTypeEnum.code == code) {
                return rightsTypeEnum;
            }
        }
        return RIGHTS_TYPE_UNKNOWN;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
