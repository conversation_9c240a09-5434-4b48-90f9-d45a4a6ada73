package com.ctrip.dcs.driver.trip.university.infrastructure.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TripUniversityConstants {

    //携程大学数据推送
    String SUBJECT_PUSH_DATA_TRIP_UNIVERSITY = "dcs.sync.driver.info.to.trip.university.subject";

    //智行力接口入参
    List<String> simpleUserFields = Arrays.asList("id","user_name","email","mobile_phone");

    List<String> userFields = Arrays.asList("id","user_name","email","dept_id","working_status","entry_date","qualified_date","leave_date","mobile_phone","working_type","precincts","customer_openid","tags");


    //携程大学-第三方用户ID标识
    String CTRIP_UNIVERSITY_USERID = "card";

    //携程大学-司机境内标签
    String CTRIP_UNIVERSITY_DOMESTIC_TAGS = "102";

    //携程大学-司机境外标签
    String CTRIP_UNIVERSITY_OVERSEAS_TAGS = "103";

    int SUCCESS_CODE = 200;

    // 默认
     String DEFAULT_TYPE = "default";

    // 根据司机ID同步
     String  BY_DRV_IDS = "byDrvIds";

    // 按日期同步
     String  BY_DATE = "byDate";

    String FORMAT = "yyyy-MM-dd";
    
    String SYNC_TO_TRIP_UNIVERSITY = "sync-to-trip-university";

}
