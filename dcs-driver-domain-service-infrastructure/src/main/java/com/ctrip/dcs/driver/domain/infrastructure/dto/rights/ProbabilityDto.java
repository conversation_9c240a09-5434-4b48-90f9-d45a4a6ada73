package com.ctrip.dcs.driver.domain.infrastructure.dto.rights;

import lombok.*;

/**
 * <AUTHOR>
 * @Description
 */

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class ProbabilityDto<T> {
    /**
     * 奖品实体
     */
    private T entity;

    /**
     * id 区别实体的id，每个实体保证唯一
     */
    private Integer id;

    /**
     * 概率值,0.1-100---》0.1%--100
     */
    private Double ProbabilityValue;

}
