package com.ctrip.dcs.driver.trip.university.infrastructure.value;

/**
 * <AUTHOR>
 * @Description //TODO $
 * @Date $ $
 * @Param $
 * @return $
 **/
public class ZhiXLResultData {

    private String access_token;
    private Long expires_in;
    private Boolean success;
    private Integer action;
    private String id;
    private String msg;


    public String getAccess_token() {
        return access_token;
    }

    public void setAccess_token(String access_token) {
        this.access_token = access_token;
    }

    public Long getExpires_in() {
        return expires_in;
    }

    public void setExpires_in(Long expires_in) {
        this.expires_in = expires_in;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
