package com.ctrip.dcs.driver.domain.infrastructure.model.notice;

/**
 * Created by <AUTHOR> on 2023/3/6 10:35
 */
public class NoticeUserModel {
  /**
   * 用户类型 1司机 2向导
   */
  private Integer userType;

  /**
   * 用户id
   */
  private Long userId;

  /**
   * 1接送机 2包车 4向导
   */
  private String productType;

  /**
   * 0 未激活 1上线 2冻结 3下线
   */
  private Integer status;

  private Long cityId;

  private Long vehicleType;

  private NoticeUserModel(Builder builder) {
    userType = builder.userType;
    userId = builder.userId;
    productType = builder.productType;
    status = builder.status;
    cityId = builder.cityId;
    vehicleType = builder.vehicleType;
  }

  public static Builder newBuilder() {
    return new Builder();
  }

  public Integer getUserType() {
    return userType;
  }

  public Long getUserId() {
    return userId;
  }

  public String getProductType() {
    return productType;
  }

  public Integer getStatus() {
    return status;
  }

  public Long getCityId() {
    return cityId;
  }

  public Long getVehicleType() {
    return vehicleType;
  }

  public static final class Builder {
    private Integer userType;
    private Long userId;
    private String productType;
    private Integer status;
    private Long cityId;
    private Long vehicleType;

    private Builder() {
    }

    public Builder withUserType(Integer val) {
      userType = val;
      return this;
    }

    public Builder withUserId(Long val) {
      userId = val;
      return this;
    }

    public Builder withProductType(String val) {
      productType = val;
      return this;
    }

    public Builder withStatus(Integer val) {
      status = val;
      return this;
    }

    public Builder withCityId(Long val) {
      cityId = val;
      return this;
    }

    public Builder withVehicleType(Long val) {
      vehicleType = val;
      return this;
    }

    public NoticeUserModel build() {
      return new NoticeUserModel(this);
    }
  }
}
