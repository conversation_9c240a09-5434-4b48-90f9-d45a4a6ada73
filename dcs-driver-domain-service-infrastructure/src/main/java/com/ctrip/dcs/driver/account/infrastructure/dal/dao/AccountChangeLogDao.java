package com.ctrip.dcs.driver.account.infrastructure.dal.dao;

import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountChangeLogPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

@Dal("dcstransportdb_w")
public interface AccountChangeLogDao extends DalRepository<AccountChangeLogPO> {

    @Sql("update account_change_log set provider_data_location = ? where uid = ?")
    void updateUdl(String udl, String uid);
}
