package com.ctrip.dcs.driver.domain.infrastructure.adapter.verify;

import com.ctrip.dcs.driver.domain.infrastructure.convert.DriverTaskInfoConvert;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverCarInspectionRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverTaskRecordDao;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverCarInspectionRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverTaskRecordPO;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverCarInspectionRecordDO;
import com.ctrip.dcs.driver.domain.infrastructure.value.DriverTaskRecordDO;
import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum;
import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
public class DriverTaskAdapterImpl implements DriverTaskAdapter {

    private static final Logger LOGGER = LoggerFactory.getLogger(DriverTaskAdapter.class);

    @Autowired
    DriverTaskRecordDao driverTaskRecordDao;

    @Autowired
    DriverCarInspectionRecordDao driverCarInspectionRecordDao;

    /**
     * 查询任务
     * */
    @Override
    public DriverTaskRecordDO queryDriverTaskByTaskId(long driverId, String taskId){
        DriverTaskRecordPO driverTaskRecordPO;
        if(StringUtils.isNotBlank(taskId)) {
            driverTaskRecordPO = driverTaskRecordDao.queryTaskByTaskId(taskId);
        } else {
            driverTaskRecordPO = driverTaskRecordDao.queryDriverCurrentActiveTask(driverId);
        }
        if(Objects.isNull(driverTaskRecordPO)){
            return null;
        }
        return DriverTaskInfoConvert.toDriverTaskRecordDO(driverTaskRecordPO);
    }

    /**
     * 查询任务
     * */
    @Override
    public DriverTaskRecordDO queryDriverTaskByDriverOrderId(long driverId, String driverOrderId){
        DriverTaskRecordPO driverTaskRecordPO;
        if(StringUtils.isNotBlank(driverOrderId)) {
            driverTaskRecordPO = driverTaskRecordDao.queryTaskByDriverOrderId(driverId, driverOrderId);
        } else {
            driverTaskRecordPO = driverTaskRecordDao.queryDriverCurrentActiveTask(driverId);
        }
        if(Objects.isNull(driverTaskRecordPO)){
            return null;
        }
        return DriverTaskInfoConvert.toDriverTaskRecordDO(driverTaskRecordPO);
    }

    /**
     * 创建任务
     * */
    @Override
    public int insertTaskRecord(DriverTaskRecordDO driverTaskRecord){
        try {
            DriverTaskRecordPO driverTaskRecordPO = new DriverTaskRecordPO();
            driverTaskRecordPO.setDriverId(driverTaskRecord.getDriverId());
            driverTaskRecordPO.setTaskId(driverTaskRecord.getTaskId());
            driverTaskRecordPO.setTaskStatus(driverTaskRecord.getTaskStatus().getValue());
            driverTaskRecordPO.setTaskPeriodWorkTime(driverTaskRecordPO.getTaskPeriodWorkTime());
            driverTaskRecordPO.setTaskCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            driverTaskRecordPO.setTaskValidEndTime(null);
            driverTaskRecordPO.setTaskFinishedTime(null);
            driverTaskRecordPO.setTaskCanceledTime(null);
            driverTaskRecordPO.setCustomerOrderId(driverTaskRecord.getCustomerOrderId());
            driverTaskRecordPO.setDriverOrderId(driverTaskRecord.getDriverOrderId());
            driverTaskRecordPO.setCustomerOrderCarId(driverTaskRecord.getCustomerOrderCarId());
            driverTaskRecordPO.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.now()));
            driverTaskRecordPO.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
            return driverTaskRecordDao.insert(driverTaskRecordPO);
        }catch (Exception e){
            return 0;
        }
    }

    /**
     * 更新任务状态
     * */
    @Override
    public void updateTaskStatus(String taskId, DriverTaskStatusEnum newTaskStatus){
        DriverTaskRecordPO taskRecord = driverTaskRecordDao.queryTaskByTaskId(taskId);
        if(Objects.nonNull(taskRecord)){
            switch (newTaskStatus){
                case CANCEL://取消
                case EXPIRE://过期
                    int taskStatus = ObjectUtils.defaultIfNull(taskRecord.getTaskStatus(), 0);
                    DriverTaskStatusEnum taskStatusEnum = DriverTaskStatusEnum.ofValue(taskStatus);
                    if(taskStatusEnum.isActive()) {
                        taskRecord.setTaskStatus(newTaskStatus.getValue());
                        taskRecord.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
                        taskRecord.setTaskCanceledTime(Timestamp.valueOf(LocalDateTime.now()));
                        driverTaskRecordDao.update(taskRecord);
                    }
                    break;
                case FINISHED:
                    taskRecord.setTaskStatus(newTaskStatus.getValue());
                    taskRecord.setTaskFinishedTime(Timestamp.valueOf(LocalDateTime.now()));
                    taskRecord.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
                    driverTaskRecordDao.update(taskRecord);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 更新任务过期时间
     * */
    @Override
    public void updateTaskValidEndTime(String taskId, int expireMinutes){
        DriverTaskRecordPO taskRecord = driverTaskRecordDao.queryTaskByTaskId(taskId);
        if(Objects.nonNull(taskRecord)){
            int taskStatus = ObjectUtils.defaultIfNull(taskRecord.getTaskStatus(), 0);
            DriverTaskStatusEnum taskStatusEnum = DriverTaskStatusEnum.ofValue(taskStatus);
            if(taskStatusEnum.isActive()){
                LocalDateTime now = LocalDateTime.now();
                taskRecord.setTaskValidEndTime(Timestamp.valueOf(now.plusMinutes(expireMinutes)));	//订单完成后+2h
                taskRecord.setDatachangeLasttime(Timestamp.valueOf(now));
                driverTaskRecordDao.update(taskRecord);
            }
        }
    }

    /**
     * 查询司机出车检查信息
     * */
    @Override
    public List<DriverCarInspectionRecordDO> queryCarInspectionInfoList(String taskId) {
        List<DriverCarInspectionRecordPO> carInspectionRecordPOList = driverCarInspectionRecordDao.queryByTaskId(taskId);
        if(CollectionUtils.isEmpty(carInspectionRecordPOList)){
            return Collections.emptyList();
        }

        List<DriverCarInspectionRecordDO> driverCarInspectionRecordList = new ArrayList<>();
        carInspectionRecordPOList.forEach(r -> {
            driverCarInspectionRecordList.add(DriverTaskInfoConvert.toDriverCarInspectionRecordDO(r));
        });
        return driverCarInspectionRecordList;
    }

    /**
     * 更新初查检查提交数据
     * */
    @Override
    public void updateCarInspectionInfo(List<DriverCarInspectionRecordDO> carInspectionRecordList){

        if(CollectionUtils.isEmpty(carInspectionRecordList)){
            return;
        }

        List<DriverCarInspectionRecordPO> carInspectionRecordPOList = driverCarInspectionRecordDao.queryByTaskId(carInspectionRecordList.get(0).getTaskId());

        carInspectionRecordList.forEach(recordInfo -> {
            DriverCarInspectionRecordPO driverCarInspectionRecord = carInspectionRecordPOList.stream().filter(m->m.getTaskStepKey().equals(recordInfo.getTaskStepKey())).findFirst().orElse(null);
            if(driverCarInspectionRecord == null) {
                driverCarInspectionRecord = new DriverCarInspectionRecordPO();
                driverCarInspectionRecord.setDriverId(recordInfo.getDriverId());
                driverCarInspectionRecord.setTaskId(recordInfo.getTaskId());
                driverCarInspectionRecord.setTaskStepKey(recordInfo.getTaskStepKey());
                driverCarInspectionRecord.setTaskStepValue(recordInfo.getTaskStepValue());
                driverCarInspectionRecord.setDatachangeCreatetime(Timestamp.valueOf(LocalDateTime.now()));
                driverCarInspectionRecord.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
                driverCarInspectionRecordDao.insert(driverCarInspectionRecord);
            } else {
                //更新数据
                driverCarInspectionRecord.setTaskStepValue(recordInfo.getTaskStepValue());
                driverCarInspectionRecord.setDatachangeLasttime(Timestamp.valueOf(LocalDateTime.now()));
                driverCarInspectionRecordDao.update(driverCarInspectionRecord);
            }
        });
    }

}
