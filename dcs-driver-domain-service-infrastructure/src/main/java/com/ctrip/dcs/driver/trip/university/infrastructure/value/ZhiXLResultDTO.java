package com.ctrip.dcs.driver.trip.university.infrastructure.value;


/**
 * <AUTHOR>
 * @Description //TODO $
 * @Date $ $
 * @Param $
 * @return $
 **/
public class ZhiXLResultDTO {

    private Integer code;
    private String msg;
    private Boolean success;
    private ZhiXLResultData data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public ZhiXLResultData getData() {
        return data;
    }

    public void setData(ZhiXLResultData data) {
        this.data = data;
    }
}
