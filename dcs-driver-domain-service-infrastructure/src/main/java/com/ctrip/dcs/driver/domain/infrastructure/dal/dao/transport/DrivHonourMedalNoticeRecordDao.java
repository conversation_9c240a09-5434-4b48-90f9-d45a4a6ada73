package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DrivHonourMedalNoticeRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

@Dal("dcstransportdb_w")
public interface DrivHonourMedalNoticeRecordDao extends DalRepository<DrivHonourMedalNoticeRecordPO> {
    @Sql("select * from driv_honour_medal_notice_record where driver_id = ? limit 1")
    DrivHonourMedalNoticeRecordPO findOne(long driverId);
}
