package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo;
import com.ctrip.dcs.shopping.utils.ShoppingCollectionUtils;
import com.ctrip.dcs.shopping.utils.ShoppingFuncUtils;
import com.ctrip.igt.framework.common.jackson.JacksonUtil;
import com.dianping.cat.utils.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class LevelConfig {

    private List<FormLevelInfo> levelInfoList;

    public Map<Long, List<FormLevelInfo>> cityLevelMap = new HashMap<>();

    @QConfig("levle_config.json")
    public void levelConfigChange(String value) {
        this.levelInfoList = JacksonUtil.parseArray(value, FormLevelInfo.class);

        Map<Long, List<FormLevelInfo>> newMap = new HashMap<>();
        levelInfoList.stream().filter(o -> o.getStatus() == 0).sorted(Comparator.comparing(FormLevelInfo::getLevel)).forEach(levelInfo -> {
            String cityIdsStr = levelInfo.getCityIdsStr();
            if (StringUtils.isNotEmpty(cityIdsStr)) {
                levelInfo.setCityList(ShoppingCollectionUtils.toList(cityIdsStr.split(","), (ShoppingFuncUtils.Func1<String, Long>) Long::parseLong).stream().distinct().collect(Collectors.toList()));
            }
            levelInfo.getCityList().stream().forEach(city -> {
                if (!newMap.containsKey(city)) {
                    newMap.put(city, new ArrayList<>());
                }
                newMap.get(city).add(levelInfo);
            });
        });
        this.cityLevelMap = newMap;
    }

    public List<FormLevelInfo> getCityDriverLevel(Long cityId) {
        return this.cityLevelMap.get(cityId);
    }
}