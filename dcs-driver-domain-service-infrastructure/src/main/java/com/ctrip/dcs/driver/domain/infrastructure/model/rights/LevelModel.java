package com.ctrip.dcs.driver.domain.infrastructure.model.rights;

import lombok.*;

import java.math.BigDecimal;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class LevelModel {
    /**
     * 等级定义ID
     */
    private Long levelConfigId;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 月份索引，yyyy-MM
     */
    private String monthIdx;

    /**
     * 司机ID
     */
    private Long drivId;

    /**
     * 司机等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
    private Integer drivLevel;

    /**
     * 司机服务分
     */
    private BigDecimal drivPoint;

    /**
     * 司机活跃分
     */
    private BigDecimal drivActivity;

    /**
     * 司机排名
     */
    private Integer drivRank;

}
