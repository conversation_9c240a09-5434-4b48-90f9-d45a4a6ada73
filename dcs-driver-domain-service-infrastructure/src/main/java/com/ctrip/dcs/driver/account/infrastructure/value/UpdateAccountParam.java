package com.ctrip.dcs.driver.account.infrastructure.value;

import lombok.Data;

@Data
public class UpdateAccountParam {

    private String source;

    /**
     * uid
     */
    private String uid;

    /**
     * 手机国家码，必填
     */
    private String countryCode;

    /**
     * 注册手机号，必填
     */
    private String phoneNumber;

    /**
     * 绑定邮箱
     */
    private String email;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 派安盈账户id，可为空
     */
    private String payoneerAccountId;

    /**
     * 是否境外，true：境外；false：境内
     */
    private Boolean isOversea;

    private String ppmAccountId;

    /**
     * 修改人
     */
    private String modifyUser;
}
