package com.ctrip.dcs.driver.domain.infrastructure.value;

import com.ctrip.dcs.driver.domain.infrastructure.constant.DriverTaskStatusEnum;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;
import java.time.LocalDateTime;

@Getter
@Setter
public class DriverTaskRecordDO {
    private long driverId;
    private String taskId;
    private DriverTaskStatusEnum taskStatus;
    private String taskPeriodWorkTime;
    private Timestamp taskValidEndTime;
    private Timestamp taskFinishedTime;
    private Timestamp taskCanceledTime;
    private String customerOrderId;
    private String driverOrderId;
    private long customerOrderCarId;
    private LocalDateTime dataChangeCreateTime;
}