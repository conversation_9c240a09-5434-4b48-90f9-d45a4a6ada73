package com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Getter;
import lombok.Setter;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Types;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2025-03-26
 */
@Getter
@Setter
@Entity
@Database(name = "dcsdriverappdb")
@Table(name = "driver_task_record")
public class DriverTaskRecordPO implements DalPojo {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机id
     */
    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    private Long driverId;

    /**
     * 任务唯一标识
     */
    @Column(name = "task_id")
    @Type(value = Types.VARCHAR)
    private String taskId;

    /**
     * 任务状态默认0初始化生成1已完成2已过期3取消4
     */
    @Column(name = "task_status")
    @Type(value = Types.INTEGER)
    private Integer taskStatus;

    /**
     * 任务对应的工作时段
     */
    @Column(name = "task_period_work_time")
    @Type(value = Types.VARCHAR)
    private String taskPeriodWorkTime;

    /**
     * 生成任务的时间，同datachange_createtime
     */
    @Column(name = "task_create_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp taskCreateTime;

    /**
     * 任务有效的完成时间
     */
    @Column(name = "task_valid_end_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp taskValidEndTime;

    /**
     * 任务状态被置为完成的时间
     */
    @Column(name = "task_finished_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp taskFinishedTime;

    /**
     * 任务状态被置为取消的时间
     */
    @Column(name = "task_canceled_time")
    @Type(value = Types.TIMESTAMP)
    private Timestamp taskCanceledTime;

    /**
     * 用户单号
     */
    @Column(name = "customer_order_id")
    @Type(value = Types.VARCHAR)
    private String customerOrderId;

    /**
     * 司机单号
     */
    @Column(name = "driver_order_id")
    @Type(value = Types.VARCHAR)
    private String driverOrderId;

    /**
     * 用户订单对应的车辆id
     */
    @Column(name = "customer_order_car_id")
    @Type(value = Types.BIGINT)
    private Long customerOrderCarId;

    /**
     * 创建时间
     */
    @Column(name = "datachange_createtime")
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeCreatetime;

    /**
     * 更新时间
     */
    @Column(name = "datachange_lasttime", insertable = false, updatable = false)
    @Type(value = Types.TIMESTAMP)
    private Timestamp datachangeLasttime;
}