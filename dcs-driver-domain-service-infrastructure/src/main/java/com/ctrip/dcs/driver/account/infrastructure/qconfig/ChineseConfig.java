package com.ctrip.dcs.driver.account.infrastructure.qconfig;

import lombok.Getter;
import lombok.Setter;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.Map;

@Getter
@Setter
@Component
public class ChineseConfig {

    @QMapConfig("chinese.properties")
    private Map<String, String> configs;

    public String get(String key) {
        return configs.getOrDefault(key, key);
    }

}
