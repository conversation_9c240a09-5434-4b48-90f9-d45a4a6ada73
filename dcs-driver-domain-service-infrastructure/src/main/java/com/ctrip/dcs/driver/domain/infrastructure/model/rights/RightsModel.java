package com.ctrip.dcs.driver.domain.infrastructure.model.rights;

import lombok.*;

/**
 * <AUTHOR>
 * @Description
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class RightsModel {
    /**
     * id
     */
    private Long id;
    /**
     * 权益定义ID
     */
    private Long rightsConfigId;

    /**
     * 权益名称
     */
    private String rightsName;

    /**
     * 权益简介
     */
    private String rightsDesc;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 月份索引，yyyy-MM
     */
    private String monthIdx;

    /**
     * 司机ID
     */
    private Long drivId;

    /**
     * 司机等级 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
    private Integer drivLevel;

    /**
     * 权益类型 1福利金 2改派 3城市王者 4初级投诉豁免卡 5高级投诉豁免卡 6每日提现
     */
    private Integer rightsType;

    /**
     * 使用限制 0无限次
     */
    private Integer useLimit;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 权益状态 0 已发放 1已使用 2已过期 3已作废
     */
    private Integer rightsStatus;

    /**
     * 权益生效开始时间,默认北京时间
     */
    private String rightsStratTime;

    /**
     * 权益生效结束时间,默认北京时间
     */
    private String rightsEndTime;

    /**
     * 权益发放时间
     */
    private String rightsIssueTime;

    /**
     * 扩展字段 例如:福利金上限金额
     */
    private String extend;

}
