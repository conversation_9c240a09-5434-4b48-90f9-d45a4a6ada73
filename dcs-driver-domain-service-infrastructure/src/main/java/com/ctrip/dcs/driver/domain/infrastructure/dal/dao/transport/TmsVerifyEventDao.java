package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.TmsVerifyEventPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcstransportdb_w")
public interface TmsVerifyEventDao extends DalRepository<TmsVerifyEventPO> {
	@Sql("select * from tms_verify_event where verify_source_id = ? and verify_source_type = 1 order by datachange_createtime desc limit 1")
	List<TmsVerifyEventPO> findNeedVerifyEvents(Long driverId);
}
