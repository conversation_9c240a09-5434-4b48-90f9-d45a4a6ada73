package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverCarInspectionRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcsdriverappdb")
public interface DriverCarInspectionRecordDao extends DalRepository<DriverCarInspectionRecordPO> {
    @Sql("select * from driver_car_inspection_record where task_id = ?")
    List<DriverCarInspectionRecordPO> queryByTaskId(String taskId);
}
