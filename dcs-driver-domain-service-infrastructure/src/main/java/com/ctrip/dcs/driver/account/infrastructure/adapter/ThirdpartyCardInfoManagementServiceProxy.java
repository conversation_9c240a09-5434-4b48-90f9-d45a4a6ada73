package com.ctrip.dcs.driver.account.infrastructure.adapter;

import com.ctrip.finance.soa.fi.thridpartycardinfomanagement.base.ThirdpartyCardInfoManagementServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.payment.soa.thirdpartycardinfo.service.JwsRequestType;
import com.ctrip.payment.soa.thirdpartycardinfo.service.JwsResponseType;

@ServiceClient(value = ThirdpartyCardInfoManagementServiceClient.class)
public interface ThirdpartyCardInfoManagementServiceProxy {
    JwsResponseType createPayCardInfo(JwsRequestType request);

    JwsResponseType getPayCardInfos(JwsRequestType request);

    //批量根据卡号查询卡参考号-batchGetCardNoRefIdByCardNo     http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********#id-%E4%B8%89%E6%96%B9%E5%8D%A1%E6%9C%8D%E5%8A%A1%E6%8E%A5%E5%85%A5%EF%BC%88PCI%EF%BC%89-4.21%EF%BC%89%E6%89%B9%E9%87%8F%E6%A0%B9%E6%8D%AE%E5%8D%A1%E5%8F%B7%E6%9F%A5%E8%AF%A2%E5%8D%A1%E5%8F%82%E8%80%83%E5%8F%B7-batchGetCardNoRefIdByCardNo
    JwsResponseType batchGetCardNoRefIdByCardNo(JwsRequestType request);
}
