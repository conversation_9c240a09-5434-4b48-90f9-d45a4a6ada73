package com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2022-07-25
 */
@Getter
@Setter
@Entity
@Database(name = "igtbidb_W")
@Table(name = "adm_prd_trh_driver_honor_info")
public class AdmPrdTrhDriverHonorInfoPO implements DalPojo {

    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    private Long id;

    /**
     * 司机id
     */
    @Column(name = "drv_id")
    @Type(value = Types.BIGINT)
    private Long drvId;

    /**
     * 司机姓名
     */
    @Column(name = "drv_name")
    @Type(value = Types.VARCHAR)
    private String drvName;

    /**
     * 城市id
     */
    @Column(name = "city_id")
    @Type(value = Types.BIGINT)
    private Long cityId;

    /**
     * 批次时间
     */
    @Column(name = "batch_time")
    @Type(value = Types.BIGINT)
    private Long batchTime;

    /**
     * 数据时间
     */
    @Column(name = "data_time")
    @Type(value = Types.VARCHAR)
    private String dataTime;

    /**
     * 数据时间所在周的周一日期
     */
    @Column(name = "data_time_week")
    @Type(value = Types.VARCHAR)
    private String dataTimeWeek;

    /**
     * 数据时间所在月
     */
    @Column(name = "data_time_month")
    @Type(value = Types.VARCHAR)
    private String dataTimeMonth;

    /**
     * 激活天数
     */
    @Column(name = "activation_days")
    @Type(value = Types.BIGINT)
    private Long activationDays;

    /**
     * 平台累计完单量
     */
    @Column(name = "order_cnt")
    @Type(value = Types.BIGINT)
    private Long orderCnt;

    /**
     * 当前完单量勋章等级
     */
    @Column(name = "order_cnt_medal_grade")
    @Type(value = Types.INTEGER)
    private Integer orderCntMedalGrade;

    /**
     * 本批次获得完单量勋章的个数
     */
    @Column(name = "order_cnt_medal_batch_cnt")
    @Type(value = Types.INTEGER)
    private Integer orderCntMedalBatchCnt;

    /**
     * 平台累计完单天数
     */
    @Column(name = "order_day")
    @Type(value = Types.BIGINT)
    private Long orderDay;

    /**
     * 当前完单天数勋章等级
     */
    @Column(name = "order_day_medal_grade")
    @Type(value = Types.INTEGER)
    private Integer orderDayMedalGrade;

    /**
     * 本批次获得完单天数勋章的个数
     */
    @Column(name = "order_day_medal_batch_cnt")
    @Type(value = Types.INTEGER)
    private Integer orderDayMedalBatchCnt;

    /**
     * 平台累计好评数
     */
    @Column(name = "good_comment")
    @Type(value = Types.BIGINT)
    private Long goodComment;

    /**
     * 当前好评勋章等级
     */
    @Column(name = "good_comment_medal_grade")
    @Type(value = Types.INTEGER)
    private Integer goodCommentMedalGrade;

    /**
     * 本批次获得好评勋章的个数
     */
    @Column(name = "good_comment_medal_batch_cnt")
    @Type(value = Types.INTEGER)
    private Integer goodCommentMedalBatchCnt;

    /**
     * 态度好服务佳标签数
     */
    @Column(name = "tdhfwj_tag_cnt")
    @Type(value = Types.BIGINT)
    private Long tdhfwjTagCnt;

    /**
     * 态度好服务佳是否达成
     */
    @Column(name = "tdhfwj_tag_medal")
    @Type(value = Types.INTEGER)
    private Integer tdhfwjTagMedal;

    /**
     * 车内整洁标签数
     */
    @Column(name = "cnzj_tag_cnt")
    @Type(value = Types.BIGINT)
    private Long cnzjTagCnt;

    /**
     * 车内整洁是否达成
     */
    @Column(name = "cnzj_tag_medal")
    @Type(value = Types.INTEGER)
    private Integer cnzjTagMedal;

    /**
     * 活地图认路准标签数
     */
    @Column(name = "hdtrlz_tag_cnt")
    @Type(value = Types.BIGINT)
    private Long hdtrlzTagCnt;

    /**
     * 活地图认路准是否达成
     */
    @Column(name = "hdtrlz_tag_medal")
    @Type(value = Types.INTEGER)
    private Integer hdtrlzTagMedal;

    /**
     * 主动帮拿行李标签数
     */
    @Column(name = "zdbnxl_tag_cnt")
    @Type(value = Types.BIGINT)
    private Long zdbnxlTagCnt;

    /**
     * 主动帮拿行李是否达成
     */
    @Column(name = "zdbnxl_tag_medal")
    @Type(value = Types.INTEGER)
    private Integer zdbnxlTagMedal;

    /**
     * 已获得勋章个数
     */
    @Column(name = "medal_get_cnt")
    @Type(value = Types.BIGINT)
    private Long medalGetCnt;

    /**
     * 本周累计完单量
     */
    @Column(name = "week_order_cnt")
    @Type(value = Types.BIGINT)
    private Long weekOrderCnt;

    /**
     * 周排行榜排名
     */
    @Column(name = "week_ranking")
    @Type(value = Types.BIGINT)
    private Long weekRanking;

    /**
     * 本月累计完单量
     */
    @Column(name = "month_order_cnt")
    @Type(value = Types.BIGINT)
    private Long monthOrderCnt;

    /**
     * 月排行榜排名
     */
    @Column(name = "month_ranking")
    @Type(value = Types.BIGINT)
    private Long monthRanking;

    @Column(name = "drv_status")
    @Type(value = Types.INTEGER)
    private Integer drvStatus;
}

