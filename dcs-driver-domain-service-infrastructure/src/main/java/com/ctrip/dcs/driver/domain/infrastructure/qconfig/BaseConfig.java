package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import org.apache.logging.log4j.util.Strings;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 */
public class BaseConfig {
    protected Map<String, String> kv;

    public String getString(String key) {
        String val = kv.get(key);
        if (null != val) {
            return val;
        } else {
            return Strings.EMPTY;
        }
    }
}
