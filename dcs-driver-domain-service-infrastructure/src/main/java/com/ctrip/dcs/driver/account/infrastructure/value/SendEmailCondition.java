package com.ctrip.dcs.driver.account.infrastructure.value;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SendEmailCondition {

    /**
     * 模板号
     */
    String sendCode;
    /**
     * 标题
     */
    String subject;

    /**
     * 发件人邮箱
     */
    String sender;

    /**
     * 收件人邮箱列表
     */
    List<String> receiver;

    String bodyContent;

    boolean isBodyHtml;
}
