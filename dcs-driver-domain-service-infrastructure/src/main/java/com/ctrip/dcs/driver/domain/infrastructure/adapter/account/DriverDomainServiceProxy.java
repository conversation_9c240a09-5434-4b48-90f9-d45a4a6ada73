package com.ctrip.dcs.driver.domain.infrastructure.adapter.account;


import com.ctrip.dcs.driver.domain.account.QueryAccountByDriverIdRequestType;
import com.ctrip.dcs.driver.domain.account.QueryAccountByDriverIdResponseType;
import com.ctrip.dcs.driver.domain.interfaces.api.DscDriverDomainServiceClient;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = DscDriverDomainServiceClient.class, format = "json")
public interface DriverDomainServiceProxy {
    QueryAccountByDriverIdResponseType queryAccountByDriverId(QueryAccountByDriverIdRequestType request);
}
