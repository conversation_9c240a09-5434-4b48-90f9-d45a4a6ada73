package com.ctrip.dcs.driver.account.infrastructure.constant;
/*
作者：pl.yang
创建时间：2025/5/14-下午3:59-2025
*/


/**
 * <AUTHOR>
 * @version V1.0
 * @Title: BankCardAreaTypeEnum
 * @Package com.ctrip.dcs.driver.account.infrastructure.constant
 * @Description: (用一句话描述该文件做什么)
 * @date 2025/5/14 下午3:59
 */


public enum BankCardAreaTypeEnum {
    domesticdriver_domesticcard(1),
    //不存在这种 暂时写在这里
    domesticdriver_overseascard(4),
    overseasDriver_domesticCard(2),
    overseasDriver_overseasCard(3);

    //1:境内司机境内卡
    //2：境外司机境内卡
    //3：境外司机境外卡
    private int code;

    BankCardAreaTypeEnum(int code) {
        this.code = code;
    }

    public static BankCardAreaTypeEnum getByCode(Integer areaType) {
        for (BankCardAreaTypeEnum value : values()) {
            if (value.code == areaType) {
                return value;
            }
        }
        return null;
    }

    public int getCode() {
        return code;
    }
}
