package com.ctrip.dcs.driver.domain.infrastructure.model.honour;

import com.ctrip.dcs.driver.domain.infrastructure.constant.HonourEnum;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
public class MedalBasicInfoModel implements Serializable {
    private static final long serialVersionUID = 1L;
    private HonourEnum.MedalTypeEnum type;
    private String medalCode;
    private String bimedalType;
    private Long bimedalGrade;
    private Long medalGradeMin;
    private Long medalGradeMax;
}
