package com.ctrip.dcs.driver.account.infrastructure.qconfig;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.Arrays;


@Getter
@Setter
@Component
@QMapConfig("driver.account.properties")
public class DriverAccountConfig {

    /**
     * Q侧http接口域名
     */
    private String qunarCarInnerHost;

    /**
     * q侧用户中心获取uid接口
     */
    private String qunarUserCenterFetchUserIdUrl;

    /**
     * 获取用户id重试次数
     */
    private int fetchUserIdRetryCount = 2;

    /**
     * 账户信息缓存时间，单位秒
     */
    private int accountInfoRedisCacheSeconds = 3600;

    /**
     * 账户身份映射信息缓存时间，单位秒
     */
    private long accountSourceUIDMappingRedisCacheSeconds = 3600;

    /**
     * 账户相关操作分布式锁等待时间（毫秒）
     */
    private int accountLockWaitMills = 3000;

    /**
     * 批量查询账户数量限制
     */
    private int batchQueryAccountCountLimit = 100;

    /**
     * 司导迁移-司机产线列表，3为包车
     */
    private int dgTransferDriverProLine = 3;

    /**
     * 开启新账户注册流程
     */
    private int enableNewRegisterProgressSwitch = 0;

    /**
     * 支持司导的版本号
     */
    private int supportDriverGuideVersion = 8379;

    /**
     * job sleep 时间
     */
    private int jobSleepMills = 50;

    /**
     * 账户更新消息延迟秒数
     */
    private int accountUpdateQmqDelaySeconds = 2;

    /**
     * 开启注册到不同子系统灰度城市id（-1表示全量）
     */
    private String registerToDiffSubSystemCityIds = "";

    /**
     * 查询不同子系统灰度开关
     */
    private boolean enableQueryDiffSubSystem = false;
    /**
     * udl缓存超时时间，单位秒
     */
    private long udLCacheTimeOut = 60 * 60 * 24;

    /**
     * udl缓存最大数量
     */
    private int udLCacheMaxCount= 10000;


    private String overseaYeePayBusinessType="";
    private String domesticYeePayBusinessType="";
    //绑定成功的
    private String bandOversCardSuccessTemplateId ="";  //4089
    //绑定失败
    private String bandOversCardFailTemplateId ="";     //4090
    //提现失败通知
    private String driverWithDrawNotifyFailTemplateId ="";     //4091

    private boolean  enableCacheAccountInfo;
    //是否校验银行卡类型是否为储蓄卡
    private boolean checkBandCardTypeIsDepositCard=true;
    private String domesticYeePayBusinessDetail = "";
    private String overseaYeePayBusinessDetail="";

    /**
     * 检测司机银行卡状态线程休眠时间
     */
    long  checkStatusByUidThreadSleepMills=100;


}
