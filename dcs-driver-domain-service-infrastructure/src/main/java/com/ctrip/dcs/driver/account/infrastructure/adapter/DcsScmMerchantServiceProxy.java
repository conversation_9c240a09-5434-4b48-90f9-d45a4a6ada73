package com.ctrip.dcs.driver.account.infrastructure.adapter;

import com.ctrip.dcs.scm.merchant.interfaces.DcsScmMerchantServiceClient;
import com.ctrip.dcs.scm.merchant.interfaces.message.*;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = DcsScmMerchantServiceClient.class, format = "json")
public interface DcsScmMerchantServiceProxy {

  CheckDriverBandCardValidResponseType checkDriverBandCardValid(CheckDriverBandCardValidRequestType request);

  //把银行卡信息提交给易宝
  YeepayRecipientReportResponseType yeepayRecipientReport(YeepayRecipientReportRequestType request);
  ///查询银行卡信息
  YeepayGetPayeeQueryResponseType yeepayGetPayeeQuery(YeepayGetPayeeQueryRequestType request);
  //在易宝删除银行卡
  YeepayRecipientDeleteResponseType yeepayRecipientDelete(YeepayRecipientDeleteRequestType request);



}
