package com.ctrip.dcs.driver.domain.infrastructure.constant;


import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;

public enum LevelEnum {
    LEVEL_TYPE_BRONZE(0, "bronze", "show.driver.grade.bronzelevel"),
    LEVEL_TYPE_SILVER(1, "silver", "show.driver.grade.silverlevel"),
    LEVEL_TYPE_GOLD(2, "gold", "show.driver.grade.goldenlevel"),
    LEVEL_TYPE_PLATINUM(3, "platinum", "show.driver.grade.platinumlevel"),
    LEVEL_TYPE_DIAMOND(4, "diamond", "show.driver.grade.diamondlevel"),
    LEVEL_TYPE_REGULAR_MEDAL(100, "regular_medal", "show.driverlevel.regular"),
    LEVEL_TYPE_BRONZE_MEDAL(101, "bronze_medal", "show.driverlevel.bronze"),
    LEVEL_TYPE_SILVER_MEDAL(102, "silver_medal", "show.driverlevel.silver"),
    LEVEL_TYPE_GOLD_MEDAL(103, "gold_medal", "show.driverlevel.gold");

    // 境外等级
    private static final List<LevelEnum> OVERSEA_LEVEL_LIST = Lists.newArrayList(LEVEL_TYPE_REGULAR_MEDAL, LEVEL_TYPE_BRONZE_MEDAL, LEVEL_TYPE_SILVER_MEDAL, LEVEL_TYPE_GOLD_MEDAL);

    private int code;
    private String name;
    private String levelSharkKey;

    LevelEnum(int code, String name, String levelSharkKey) {
        this.code = code;
        this.name = name;
        this.levelSharkKey = levelSharkKey;
    }

    public static LevelEnum getByCode(Integer code) {
        for (LevelEnum levelEnum : LevelEnum.values()) {
            if (levelEnum.code == code) {
                return levelEnum;
            }
        }
        return LEVEL_TYPE_BRONZE;
    }

    public static boolean isOverseaLevel(Integer level) {
        return level != null && OVERSEA_LEVEL_LIST.stream().anyMatch(o -> Objects.equals(o.getCode(), level));
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLevelSharkKey() {
        return levelSharkKey;
    }
}
