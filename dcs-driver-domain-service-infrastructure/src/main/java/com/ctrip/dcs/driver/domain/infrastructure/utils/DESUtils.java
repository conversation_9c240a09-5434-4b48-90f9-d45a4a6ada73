package com.ctrip.dcs.driver.domain.infrastructure.utils;

import com.ctrip.igt.framework.common.clogging.Logger;
import com.ctrip.igt.framework.common.clogging.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

public final class DESUtils {
    private static final Logger logger = LoggerFactory.getLogger(DESUtils.class);
    private DESUtils() {
    }

    public static String decryptDES(String decryptString, String decryptKey) {
        try {
            SecretKeySpec key = new SecretKeySpec(getKey(decryptKey), "DES");
            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(2, key);
            byte[] decryptedData = cipher.doFinal(hexStringToByte(decryptString));
            return new String(decryptedData, "UTF-8");
        } catch (Exception var5) {
            logger.warn(var5);
            return null;
        }
    }

    public static String encryptDES(String encryptString, String encryptKey) {
        try {
            SecretKeySpec key = new SecretKeySpec(getKey(encryptKey), "DES");
            Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            cipher.init(1, key);
            byte[] encryptedData = cipher.doFinal(encryptString.getBytes("UTF-8"));
            return bytesToHexString(encryptedData);
        } catch (Exception var5) {
            logger.warn(var5);
            return null;
        }
    }

    private static String bytesToHexString(byte[] bArray) {
        if (bArray == null) {
            return "";
        } else {
            StringBuilder sb = new StringBuilder(bArray.length);

            for(int i = 0; i < bArray.length; ++i) {
                String sTemp = Integer.toHexString(255 & bArray[i]);
                if (sTemp.length() < 2) {
                    sb.append(0);
                }

                sb.append(sTemp.toUpperCase());
            }

            return sb.toString();
        }
    }

    private static byte[] hexStringToByte(String hex) {
        int len = hex.length() / 2;
        byte[] result = new byte[len];
        char[] achar = hex.toCharArray();

        for(int i = 0; i < len; ++i) {
            int pos = i * 2;
            result[i] = (byte)(toByte(achar[pos]) << 4 | toByte(achar[pos + 1]) & 255);
        }

        return result;
    }

    private static byte[] getKey(String keyRule) {
        byte[] keyByte = keyRule.getBytes();
        byte[] byteTemp = new byte[8];

        for(int i = 0; i < byteTemp.length && i < keyByte.length; ++i) {
            byteTemp[i] = keyByte[i];
        }

        Key key = new SecretKeySpec(byteTemp, "DES");
        return key.getEncoded();
    }

    private static byte toByte(char c) {
        return (byte)"0123456789ABCDEF".indexOf(c);
    }
}
