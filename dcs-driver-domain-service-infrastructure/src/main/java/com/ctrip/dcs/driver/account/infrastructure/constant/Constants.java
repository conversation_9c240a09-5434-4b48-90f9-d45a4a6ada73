package com.ctrip.dcs.driver.account.infrastructure.constant;

import com.google.common.collect.Lists;

import java.util.List;

public interface Constants {

    /**
     * 账户基本信息变更topic
     */
    String ACCOUNT_BASE_INFO_UPDATE_TOPIC = "dcs.driver.account.base.info.update";

    /**
     * 账户变更日志topic
     */
    String ACCOUNT_CHANGE_LOG_TOPIC = "dcs.driver.account.change.log";

    /**
     * app push message
     */
    String DRIVER_PUSH_MESSAGE_TOPIC = "dcs.driver.push.message";

    /**
     * 账户相关操作分布式锁key模板
     */
    String ACCOUNT_LOCK_KEY_PATTERN = "dcs:driver:account:lock:%s:%s";
    String ACCOUNT_LOCK_KEY_PATTERN_V1 = "dcs:driver:accountv1:lock:%s:%s";

    /**
     * 账户变更分布式锁
     */
    String ACCOUNT_UPDATE_KEY_PATTERN = "dcs:driver:account:update:lock:%s";

    /**
     * 司机无效状态 0:未激活，1：上线，2冻结，3下线
     */
    List<Integer> DRIVER_INVALID_STATUS = Lists.newArrayList(0, 3);

    /**
     * 绑卡相关操作分布式锁key模板  境内
     */
    String BANKCARD_LOCK_KEY_PATTERN = "dcs:driver:bankcard:lock:%s";
    //绑卡相关操作分布式锁key模板 境外
    String BANKCARD_OVERSEA_LOCK_KEY_PATTERN = "dcs:driver:bankcard:lock:%s";
    /**
     * 供应链司机审核通过消息topic
     */
    String DRIVER_APPROVE_TO_OFFICIAL_TOPIC = "dcs.tms.transport.approve.to.official.qmq";
    /**
     * 数据dbname
     */
    String TMS_TRANSPORT_DBNAME = "dcstransportdb_w";

    String DRIVER_CARINSPECTION_NOTICE = "dcs.driver.terminal.carinspection.notice";

    /**
     * 司机账号 绑定银行卡 结果处理
     */
    String DRIVER_BANK_CARD_REGISTRATION_RESULT="dcs.driver.bank.card.registration.result";
    //司机提现失败，成功通知
    String DRIVER_WITHDRAW_RESULT="dcs.driver.withdraw.result.notify";

    // udl 国际
    public static final String UDL_US_SPD = "US_SPD";
    //udl 国内
    public static final String UDL_CN_CSPD = "CN_CSPD";

    public static final String RequestFrom_US_SPD ="SPD" ;
    public static final String RequestFrom_CN_CSPD ="CSPD" ;

    public static final String UDL_REGISTER_SOURCE_OTA="OTA" ;
    /**
     * ota 司机账号生成udl 分布式锁的key
     */
    String ACCOUNT_OTA_CREATE_UDL_KEY_PATTERN = "dcs:driver:ota:account:udl:create:lock:%s";
}
