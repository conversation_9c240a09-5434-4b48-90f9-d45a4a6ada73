package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverAppGuidanceControlPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;


@Dal("dcstransportdb_w")
public interface DriverAppGuidanceControlDao extends DalRepository<DriverAppGuidanceControlPO> {
    @Sql("select * from driver_app_guidance_control where uid = ? and guidance_type in (?)")
    List<DriverAppGuidanceControlPO> findMany(String uid, List<String> guidanceTypes);
}
