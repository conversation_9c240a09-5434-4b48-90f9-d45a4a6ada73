package com.ctrip.dcs.driver.domain.infrastructure.geateway.impl;

import com.ctrip.dcs.driver.domain.infrastructure.geateway.GeoGateway;
import com.ctrip.dcs.driver.domain.infrastructure.qconfig.SystemQConfig;
import com.ctrip.dcs.geo.domain.repository.TimeZoneRepository;
import com.ctrip.dcs.geo.domain.value.TransformTimeZoneParameter;
import com.ctrip.dcs.geo.domain.value.TransformTimeZoneResult;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GeoGatewayImpl implements GeoGateway {
    public static final Long BEIJING_CITY_ID = 1L;

    /**
     * 批量转换时区size
     */
    private static final String BATCH_TRANSFORM_TIME_ZONE_SIZE = "batch_transform_time_zone_size";

    @Resource
    TimeZoneRepository timeZoneRepository;

    @Resource
    SystemQConfig systemQConfig;

    @Override
    public LocalDateTime getLocalCurrentTime(Long targetCityId) {
        return convertToLocalTime(LocalDateTime.now(), targetCityId);
    }

    @Override
    public Map<Long, LocalDateTime> getLocalCurrentTime(List<Long> targetCityIdList) {
        List<TransformTimeZoneResult> list = convertToLocalTime(LocalDateTime.now(), targetCityIdList);
        return list.stream().collect(Collectors.toMap(TransformTimeZoneResult::getTargetCityId, TransformTimeZoneResult::getTargetDateTime, (o, n) -> o));
    }

    @Override
    public LocalDateTime convertToLocalTime(LocalDateTime sourceDateTime, Long targetCityId) {
        if (targetCityId == null) {
            return null;
        }
        TransformTimeZoneResult result = transform(sourceDateTime, BEIJING_CITY_ID, targetCityId);
        return result != null ? result.getTargetDateTime() : null;
    }


    public List<TransformTimeZoneResult> convertToLocalTime(LocalDateTime sourceDateTime, List<Long> targetCityIdList) {
        List<TransformTimeZoneParameter> paramList = targetCityIdList.stream().distinct()
                .map(o -> new TransformTimeZoneParameter(BEIJING_CITY_ID, sourceDateTime, o)).collect(Collectors.toList());
        return transform(paramList);
    }

    private TransformTimeZoneResult transform(LocalDateTime sourceDateTime, Long sourceCityId, Long targetCityId) {
        if (sourceDateTime == null) {
            return null;
        }
        TransformTimeZoneParameter request = TransformTimeZoneParameter.builder()
                .sourceCityId(sourceCityId)
                .sourceDateTime(sourceDateTime)
                .targetCityId(targetCityId)
                .build();
        return timeZoneRepository.transform(request);
    }

    private List<TransformTimeZoneResult> transform(List<TransformTimeZoneParameter> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        // 分批查询
        int batchTransformTimeZone = Integer.parseInt(StringUtils.defaultIfBlank(systemQConfig.getString (BATCH_TRANSFORM_TIME_ZONE_SIZE), "50"));
        List<TransformTimeZoneResult> resultList = Lists.newArrayList();
        List<List<TransformTimeZoneParameter>> parts = Lists.partition(list, batchTransformTimeZone);
        for (List<TransformTimeZoneParameter> part : parts) {
            resultList.addAll(timeZoneRepository.transform(part));
        }
        return resultList;
    }

}
