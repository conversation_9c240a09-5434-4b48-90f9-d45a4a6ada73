package com.ctrip.dcs.driver.account.infrastructure.dal.dao;

import com.ctrip.dcs.driver.account.infrastructure.dal.po.AccountBankCardRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcsdriverappdb")
public interface AccountBankCardRecordDao extends DalRepository<AccountBankCardRecordPO> {

    //绑定，以及审核中那条数据
    @Sql("select * from account_bank_card_record where uid = ? and operate_type in(1,3) order by datachange_lasttime desc limit 1")
    AccountBankCardRecordPO queryLastAccountBankCardByUid(String driverUid);


    @Sql("select * from account_bank_card_record where uid = ? and bank_card_no = ? order by datachange_lasttime desc limit 1")
    AccountBankCardRecordPO queryAccountBankCardByIdCard(String uid, String idCard);

    @Sql("select * from account_bank_card_record where request_id=? order by datachange_lasttime desc limit 1")
    AccountBankCardRecordPO findOneByRequestId(String requestId);


    @Sql("select * from account_bank_card_record where uid = ? and bank_card_no = ? and operate_type = 1 order by datachange_lasttime desc limit 1")
    AccountBankCardRecordPO queryAccountBindingBankCard(String uid,String idCard);

    @Sql("select * from account_bank_card_record where bank_card_no=?")
    AccountBankCardRecordPO findOneByBankCardNo(String bankCardNo);

    @Sql("update account_bank_card_record set operate_type = ? where id = ?")
    int updateStatus(Integer cardStatusCode, Long id);

    //审核中
    @Sql("select * from account_bank_card_record where operate_type = 3 and datachange_lasttime>=? and datachange_lasttime<?  and id > ? limit 1000")
    List<AccountBankCardRecordPO> queryBankCardProcessing(String startDate, String endDate, long id);

    //todo    bank_card_no 加一个索引
    @Sql("select * from account_bank_card_record where bank_card_no=? order by datachange_lasttime desc limit 1")
    AccountBankCardRecordPO queryBankCardbyBardNO(String bardCardNo);
}
