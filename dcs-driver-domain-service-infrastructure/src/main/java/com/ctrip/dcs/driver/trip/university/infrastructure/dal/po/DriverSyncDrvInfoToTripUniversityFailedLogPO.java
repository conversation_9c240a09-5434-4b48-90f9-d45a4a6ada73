package com.ctrip.dcs.driver.trip.university.infrastructure.dal.po;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.Data;

import javax.persistence.*;
import java.sql.Types;
import java.time.LocalDateTime;

@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_sync_drv_info_to_trip_university_failed_log")
@Data
public class DriverSyncDrvInfoToTripUniversityFailedLogPO implements DalPojo {

  /**
   * 主键
   */
  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.AUTO)
  @Type(value = Types.BIGINT)
  private Long id;

  /**
   * 司机UID
   */
  @Column(name = "uid")
  @Type(value = Types.VARCHAR)
  private String uid;

  /**
   * 司机ID
   */
  @Column(name = "drv_id")
  @Type(value = Types.BIGINT)
  private Long drvId;

  /**
   * 重试次数
   */
  @Column(name = "retry_count")
  @Type(value = Types.INTEGER)
  private Integer retryCount;

  /**
   * 创建时间
   */
  @Column(name = "datachange_createtime")
  @Type(value = Types.TIMESTAMP)
  private LocalDateTime datachangeCreatetime;

  /**
   * 创建人
   */
  @Column(name = "create_user")
  @Type(value = Types.VARCHAR)
  private String createUser;

  /**
   * 变更人
   */
  @Column(name = "modify_user")
  @Type(value = Types.VARCHAR)
  private String modifyUser;

  /**
   * 更新时间
   */
  @Column(name = "datachange_lasttime", insertable = false, updatable = false)
  @Type(value = Types.TIMESTAMP)
  private LocalDateTime datachangeLasttime;

  /**
   * 失败原因代码
   */
  @Column(name = "failed_reason_code")
  @Type(value = Types.INTEGER)
  private Integer failedReasonCode;

  /**
   * 失败具体原因
   */
  @Column(name = "failed_reason_detail")
  @Type(value = Types.VARCHAR)
  private String failedReasonDetail;
}
