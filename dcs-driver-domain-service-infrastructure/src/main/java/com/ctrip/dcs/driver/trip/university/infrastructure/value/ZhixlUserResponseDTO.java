package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ZhixlUserResponseDTO {
  private int code;
  private boolean success;
  private String respon_id;
  private String msg;

  public boolean isSuccess() {
    return success && Objects.equals(code, TripUniversityConstants.SUCCESS_CODE);
  }
}
