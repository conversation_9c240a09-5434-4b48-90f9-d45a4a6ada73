package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.transport;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.transport.DriverOrderPushConfigPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

/**
 * 司机配置信息
 */
@Dal("dcstransportdb_w")
public interface DriverOrderPushConfigDao extends DalRepository<DriverOrderPushConfigPO>{

	@Sql("select * from drv_driver_order_push_config where drv_id = ?")
	DriverOrderPushConfigPO findOne(Long driverId);

	@Sql("select * from drv_driver_order_push_config where id > 0 order by id limit ?,?")
	List<DriverOrderPushConfigPO> findByLimit(int startIndex, int count);
}
