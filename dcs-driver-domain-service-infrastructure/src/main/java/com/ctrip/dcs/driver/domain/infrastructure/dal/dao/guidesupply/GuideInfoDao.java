package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.guidesupply;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.guidesupply.GuideInfoPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("dcsguidesupplydb")
public interface GuideInfoDao extends DalRepository<GuideInfoPO> {
  @Sql("select guide_id from guide_info order by guide_id desc limit 1")
  List<GuideInfoPO> queryMaxGuideIdInfo();

  @Sql("select guide_id from guide_info where guide_id >= ? and guide_id < ?")
  List<GuideInfoPO> queryGuideInfoListByGuideId(Long startGuideId,Long endGuideId);
}
