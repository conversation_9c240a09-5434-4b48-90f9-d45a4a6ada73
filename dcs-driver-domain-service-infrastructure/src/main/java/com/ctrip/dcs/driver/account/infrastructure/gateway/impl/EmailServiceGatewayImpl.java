package com.ctrip.dcs.driver.account.infrastructure.gateway.impl;

import com.ctrip.dcs.driver.account.infrastructure.adapter.EmailServiceProxy;
import com.ctrip.dcs.driver.account.infrastructure.gateway.EmailServiceGateway;
import com.ctrip.dcs.driver.account.infrastructure.value.SendEmailCondition;
import com.ctrip.dcs.go.log.Log;
import com.ctrip.dcs.go.util.JsonUtil;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailRequest;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class EmailServiceGatewayImpl implements EmailServiceGateway {
    Log log = Log.getInstance(EmailServiceGatewayImpl.class);
    @Resource
    EmailServiceProxy proxy;

    @Override
    public boolean sendEmail(SendEmailCondition condition) {
        if (StringUtils.isBlank(condition.getSender()) || CollectionUtils.isEmpty(condition.getReceiver())) {
            return false;
        }
        try {
            SendEmailResponse sendEmailResponse = proxy.sendEmail(convert(condition));
            if (sendEmailResponse.getResultCode() != 1) {
                log.info("sendEmail fail", JsonUtil.toString(sendEmailResponse));
                return false;
            }
        } catch (Exception e) {
            log.warn("sendEmail error", e);
        }
        return true;
    }

    protected SendEmailRequest convert(SendEmailCondition condition) {
        SendEmailRequest request = new SendEmailRequest();
        request.setSendCode(condition.getSendCode());
        request.setAppID(Integer.valueOf(Foundation.app().getAppId()));
        request.setSender(condition.getSender());
        request.setRecipient(condition.getReceiver());
        request.setSubject(condition.getSubject());
        request.setBodyTemplateID(Integer.parseInt(condition.getSendCode()));
        request.setBodyContent(condition.getBodyContent());
        request.setIsBodyHtml(condition.isBodyHtml());
        return request;
    }
}
