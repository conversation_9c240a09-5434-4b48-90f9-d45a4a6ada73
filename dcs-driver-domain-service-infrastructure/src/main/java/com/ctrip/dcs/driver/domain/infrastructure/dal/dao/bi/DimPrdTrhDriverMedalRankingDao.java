package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.bi;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.bi.DimPrdTrhDriverMedalRankingPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

import java.util.List;

@Dal("igtbidb_W")
public interface DimPrdTrhDriverMedalRankingDao extends DalRepository<DimPrdTrhDriverMedalRankingPO> {
    @Sql("select * from dim_prd_trh_driver_medal_ranking where drv_id = ?")
    public List<DimPrdTrhDriverMedalRankingPO> findDriverRankInfo(long driverId);
}
