package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverTaskRecordPO;
import com.ctrip.igt.framework.dal.DalRepository;
import com.ctrip.igt.framework.dal.query.Dal;
import com.ctrip.igt.framework.dal.query.Sql;

@<PERSON>("dcsdriverappdb")
public interface DriverTaskRecordDao extends DalRepository<DriverTaskRecordPO> {

    @Sql("select * from driver_task_record where driver_id = ? and driver_order_id = ? order by id desc limit 1")
    DriverTaskRecordPO queryTaskByDriverOrderId(long driverId, String driverOrderId);

    @Sql("select * from driver_task_record where task_id = ? order by id desc limit 1")
    DriverTaskRecordPO queryTaskByTaskId(String taskId);

    @Sql("select * from driver_task_record where driver_id = ? order by id desc limit 1")
    DriverTaskRecordPO queryDriverCurrentActiveTask(long driverId);
}
