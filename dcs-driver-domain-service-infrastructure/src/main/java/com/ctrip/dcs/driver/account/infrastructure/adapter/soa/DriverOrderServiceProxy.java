package com.ctrip.dcs.driver.account.infrastructure.adapter.soa;

import com.ctrip.dcs.dsp.self.order.query.api.SelfOrderQueryServiceClient;
import com.ctrip.dcs.self.order.query.api.BatchQueryDriverOrderBaseInfoRequestType;
import com.ctrip.dcs.self.order.query.api.BatchQueryDriverOrderBaseInfoResponseType;
import com.ctrip.dcs.self.order.query.api.QueryOrderListRequestType;
import com.ctrip.dcs.self.order.query.api.QueryOrderListResponseType;
import com.ctrip.igt.framework.soa.client.ServiceClient;

@ServiceClient(value = SelfOrderQueryServiceClient.class)
public interface DriverOrderServiceProxy {

	QueryOrderListResponseType queryOrderList(QueryOrderListRequestType request);

	/**
	 * 根据用户订单，批量查司机单信息
	 * @param request
	 * @return
	 */
	BatchQueryDriverOrderBaseInfoResponseType batchQueryDriverOrderBaseInfo(BatchQueryDriverOrderBaseInfoRequestType request);

}