package com.ctrip.dcs.driver.trip.university.infrastructure.value;

import com.ctrip.dcs.driver.trip.university.infrastructure.constant.TripUniversityDriverIdentityEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TripUniversityDriverDTO {

  /**
   * 失败日志ID
   */
  private Long failedLogId;

  /**
   * 重试次数
   */
  private int retryCount;

  private String uid;
  private Long drvId;

  /**
   * 语言
   */
  private String driverLanguage;
  /**
   * 姓名
   */
  private String name;

  /**
   * 身份证号
   */
  private String idCardNo;

  /**
   * 国家码
   */
  private String countryCode;

  /**
   * 手机号
   */
  private String phoneNumber;

  /**
   * 邮箱
   */
  private String email;

  /**
   * 是否海外
   */
  private Boolean oversea;

  /**
   * 身份
   */
  private TripUniversityDriverIdentityEnum identityEnum;

}
