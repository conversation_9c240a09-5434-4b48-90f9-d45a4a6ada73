package com.ctrip.dcs.driver.domain.infrastructure.dal.dao.rights;

import com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights.DrivLevelPO;
import com.ctrip.platform.dal.dao.DalClient;
import com.ctrip.platform.dal.dao.DalHints;
import com.ctrip.platform.dal.dao.base.DalTableOperations;
import com.ctrip.platform.dal.dao.client.DalOperationsFactory;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;

@Component
public class DrivLevelDAlDao {
    private final DalTableOperations<DrivLevelPO> DAL_TABLE_OPERATIONS = DalOperationsFactory.getDalTableOperations(DrivLevelPO.class);

    public DalClient getClient() {
        return DAL_TABLE_OPERATIONS.getClient();
    }

    public int insertDuplicateUpdate(DalHints hints, DrivLevelPO daoPojo) throws SQLException {
        return DAL_TABLE_OPERATIONS.insertDuplicateUpdate(hints, daoPojo);
    }

    public int[] batchInsertDuplicateUpdate(DalHints hints, List<DrivLevelPO> daoPojos) throws SQLException {
        return DAL_TABLE_OPERATIONS.batchInsertDuplicateUpdate(hints, daoPojos);
    }
}