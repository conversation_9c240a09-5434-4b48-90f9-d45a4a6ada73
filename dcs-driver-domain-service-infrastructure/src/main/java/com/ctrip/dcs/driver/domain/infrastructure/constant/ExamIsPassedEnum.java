package com.ctrip.dcs.driver.domain.infrastructure.constant;

public enum ExamIsPassedEnum {
    PASSED(1, "passed"),
    UNPASSED(0, "unpassed");

    private int code;
    private String name;

    ExamIsPassedEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ExamIsPassedEnum getByCode(Integer code) {
        if(code == null){
            return UNPASSED;
        }
        for (ExamIsPassedEnum examIsPassedEnum : ExamIsPassedEnum.values()) {
            if (examIsPassedEnum.code == code) {
                return examIsPassedEnum;
            }
        }
        return UNPASSED;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
