package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

/**
 * <AUTHOR>
 * @Description
 */
@Component
@QMapConfig("guide.exam.interface.config.properties")
public class ExamInterfaceConfig {

    private String tenantid;

    private String ssoDomainName;

    private String openapiDomainName;

    private String apiDomainName;

    private String openAuthInterfaceUrl;

    private String queryDeptInfoUrl;

    private String queryUserExamInfoUrl;

    private String changeUserInfoUrl;

    private String queryUserInfoUrl;

    private String oauthDomainName;

    private String oauthUrl;

    private String rootDeptId;

    public String getRootDeptId() {
        return rootDeptId;
    }

    public void setRootDeptId(String rootDeptId) {
        this.rootDeptId = rootDeptId;
    }

    public String getOauthDomainName() {
        return oauthDomainName;
    }

    public void setOauthDomainName(String oauthDomainName) {
        this.oauthDomainName = oauthDomainName;
    }

    public String getOauthUrl() {
        return oauthUrl;
    }

    public void setOauthUrl(String oauthUrl) {
        this.oauthUrl = oauthUrl;
    }

    public String getSsoDomainName() {
        return ssoDomainName;
    }

    public void setSsoDomainName(String ssoDomainName) {
        this.ssoDomainName = ssoDomainName;
    }

    public String getOpenapiDomainName() {
        return openapiDomainName;
    }

    public void setOpenapiDomainName(String openapiDomainName) {
        this.openapiDomainName = openapiDomainName;
    }

    public String getApiDomainName() {
        return apiDomainName;
    }

    public void setApiDomainName(String apiDomainName) {
        this.apiDomainName = apiDomainName;
    }

    public String getOpenAuthInterfaceUrl() {
        return openAuthInterfaceUrl;
    }

    public void setOpenAuthInterfaceUrl(String openAuthInterfaceUrl) {
        this.openAuthInterfaceUrl = openAuthInterfaceUrl;
    }

    public String getQueryDeptInfoUrl() {
        return queryDeptInfoUrl;
    }

    public void setQueryDeptInfoUrl(String queryDeptInfoUrl) {
        this.queryDeptInfoUrl = queryDeptInfoUrl;
    }

    public String getQueryUserExamInfoUrl() {
        return queryUserExamInfoUrl;
    }

    public void setQueryUserExamInfoUrl(String queryUserExamInfoUrl) {
        this.queryUserExamInfoUrl = queryUserExamInfoUrl;
    }

    public String getChangeUserInfoUrl() {
        return changeUserInfoUrl;
    }

    public void setChangeUserInfoUrl(String changeUserInfoUrl) {
        this.changeUserInfoUrl = changeUserInfoUrl;
    }

    public String getQueryUserInfoUrl() {
        return queryUserInfoUrl;
    }

    public void setQueryUserInfoUrl(String queryUserInfoUrl) {
        this.queryUserInfoUrl = queryUserInfoUrl;
    }

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
}
