package com.ctrip.dcs.driver.domain.infrastructure.dal.po.rights;

import com.ctrip.platform.dal.dao.DalPojo;
import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.*;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;

/**
 * <AUTHOR>
 * @date 2023-07-14
 */
@Getter
@Setter
@Entity
@Database(name = "dcstransportdb_w")
@Table(name = "driver_point_total_info")
public class DriverPointTotalInfoPO implements DalPojo {

    /**
     * 主键
     */
    @Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Type(value = Types.BIGINT)
	private Long id;

    /**
     * 司机等级(不包含排名条件) 0青铜 1白银 2黄金 3铂金 4钻石 100普通 101铜牌 102银牌 103金牌
     */
	@Column(name = "driv_level")
	@Type(value = Types.TINYINT)
	private Integer drivLevel;

    /**
     * 司机id
     */
	@Column(name = "driver_id")
	@Type(value = Types.VARCHAR)
	private String driverId;

    /**
     * 所属城市id
     */
	@Column(name = "city_id")
	@Type(value = Types.VARCHAR)
	private String cityId;

    /**
     * 订单信息分数分数
     */
	@Column(name = "order_info_point")
	@Type(value = Types.DECIMAL)
	private BigDecimal orderInfoPoint;

    /**
     * 奖励信息分数
     */
	@Column(name = "reward_info_point")
	@Type(value = Types.DECIMAL)
	private BigDecimal rewardInfoPoint;

    /**
     * 司机状态(0未激活、1上线、2下线、3冻结)
     */
	@Column(name = "driver_status")
	@Type(value = Types.TINYINT)
	private Integer driverStatus;

    /**
     * 创建时间
     */
	@Column(name = "create_time")
	@Type(value = Types.TIMESTAMP)
	private Timestamp createTime;

    /**
     * 更新时间
     */
	@Column(name = "datachange_lasttime", insertable = false, updatable = false)
	@Type(value = Types.TIMESTAMP)
	private Timestamp datachangeLasttime;

	/**
	 * 安全信息分数
	 */
	@Column(name = "safe_point")
	@Type(value = Types.DECIMAL)
	private BigDecimal safePoint;
}