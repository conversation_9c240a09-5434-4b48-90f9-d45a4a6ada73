package com.ctrip.dcs.driver.account.infrastructure.adapter.db.entity;

import com.ctrip.platform.dal.dao.annotation.Database;
import com.ctrip.platform.dal.dao.annotation.Type;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Types;
import java.time.LocalDateTime;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "phone_check_task")
@Database(name = "dcstransportdb_w")
public class PhoneCheckTaskEntity {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Type(value = Types.BIGINT)
    Long id;

    @Column(name = "driver_id")
    @Type(value = Types.BIGINT)
    Long driverId;

    @Column(name = "phone_prefix")
    @Type(value = Types.VARCHAR)
    String phonePrefix;

    @Column(name = "phone_number")
    @Type(value = Types.VARCHAR)
    String phoneNumber;

    @Column(name = "task_type")
    @Type(value = Types.INTEGER)
    Integer taskType;

    @Column(name = "task_state")
    @Type(value = Types.INTEGER)
    Integer taskState;

    @Column(name = "task_content")
    @Type(value = Types.VARCHAR)
    String taskContent;

    @Column(name = "task_result")
    @Type(value = Types.VARCHAR)
    String taskResult;

    @Column(name = "submit_time")
    @Type(value = Types.TIMESTAMP)
    LocalDateTime submitTime;

    @Column(name = "plan_time")
    @Type(value = Types.TIMESTAMP)
    LocalDateTime planTime;

    @Column(name = "execute_time")
    @Type(value = Types.TIMESTAMP)
    LocalDateTime executeTime;

    @Column(name = "finish_time")
    @Type(value = Types.TIMESTAMP)
    LocalDateTime finishTime;

    @Column(name = "call_return_code")
    @Type(value = Types.VARCHAR)
    String callReturnCode;

    @Column(name = "call_return_reason")
    @Type(value = Types.VARCHAR)
    String callReturnReason;

    @Column(name = "call_hangup_code")
    @Type(value = Types.VARCHAR)
    String callHangupCode;

    @Column(name = "call_hangup_reason")
    @Type(value = Types.VARCHAR)
    String callHangupReason;

    @Column(name = "call_menu_code")
    @Type(value = Types.VARCHAR)
    String callMenuCode;

    @Column(name = "call_menu_result")
    @Type(value = Types.VARCHAR)
    String callMenuResult;

    @Column(name = "call_alert_seconds")
    @Type(value = Types.INTEGER)
    Integer callAlertSeconds;

    @Column(name = "call_duration_seconds")
    @Type(value = Types.INTEGER)
    Integer callDurationSeconds;

}