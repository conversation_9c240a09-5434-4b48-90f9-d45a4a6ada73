package com.ctrip.dcs.driver.account.infrastructure.common;

import lombok.Getter;

@Getter
public enum AccountExceptionCode {

    /**
     * 注册账户未知异常
     */
    REGISTER_ACCOUNT_UNKNOWN_ERROR("1017100", "register account unknown error"),

    /**
     * 用户中心注册失败
     */
    INVOKE_USER_CENTER_REGISTER_ERROR("1117101", "register from user center failed"),

    /**
     * 用户中心绑定失败
     */
    INVOKE_USER_CENTER_BIND_ERROR("1117102", "user center bind account failed"),

    /**
     * 获取司机q侧账户id（ppm账户）失败
     */
    INVOKE_QUNAR_FETCH_ACCOUNT_ID_ERROR("1117103", "get ppm account failed"),

    /**
     * 用户中心解绑失败
     */
    INVOKE_USER_CENTER_UNBIND_ERROR("1117104", "user center unbind account failed"),

    SYNC_ACCOUNT_FAILED("1117105", "sync account failed"),

    /**
     * 解析手机号失败
     */
    SPLIT_PHONE_NUMBER("1117106", "parse phone number failed"),

    /**
     * 账户不存在
     */
    ACCOUNT_NOT_EXIST("1517100", "account not exist"),

    /**
     * 注册sourceId不正确
     */
    REGISTER_ACCOUNT_ERROR("1517101", "register error"),

    /**
     * 注册sourceId不正确
     */
    REGISTER_SOURCE_ID_ERROR("1517102", "source id error"),

    /**
     * 注册手机号错误
     */
    REGISTER_PHONE_ERROR("1517103", "mobile phone error"),

    /**
     * 查询数量过长
     */
    QUERY_SIZE_TOO_LONG("1517104", "query size too long"),

    /**
     * 派安盈账号已存在
     */
    PAYONEER_ACCOUNT_REPEAT("1517105", "payoneer account repeat"),

    /**
     * 手机号已被绑定
     */
    PHONE_BIND_ALREADY("1517106", "phone number has been bound"),

    /**
     * 邮箱已绑定
     */
    EMAIL_BIND_ALREADY("1517107", "mail has been bound"),


    /**
     * 参数异常
     */
    PARAM_INVALID("1517199", "param invalid"),

    //司机、司导 区域注册错误
    DRIVER_REGISTER_AREA_ERROR("1517200", "driver register area error"),
    ;

    private final String code;

    private final String msg;

    AccountExceptionCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
