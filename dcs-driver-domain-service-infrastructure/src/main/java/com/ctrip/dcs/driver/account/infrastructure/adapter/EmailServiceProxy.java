package com.ctrip.dcs.driver.account.infrastructure.adapter;

import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.soa.platform.basesystem.emailservice.v1.EmailServiceClient;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailRequest;
import com.ctrip.soa.platform.basesystem.emailservice.v1.SendEmailResponse;

@ServiceClient(value = EmailServiceClient.class)
public interface EmailServiceProxy {

    SendEmailResponse sendEmail(SendEmailRequest request);
}
