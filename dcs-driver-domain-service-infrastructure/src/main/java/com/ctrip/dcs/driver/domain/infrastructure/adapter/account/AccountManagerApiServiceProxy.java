package com.ctrip.dcs.driver.domain.infrastructure.adapter.account;

import com.ctrip.igt.framework.soa.client.ServiceClient;
import com.ctrip.soa.platform.accountmanagerapiserverinternal.v1.*;

@ServiceClient(value = AccountManagerApiServerInternalClient.class, format = "json")
public interface AccountManagerApiServiceProxy {

    BindMobilePhoneResponseType bindMobilePhone(BindMobilePhoneRequestType request);

    BindEmailResponseType bindEmail(BindEmailRequestType request);

    UnbindEmailResponseType unbindEmail(UnbindEmailRequestType request);

    UnbindMobilePhoneResponseType unbindMobilePhone(UnbindMobilePhoneRequestType request);

    UnfreezeAccountByUidResponseType unfreezeAccountByUid(UnfreezeAccountByUidRequestType request);
}
