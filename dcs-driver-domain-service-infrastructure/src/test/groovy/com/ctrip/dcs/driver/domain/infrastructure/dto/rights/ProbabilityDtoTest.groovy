package com.ctrip.dcs.driver.domain.infrastructure.dto.rights

import org.mockito.InjectMocks
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class ProbabilityDtoTest extends Specification {
    @InjectMocks
    ProbabilityDto probabilityDto

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test set Entity"() {
        when:
        probabilityDto.setEntity("entity")

        then:
        true
    }

    def "test set Id"() {
        when:
        probabilityDto.setId(0)

        then:
        true
    }

    def "test set Probability Value"() {
        when:
        probabilityDto.setProbabilityValue(0d)

        then:
        true
    }

    def "test builder"() {
        when:
        ProbabilityDto.ProbabilityDtoBuilder result = ProbabilityDto.builder()

        then:
        true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme