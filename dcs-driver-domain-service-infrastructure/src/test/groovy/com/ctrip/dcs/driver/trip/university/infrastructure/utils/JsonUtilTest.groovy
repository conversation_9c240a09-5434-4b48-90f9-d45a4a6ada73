package com.ctrip.dcs.driver.trip.university.infrastructure.utils

import com.ctrip.dcs.driver.trip.university.infrastructure.value.TripUniversityDriverDTO
import com.fasterxml.jackson.core.type.TypeReference
import spock.lang.Specification
import spock.lang.Unroll

class JsonUtilTest extends Specification {

    @Unroll
    def "fromJsonTest"() {
        given: "设定相关方法入参"
        when:
        def result = JsonUtil.fromJson(json, typeRef)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        json                  | typeRef                                         || expectedResult
        "{\"failedLogId\":1}" | new TypeReference<TripUniversityDriverDTO>() {
        }                                                                       || new TripUniversityDriverDTO(failedLogId: 1L)
    }
}
