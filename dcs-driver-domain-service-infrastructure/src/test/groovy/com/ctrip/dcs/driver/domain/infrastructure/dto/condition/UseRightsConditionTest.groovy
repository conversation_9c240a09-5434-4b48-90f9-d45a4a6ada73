package com.ctrip.dcs.driver.domain.infrastructure.dto.condition

import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class UseRightsConditionTest extends Specification {
    @Mock
    BigDecimal money
    @InjectMocks
    UseRightsCondition useRightsCondition

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test set Driver Id"() {
        when:
        useRightsCondition.setDriverId(1l)

        then:
        true
    }

    def "test set Rights Type"() {
        when:
        useRightsCondition.setRightsType(0)

        then:
        true
    }

    def "test set User Order Id"() {
        when:
        useRightsCondition.setUserOrderId("userOrderId")

        then:
        true
    }

    def "test set Purchase Order Id"() {
        when:
        useRightsCondition.setPurchaseOrderId("purchaseOrderId")

        then:
        true
    }

    def "test set Supply Order Id"() {
        when:
        useRightsCondition.setSupplyOrderId("supplyOrderId")

        then:
        true
    }

    def "test set Punish Order Id"() {
        when:
        useRightsCondition.setPunishOrderId("punishOrderId")

        then:
        true
    }

    def "test set Money"() {
        when:
        useRightsCondition.setMoney(0 as BigDecimal)

        then:
        true
    }

    def "test builder"() {
        when:
        UseRightsCondition.UseRightsConditionBuilder result = UseRightsCondition.builder()

        then:
        true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme