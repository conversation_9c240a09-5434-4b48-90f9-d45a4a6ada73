package com.ctrip.dcs.driver.trip.university.infrastructure.utils

import com.ctrip.arch.coreinfo.enums.KeyType
import spock.lang.Specification
import spock.lang.Unroll

class CoreInfoUtilTest extends Specification {
    def testObj = new CoreInfoUtil()

    @Unroll
    def "decryptTest"() {
        given: "设定相关方法入参"
        when:
        def result = CoreInfoUtil.decrypt(paramKey, keyType)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        paramKey   | keyType       || expectedResult
        "paramKey" | KeyType.Phone || "paramKey"
    }
}
