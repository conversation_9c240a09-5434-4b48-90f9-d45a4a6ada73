package com.ctrip.dcs.driver.trip.university.infrastructure.utils

import spock.lang.Specification
import spock.lang.Unroll

import java.sql.Timestamp

class DateUtilTest extends Specification {
    def testObj = new DateUtil()

    @Unroll
    def "string2TimestampTest"() {
        given: "设定相关方法入参"
        when:
        def result = DateUtil.string2Timestamp(time, format)

        then: "验证返回结果里属性值是否符合预期"
        result == expectedResult
        where: "表格方式验证多种分支调用场景"
        format   | time   || expectedResult
        "format" | "time" || null
    }
}
