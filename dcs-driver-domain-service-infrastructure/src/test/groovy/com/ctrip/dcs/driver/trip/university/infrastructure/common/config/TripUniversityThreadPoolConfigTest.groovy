package com.ctrip.dcs.driver.trip.university.infrastructure.common.config

import com.ctrip.dcs.driver.trip.university.infrastructure.qconfig.TripUniversityQconfig
import com.dianping.cat.wrapper.CatExecutorService
import org.apache.dubbo.common.threadpool.support.cached.CachedThreadPool
import spock.lang.Specification
import spock.lang.Unroll

import java.util.concurrent.ThreadPoolExecutor

class TripUniversityThreadPoolConfigTest extends Specification {
    def testObj = new TripUniversityThreadPoolConfig()
    def tripUniversityQconfig = Mock(TripUniversityQconfig)

    def setup() {

        testObj.tripUniversityQconfig = tripUniversityQconfig
    }

    @Unroll
    def "getExecutorServiceTest"() {
        given: "设定相关方法入参"
        and: "Mock相关接口返回"
        tripUniversityQconfig.getPoolCoreSize() >> 1
        tripUniversityQconfig.getDriverMaxSize() >> 1
        tripUniversityQconfig.getPoolWorkQueueSize() >> 1

        and: "Spy相关接口"
        def spy = Spy(testObj)
        spy.getPool(_, _, _, _) >> new CatExecutorService(null, false)
        when:
        def result = spy.getExecutorService()

        then: "验证返回结果里属性值是否符合预期"
        (result != null) == expectedResult
        where: "表格方式验证多种分支调用场景"
        t|expectedResult
        "t"|true
    }

    @Unroll
    def "getPoolTest"() {
        given: "设定相关方法入参"
        when:
        def result = testObj.getPool(poolName, coreSize, maxSize, workQueueSize)

        then: "验证返回结果里属性值是否符合预期"
        (result != null) == expectedResult
        where: "表格方式验证多种分支调用场景"
        coreSize | maxSize | workQueueSize | poolName   || expectedResult
        1        | 1       | 1             | "poolName" || true
    }
}
