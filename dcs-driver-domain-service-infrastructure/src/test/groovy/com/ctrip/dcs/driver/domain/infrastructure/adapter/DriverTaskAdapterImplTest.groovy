package com.ctrip.dcs.driver.domain.infrastructure.adapter

import com.ctrip.dcs.driver.domain.infrastructure.adapter.verify.DriverTaskAdapterImpl
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverCarInspectionRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.driverapp.DriverTaskRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverCarInspectionRecordPO
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.driverapp.DriverTaskRecordPO
import org.apache.commons.collections.CollectionUtils
import spock.lang.Specification
import spock.lang.Subject

class DriverTaskAdapterImplTest extends Specification {

    @Subject
    DriverTaskAdapterImpl adapter = new DriverTaskAdapterImpl()

    DriverTaskRecordDao driverTaskRecordDao = Mock(DriverTaskRecordDao)
    DriverCarInspectionRecordDao driverCarInspectionRecordDao = Mock(DriverCarInspectionRecordDao)

    def setup() {
        adapter.driverTaskRecordDao = driverTaskRecordDao
        adapter.driverCarInspectionRecordDao = driverCarInspectionRecordDao
    }

    def "queryDriverCurrentActiveTask.taskId"() {
        given:
        def taskId = "TASK_001"
        def taskRecordPO = new DriverTaskRecordPO(taskId: taskId)

        when:
        def result = adapter.queryDriverTaskByTaskId(123L, taskId)

        then:
        1 * driverTaskRecordDao.queryTaskByTaskId(taskId) >> taskRecordPO
        result != null
        result.taskId == taskId
    }

    def "queryDriverTaskByDriverOrderId.driverOrderId"() {
        given:
        def driverOrderId = "ORDER_001"
        def taskRecordPO = new DriverTaskRecordPO(driverOrderId: driverOrderId)

        when:
        def result = adapter.queryDriverTaskByDriverOrderId(123L, driverOrderId)

        then:
        1 * driverTaskRecordDao.queryTaskByDriverOrderId(123L, driverOrderId) >> taskRecordPO
        result != null
        result.driverOrderId == driverOrderId
    }

    def "queryCarInspectionInfoList"() {
        given:
        def taskId = "TASK_001"
        def inspectionRecordPO = new DriverCarInspectionRecordPO(taskId: taskId)

        when:
        def result = adapter.queryCarInspectionInfoList(taskId)

        then:
        1 * driverCarInspectionRecordDao.queryByTaskId(taskId) >> [inspectionRecordPO]
        !CollectionUtils.isEmpty(result)
    }
}