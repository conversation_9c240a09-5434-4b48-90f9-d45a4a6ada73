package com.ctrip.dcs.driver.domain.infrastructure.adapter

import com.ctrip.dcs.driver.domain.infrastructure.adapter.db.DriverFreezeAdapter
import com.ctrip.dcs.driver.domain.infrastructure.condition.freeze.DriverApplyFreezeRecordCondition
import com.ctrip.dcs.driver.domain.infrastructure.condition.freeze.DriverFrozenBufferCondition
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.DriverApplyFreezeRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.dao.DriverFrozenBufferRecordDao
import com.ctrip.dcs.driver.domain.infrastructure.dal.po.DriverFrozenBufferRecordPO
import com.ctrip.dcs.driver.domain.infrastructure.utils.LocalDateTimeUtils
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import java.sql.Timestamp
import static org.mockito.Mockito.when

class DriverFreezeAdapterTest extends Specification {

    @Mock
    DriverApplyFreezeRecordDao driverApplyFreezeRecordDao;
    @Mock
    DriverFrozenBufferRecordDao driverFrozenBufferRecordDao;
    @InjectMocks
    DriverFreezeAdapter driverFreezeAdapter

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "createDriverApplyFreezeRecord"() {
        given:
        Long driverId = 1000004L
        def request = new DriverApplyFreezeRecordCondition(driverId: driverId)
        when:
        driverFreezeAdapter.createDriverApplyFreezeRecord(request)

        then:
        0 * driverApplyFreezeRecordDao.insert(_)
    }

    def "createDriverFrozenBuffer"() {
        given:
        Long driverId = 1000004L
        def request = new DriverFrozenBufferCondition(driverId: driverId)
        when:
        driverFreezeAdapter.createDriverFrozenBuffer(request)

        then:
        0 * driverFrozenBufferRecordDao.insert(_)
    }

    def "queryDriverFrozenBufferEndTime_null"() {
        given:
        when(driverFrozenBufferRecordDao.findOneByDriverId(1L)).thenReturn(null)
        when:
        def time = driverFreezeAdapter.queryDriverFrozenBufferEndTime(1L)
        then:
        time == null
    }

    def "queryDriverFrozenBufferEndTime_have"() {
        given:
        def date = LocalDateTimeUtils.localDateTime("2025-03-26 17:18:26")

        DriverFrozenBufferRecordPO p = new DriverFrozenBufferRecordPO(bufferEndTime: Timestamp.valueOf(date))
        when(driverFrozenBufferRecordDao.findOneByDriverId(1L)).thenReturn(p)
        when:
        def time = driverFreezeAdapter.queryDriverFrozenBufferEndTime(1L)

        then:
        time == "2025-03-26 17:18:26"
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme