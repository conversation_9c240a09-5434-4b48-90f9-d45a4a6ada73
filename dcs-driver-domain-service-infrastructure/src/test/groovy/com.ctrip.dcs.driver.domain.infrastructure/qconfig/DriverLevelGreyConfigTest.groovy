package com.ctrip.dcs.driver.domain.infrastructure.qconfig

import com.ctrip.igt.framework.common.clogging.Logger
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import java.time.LocalDate

import static org.mockito.Mockito.when

class DriverLevelGreyConfigTest extends Specification {
    @Mock
    Logger LOGGER
    @Mock
    SystemQConfig systemQConfig
    @Mock
    Map<Long, String> cityMap
    @InjectMocks
    DriverLevelGreyConfig driverLevelGreyConfig

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test on Config Change"() {
        when:
        driverLevelGreyConfig.onConfigChange("[{\"cityIds\":\"2,4,5,10,11,50\",\"effectTime\":\"2023-05-18\"},{\"cityIds\":\"1,7,30,16\",\"effectTime\":\"2023-06-01\"},{\"cityIds\":\"3,8\",\"effectTime\":\"2023-06-08\"}]")

        then:
        true
    }

    def "test can Use Vacation Rights"() {
        when:
        Map<Long, String> cityMap = new HashMap<>();
        cityMap.put(1L, LocalDate.now())
        driverLevelGreyConfig.cityMap = cityMap
        when(systemQConfig.getString("vacation_rights_citys")).thenReturn("1,2,3")
        boolean result = driverLevelGreyConfig.canUseVacationRights(city)

        then:
        result == code
        where:
        city || code
        1L   || true
        0L   || true
        4L   || true
    }

    def "test is In Grey"() {
        when:
        Map<Long, String> cityMap = new HashMap<>();
        cityMap.put(1L, LocalDate.now())
        driverLevelGreyConfig.cityMap = cityMap
        Boolean result = driverLevelGreyConfig.isInGrey(city)

        then:
        result == code
        where:
        city || code
        1L   || true
        0L   || false
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme