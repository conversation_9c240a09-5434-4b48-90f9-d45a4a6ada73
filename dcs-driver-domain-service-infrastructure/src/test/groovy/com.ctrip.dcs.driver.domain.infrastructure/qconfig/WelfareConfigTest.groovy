package com.ctrip.dcs.driver.domain.infrastructure.qconfig

import com.ctrip.dcs.driver.domain.infrastructure.constant.LevelEnum
import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.ProbabilityDto
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormWelfareDetailInfo
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class WelfareConfigTest extends Specification {
    @Mock
    List<FormWelfareDetailInfo> diamondList
    @Mock
    List<FormWelfareDetailInfo> platinumList
    @InjectMocks
    WelfareConfig welfareConfig

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test welfare Config Change"() {
        when:
        welfareConfig.welfareConfigChange("{\"diamondList\":[{\"diamondMoney\":\"9.99\",\"diamondProbability\":\"50\",\"welfare\":\"9.99\",\"probability\":\"50\"},{\"diamondMoney\":\"100\",\"diamondProbability\":\"20\",\"welfare\":\"100\",\"probability\":\"20\"},{\"diamondMoney\":\"200\",\"diamondProbability\":\"30\",\"welfare\":\"200\",\"probability\":\"30\"}],\"platinumList\":[{\"platinumMoney\":\"10\",\"platinumProbability\":\"50\",\"welfare\":\"10\",\"probability\":\"50\"},{\"platinumMoney\":\"20\",\"platinumProbability\":\"10\",\"welfare\":\"20\",\"probability\":\"10\"},{\"platinumMoney\":\"33\",\"platinumProbability\":\"40\",\"welfare\":\"15\",\"probability\":\"40\"}]}")

        then:
        true
    }

    def "test get Probability List"() {
        when:
        List<ProbabilityDto<BigDecimal>> result = welfareConfig.getProbabilityList(type)

        then:
        result != null
        where:
        type                          || code
        LevelEnum.LEVEL_TYPE_DIAMOND  || true
        LevelEnum.LEVEL_TYPE_BRONZE   || true
        LevelEnum.LEVEL_TYPE_PLATINUM || true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme