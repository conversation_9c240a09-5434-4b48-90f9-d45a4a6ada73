package com.ctrip.dcs.driver.domain.infrastructure.qconfig

import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsInfo
import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormRightsIntroduceInfo
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class RightsConfigTest extends Specification {
    @Mock
    List<FormRightsInfo> rightsInfoList
    @Mock
    Map<Long, List<FormRightsInfo>> cityRightsMap
    @Mock
    Map<Integer, List<Long>> levelRightsMap
    @Mock
    Map<Long, FormRightsInfo> rightsIdMap
    @InjectMocks
    RightsConfig rightsConfig

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test rights Config Change"() {
        when:
        rightsConfig.rightsConfigChange("[{\"id\":1,\"rightsType\":\"1\",\"rightsName\":\"福利金名称\",\"rightsDesc\":\"福利金简介\",\"rightsIntroduceList\":[{\"title\":\"标题1111\",\"Introduce\":\"说明111\"}],\"levelList\":[{\"level\":\"0\",\"isLimit\":\"1\",\"useLimit\":\"99\",\"extend\":\"50\"},{\"level\":\"3\",\"isLimit\":\"1\",\"extend\":\"100\",\"useLimit\":\"10\"}],\"cityIdsStr\":\"1,2,3,4,10\",\"status\":\"0\"},{\"id\":2,\"rightsType\":\"3\",\"rightsName\":\"城市王者名称\",\"rightsDesc\":\"城市王者简介\",\"rightsIntroduceList\":[{\"title\":\"城市王者1111\",\"Introduce\":\"城市王者111\"},{\"title\":\"城市王者2\",\"Introduce\":\"城市王者2\"}],\"levelList\":[{\"level\":\"0\",\"isLimit\":\"0\",\"useLimit\":\"\"},{\"level\":\"4\",\"isLimit\":\"1\",\"useLimit\":\"1\"}],\"cityIdsStr\":\"1,2\",\"status\":\"0\"},{\"id\":3,\"rightsType\":\"1\",\"rightsName\":\"福利金2222\",\"rightsDesc\":\"福利金2222\",\"rightsIntroduceList\":[{\"title\":\"福利金22221\",\"Introduce\":\"福利金22221\"}],\"levelList\":[{\"level\":\"3\",\"isLimit\":\"0\"}],\"cityIdsStr\":\"10\",\"status\":\"0\"},{\"id\":4,\"rightsType\":\"2\",\"rightsName\":\"改派权益\",\"rightsDesc\":\"改派权益简介\",\"rightsIntroduceList\":[{\"title\":\"改派说明标题1\",\"Introduce\":\"改派说明1\"}],\"levelList\":[{\"level\":\"0\",\"isLimit\":\"1\",\"useLimit\":\"6\"}],\"cityIdsStr\":\"1,2,3\",\"status\":\"0\"}]")

        then:
        true
    }

    def "test get Rights Info"() {
        when:
        rightsConfig.rightsConfigChange("[{\"id\":1,\"rightsType\":\"1\",\"rightsName\":\"福利金\",\"rightsDesc\":\"福利金简介\",\"rightsIntroduceList\":[{\"title\":\"标题1111\",\"Introduce\":\"说明111\"}],\"levelList\":[{\"level\":\"3\",\"isLimit\":\"0\",\"useLimit\":\"\",\"extend\":\"50\"},{\"level\":\"4\",\"isLimit\":\"0\",\"extend\":\"100\",\"useLimit\":\"\"}],\"cityIdsStr\":\"1,2,3,4,10\",\"status\":\"0\"},{\"id\":3,\"rightsType\":\"1\",\"rightsName\":\"福利金\",\"rightsDesc\":\"福利金2222\",\"rightsIntroduceList\":[{\"title\":\"福利金22221\",\"Introduce\":\"福利金22221\"}],\"levelList\":[{\"level\":\"3\",\"isLimit\":\"0\"}],\"cityIdsStr\":\"10\",\"status\":\"0\"},{\"id\":4,\"rightsType\":\"2\",\"rightsName\":\"改派权益\",\"rightsDesc\":\"改派权益简介\",\"rightsIntroduceList\":[{\"title\":\"改派说明标题1\",\"Introduce\":\"改派说明1\"}],\"levelList\":[{\"level\":\"2\",\"isLimit\":\"1\",\"useLimit\":\"4\"},{\"level\":\"3\",\"isLimit\":\"1\",\"useLimit\":\"6\"},{\"level\":\"4\",\"isLimit\":\"1\",\"useLimit\":\"8\"}],\"cityIdsStr\":\"1,2,3\",\"status\":\"0\"},{\"id\":5,\"rightsType\":\"4\",\"rightsName\":\"初级投诉豁免卡\",\"rightsDesc\":\"初级投诉豁免卡简介\",\"rightsIntroduceList\":[{\"title\":\"初级投诉豁免卡标题\",\"Introduce\":\"初级投诉豁免卡说明\"}],\"levelList\":[{\"level\":\"3\",\"isLimit\":\"1\",\"useLimit\":\"1\"},{\"level\":\"4\",\"isLimit\":\"1\",\"useLimit\":\"1\"}],\"cityIdsStr\":\"1,2\",\"status\":\"0\"},{\"id\":6,\"rightsType\":\"5\",\"rightsName\":\"高级投诉豁免卡\",\"rightsDesc\":\"高级投诉豁免卡简介\",\"rightsIntroduceList\":[{\"title\":\"高级投诉豁免卡标题\",\"Introduce\":\"高级投诉豁免卡说明\"}],\"levelList\":[{\"level\":\"3\",\"isLimit\":\"1\",\"useLimit\":\"1\"},{\"level\":\"4\",\"isLimit\":\"1\",\"useLimit\":\"1\"}],\"cityIdsStr\":\"1,2\",\"status\":\"0\"}]")

        List<FormRightsInfo> result = rightsConfig.getRightsInfo(2l, 3)
        then:
        true
    }

    def "test get Rights Info 2"() {
        when:
        FormRightsInfo result = rightsConfig.getRightsInfo(1l, 0, 0)

        then:
        true
    }

    def "test get Rights Introduces"() {
        when:
        List<FormRightsIntroduceInfo> result = rightsConfig.getRightsIntroduces(1l, 0)

        then:
        true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme