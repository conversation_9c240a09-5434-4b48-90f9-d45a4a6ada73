package com.ctrip.dcs.driver.domain.infrastructure.qconfig

import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormLevelInfo
import com.ctrip.dcs.geo.domain.repository.CityRepository
import com.ctrip.dcs.geo.domain.value.City
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

class LevelConfigTest extends Specification {
    @Mock
    List<FormLevelInfo> levelInfoList
    @Mock
    Map<Long, List<FormLevelInfo>> cityLevelMap
    @InjectMocks
    LevelConfig levelConfig
    @Mock
    CityRepository cityRepository

    def setup() {
        MockitoAnnotations.initMocks(this)
    }

    def "test level Config Change"() {
        when:
        levelConfig.levelConfigChange("[{\"level\":\"0\",\"levelName\":\"青铜司机\",\"cityIdsStr\":\"1,2\",\"status\":\"0\",\"id\":1},{\"level\":\"1\",\"levelName\":\"白银司机\",\"cityIdsStr\":\"1,2,3\",\"driverPointLow\":10,\"activityLow\":10,\"hasRank\":\"0\",\"status\":\"0\",\"id\":2},{\"level\":\"4\",\"levelName\":\"砖石司机\",\"cityIdsStr\":\"1,2,3,4,5\",\"driverPointLow\":200,\"activityLow\":200,\"hasRank\":\"1\",\"rankLow\":\"100\",\"status\":\"0\",\"id\":3},{\"level\":\"2\",\"levelName\":\"黄金司机\",\"cityIdsStr\":\"1,2\",\"driverPointLow\":5,\"activityLow\":5,\"hasRank\":\"0\",\"rankLow\":\"\",\"status\":\"0\",\"id\":4}]")

        then:
        true
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme