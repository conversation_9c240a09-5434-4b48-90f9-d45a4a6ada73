package com.ctrip.dcs.driver.domain.infrastructure.utils

import com.ctrip.dcs.driver.domain.infrastructure.dto.rights.ProbabilityDto
import spock.lang.*

class LotteryUtilsTest extends Specification {

    def "test probability Lottery"() {
        when:
        ProbabilityDto<BigDecimal> result = LotteryUtils.probabilityLottery([new ProbabilityDto<BigDecimal>(new BigDecimal(1), 0, 0d)], new ProbabilityDto<BigDecimal>(new BigDecimal(1), 0, 0d))

        then:
        result !=null
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme