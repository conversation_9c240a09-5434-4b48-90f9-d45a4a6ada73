package com.ctrip.dcs.driver.domain.infrastructure.qconfig.form;

import mockit.Tested;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

@Component
public class DriverGreyConfigTest {

  @Tested
  DriverGreyConfig driverGreyConfig;

  @Test
  public void isGrey() {
    driverGreyConfig.onConfigChange("[{\"key\":\"im_push\",\"cityIds\":\"\",\"countryIds\":\"\",\"rnVer\":\"\"}]");
    boolean isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 0L, 0L, BigDecimal.ZERO);
    Assert.assertTrue(isGrey);
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 2L, 1L, new BigDecimal("2023101000"));
    Assert.assertTrue(isGrey);

    driverGreyConfig.onConfigChange("[{\"key\":\"im_push\",\"cityIds\":\"0\",\"countryIds\":\"0\",\"rnVer\":\"0\"}]");
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 0L, 0L, BigDecimal.ZERO);
    Assert.assertTrue(isGrey);
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 2L, 1L, new BigDecimal("2023101000"));
    Assert.assertFalse(isGrey);

    driverGreyConfig.onConfigChange("[{\"key\":\"im_push\",\"cityIds\":\"2,3\",\"countryIds\":\"1,2\",\"rnVer\":\"2023101900\"}]");
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 0L, 0L, BigDecimal.ZERO);
    Assert.assertFalse(isGrey);

    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 5L, 3L, new BigDecimal("2023101000"));
    Assert.assertFalse(isGrey);
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 5L, 3L, new BigDecimal("2023102000"));
    Assert.assertFalse(isGrey);

    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 5L, 1L, new BigDecimal("2023101000"));
    Assert.assertFalse(isGrey);
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 5L, 1L, new BigDecimal("2023102000"));
    Assert.assertTrue(isGrey);

    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 2L, 3L, new BigDecimal("2023101000"));
    Assert.assertFalse(isGrey);
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 2L, 3L, new BigDecimal("2023102000"));
    Assert.assertTrue(isGrey);
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 2L, 1L, new BigDecimal("2023101000"));
    Assert.assertFalse(isGrey);
    isGrey = driverGreyConfig.isGrey(DriverGreyConfig.IM_PUSH_GREY_KEY, 2L, 1L, new BigDecimal("2023102000"));
    Assert.assertTrue(isGrey);

  }
}
