package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import com.ctrip.dcs.driver.account.infrastructure.qconfig.DriverWalletInfoConfig;
import mockit.Tested;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Component
public class DriverWalletInfoConfigTest {

  @Tested
  DriverWalletInfoConfig driverWalletInfoConfig;

  @Test
  public void updateData() {
    Map<String, String> map = new HashMap<>();
    driverWalletInfoConfig.onChange(map);

    map = new HashMap<>();
    map.put("wallet_pay_merId", "wallet_pay_merId");
    map.put("wallet_pay_keyid", "wallet_pay_keyid");
    map.put("wallet_kms_payment_public_token", "wallet_kms_payment_public_token");
    map.put("wallet_kms_driver_private_token", "wallet_kms_driver_private_token");
    map.put("connectTimeout", "3000");
    map.put("requestTimeout", "3000");
    map.put("socketTimeout", "3000");

    driverWalletInfoConfig.onChange(map);
    Assert.assertEquals("wallet_pay_merId", driverWalletInfoConfig.getWalletPayMerid());
    Assert.assertEquals("wallet_pay_keyid", driverWalletInfoConfig.getWalletPayKeyid());
    Assert.assertEquals("wallet_kms_payment_public_token", driverWalletInfoConfig.getWalletKmsPaymentPublicToken());
    Assert.assertEquals("wallet_kms_driver_private_token", driverWalletInfoConfig.getWalletKmsDriverPrivateToken());
    Assert.assertEquals(3000, driverWalletInfoConfig.getConnectTimeout());
    Assert.assertEquals(3000, driverWalletInfoConfig.getRequestTimeout());
    Assert.assertEquals(3000, driverWalletInfoConfig.getSocketTimeout());
  }
}
