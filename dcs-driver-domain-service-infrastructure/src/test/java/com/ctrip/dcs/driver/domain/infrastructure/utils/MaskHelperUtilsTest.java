package com.ctrip.dcs.driver.domain.infrastructure.utils;

import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.owasp.csrfguard.util.Strings;


public class MaskHelperUtilsTest {

    @Tested
    MaskHelperUtils maskHelperUtils;

    @Test
    public void maskName() {
        String value = MaskHelperUtils.maskName(null);
        Assert.assertEquals(Strings.EMPTY, value);
        value = MaskHelperUtils.maskName(Strings.EMPTY);
        Assert.assertEquals(Strings.EMPTY, value);
        value = MaskHelperUtils.maskName("A");
        Assert.assertEquals("A", value);
        value = MaskHelperUtils.maskName("AB");
        Assert.assertEquals("A*", value);
        value = MaskHelperUtils.maskName("ABC");
        Assert.assertEquals("A*C", value);
        value = MaskHelperUtils.maskName("ABCD");
        Assert.assertEquals("A**D", value);
        value = MaskHelperUtils.maskName("ABCDE");
        Assert.assertEquals("A***E", value);
    }
}
