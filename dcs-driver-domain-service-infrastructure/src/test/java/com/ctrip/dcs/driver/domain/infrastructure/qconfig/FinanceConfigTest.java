package com.ctrip.dcs.driver.domain.infrastructure.qconfig;

import mockit.Tested;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class FinanceConfigTest {

  @Tested
  FinanceConfig financeConfig;

  @Test
  public void updateData() {
    Map<String, String> map = new HashMap<>();
    map.put("thread_wait_time", "5000");
    financeConfig.updateData(map);

    long threadWaitTime = financeConfig.getThreadWaitTime();
    Assert.assertEquals(threadWaitTime, 5000L);
  }
}
