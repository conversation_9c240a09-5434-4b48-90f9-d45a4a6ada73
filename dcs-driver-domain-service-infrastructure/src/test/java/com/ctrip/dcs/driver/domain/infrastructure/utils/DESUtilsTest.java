package com.ctrip.dcs.driver.domain.infrastructure.utils;

import mockit.Tested;

import org.junit.Assert;
import org.junit.Test;


public final class DESUtilsTest {

    @Tested
    DESUtils desUtils;

    @Test
    public void decryptDES() {
        String key = "trip_Drv";
        String value = DESUtils.decryptDES("15C125EC136FB8DB7345424CEBD348CC44A481BC711B962E", key);
        Assert.assertEquals("test-driver-domain", value);
    }

    @Test
    public void encryptDES() {
        String key = "trip_Drv";
        String value = DESUtils.encryptDES("test-driver-domain", key);
        Assert.assertEquals("15C125EC136FB8DB7345424CEBD348CC44A481BC711B962E", value);
    }


}
