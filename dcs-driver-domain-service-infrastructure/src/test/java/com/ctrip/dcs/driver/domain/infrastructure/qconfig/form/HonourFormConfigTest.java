package com.ctrip.dcs.driver.domain.infrastructure.qconfig.form;

import com.ctrip.dcs.driver.domain.infrastructure.model.form.FormHonourWallDriverMedalInfo;
import com.ctrip.dcs.driver.domain.infrastructure.model.honour.GoldMedalDriverModel;
import mockit.Tested;
import org.junit.Assert;
import org.junit.Test;
import org.owasp.csrfguard.util.Strings;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class HonourFormConfigTest {

  @Tested
  HonourFormConfig honourFormConfig;

  @Test
  public void driverHonourChange() {
    honourFormConfig.driverHonourChange(Strings.EMPTY);
    honourFormConfig.driverHonourChange("{\"showHorour\":true,\"showRank\":true,\"cityList\":[1,2],\"honourWall\":[{\"code\":\"Antiepidemicpioneer\",\"name\":\"show.drivermedal.fighttheepidemicname\",\"icon\":\"https://pages.c-ctrip.com/zhuanche/images/honor/medal/honorMedal/active/01_%E6%8A%97%E7%96%AB%E5%85%88%E9%94%8B.png\",\"list\":[{\"date\":\"2022-07-19\",\"driverIdListStr\":\"1000004,3452813\"}],\"grayIcon\":\"https://pages.c-ctrip.com/zhuanche/images/honor/medal/honorMedal/inactive/01_%E6%8A%97%E7%96%AB%E5%85%88%E9%94%8B.png\",\"newIcon\":\"https://pages.c-ctrip.com/zhuanche/images/honor/profileMedal/honorMedal/01_%E6%8A%97%E7%96%AB%E5%85%88%E9%94%8B%E5%A4%87%E4%BB%BD.png\",\"customerShowIndex\":200,\"toCustomerDesc\":\"show.drivermedal.fighttheepidemicexplanation\",\"customerShow\":true,\"desc\":\"抗疫先锋\"},{\"code\":\"herohonor\",\"name\":\"show.drivermedal.gooddeedsname\",\"icon\":\"https://pages.c-ctrip.com/zhuanche/images/honor/medal/honorMedal/active/02_%E5%A5%BD%E4%BA%BA%E5%A5%BD%E4%BA%8B.png\",\"grayIcon\":\"https://pages.c-ctrip.com/zhuanche/images/honor/medal/honorMedal/inactive/02_%E5%A5%BD%E4%BA%BA%E5%A5%BD%E4%BA%8B.png\",\"newIcon\":\"https://pages.c-ctrip.com/zhuanche/images/honor/profileMedal/honorMedal/02_%E5%A5%BD%E4%BA%BA%E5%A5%BD%E4%BA%8B%202.png\",\"list\":[{\"date\":\"2023-09-05\",\"driverIdListStr\":\"1000004\"}],\"toCustomerDesc\":\"show.drivermedal.gooddeedsexplanation\",\"customerShowIndex\":6,\"customerShow\":true,\"desc\":\"好人好事\"},{\"code\":\"goldMedal\",\"name\":\"金牌司机\",\"icon\":\"https://pages.c-ctrip.com/zhuanche/images/driver/myGrade/gold_activated.png\",\"grayIcon\":\"https://pages.c-ctrip.com/zhuanche/images/driver/myGrade/gold_unactivated.png\",\"newIcon\":\"https://pages.c-ctrip.com/zhuanche/images/driver/myGrade/gold_service.png\",\"list\":[],\"customerShow\":false,\"desc\":\"金牌服务\"},{\"code\":\"kingofcity\",\"name\":\"show.drivermedal.kingofcityname\",\"toCustomerDesc\":\"show.drivermedal.kingofcityexplanation\",\"customerShowIndex\":4,\"icon\":\"https://dimg04.c-ctrip.com/images/1ut6p12000dp1kxcm6760.png\",\"grayIcon\":\"https://dimg04.c-ctrip.com/images/1ut5q12000dp1khjp3425.png\",\"newIcon\":\"https://dimg04.c-ctrip.com/images/1ut1212000dp3171rF794.png\",\"list\":[],\"customerShow\":true,\"desc\":\"城市王者\"},{\"code\":\"publicwelfareambassadors\",\"name\":\"show.drivermedal.publicwelfareambassadorsname\",\"toCustomerDesc\":\"show.drivermedal.publicwelfareambassadorsexplanation\",\"customerShowIndex\":5,\"icon\":\"https://dimg04.c-ctrip.com/images/1ut3912000dp1kray3796.png\",\"grayIcon\":\"https://dimg04.c-ctrip.com/images/1ut7012000dp1l811CC22.png\",\"newIcon\":\"https://dimg04.c-ctrip.com/images/1ut4s12000dp30sf7415B.png\",\"list\":[],\"customerShow\":true,\"desc\":\"公益大使\"},{\"code\":\"goodsamaritan\",\"name\":\"show.drivermedal.goodsamaritanname\",\"toCustomerDesc\":\"show.drivermedal.goodsamaritanexplanation\",\"customerShowIndex\":1,\"icon\":\"https://dimg04.c-ctrip.com/images/1ut3c12000dp1kq0oBC90.png\",\"grayIcon\":\"https://dimg04.c-ctrip.com/images/1ut0s12000dp1l1p40A46.png\",\"newIcon\":\"https://dimg04.c-ctrip.com/images/1ut3b12000dp3184p8897.png\",\"list\":[],\"customerShow\":true,\"desc\":\"见义勇为\"},{\"code\":\"returninglostmoney\",\"name\":\"show.drivermedal.returninglostmoneyname\",\"toCustomerDesc\":\"show.drivermedal.returninglostmoneyexplanation\",\"customerShowIndex\":2,\"icon\":\"https://dimg04.c-ctrip.com/images/1ut0b12000dp1kvdl8395.png\",\"grayIcon\":\"https://dimg04.c-ctrip.com/images/1ut0f12000dp1khjtC2E9.png\",\"newIcon\":\"https://dimg04.c-ctrip.com/images/1ut5812000dp31bv3A076.png\",\"list\":[],\"customerShow\":true,\"desc\":\"拾金不昧\"},{\"code\":\"surpriseservice\",\"name\":\"show.drivermedal.surpriseservicename\",\"toCustomerDesc\":\"show.drivermedal.surpriseserviceexplanation\",\"customerShowIndex\":7,\"icon\":\"https://dimg04.c-ctrip.com/images/1ut0i12000dp1kxcpF74C.png\",\"grayIcon\":\"https://dimg04.c-ctrip.com/images/1ut0f12000dp1khjtC2E9.png\",\"newIcon\":\"https://dimg04.c-ctrip.com/images/1ut1u12000dp314ypCEBB.png\",\"list\":[],\"customerShow\":true,\"desc\":\"惊喜服务\"},{\"code\":\"retiredsoldiers\",\"name\":\"show.drivermedal.retiredsoldiersname\",\"toCustomerDesc\":\"show.drivermedal.retiredsoldiersexplanation\",\"customerShowIndex\":3,\"icon\":\"https://dimg04.c-ctrip.com/images/1ut5112000dp1krb3E006.png\",\"grayIcon\":\"https://dimg04.c-ctrip.com/images/1ut7412000dp1l5lf2E66.png\",\"newIcon\":\"https://dimg04.c-ctrip.com/images/1ut3612000dp30z35D253.png\",\"list\":[],\"customerShow\":true,\"desc\":\"退役军人\"}],\"showHonour\":true,\"showCommemoration\":true,\"showMeadl\":true,\"showMedal\":true,\"rankCityListStr\":\"1,2\",\"serviceMedalTocDesc\":[{\"code\":\"serviceDays\",\"toCustomerDesc\":\"show.drivermedal.daysofserviceexplanation\",\"name\":\"show.drivermedal.daysofservicename\",\"customerShow\":false},{\"code\":\"orders\",\"toCustomerDesc\":\"show.drivermedal.servicecompletedexplanation\",\"name\":\"show.drivermedal.servicecompletedname\",\"customerShow\":false},{\"code\":\"comments\",\"toCustomerDesc\":\"show.drivermedal.goodserviceexplanation\",\"customerShowIndex\":104,\"name\":\"show.drivermedal.goodservicename\",\"customerShow\":true},{\"code\":\"goodManner\",\"toCustomerDesc\":\"show.drivermedal.goodserviceandgoodattitudeexplanation\",\"customerShowIndex\":100,\"name\":\"show.drivermedal.goodserviceandgoodattitudename\",\"customerShow\":true},{\"code\":\"goodCar\",\"toCustomerDesc\":\"show.drivermedal.cleancarexplanation\",\"customerShowIndex\":101,\"name\":\"show.drivermedal.cleancarname\",\"customerShow\":true},{\"code\":\"goodMap\",\"toCustomerDesc\":\"show.drivermedal.livemapexplanation\",\"customerShowIndex\":102,\"name\":\"show.drivermedal.livemapname\",\"customerShow\":true},{\"code\":\"goodLuggage\",\"toCustomerDesc\":\"show.drivermedal.helpwithluggageexplanation\",\"customerShowIndex\":103,\"name\":\"show.drivermedal.helpwithluggagename\",\"customerShow\":true}]}");

    boolean isShowMedal = honourFormConfig.isShowMedal();
    Assert.assertTrue(isShowMedal);

    boolean isShowCommemoration = honourFormConfig.isShowCommemoration();
    Assert.assertTrue(isShowCommemoration);

    boolean isRankOpenCity = honourFormConfig.isRankOpenCity(2L);
    Assert.assertTrue(isRankOpenCity);

    List<Long> cityList = honourFormConfig.getRankOpenCity();
    Assert.assertTrue(cityList.size() > 0);

    long count = honourFormConfig.getHonourWallMedalCount(1000004L);
    Assert.assertEquals(2, count);

    GoldMedalDriverModel goldMedalDriverModel = new GoldMedalDriverModel();
    List<FormHonourWallDriverMedalInfo> medalInfos = honourFormConfig.getHonourWallMedalInfo(1000004L, true, goldMedalDriverModel);
    Assert.assertTrue(medalInfos.size() > 0);

    medalInfos = honourFormConfig.getHonourWallMedalInfo(123456L, false, goldMedalDriverModel);
    Assert.assertTrue(medalInfos.size() > 0);
  }
}
