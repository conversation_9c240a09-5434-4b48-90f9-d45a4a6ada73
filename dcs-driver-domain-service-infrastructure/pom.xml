<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dcs-driver-domain-service</artifactId>
        <groupId>com.ctrip.dcs.driver</groupId>
        <version>1.0.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>1.0.2</version>
    <artifactId>dcs-driver-domain-service-infrastructure</artifactId>

    <name>dcs-driver-domain-service-infrastructure</name>

    <dependencies>
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz.accountloginapiserverinternal</groupId>
            <artifactId>accountloginapiserverinternal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz.accountmanagerapiserverinternal</groupId>
            <artifactId>accountmanagerapiserverinternal</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>account-api-server-soa-commontypes</artifactId>
                    <groupId>com.ctrip.basebiz.accountapiserver</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>account-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.dcs.pom</groupId>
            <artifactId>dcs-shopping-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-server</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>soa-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.tms</groupId>
            <artifactId>transport-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.23718</groupId>
            <artifactId>gms-transport-domain-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.message</groupId>
            <artifactId>message-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.infosec.kms</groupId>
            <artifactId>kms-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.infrastructure.service</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.ibu.platform</groupId>
            <artifactId>ibu-shark-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.supply</groupId>
            <artifactId>supply-account-transfer-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.arch</groupId>
            <artifactId>distlock-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.platform.softpbx</groupId>
            <artifactId>softpbxvoipcloud</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.im</groupId>
            <artifactId>im-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.tour.ai</groupId>
            <artifactId>one-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.platform.basesystem.emailservice.v1</groupId>
            <artifactId>emailservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.igt.framework</groupId>
            <artifactId>qmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.frt</groupId>
            <artifactId>product-basic-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver.domain</groupId>
            <artifactId>driver-domain-service</artifactId>
        </dependency>
        <dependency>
            <artifactId>geo-platform-sdk</artifactId>
            <groupId>com.ctrip.dcs.geo</groupId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.driver</groupId>
            <artifactId>dcs-driver-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.payment.fi</groupId>
            <artifactId>thirdparty-cardinfo-management-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.soa.dcs.jnt.dcsscmmerchantservice.v1</groupId>
            <artifactId>dcsscmmerchantservice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.dcs.go</groupId>
            <artifactId>dal</artifactId>
        </dependency>
        <!--电话平台契约-->
        <dependency>
            <groupId>com.ctrip.soa.basebiz.softivr</groupId>
            <artifactId>outcall-soa-predict-service</artifactId>
            <version>1.7.2</version>
        </dependency>
        <!--拆码平台契约-->
        <dependency>
            <groupId>com.ctrip.soa.23343</groupId>
            <artifactId>phonenumbersplitservice</artifactId>
        </dependency>
        <!--自营订单查询服务契约-->
        <dependency>
            <groupId>com.ctrip.dcs.self</groupId>
            <artifactId>self-orderquery-service-api</artifactId>
            <version>1.0.69</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.sysdev</groupId>
            <artifactId>daas-client-soa</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.payment</groupId>
            <artifactId>sec-crypto</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk15on</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-jdk16</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.commons</groupId>
                    <artifactId>commons-pool2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
    </dependencies>
</project>
